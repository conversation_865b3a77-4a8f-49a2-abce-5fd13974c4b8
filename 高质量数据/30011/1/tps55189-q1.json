{"part_number": "TPS55189-Q1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS55189-Q1 具有 I2C 接口的 27V、8A 降压/升压转换器", "features": ["符合 AEC-Q100 标准：温度等级 1：–40°C 至 +125°C 环境工作温度范围", "功能安全型，可提供用于功能安全系统设计的文档", "可编程电源 (PPS) 支持 USB 供电 (USB PD)", "可编程输入电压范围：3.0V 至 27V（绝对最大值为 42V）", "可编程输出电压范围：0.8V 至 22V，步长为 10mV", "可编程电压压摆率", "可编程的压降可调输出电压补偿", "可编程输出电流限值高达 6.35A，步长为 50mA", "±5% 输出电流监测", "在整个负载范围内具有高效率，VIN = 12V、VOUT = 20V 且 IOUT = 3A 时效率为 96%", "轻负载状态下可编程 PFM 和 FPWM 模式", "避免 AM 频段干扰和串扰", "可选的开关频率范围为 200kHz 至 2.2MHz", "可选的频率扩展频谱和相移以降低 EMI", "丰富的保护特性：输出过压保护、利用断续模式实现输出短路保护、逐周期电感器电流限值、8A 平均电感器电流限值", "小解决方案尺寸，开关频率高达 2.2MHz", "3.0mm × 5.0mm HotRod™ QFN 封装"], "description": "TPS55189-Q1 同步降压/升压转换器经过优化，可集成电池或适配器电压转换为电源轨。TPS55189-Q1 集成四个 MOSFET 开关，可为 USB 电力输送 (USB PD) 应用提供紧凑型解决方案。TPS55189-Q1 的输入电压可以高达 27V。通过 I2C 接口，TPS55189-Q1 的输出电压可以在 0.8V 至 22V 之间（步长为 10mV）进行编程。在升压模式下，输入电压为 12V 时，可实现 100W 的功率。它能够通过 9V 输入电压提供 45W 的功率。TPS55189-Q1 采用平均电流模式控制方案。开关频率可通过外部电阻在 200kHz 至 2.2MHz 之间进行编程，并且可与外部时钟同步。TPS55189-Q1 还提供展频选项，从而更大限度地减少峰值 EMI。TPS55189-Q1 提供输出过压保护、平均电感器电流限值、逐周期峰值电感器电流限值和输出短路保护。TPS55189-Q1 还在轻负载条件下通过可选输出电流限值和断续模式保护来确保安全运行。TPS55189-Q1 可以使用具有高开关频率的小型电感器和电容器。它采用 3.0mm × 5.0mm QFN 封装。", "applications": ["汽车充电器", "USB PD", "无线充电器", "汽车信息娱乐系统与仪表组", "汽车尾部照明", "高级驾驶辅助系统 (ADAS)"], "ordering_information": [{"part_number": "TPS55189-Q1", "order_device": "TPS55189QWRYQRQ1", "package_type": "VQFN-HR", "package_drawing_code": "RYQ0021B", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS55189-Q1", "order_device": "TPS55189QWRYQRQ1.A", "package_type": "VQFN-HR", "package_drawing_code": "RYQ0021B", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS55189-Q1", "package_type": "VQFN-HR (RYQ)", "pins": [{"pin_number": "1", "pin_name": "EN/UVLO", "pin_description": "启用和可编程 UVLO。逻辑高电平可启用器件。逻辑低电平可禁用器件。将此引脚连接到外部电阻分压器，可将可编程 UVLO 输入。EN/UVLO 引脚具有 1.23V 的内部基准电压。"}, {"pin_number": "2", "pin_name": "MODE", "pin_description": "I2C 目标地址选择。当它连接至逻辑高电压时，I2C 目标地址为 74H。当它连接至逻辑低电压时，I2C 目标地址为 75H。"}, {"pin_number": "3", "pin_name": "SCL", "pin_description": "I2C 时钟数据。"}, {"pin_number": "4", "pin_name": "SDA", "pin_description": "I2C 数据。"}, {"pin_number": "5", "pin_name": "DITH/SYNC", "pin_description": "抖动频率和同步。当连接至接地端或 1.2V 以上时，无抖动功能。可以在该引脚上应用外部时钟，来同步开关频率。"}, {"pin_number": "6", "pin_name": "FSW", "pin_description": "开关频率可通过该引脚和 AGND 引脚之间的电阻进行编程。"}, {"pin_number": "7", "pin_name": "VIN", "pin_description": "降压/升压转换器的输入。"}, {"pin_number": "8", "pin_name": "SW1", "pin_description": "降压侧的开关节点引脚。它连接到内部降压低侧功率 MOSFET 的漏极，以及内部降压高侧功率 MOSFET 的源极。"}, {"pin_number": "9", "pin_name": "PGND", "pin_description": "器件的电源接地。"}, {"pin_number": "10", "pin_name": "SW2", "pin_description": "升压侧的开关节点引脚。它连接到内部升压低侧功率 MOSFET 的漏极，以及内部升压高侧功率 MOSFET 的源极。"}, {"pin_number": "11", "pin_name": "VOUT", "pin_description": "降压/升压转换器的输出。"}, {"pin_number": "12", "pin_name": "ISP", "pin_description": "电流检测放大器的正输入。在 ISP 引脚和 ISN 引脚之间连接的可选电流检测电阻可以限制输出电流。如果检测到的电压达到寄存器中设置的限值，将激活恒定电流控制环路，并开始调节 ISP 引脚和 ISN 引脚之间的电压。将 ISP 引脚和 ISN 引脚连接到 VOUT 引脚，可以禁用输出电流限制，并使器件在开路状态下运行。"}, {"pin_number": "13", "pin_name": "ISN", "pin_description": "电流检测放大器的负输入。在 ISP 引脚和 ISN 引脚之间连接的可选电流检测电阻可以限制输出电流。如果检测到的电压达到寄存器中设置的限值，将激活恒定电流控制环路，并开始调节 ISP 引脚和 ISN 引脚之间的电压。将 ISP 引脚和 ISN 引脚连接到 VOUT 引脚，可以禁用输出电流限制，它将处于开路状态。"}, {"pin_number": "14", "pin_name": "FB/INT", "pin_description": "当器件使用外部输出电压反馈时，连接到限流电阻的中心抽头以对输出电压进行编程。当器件设置为使用内部反馈时，该引脚会变成开漏输出引脚，当发生内部故障时，该引脚会逻辑低电平。"}, {"pin_number": "15", "pin_name": "COMP", "pin_description": "内部误差放大器的输出。在该引脚和 AGND 引脚之间连接补偿网络。"}, {"pin_number": "16", "pin_name": "CDC", "pin_description": "电压输出。该引脚的电压与 ISP 引脚和 AGND 之间使用的外部电阻器上的电压成正比。在该引脚和 AGND 之间使用一个电阻器，以补偿输出电压。如果使用内部补偿，则该引脚可保持开路。"}, {"pin_number": "17", "pin_name": "AGND", "pin_description": "器件的信号接地。"}, {"pin_number": "18", "pin_name": "VCC", "pin_description": "内部稳压器的输出。在此引脚和 AGND 引脚之间需要一个大于 4.7 µF 的内部电容器。"}, {"pin_number": "19", "pin_name": "BOOT2", "pin_description": "升压高侧 MOSFET 栅极驱动器的电源。必须在此引脚和 SW2 引脚之间连接一个 0.1 µF 的陶瓷电容。"}, {"pin_number": "20", "pin_name": "BOOT1", "pin_description": "降压高侧 MOSFET 栅极驱动器的电源。必须在此引脚和 SW1 引脚之间连接一个 0.1 µF 的陶瓷电容。"}, {"pin_number": "21", "pin_name": "EXTVCC", "pin_description": "为 VCC 选择内部 LDO 或外部 5V。当它连接到逻辑高电压或者保持悬空时，可选择内部 LDO。将它连接到逻辑低电压时，可为 VCC 选择外部 5V。"}]}], "datasheet_cn": "datasheets/20240722110038.pdf", "datasheet_en": "SLVSHW7", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "27V", "min_input_voltage": "3V", "max_output_voltage": "22V", "min_output_voltage": "1V", "max_output_current": "8A", "max_switch_frequency": "2.2MHz", "quiescent_current": "760µA", "high_side_mosfet_resistance": "14mΩ", "low_side_mosfet_resistance": "22mΩ", "over_current_protection_threshold": "8A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM, FPWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "自动重启", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.282V", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.5", "height": "5.5", "length": "5", "width": "3", "type": "OPTION", "pin_count": "1"}]}