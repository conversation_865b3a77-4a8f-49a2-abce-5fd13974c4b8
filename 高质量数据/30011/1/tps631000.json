{"part_number": "TPS631000", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压-升压芯片 (<PERSON><PERSON><PERSON><PERSON>)", "part_number_title": "TPS631000 1.5A 输出电流高功率密度降压/升压转换器", "features": ["1.6V 至 5.5V 输入电压范围，器件启动时输入电压大于 1.65 V", "1.2V 至 5.3V 输出电压范围 (可调节)", "高输出电流能力, 3A 峰值开关电流: VIN ≥ 3V、VOUT = 3.3V 时, 输出电流为 2A; VIN ≥ 2.7V、VOUT = 3.3V 时, 输出电流为 1.5A", "在整个负载范围内具有高效率: 8µA 静态电流 (典型值), 省电模式", "1A 电流阶跃时具有 150mV 的负载阶跃响应", "峰值电流降压/升压模式架构: 无缝转换模式, 输出波纹 < 20mV; 正向和反向电流运行; 启动至预偏置输出; 固定频率运行, 2MHz 开关频率", "安全、可靠运行的特性: 过流保护和短路保护; 采用有源斜坡的集成软启动; 过热保护和过压保护; 带负载断开功能的真正关断功能; 正向和反向电流限制", "小解决方案尺寸: 小型 1µH 电感器; 在整个 VOUT 范围内支持一个 0805 输出电容器", "使用 TPS631000 并借助 WEBENCH® Power Designer 创建定制设计"], "description": "TPS631000 是一款采用恒定频率峰值电流模式控制的降压/升压转换器。该器件具有 3A 峰值电流限制 (典型值) 和 1.6V 至 5.5V 输入电压范围。TPS631000 为系统预稳压器和稳压器提供电源解决方案。根据输入电压不同, 当输入电压近似等于输出电压时, TPS631000 会自动以升压、降压或 3 周期降压/升压模式运行。以定义的占空比进行模式切换, 避免不必要的模式内切换, 以减少输出电压纹波。静态电流为 8µA, 电源处于省电模式, 可在轻载甚至空载条件下实现出色效率。TPS631000 提供非常小的解决方案尺寸, 采用 1.2mm × 2.1mm SOT-583 封装、一个 1µH 电感器和一个 0805 输出电容器。", "applications": ["系统预稳压器 (智能手机、平板电脑、终端、远程信息处理)", "负载点调节 (有线传感器、端口/电缆适配器和加密狗)", "指纹、摄像头传感器 (电子智能锁、IP 网络摄像机)", "射频放大器电源 (智能传感器)", "稳压器 (数据通信、光学模块、制冷/加热)"], "ordering_information": [{"part_number": "TPS631000", "order_device": "TPS631000DRLR", "package_type": "SOT-5X3", "package_drawing_code": "DRL0008A", "output_voltage": "可调节", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS631000", "order_device": "TPS631000DRLR.A", "package_type": "SOT-5X3", "package_drawing_code": "DRL0008A", "output_voltage": "可调节", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS631000", "package_type": "SOT-5X3 (DRL)", "pins": [{"pin_number": "1", "pin_name": "VOUT", "pin_description": "Power stage output"}, {"pin_number": "2", "pin_name": "LX2", "pin_description": "Inductor switching node of the boost stage"}, {"pin_number": "3", "pin_name": "LX1", "pin_description": "Inductor switching node of the buck stage"}, {"pin_number": "4", "pin_name": "VIN", "pin_description": "Supply input voltage"}, {"pin_number": "5", "pin_name": "EN", "pin_description": "Device enable. Set High to enable and Low to disable. It must not be left floating."}, {"pin_number": "6", "pin_name": "MODE", "pin_description": "PFM/PWM selection. Set Low for power save mode, set High for forced PWM. It must not be left floating."}, {"pin_number": "7", "pin_name": "GND", "pin_description": "Power ground"}, {"pin_number": "8", "pin_name": "FB", "pin_description": "Voltage feedback. Sensing pin"}]}], "datasheet_cn": "ZHCSP59B", "datasheet_en": "SLVSFH3", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.6V", "max_output_voltage": "5.3V", "min_output_voltage": "1.2V", "max_output_current": "1.5A", "max_switch_frequency": "2.2MHz", "quiescent_current": "8µA", "high_side_mosfet_resistance": "50mΩ", "low_side_mosfet_resistance": "85mΩ", "over_current_protection_threshold": "3A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.5V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "OPTION", "pitch": "0.5", "height": "0.6", "length": "2.1", "width": "1.6", "pin_count": "4"}]}