{"part_number": "SM72442", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC控制器", "category_lv3": "MPPT控制器", "part_number_title": "Programmable Maximum Power Point Tracking Controller for Photovoltaic Solar Panels", "features": ["Renewable Energy Grade", "Programmable Maximum Power Point Tracking", "Photovoltaic Solar Panel Voltage and Current Diagnostic", "Single Inductor Four Switch Buck-Boost Converter Control", "I2C Interface for Communication", "VOUT Overvoltage Protection", "Over-Current Protection", "Package: TSSOP-28"], "description": "The SM72442 is a programmable MPPT controller capable of controlling four PWM gate drive signals for a 4-switch buck-boost converter. The SM72442 also features a proprietary algorithm called Panel Mode which allows for the panel to be connected directly to the output of your power optimizer circuit. Along with the SM72295 (Photovoltaic Full Bridge Driver), it creates a solution for an MPPT configured DC-DC converter with efficiencies up to 99.5%. Integrated into the chip is an 8-channel, 12 bit A/D converter used to sense input and output voltages and currents, as well as board configuration. Externally programmable values include maximum output voltage and current as well as different settings forslew rate, soft-start and Panel Mode.", "applications": ["Renewable Energy", "Photovoltaic Solar Panels", "Power Optimizer"], "ordering_information": [{"part_number": "SM72442", "order_device": "SM72442MT/NOPB", "package_type": "TSSOP", "package_drawing_code": "PW0028A", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "SM72442", "order_device": "SM72442MT/NOPB.A", "package_type": "TSSOP", "package_drawing_code": "PW0028A", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "SM72442", "order_device": "SM72442MTE/NOPB", "package_type": "TSSOP", "package_drawing_code": "PW0028A", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "SM72442", "order_device": "SM72442MTE/NOPB.A", "package_type": "TSSOP", "package_drawing_code": "PW0028A", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "SM72442", "order_device": "SM72442MTX/NOPB", "package_type": "TSSOP", "package_drawing_code": "PW0028A", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "SM72442", "order_device": "SM72442MTX/NOPB.A", "package_type": "TSSOP", "package_drawing_code": "PW0028A", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "SM72442", "package_type": "TSSOP-28", "pins": [{"pin_number": "1", "pin_name": "RST", "pin_description": "Active low signal. External reset input signal to the digital circuit."}, {"pin_number": "2", "pin_name": "NC1", "pin_description": "Reserved for test only. This pin should be grounded."}, {"pin_number": "3", "pin_name": "VDDD", "pin_description": "Digital supply voltage. This pin should be connected to a 5V supply, and bypassed to VSSD with a 0.1 µF monolithic ceramic capacitor."}, {"pin_number": "4", "pin_name": "VSSD", "pin_description": "Digital ground. The ground return for the digital supply and signals."}, {"pin_number": "5", "pin_name": "NC2", "pin_description": "No Connect. This pin should be pulled up to the 5V supply using 10k resistor."}, {"pin_number": "6", "pin_name": "I2C0", "pin_description": "Addressing for I2C communication."}, {"pin_number": "7", "pin_name": "I2C1", "pin_description": "Addressing for I2C communication."}, {"pin_number": "8", "pin_name": "SCL", "pin_description": "I2C clock."}, {"pin_number": "9", "pin_name": "SDA", "pin_description": "I2C data."}, {"pin_number": "10", "pin_name": "NC3", "pin_description": "Reserved for test only. This pin should be grounded."}, {"pin_number": "11", "pin_name": "PM_OUT", "pin_description": "When Panel Mode is active, this pin will output a 400 kHz square wave signal with amplitude of 5V. Otherwise, it stays low."}, {"pin_number": "12", "pin_name": "VDDA", "pin_description": "Analog supply voltage. This voltage is also used as the reference voltage. This pin should be connected to a 5V supply, and bypassed to VSSA with a 1 µF and 0.1 µF monolithic ceramic capacitor."}, {"pin_number": "13", "pin_name": "VSSA", "pin_description": "Analog ground. The ground return for the analog supply and signals."}, {"pin_number": "14", "pin_name": "A0", "pin_description": "A/D Input Channel 0. Connect a resistor divider to 5V supply to set the maximum output voltage. Please refer to the application section for more information on setting the resistor value."}, {"pin_number": "15", "pin_name": "AVIN", "pin_description": "Input voltage sensing pin."}, {"pin_number": "16", "pin_name": "A2", "pin_description": "A/D Input Channel 2. Connect a resistor divider to a 5V supply to set the condition to enter and exit Panel Mode (PM). Refer to configurable modes for SM72442 in the application section."}, {"pin_number": "17", "pin_name": "AVOUT", "pin_description": "Output voltage sensing pin."}, {"pin_number": "18", "pin_name": "A4", "pin_description": "A/D Input Channel 4. Connect a resistor divider to a 5V supply to set the maximum output current. Please refer to the application section for more information on setting the resistor value."}, {"pin_number": "19", "pin_name": "AIIN", "pin_description": "Input current sensing pin."}, {"pin_number": "20", "pin_name": "A6", "pin_description": "A/D Input Channel 6. Connect a resistor divider to a 5V supply to set the output voltage slew rate and various PM configurations. Refer to configurable modes for SM72442 in the application section."}, {"pin_number": "21", "pin_name": "AIOUT", "pin_description": "Output current sensing pin."}, {"pin_number": "22", "pin_name": "I2C2", "pin_description": "Addressing for I2C communication."}, {"pin_number": "23", "pin_name": "NC4", "pin_description": "No Connect. This pin should be connected with 60.4k pull-up resistor to 5V."}, {"pin_number": "24", "pin_name": "LIB", "pin_description": "Low side boost PWM output."}, {"pin_number": "25", "pin_name": "HIB", "pin_description": "High side boost PWM output."}, {"pin_number": "26", "pin_name": "HIA", "pin_description": "High side buck PWM output."}, {"pin_number": "27", "pin_name": "LIA", "pin_description": "Low side buck PWM output."}, {"pin_number": "28", "pin_name": "PM", "pin_description": "Panel Mode Pin. Active low. Pulling this pin low will force the chip into Panel Mode."}]}], "datasheet_cn": null, "datasheet_en": "SM72442 - SNVS689H (2013-04-01)", "family_comparison": null, "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": null, "min_input_voltage": null, "max_output_voltage": "可调", "min_output_voltage": null, "max_output_current": "可调", "max_switch_frequency": "0.22MHz", "quiescent_current": null, "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "No", "light_load_mode": null, "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "No", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "No", "pass_through_mode": "True", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": null, "output_reference_voltage": "不适用", "loop_control_mode": "未找到"}, "package": [{"type": "OPTION", "pin_count": "2442", "pitch": "0.65", "height": "1.2", "width": "4.4", "length": "9.7"}]}