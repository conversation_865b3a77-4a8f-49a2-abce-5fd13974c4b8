[{"part_number": "TPS631012", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS631012 和 TPS631013 采用 Wafer Chip Scale Package 封装、支持I²C的1.6V 至5.5V输入电压、1.5A降压/升压转换器", "features": ["输入电压范围为1.6V 至5.5V - 器件启动时输入电压大于 1.65V", "1.0V至5.5V 输出电压范围(可调)", "高输出电流能力, 3A 峰值开关电流 - 当VIN ≥ 3V 且 VOUT = 3.3V 时, IOUT 为 2A; 当VIN ≥ 2.7V 且 VOUT = 3.3V 时, IOUT为1.5A", "在整个负载范围内具有高效率 - 8µA静态电流(典型值); 可配置的自动节电模式和强制 PWM 模式", "峰值电流降压/升压模式架构 - 无缝模式转换; 正向和反向电流运行; 启动至预偏置输出; 固定频率运行, 2MHz 开关频率", "安全、可靠运行的特性 - 过流保护和短路保护; 采用有源斜坡的集成软启动; 过热保护和过压保护; 带负载断开功能的真正关断功能; 正向和反向电流限制", "内部EN的默认设置 - TPS631012 和 TPS631012X (X=1、2、3): CONVERTER_EN = 0; TPS631013 : CONVERTER_EN = 1", "小尺寸解决方案 - 小型 1µH 电感器; 1.803mm x 0.905mm, 晶圆芯片级封装 (WCSP)"], "description": "TPS631012 和 TPS631013 是采用微型 Wafer Chip Scale Package 的恒定频率峰值电流模式控制降压/升压转换器。这两款转换器具有3A的典型峰值电流限制和1.6V 至5.5V的输入电压范围,可提供适用于系统前置稳压器和电压稳定器的电源解决方案。根据输入电压的不同,当输入电压近似等于输出电压时,TPS631012 和 TPS631013会自动以升压、降压或3周期降压/升压模式运行。模式切换采用定义的占空比进行,避免了不必要的模式内切换,从而减少输出电压纹波。8µA静态电流和省电模式可在轻负载甚至空载条件下实现超高效率。这些器件采用WCSP 封装,具有超小解决方案尺寸。", "applications": ["TWS", "系统预稳压器(智能手机、平板电脑、终端、远程信息处理)", "负载点调节(有线传感器、端口/电缆适配器和加密狗)", "指纹、摄像头传感器(电子智能锁、IP 网络摄像机)", "稳压器(数据通信、光学模块、制冷/加热)"], "ordering_information": [{"part_number": "TPS631012", "order_device": "TPS631012YBGR", "package_type": "DSBGA", "package_drawing_code": "YBG0008-C02", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS631012", "order_device": "TPS631012YBGR.A", "package_type": "DSBGA", "package_drawing_code": "YBG0008-C02", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS631012", "package_type": "YBG (WCSP)", "pins": [{"pin_number": "A1", "pin_name": "VIN", "pin_description": "电源输入电压"}, {"pin_number": "A2", "pin_name": "EN", "pin_description": "器件使能。设置为高电平进行启用,设置为低电平进行禁用。禁止处于悬空状态。"}, {"pin_number": "B1", "pin_name": "LX1", "pin_description": "降压级的电感器开关节点"}, {"pin_number": "B2", "pin_name": "SDA", "pin_description": "I2C 串行接口数据。使用电阻器将该引脚上拉至I2C总线电压。"}, {"pin_number": "C1", "pin_name": "LX2", "pin_description": "升压级的电感器开关节点"}, {"pin_number": "C2", "pin_name": "GND", "pin_description": "电源接地"}, {"pin_number": "D1", "pin_name": "VOUT", "pin_description": "功率级输出"}, {"pin_number": "D2", "pin_name": "SCL", "pin_description": "I2C 串行接口时钟。使用电阻器将该引脚上拉至I2C总线电压。"}]}], "datasheet_cn": "ZHCSRH7B", "datasheet_en": "SLVSH51", "family_comparison": "TPS631012和TPS631013的主要区别在于内部EN(CONVERTER_EN)的默认设置。TPS631012默认值为0（禁用），TPS631013默认值为1（启用）。此外，TPS631012的不同变体（TPS631012X）具有不同的I2C目标地址。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.6V", "max_output_voltage": "5.5V", "min_output_voltage": "1.0V", "max_output_current": "1.5A", "max_switch_frequency": "2.2MHz", "quiescent_current": "8µA", "high_side_mosfet_resistance": "45mΩ", "low_side_mosfet_resistance": "50mΩ", "over_current_protection_threshold": "3A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部可调", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "未找到", "output_reference_voltage": "不适用(数字设定)", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.5", "length": "1.823", "width": "0.925", "type": "DSBGA", "pin_count": "1"}]}, {"part_number": "TPS631013", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS631012 和 TPS631013 采用 Wafer Chip Scale Package 封装、支持I²C的1.6V 至5.5V输入电压、1.5A降压/升压转换器", "features": ["输入电压范围为1.6V 至5.5V - 器件启动时输入电压大于 1.65V", "1.0V至5.5V 输出电压范围(可调)", "高输出电流能力, 3A 峰值开关电流 - 当VIN ≥ 3V 且 VOUT = 3.3V 时, IOUT 为 2A; 当VIN ≥ 2.7V 且 VOUT = 3.3V 时, IOUT为1.5A", "在整个负载范围内具有高效率 - 8µA静态电流(典型值); 可配置的自动节电模式和强制 PWM 模式", "峰值电流降压/升压模式架构 - 无缝模式转换; 正向和反向电流运行; 启动至预偏置输出; 固定频率运行, 2MHz 开关频率", "安全、可靠运行的特性 - 过流保护和短路保护; 采用有源斜坡的集成软启动; 过热保护和过压保护; 带负载断开功能的真正关断功能; 正向和反向电流限制", "内部EN的默认设置 - TPS631012 和 TPS631012X (X=1、2、3): CONVERTER_EN = 0; TPS631013 : CONVERTER_EN = 1", "小尺寸解决方案 - 小型 1µH 电感器; 1.803mm x 0.905mm, 晶圆芯片级封装 (WCSP)"], "description": "TPS631012 和 TPS631013 是采用微型 Wafer Chip Scale Package 的恒定频率峰值电流模式控制降压/升压转换器。这两款转换器具有3A的典型峰值电流限制和1.6V 至5.5V的输入电压范围,可提供适用于系统前置稳压器和电压稳定器的电源解决方案。根据输入电压的不同,当输入电压近似等于输出电压时,TPS631012 和 TPS631013会自动以升压、降压或3周期降压/升压模式运行。模式切换采用定义的占空比进行,避免了不必要的模式内切换,从而减少输出电压纹波。8µA静态电流和省电模式可在轻负载甚至空载条件下实现超高效率。这些器件采用WCSP 封装,具有超小解决方案尺寸。", "applications": ["TWS", "系统预稳压器(智能手机、平板电脑、终端、远程信息处理)", "负载点调节(有线传感器、端口/电缆适配器和加密狗)", "指纹、摄像头传感器(电子智能锁、IP 网络摄像机)", "稳压器(数据通信、光学模块、制冷/加热)"], "ordering_information": [{"part_number": "TPS631013", "order_device": "TPS631013YBGR", "package_type": "DSBGA", "package_drawing_code": "YBG0008-C02", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS631013", "order_device": "TPS631013YBGR.A", "package_type": "DSBGA", "package_drawing_code": "YBG0008-C02", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS631013", "package_type": "YBG (WCSP)", "pins": [{"pin_number": "A1", "pin_name": "VIN", "pin_description": "电源输入电压"}, {"pin_number": "A2", "pin_name": "EN", "pin_description": "器件使能。设置为高电平进行启用,设置为低电平进行禁用。禁止处于悬空状态。"}, {"pin_number": "B1", "pin_name": "LX1", "pin_description": "降压级的电感器开关节点"}, {"pin_number": "B2", "pin_name": "SDA", "pin_description": "I2C 串行接口数据。使用电阻器将该引脚上拉至I2C总线电压。"}, {"pin_number": "C1", "pin_name": "LX2", "pin_description": "升压级的电感器开关节点"}, {"pin_number": "C2", "pin_name": "GND", "pin_description": "电源接地"}, {"pin_number": "D1", "pin_name": "VOUT", "pin_description": "功率级输出"}, {"pin_number": "D2", "pin_name": "SCL", "pin_description": "I2C 串行接口时钟。使用电阻器将该引脚上拉至I2C总线电压。"}]}], "datasheet_cn": "ZHCSRH7B", "datasheet_en": "SLVSH51", "family_comparison": "TPS631012和TPS631013的主要区别在于内部EN(CONVERTER_EN)的默认设置。TPS631012默认值为0（禁用），TPS631013默认值为1（启用）。此外，TPS631012的不同变体（TPS631012X）具有不同的I2C目标地址。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.6V", "max_output_voltage": "5.5V", "min_output_voltage": "1.0V", "max_output_current": "1.5A", "max_switch_frequency": "2.2MHz", "quiescent_current": "8µA", "high_side_mosfet_resistance": "45mΩ", "low_side_mosfet_resistance": "50mΩ", "over_current_protection_threshold": "3A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部可调", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "未找到", "output_reference_voltage": "不适用(数字设定)", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.5", "length": "1.823", "width": "0.925", "type": "DSBGA", "pin_count": "1"}]}]