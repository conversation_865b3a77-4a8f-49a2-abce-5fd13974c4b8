[{"part_number": "MAX77680", "manufacturer": "Maxim Integrated", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "SIMO", "part_number_title": "3-Output SIMO Buck-Boost Regulator with Power Sequencer and 3µA IQ", "features": ["Single-Inductor, Multiple-Output (SIMO) Extends Battery Life", "300nA Shutdown Current", "3.0μA Operating Quiescent Current (3 SIMO Channels On)", "Improves Overall System Efficiency while Reducing Size", "Maintains Regulation without Dropout unlike Traditional Bucks", "Glitchless Buck-Boost Operation", "Compact, High-Efficiency Power Solution", "Three Independent Channels from SIMO Regulator", "2.7V to 5.5V Input Voltage Range from Single Cell Li-Ion", "0.8V to 5.25V Output Voltage Range (Table 1)", "Flexible Power Sequencing", "On-Key Input for Hardware Enable", "Reset Output", "Small Size", "2.75mm x 2.15mm (0.7mm max height) WLP", "30-Bump, 0.4mm Pitch, 6 x 5 Array", "15.2mm² Total Solution Size"], "description": "The MAX77680/MAX77681 is a 3-channel single-inductor multiple-output (SIMO) buck-boost regulator that regulates three independent rails using only 3µA of quiescent current (IQ). The SIMO improves battery life by replacing inefficient LDOs while being competitive in efficiency to traditional single-output bucks. The SIMO operates on a input supply between 2.7V and 5.5V. The outputs are independently programmable between 0.8V and 5.25V depending on ordering option. Each output is a buck-boost with glitchless transition between buck and boost operation. The SIMO can support >300mA loads (1.8VOUT, 3.7VIN). The device integrates a flexible power sequencer (FPS) to control power-up/down order of each output. The default output voltages and sequence can be programmed at the factory. An I2C serial interface is used to further configure the device. The MAX77680/MAX77681 is available in a 30-bump wafer-level package (WLP). Total solution size is only 15.5mm². For a similar product with an LDO and battery charger, refer to the MAX77650 data sheet.", "applications": ["Hearables: Bluetooth Headphones and Earbuds", "Wearables: Fitness, Health, and Activity Monitors", "Action Cameras, Wearable/Body Cameras", "Low-Power Internet of Things (IoT) Gadgets"], "ordering_information": [{"part_number": "MAX77680", "order_device": "MAX77680EWV+*", "package_type": "WLP", "package_drawing_code": "21-100047", "output_voltage": "Programmable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "MAX77680", "order_device": "MAX77680AEWV+T", "package_type": "WLP", "package_drawing_code": "21-100047", "output_voltage": "Programmable", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX77680", "package_type": "WLP", "pins": [{"pin_number": "A1", "pin_name": "PWR_HLD", "pin_description": "Active-High Power Hold Input. Assert PWR_HLD to keep the on/off controller in its on state. If PWR_HLD is not needed, connect it to SYS and use the SFT_RST bits to power the device down."}, {"pin_number": "A2", "pin_name": "nEN", "pin_description": "Active-Low Enable Input. EN supports push-button or slide-switch configurations."}, {"pin_number": "A3", "pin_name": "SDA", "pin_description": "I2C Data"}, {"pin_number": "A4, A5, A6, B1, B3, B5, B6, C3, D1, D2, E1", "pin_name": "GND", "pin_description": "Quiet Ground. Connect GND and PGND to the low-impedance ground plane of the PCB."}, {"pin_number": "B2", "pin_name": "nRST", "pin_description": "Active-Low, Open-Drain Reset Output. Connect a 100kΩ pullup resistor between RST and a voltage equal to or less than VSYS."}, {"pin_number": "B4", "pin_name": "SCL", "pin_description": "I2C Clock"}, {"pin_number": "C1, D3", "pin_name": "NC", "pin_description": "No Connection. Not internally connected."}, {"pin_number": "C2", "pin_name": "nIRQ", "pin_description": "Active-Low, Open-Drain Interrupt Output. Connect a 100kΩ pullup resistor between IRQ and a voltage equal to or less than VSYS."}, {"pin_number": "C4", "pin_name": "Vio", "pin_description": "I2C Interface Power"}, {"pin_number": "C5", "pin_name": "BST", "pin_description": "SIMO Power Input for the High-Side Output NMOS Drivers. Connect a 3300pF ceramic capacitor between BST and LXB."}, {"pin_number": "C6", "pin_name": "SBB0", "pin_description": "SIMO Buck-Boost Output 0. SBB0 is the power output for channel 0 of the SIMO buck-boost. Bypass SBB0 to PGND with a 10µF ceramic capacitor."}, {"pin_number": "D4", "pin_name": "LXA", "pin_description": "Switching Node A. LXA is driven between PGND and IN_SBB when any SIMO channel is enabled. LXA is driven to PGND when all SIMO channels are disabled. Connect a 1.5µH inductor between LXA and LXB."}, {"pin_number": "D5", "pin_name": "LXB", "pin_description": "Switching Node B. LXB is driven between PGND and SBBx when SBBx is enabled. LXB is driven to PGND when all SIMO channels are disabled. Connect a 1.5µH inductor between LXA and LXB."}, {"pin_number": "D6", "pin_name": "SBB1", "pin_description": "SIMO Buck-Boost Output 1. SBB1 is the power output for channel 1 of the SIMO buck-boost. Bypass SBB1 to PGND with a 10µF ceramic capacitor."}, {"pin_number": "E2, E3", "pin_name": "SYS", "pin_description": "System Power Output. SYS provides power to the system resources as well as the control logic of the device. Connect to IN_SBB and bypass to GND with a 22µF ceramic capacitor."}, {"pin_number": "E4", "pin_name": "IN_SBB", "pin_description": "SIMO Power Input. Connect IN_SBB to SYS and bypass to PGND with a 22µF ceramic capacitor as close as possible to the IN_SBB pin."}, {"pin_number": "E5", "pin_name": "PGND", "pin_description": "Power ground for the SIMO low-side FETs. Connect both PGND and GND to the low-impedance ground plane of the PCB."}, {"pin_number": "E6", "pin_name": "SBB2", "pin_description": "SIMO Buck-Boost Output 2. SBB2 is the power output for channel 2 of the SIMO buck-boost. Bypass SBB2 to PGND with a 10µF ceramic capacitor."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77680/MAX77681, Rev 0, 2018-07-01", "family_comparison": "Key differences between MAX77680 and MAX77681 are the output voltage ranges for channels SBB1 and SBB2. MAX77680 has lower voltage ranges (up to 1.5875V/3.95V) while MAX77681 supports higher ranges (up to 5.25V).", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 3, "max_input_voltage": "5.5V", "min_input_voltage": "2.7V", "max_output_voltage": "3.95V", "min_output_voltage": "1V", "max_output_current": "0.3A", "max_switch_frequency": "未找到", "quiescent_current": "3µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "1A", "operation_mode": "同步", "pass_through_mode": "No", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "Latch", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "2.5", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "Information", "pin_count": "1", "pitch": "0.4", "height": "0.7", "width": "2.15", "length": "2.75"}]}, {"part_number": "MAX77681", "manufacturer": "Maxim Integrated", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "SIMO", "part_number_title": "3-Output SIMO Buck-Boost Regulator with Power Sequencer and 3µA IQ", "features": ["Single-Inductor, Multiple-Output (SIMO) Extends Battery Life", "300nA Shutdown Current", "3.0μA Operating Quiescent Current (3 SIMO Channels On)", "Improves Overall System Efficiency while Reducing Size", "Maintains Regulation without Dropout unlike Traditional Bucks", "Glitchless Buck-Boost Operation", "Compact, High-Efficiency Power Solution", "Three Independent Channels from SIMO Regulator", "2.7V to 5.5V Input Voltage Range from Single Cell Li-Ion", "0.8V to 5.25V Output Voltage Range (Table 1)", "Flexible Power Sequencing", "On-Key Input for Hardware Enable", "Reset Output", "Small Size", "2.75mm x 2.15mm (0.7mm max height) WLP", "30-Bump, 0.4mm Pitch, 6 x 5 Array", "15.2mm² Total Solution Size"], "description": "The MAX77680/MAX77681 is a 3-channel single-inductor multiple-output (SIMO) buck-boost regulator that regulates three independent rails using only 3µA of quiescent current (IQ). The SIMO improves battery life by replacing inefficient LDOs while being competitive in efficiency to traditional single-output bucks. The SIMO operates on a input supply between 2.7V and 5.5V. The outputs are independently programmable between 0.8V and 5.25V depending on ordering option. Each output is a buck-boost with glitchless transition between buck and boost operation. The SIMO can support >300mA loads (1.8VOUT, 3.7VIN). The device integrates a flexible power sequencer (FPS) to control power-up/down order of each output. The default output voltages and sequence can be programmed at the factory. An I2C serial interface is used to further configure the device. The MAX77680/MAX77681 is available in a 30-bump wafer-level package (WLP). Total solution size is only 15.5mm². For a similar product with an LDO and battery charger, refer to the MAX77650 data sheet.", "applications": ["Hearables: Bluetooth Headphones and Earbuds", "Wearables: Fitness, Health, and Activity Monitors", "Action Cameras, Wearable/Body Cameras", "Low-Power Internet of Things (IoT) Gadgets"], "ordering_information": [{"part_number": "MAX77681", "order_device": "MAX77681EWV+*", "package_type": "WLP", "package_drawing_code": "21-100047", "output_voltage": "Programmable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "MAX77681", "order_device": "MAX77681AEWV+T", "package_type": "WLP", "package_drawing_code": "21-100047", "output_voltage": "Programmable", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX77681", "package_type": "WLP", "pins": [{"pin_number": "A1", "pin_name": "PWR_HLD", "pin_description": "Active-High Power Hold Input. Assert PWR_HLD to keep the on/off controller in its on state. If PWR_HLD is not needed, connect it to SYS and use the SFT_RST bits to power the device down."}, {"pin_number": "A2", "pin_name": "nEN", "pin_description": "Active-Low Enable Input. EN supports push-button or slide-switch configurations."}, {"pin_number": "A3", "pin_name": "SDA", "pin_description": "I2C Data"}, {"pin_number": "A4, A5, A6, B1, B3, B5, B6, C3, D1, D2, E1", "pin_name": "GND", "pin_description": "Quiet Ground. Connect GND and PGND to the low-impedance ground plane of the PCB."}, {"pin_number": "B2", "pin_name": "nRST", "pin_description": "Active-Low, Open-Drain Reset Output. Connect a 100kΩ pullup resistor between RST and a voltage equal to or less than VSYS."}, {"pin_number": "B4", "pin_name": "SCL", "pin_description": "I2C Clock"}, {"pin_number": "C1, D3", "pin_name": "NC", "pin_description": "No Connection. Not internally connected."}, {"pin_number": "C2", "pin_name": "nIRQ", "pin_description": "Active-Low, Open-Drain Interrupt Output. Connect a 100kΩ pullup resistor between IRQ and a voltage equal to or less than VSYS."}, {"pin_number": "C4", "pin_name": "Vio", "pin_description": "I2C Interface Power"}, {"pin_number": "C5", "pin_name": "BST", "pin_description": "SIMO Power Input for the High-Side Output NMOS Drivers. Connect a 3300pF ceramic capacitor between BST and LXB."}, {"pin_number": "C6", "pin_name": "SBB0", "pin_description": "SIMO Buck-Boost Output 0. SBB0 is the power output for channel 0 of the SIMO buck-boost. Bypass SBB0 to PGND with a 10µF ceramic capacitor."}, {"pin_number": "D4", "pin_name": "LXA", "pin_description": "Switching Node A. LXA is driven between PGND and IN_SBB when any SIMO channel is enabled. LXA is driven to PGND when all SIMO channels are disabled. Connect a 1.5µH inductor between LXA and LXB."}, {"pin_number": "D5", "pin_name": "LXB", "pin_description": "Switching Node B. LXB is driven between PGND and SBBx when SBBx is enabled. LXB is driven to PGND when all SIMO channels are disabled. Connect a 1.5µH inductor between LXA and LXB."}, {"pin_number": "D6", "pin_name": "SBB1", "pin_description": "SIMO Buck-Boost Output 1. SBB1 is the power output for channel 1 of the SIMO buck-boost. Bypass SBB1 to PGND with a 10µF ceramic capacitor."}, {"pin_number": "E2, E3", "pin_name": "SYS", "pin_description": "System Power Output. SYS provides power to the system resources as well as the control logic of the device. Connect to IN_SBB and bypass to GND with a 22µF ceramic capacitor."}, {"pin_number": "E4", "pin_name": "IN_SBB", "pin_description": "SIMO Power Input. Connect IN_SBB to SYS and bypass to PGND with a 22µF ceramic capacitor as close as possible to the IN_SBB pin."}, {"pin_number": "E5", "pin_name": "PGND", "pin_description": "Power ground for the SIMO low-side FETs. Connect both PGND and GND to the low-impedance ground plane of the PCB."}, {"pin_number": "E6", "pin_name": "SBB2", "pin_description": "SIMO Buck-Boost Output 2. SBB2 is the power output for channel 2 of the SIMO buck-boost. Bypass SBB2 to PGND with a 10µF ceramic capacitor."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77680/MAX77681, Rev 0, 2018-07-01", "family_comparison": "Key differences between MAX77680 and MAX77681 are the output voltage ranges for channels SBB1 and SBB2. MAX77680 has lower voltage ranges (up to 1.5875V/3.95V) while MAX77681 supports higher ranges (up to 5.25V).", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 3, "max_input_voltage": "5.5V", "min_input_voltage": "2.7V", "max_output_voltage": "5.25V", "min_output_voltage": "1V", "max_output_current": "0.3A", "max_switch_frequency": "未找到", "quiescent_current": "3µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "1A", "operation_mode": "同步", "pass_through_mode": "No", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "Latch", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "2.5", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "Information", "pin_count": "1", "pitch": "0.4", "height": "0.7", "width": "2.15", "length": "2.75"}]}]