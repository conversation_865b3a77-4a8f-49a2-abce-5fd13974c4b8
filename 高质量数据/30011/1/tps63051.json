[{"part_number": "TPS63050", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS6305x 开关电流为1A、具备可调节软启动功能的单电感降压-升压转换器", "features": ["实时降压或升压,支持在降压和升压模式之间无缝转换", "输入电压范围为2.5V 至5.5V", "0.5A 持续输出电流:Vin ≥ 2.5V、VOUT = 3.3V", "具有可调节输出电压和固定输出电压两个版本可选", "在升压模式中效率大于90%,在降压模式中效率大于95%", "开关频率典型值为2.5MHz", "平均输入电流限制可调节", "软启动时间可调节", "器件静态电流小于60µA", "具有自动节电模式或强制 PWM 模式", "关断期间负载断开", "提供过热保护", "采用1.6mm x 1.2mm、12引脚 WCSP 小型封装和 2.5mm x 2.5mm、12引脚、HotRod TM QFN封装", "借助以下工具创建定制设计方案：– TPS63050，使用 WEBENCH® 电源设计器– TPS63051，使用 WEBENCH® 电源设计器"], "description": "TPS6305x 系列器件是一款静态电流较低的高效降压/升压转换器, 适用于输入电压高于或低于输出电压的应用。在升压模式下,持续输出电流最高可达 500mA;在降压模式下,持续输出最高可达1A。最大平均开关电流限制为1A(典型值)。TPS6305x 系列器件在整个输入电压范围内针对输出电压进行稳压操作,可根据输入电压自动切换为降压或升压模式,从而在两种模式之间实现无缝转换。该降压/升压转换器基于使用同步整流的固定频率PWM 控制器,可实现最高效率。在负载电流较低的情况下,该转换器进入节能模式,从而在整个负载电流范围内保持高效率。用户可以通过脉频调制 (PFM)/PWM 引脚选择自动PFM/PWM 工作模式或强制 PWM 工作模式。在PWM模式下通常使用2.5MHz 固定频率。使用一个外部电阻分压器可对输出电压进行编程,或者在芯片上对输出电压进行内部固定。转换器可被禁用以最大限度地减少电池消耗。在关断期间,负载从电池上断开。该器件采用12引脚芯片尺寸球状引脚栅格阵列 (DSBGA) 封装和12引脚 HotRod 封装。", "applications": ["手机和智能电话", "平板电脑", "PC 和智能手机附件", "通过电池供电的应用", "智能电网/智能仪表"], "ordering_information": [{"part_number": "TPS63050", "order_device": "TPS63050RMWR", "package_type": "VQFN-HR", "package_drawing_code": "RMW0012A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63050", "order_device": "TPS63050RMWT", "package_type": "VQFN-HR", "package_drawing_code": "RMW0012A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63050", "order_device": "TPS63050YFFR", "package_type": "DSBGA", "package_drawing_code": "YFF0012", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63050", "order_device": "TPS63050YFFT", "package_type": "DSBGA", "package_drawing_code": "YFF0012", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63050", "package_type": "WCSP", "pins": [{"pin_number": "A3", "pin_name": "EN", "pin_description": "Enable input. (1 enabled, 0 disabled). It must not be left floating"}, {"pin_number": "D2", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT on fixed output voltage versions"}, {"pin_number": "B1", "pin_name": "GND", "pin_description": "Ground for Power stage and Control stage"}, {"pin_number": "B2", "pin_name": "ILIM0", "pin_description": "Programmable inrush current limit input works together with ILIM1. It must not be left floating"}, {"pin_number": "B3", "pin_name": "ILIM1", "pin_description": "Programmable inrush current limit input works together with ILIM0. Do not leave floating"}, {"pin_number": "A1", "pin_name": "L1", "pin_description": "Connection for Inductor"}, {"pin_number": "C1", "pin_name": "L2", "pin_description": "Connection for Inductor"}, {"pin_number": "C2", "pin_name": "PFM/PWM", "pin_description": "0 for PFM mode 1 for forced PWM mode. It must not be left floating"}, {"pin_number": "C3", "pin_name": "PG", "pin_description": "Power good open drain output"}, {"pin_number": "D3", "pin_name": "SS", "pin_description": "Adjustable Soft-Start. If left floating default soft-start time is set"}, {"pin_number": "A2", "pin_name": "VIN", "pin_description": "Supply voltage for power stage and control stage"}, {"pin_number": "D1", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}]}, {"product_part_number": "TPS63050", "package_type": "HotRod", "pins": [{"pin_number": "11", "pin_name": "EN", "pin_description": "Enable input. (1 enabled, 0 disabled). It must not be left floating"}, {"pin_number": "5", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT on fixed output voltage versions"}, {"pin_number": "2,9", "pin_name": "GND", "pin_description": "Ground for Power stage and Control stage"}, {"pin_number": "10", "pin_name": "ILIM0", "pin_description": "Programmable inrush current limit input works together with ILIM1. It must not be left floating"}, {"pin_number": "1", "pin_name": "L1", "pin_description": "Connection for Inductor"}, {"pin_number": "3", "pin_name": "L2", "pin_description": "Connection for Inductor"}, {"pin_number": "6", "pin_name": "PFM/PWM", "pin_description": "0 for PFM mode 1 for forced PWM mode. It must not be left floating"}, {"pin_number": "8", "pin_name": "PG", "pin_description": "Power good open drain output"}, {"pin_number": "7", "pin_name": "SS", "pin_description": "Adjustable Soft-Start. If left floating default soft-start time is set"}, {"pin_number": "12", "pin_name": "VIN", "pin_description": "Supply voltage for power stage and control stage"}, {"pin_number": "4", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}]}], "datasheet_cn": "ZHCSBD3D", "datasheet_en": "SLVSAM8", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.5V", "max_output_voltage": "5.5V", "min_output_voltage": "2.5V", "max_output_current": "1A", "max_switch_frequency": "2.5MHz", "quiescent_current": "43µA", "high_side_mosfet_resistance": "145mΩ", "low_side_mosfet_resistance": "170mΩ", "over_current_protection_threshold": "1A", "operation_mode": "同步", "pass_through_mode": "False", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "电流限制", "over_temperature_protection": "热关断", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.1%", "output_reference_voltage": "0.8V", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.5", "height": "0.625", "length": "2.6", "width": "2.6", "type": "OPTION", "pin_count": "12"}]}, {"part_number": "TPS63051", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS6305x 开关电流为1A、具备可调节软启动功能的单电感降压-升压转换器", "features": ["实时降压或升压,支持在降压和升压模式之间无缝转换", "输入电压范围为2.5V 至5.5V", "0.5A 持续输出电流:Vin ≥ 2.5V、VOUT = 3.3V", "具有可调节输出电压和固定输出电压两个版本可选", "在升压模式中效率大于90%,在降压模式中效率大于95%", "开关频率典型值为2.5MHz", "平均输入电流限制可调节", "软启动时间可调节", "器件静态电流小于60µA", "具有自动节电模式或强制 PWM 模式", "关断期间负载断开", "提供过热保护", "采用1.6mm x 1.2mm、12引脚 WCSP 小型封装和 2.5mm x 2.5mm、12引脚、HotRod TM QFN封装", "借助以下工具创建定制设计方案：– TPS63050，使用 WEBENCH® 电源设计器– TPS63051，使用 WEBENCH® 电源设计器"], "description": "TPS6305x 系列器件是一款静态电流较低的高效降压/升压转换器, 适用于输入电压高于或低于输出电压的应用。在升压模式下,持续输出电流最高可达 500mA;在降压模式下,持续输出最高可达1A。最大平均开关电流限制为1A(典型值)。TPS6305x 系列器件在整个输入电压范围内针对输出电压进行稳压操作,可根据输入电压自动切换为降压或升压模式,从而在两种模式之间实现无缝转换。该降压/升压转换器基于使用同步整流的固定频率PWM 控制器,可实现最高效率。在负载电流较低的情况下,该转换器进入节能模式,从而在整个负载电流范围内保持高效率。用户可以通过脉频调制 (PFM)/PWM 引脚选择自动PFM/PWM 工作模式或强制 PWM 工作模式。在PWM模式下通常使用2.5MHz 固定频率。使用一个外部电阻分压器可对输出电压进行编程,或者在芯片上对输出电压进行内部固定。转换器可被禁用以最大限度地减少电池消耗。在关断期间,负载从电池上断开。该器件采用12引脚芯片尺寸球状引脚栅格阵列 (DSBGA) 封装和12引脚 HotRod 封装。", "applications": ["手机和智能电话", "平板电脑", "PC 和智能手机附件", "通过电池供电的应用", "智能电网/智能仪表"], "ordering_information": [{"part_number": "TPS63051", "order_device": "TPS63051RMWR", "package_type": "VQFN-HR", "package_drawing_code": "RMW0012A", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63051", "order_device": "TPS63051RMWT", "package_type": "VQFN-HR", "package_drawing_code": "RMW0012A", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63051", "order_device": "TPS63051YFFR", "package_type": "DSBGA", "package_drawing_code": "YFF0012", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63051", "order_device": "TPS63051YFFT", "package_type": "DSBGA", "package_drawing_code": "YFF0012", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63051", "package_type": "WCSP", "pins": [{"pin_number": "A3", "pin_name": "EN", "pin_description": "Enable input. (1 enabled, 0 disabled). It must not be left floating"}, {"pin_number": "D2", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT on fixed output voltage versions"}, {"pin_number": "B1", "pin_name": "GND", "pin_description": "Ground for Power stage and Control stage"}, {"pin_number": "B2", "pin_name": "ILIM0", "pin_description": "Programmable inrush current limit input works together with ILIM1. It must not be left floating"}, {"pin_number": "B3", "pin_name": "ILIM1", "pin_description": "Programmable inrush current limit input works together with ILIM0. Do not leave floating"}, {"pin_number": "A1", "pin_name": "L1", "pin_description": "Connection for Inductor"}, {"pin_number": "C1", "pin_name": "L2", "pin_description": "Connection for Inductor"}, {"pin_number": "C2", "pin_name": "PFM/PWM", "pin_description": "0 for PFM mode 1 for forced PWM mode. It must not be left floating"}, {"pin_number": "C3", "pin_name": "PG", "pin_description": "Power good open drain output"}, {"pin_number": "D3", "pin_name": "SS", "pin_description": "Adjustable Soft-Start. If left floating default soft-start time is set"}, {"pin_number": "A2", "pin_name": "VIN", "pin_description": "Supply voltage for power stage and control stage"}, {"pin_number": "D1", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}]}, {"product_part_number": "TPS63051", "package_type": "HotRod", "pins": [{"pin_number": "11", "pin_name": "EN", "pin_description": "Enable input. (1 enabled, 0 disabled). It must not be left floating"}, {"pin_number": "5", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT on fixed output voltage versions"}, {"pin_number": "2,9", "pin_name": "GND", "pin_description": "Ground for Power stage and Control stage"}, {"pin_number": "10", "pin_name": "ILIM0", "pin_description": "Programmable inrush current limit input works together with ILIM1. It must not be left floating"}, {"pin_number": "1", "pin_name": "L1", "pin_description": "Connection for Inductor"}, {"pin_number": "3", "pin_name": "L2", "pin_description": "Connection for Inductor"}, {"pin_number": "6", "pin_name": "PFM/PWM", "pin_description": "0 for PFM mode 1 for forced PWM mode. It must not be left floating"}, {"pin_number": "8", "pin_name": "PG", "pin_description": "Power good open drain output"}, {"pin_number": "7", "pin_name": "SS", "pin_description": "Adjustable Soft-Start. If left floating default soft-start time is set"}, {"pin_number": "12", "pin_name": "VIN", "pin_description": "Supply voltage for power stage and control stage"}, {"pin_number": "4", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}]}], "datasheet_cn": "ZHCSBD3D", "datasheet_en": "SLVSAM8", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.5V", "max_output_voltage": "3.3V", "min_output_voltage": "3.3V", "max_output_current": "1A", "max_switch_frequency": "2.5MHz", "quiescent_current": "43µA", "high_side_mosfet_resistance": "145mΩ", "low_side_mosfet_resistance": "170mΩ", "over_current_protection_threshold": "1A", "operation_mode": "同步", "pass_through_mode": "False", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "电流限制", "over_temperature_protection": "热关断", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.2%", "output_reference_voltage": "不适用", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.5", "height": "0.625", "length": "2.6", "width": "2.6", "type": "OPTION", "pin_count": "12"}]}]