[{"part_number": "MAX77816A", "manufacturer": "Maxim Integrated", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "High-Efficiency Buck-Boost Regulator with 5A Switches", "features": ["Buck and Boost Operation Including Seamless Transition between Buck and Boost Modes", "2.3V to 5.5V VIN Range", "2.60V to 5.14V VOUT with 20mV Step", "3A Minimum Continuous Output Current (VINBB ≥ 3.0V, VOUTBB = 3.3V)", "Burst Current: 3.6A Minimum Output Current for 800µs (VINBB ≥ 3.0V, VOUTBB = 3.3V)", "I2C Serial Interface Allows Dynamic VOUT Adjustment and Provides Design Flexibility", "97.5% Peak Efficiency", "40µA Quiescent Current", "Safety Features Enhance Device and System Reliability (Soft-Start, True Shutdown™, Thermal Shutdown and Short-Circuit Protection)", "Multifunction GPIO Pin (MAX77816A/F: FPWM Mode Enable, MAX77816B: Inductor Peak Current-Limit selection, MAX77816C: Output Voltage Selection, MAX77816D: Power-OK indicator, MAX77816E: Interrupt Indicator)", "Small Size: 1.827mm x 2.127mm, 20-Bump WLP, 0.4mm Pitch"], "description": "The MAX77816 is a high-current, high-efficiency buck-boost regulator targeting single-cell Li-ion battery-powered applications. It supports a wide output voltage range from 2.60V to 5.14V. The IC allows 5A (typ) maximum switch current. In buck mode, the output current can go as high as 4A, and in boost mode, the maximum output current can be 3A. A unique control algorithm allows high efficiency, outstanding line/load transient response, and seamless transition between buck and boost modes. The IC features an I2C-compatible serial interface. The I2C interface allows the output voltage to be dynamically adjusted, thus enabling finer control of system power consumption. The I2C interface also provides features such as enable control and device status monitoring. The multifunction GPIO pin is register settable to 5 different options, such as FPWM mode enable and inductor peak current level selection. These options provide design flexibility that allows the IC to cover a wide range of applications and use cases.", "applications": ["Smartphones and Tablets", "Wearable Devices", "Wireless Communication Devices", "RF Power Amplifiers", "Battery-Powered Applications"], "ordering_information": [{"part_number": "MAX77816A", "order_device": "MAX77816AEWP+T", "package_type": "WLP", "package_drawing_code": "W201F2+1", "output_voltage": "3.4V", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX77816A", "package_type": "WLP", "pins": [{"pin_number": "A1", "pin_name": "SYS", "pin_description": "System (Battery) Voltage Input. Bypass to GND with a 1µF capacitor."}, {"pin_number": "A2", "pin_name": "EN", "pin_description": "Active-High, Buck-Boost External Enable Input. An 800kΩ internal pulldown resistance to the GND."}, {"pin_number": "A3", "pin_name": "GND", "pin_description": "Quiet Ground. Star-ground connection to system GND."}, {"pin_number": "A4", "pin_name": "SDA", "pin_description": "I2C Data I/O (Hi-Z in OFF State). This pin requires a pullup resistor to I2C power supply. Connect to GND if not used."}, {"pin_number": "A5", "pin_name": "SCL", "pin_description": "I2C Clock Input (Hi-Z in OFF State). This pin requires a pullup resistor to I2C power supply. Connect to GND if not used."}, {"pin_number": "B1", "pin_name": "FB_BB", "pin_description": "Buck-Boost Output Voltage Feedback"}, {"pin_number": "B2, C2, D2", "pin_name": "LXBB2", "pin_description": "Buck-<PERSON><PERSON> Switching Node 2"}, {"pin_number": "B3", "pin_name": "GPIO", "pin_description": "Multifunction GPIO: MAX77816A/B/C/F: General Purpose Input. An 800kΩ internal pulldown resistance to the GND. MAX77816D/E: Open-Drain Output. An external pullup resistor is required."}, {"pin_number": "B4, C4, D4", "pin_name": "LXBB1", "pin_description": "Buck-Boost Switching Node 1"}, {"pin_number": "B5, C5, D5", "pin_name": "INBB", "pin_description": "Buck-Boost Input. Bypass to PGNDBB with a 10µF capacitor."}, {"pin_number": "C1, D1", "pin_name": "OUTBB", "pin_description": "Buck<PERSON><PERSON><PERSON> Output"}, {"pin_number": "C3, D3", "pin_name": "PGNDBB", "pin_description": "Buck-Boost Power Ground. Star-ground connection to system GND."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77816", "family_comparison": "The MAX77816 family consists of variants A, B, C, D, E, and F, which differ primarily in their default output voltage and the function of the multifunction GPIO pin. MAX77816A/F: GPIO for FPWM Mode Enable. MAX77816B: GPIO for Inductor Peak Current-Limit selection. MAX77816C: GPIO for Output Voltage Selection. MAX77816D: GPIO as Power-OK indicator. MAX77816E: GPIO as Interrupt Indicator. Default VOUT for A is 3.4V, while B, C, D, E, F are 3.3V.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.3V", "max_output_voltage": "5.14V", "min_output_voltage": "2.6V", "max_output_current": "3A", "max_switch_frequency": "2.75MHz", "quiescent_current": "40µA", "high_side_mosfet_resistance": "34mΩ", "low_side_mosfet_resistance": "45mΩ", "over_current_protection_threshold": "5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "SKIP, FPWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "WLP", "pitch": "0.4", "length": "2.127", "width": "1.827", "pin_count": "40", "height": "0.4"}]}, {"part_number": "MAX77816B", "manufacturer": "Maxim Integrated", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "High-Efficiency Buck-Boost Regulator with 5A Switches", "features": ["Buck and Boost Operation Including Seamless Transition between Buck and Boost Modes", "2.3V to 5.5V VIN Range", "2.60V to 5.14V VOUT with 20mV Step", "3A Minimum Continuous Output Current (VINBB ≥ 3.0V, VOUTBB = 3.3V)", "Burst Current: 3.6A Minimum Output Current for 800µs (VINBB ≥ 3.0V, VOUTBB = 3.3V)", "I2C Serial Interface Allows Dynamic VOUT Adjustment and Provides Design Flexibility", "97.5% Peak Efficiency", "40µA Quiescent Current", "Safety Features Enhance Device and System Reliability (Soft-Start, True Shutdown™, Thermal Shutdown and Short-Circuit Protection)", "Multifunction GPIO Pin (MAX77816A/F: FPWM Mode Enable, MAX77816B: Inductor Peak Current-Limit selection, MAX77816C: Output Voltage Selection, MAX77816D: Power-OK indicator, MAX77816E: Interrupt Indicator)", "Small Size: 1.827mm x 2.127mm, 20-Bump WLP, 0.4mm Pitch"], "description": "The MAX77816 is a high-current, high-efficiency buck-boost regulator targeting single-cell Li-ion battery-powered applications. It supports a wide output voltage range from 2.60V to 5.14V. The IC allows 5A (typ) maximum switch current. In buck mode, the output current can go as high as 4A, and in boost mode, the maximum output current can be 3A. A unique control algorithm allows high efficiency, outstanding line/load transient response, and seamless transition between buck and boost modes. The IC features an I2C-compatible serial interface. The I2C interface allows the output voltage to be dynamically adjusted, thus enabling finer control of system power consumption. The I2C interface also provides features such as enable control and device status monitoring. The multifunction GPIO pin is register settable to 5 different options, such as FPWM mode enable and inductor peak current level selection. These options provide design flexibility that allows the IC to cover a wide range of applications and use cases.", "applications": ["Smartphones and Tablets", "Wearable Devices", "Wireless Communication Devices", "RF Power Amplifiers", "Battery-Powered Applications"], "ordering_information": [{"part_number": "MAX77816B", "order_device": "MAX77816BEWP+T", "package_type": "WLP", "package_drawing_code": "W201F2+1", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX77816B", "package_type": "WLP", "pins": [{"pin_number": "A1", "pin_name": "SYS", "pin_description": "System (Battery) Voltage Input. Bypass to GND with a 1µF capacitor."}, {"pin_number": "A2", "pin_name": "EN", "pin_description": "Active-High, Buck-Boost External Enable Input. An 800kΩ internal pulldown resistance to the GND."}, {"pin_number": "A3", "pin_name": "GND", "pin_description": "Quiet Ground. Star-ground connection to system GND."}, {"pin_number": "A4", "pin_name": "SDA", "pin_description": "I2C Data I/O (Hi-Z in OFF State). This pin requires a pullup resistor to I2C power supply. Connect to GND if not used."}, {"pin_number": "A5", "pin_name": "SCL", "pin_description": "I2C Clock Input (Hi-Z in OFF State). This pin requires a pullup resistor to I2C power supply. Connect to GND if not used."}, {"pin_number": "B1", "pin_name": "FB_BB", "pin_description": "Buck-Boost Output Voltage Feedback"}, {"pin_number": "B2, C2, D2", "pin_name": "LXBB2", "pin_description": "Buck-<PERSON><PERSON> Switching Node 2"}, {"pin_number": "B3", "pin_name": "GPIO", "pin_description": "Multifunction GPIO: MAX77816A/B/C/F: General Purpose Input. An 800kΩ internal pulldown resistance to the GND. MAX77816D/E: Open-Drain Output. An external pullup resistor is required."}, {"pin_number": "B4, C4, D4", "pin_name": "LXBB1", "pin_description": "Buck-Boost Switching Node 1"}, {"pin_number": "B5, C5, D5", "pin_name": "INBB", "pin_description": "Buck-Boost Input. Bypass to PGNDBB with a 10µF capacitor."}, {"pin_number": "C1, D1", "pin_name": "OUTBB", "pin_description": "Buck<PERSON><PERSON><PERSON> Output"}, {"pin_number": "C3, D3", "pin_name": "PGNDBB", "pin_description": "Buck-Boost Power Ground. Star-ground connection to system GND."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77816", "family_comparison": "The MAX77816 family consists of variants A, B, C, D, E, and F, which differ primarily in their default output voltage and the function of the multifunction GPIO pin. MAX77816A/F: GPIO for FPWM Mode Enable. MAX77816B: GPIO for Inductor Peak Current-Limit selection. MAX77816C: GPIO for Output Voltage Selection. MAX77816D: GPIO as Power-OK indicator. MAX77816E: GPIO as Interrupt Indicator. Default VOUT for A is 3.4V, while B, C, D, E, F are 3.3V.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.3V", "max_output_voltage": "5.14V", "min_output_voltage": "2.6V", "max_output_current": "3A", "max_switch_frequency": "2.75MHz", "quiescent_current": "40µA", "high_side_mosfet_resistance": "34mΩ", "low_side_mosfet_resistance": "45mΩ", "over_current_protection_threshold": "5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "SKIP, FPWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "WLP", "pitch": "0.4", "length": "2.127", "width": "1.827", "pin_count": "40", "height": "0.4"}]}, {"part_number": "MAX77816C", "manufacturer": "Maxim Integrated", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "High-Efficiency Buck-Boost Regulator with 5A Switches", "features": ["Buck and Boost Operation Including Seamless Transition between Buck and Boost Modes", "2.3V to 5.5V VIN Range", "2.60V to 5.14V VOUT with 20mV Step", "3A Minimum Continuous Output Current (VINBB ≥ 3.0V, VOUTBB = 3.3V)", "Burst Current: 3.6A Minimum Output Current for 800µs (VINBB ≥ 3.0V, VOUTBB = 3.3V)", "I2C Serial Interface Allows Dynamic VOUT Adjustment and Provides Design Flexibility", "97.5% Peak Efficiency", "40µA Quiescent Current", "Safety Features Enhance Device and System Reliability (Soft-Start, True Shutdown™, Thermal Shutdown and Short-Circuit Protection)", "Multifunction GPIO Pin (MAX77816A/F: FPWM Mode Enable, MAX77816B: Inductor Peak Current-Limit selection, MAX77816C: Output Voltage Selection, MAX77816D: Power-OK indicator, MAX77816E: Interrupt Indicator)", "Small Size: 1.827mm x 2.127mm, 20-Bump WLP, 0.4mm Pitch"], "description": "The MAX77816 is a high-current, high-efficiency buck-boost regulator targeting single-cell Li-ion battery-powered applications. It supports a wide output voltage range from 2.60V to 5.14V. The IC allows 5A (typ) maximum switch current. In buck mode, the output current can go as high as 4A, and in boost mode, the maximum output current can be 3A. A unique control algorithm allows high efficiency, outstanding line/load transient response, and seamless transition between buck and boost modes. The IC features an I2C-compatible serial interface. The I2C interface allows the output voltage to be dynamically adjusted, thus enabling finer control of system power consumption. The I2C interface also provides features such as enable control and device status monitoring. The multifunction GPIO pin is register settable to 5 different options, such as FPWM mode enable and inductor peak current level selection. These options provide design flexibility that allows the IC to cover a wide range of applications and use cases.", "applications": ["Smartphones and Tablets", "Wearable Devices", "Wireless Communication Devices", "RF Power Amplifiers", "Battery-Powered Applications"], "ordering_information": [{"part_number": "MAX77816C", "order_device": "MAX77816CEWP+T", "package_type": "WLP", "package_drawing_code": "W201F2+1", "output_voltage": "3.3V/5V", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX77816C", "package_type": "WLP", "pins": [{"pin_number": "A1", "pin_name": "SYS", "pin_description": "System (Battery) Voltage Input. Bypass to GND with a 1µF capacitor."}, {"pin_number": "A2", "pin_name": "EN", "pin_description": "Active-High, Buck-Boost External Enable Input. An 800kΩ internal pulldown resistance to the GND."}, {"pin_number": "A3", "pin_name": "GND", "pin_description": "Quiet Ground. Star-ground connection to system GND."}, {"pin_number": "A4", "pin_name": "SDA", "pin_description": "I2C Data I/O (Hi-Z in OFF State). This pin requires a pullup resistor to I2C power supply. Connect to GND if not used."}, {"pin_number": "A5", "pin_name": "SCL", "pin_description": "I2C Clock Input (Hi-Z in OFF State). This pin requires a pullup resistor to I2C power supply. Connect to GND if not used."}, {"pin_number": "B1", "pin_name": "FB_BB", "pin_description": "Buck-Boost Output Voltage Feedback"}, {"pin_number": "B2, C2, D2", "pin_name": "LXBB2", "pin_description": "Buck-<PERSON><PERSON> Switching Node 2"}, {"pin_number": "B3", "pin_name": "GPIO", "pin_description": "Multifunction GPIO: MAX77816A/B/C/F: General Purpose Input. An 800kΩ internal pulldown resistance to the GND. MAX77816D/E: Open-Drain Output. An external pullup resistor is required."}, {"pin_number": "B4, C4, D4", "pin_name": "LXBB1", "pin_description": "Buck-Boost Switching Node 1"}, {"pin_number": "B5, C5, D5", "pin_name": "INBB", "pin_description": "Buck-Boost Input. Bypass to PGNDBB with a 10µF capacitor."}, {"pin_number": "C1, D1", "pin_name": "OUTBB", "pin_description": "Buck<PERSON><PERSON><PERSON> Output"}, {"pin_number": "C3, D3", "pin_name": "PGNDBB", "pin_description": "Buck-Boost Power Ground. Star-ground connection to system GND."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77816", "family_comparison": "The MAX77816 family consists of variants A, B, C, D, E, and F, which differ primarily in their default output voltage and the function of the multifunction GPIO pin. MAX77816A/F: GPIO for FPWM Mode Enable. MAX77816B: GPIO for Inductor Peak Current-Limit selection. MAX77816C: GPIO for Output Voltage Selection. MAX77816D: GPIO as Power-OK indicator. MAX77816E: GPIO as Interrupt Indicator. Default VOUT for A is 3.4V, while B, C, D, E, F are 3.3V.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.3V", "max_output_voltage": "5.14V", "min_output_voltage": "2.6V", "max_output_current": "3A", "max_switch_frequency": "2.75MHz", "quiescent_current": "40µA", "high_side_mosfet_resistance": "34mΩ", "low_side_mosfet_resistance": "45mΩ", "over_current_protection_threshold": "5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "SKIP, FPWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "WLP", "pitch": "0.4", "length": "2.127", "width": "1.827", "pin_count": "40", "height": "0.4"}]}, {"part_number": "MAX77816D", "manufacturer": "Maxim Integrated", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "High-Efficiency Buck-Boost Regulator with 5A Switches", "features": ["Buck and Boost Operation Including Seamless Transition between Buck and Boost Modes", "2.3V to 5.5V VIN Range", "2.60V to 5.14V VOUT with 20mV Step", "3A Minimum Continuous Output Current (VINBB ≥ 3.0V, VOUTBB = 3.3V)", "Burst Current: 3.6A Minimum Output Current for 800µs (VINBB ≥ 3.0V, VOUTBB = 3.3V)", "I2C Serial Interface Allows Dynamic VOUT Adjustment and Provides Design Flexibility", "97.5% Peak Efficiency", "40µA Quiescent Current", "Safety Features Enhance Device and System Reliability (Soft-Start, True Shutdown™, Thermal Shutdown and Short-Circuit Protection)", "Multifunction GPIO Pin (MAX77816A/F: FPWM Mode Enable, MAX77816B: Inductor Peak Current-Limit selection, MAX77816C: Output Voltage Selection, MAX77816D: Power-OK indicator, MAX77816E: Interrupt Indicator)", "Small Size: 1.827mm x 2.127mm, 20-Bump WLP, 0.4mm Pitch"], "description": "The MAX77816 is a high-current, high-efficiency buck-boost regulator targeting single-cell Li-ion battery-powered applications. It supports a wide output voltage range from 2.60V to 5.14V. The IC allows 5A (typ) maximum switch current. In buck mode, the output current can go as high as 4A, and in boost mode, the maximum output current can be 3A. A unique control algorithm allows high efficiency, outstanding line/load transient response, and seamless transition between buck and boost modes. The IC features an I2C-compatible serial interface. The I2C interface allows the output voltage to be dynamically adjusted, thus enabling finer control of system power consumption. The I2C interface also provides features such as enable control and device status monitoring. The multifunction GPIO pin is register settable to 5 different options, such as FPWM mode enable and inductor peak current level selection. These options provide design flexibility that allows the IC to cover a wide range of applications and use cases.", "applications": ["Smartphones and Tablets", "Wearable Devices", "Wireless Communication Devices", "RF Power Amplifiers", "Battery-Powered Applications"], "ordering_information": [{"part_number": "MAX77816D", "order_device": "MAX77816DEWP+T", "package_type": "WLP", "package_drawing_code": "W201F2+1", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX77816D", "package_type": "WLP", "pins": [{"pin_number": "A1", "pin_name": "SYS", "pin_description": "System (Battery) Voltage Input. Bypass to GND with a 1µF capacitor."}, {"pin_number": "A2", "pin_name": "EN", "pin_description": "Active-High, Buck-Boost External Enable Input. An 800kΩ internal pulldown resistance to the GND."}, {"pin_number": "A3", "pin_name": "GND", "pin_description": "Quiet Ground. Star-ground connection to system GND."}, {"pin_number": "A4", "pin_name": "SDA", "pin_description": "I2C Data I/O (Hi-Z in OFF State). This pin requires a pullup resistor to I2C power supply. Connect to GND if not used."}, {"pin_number": "A5", "pin_name": "SCL", "pin_description": "I2C Clock Input (Hi-Z in OFF State). This pin requires a pullup resistor to I2C power supply. Connect to GND if not used."}, {"pin_number": "B1", "pin_name": "FB_BB", "pin_description": "Buck-Boost Output Voltage Feedback"}, {"pin_number": "B2, C2, D2", "pin_name": "LXBB2", "pin_description": "Buck-<PERSON><PERSON> Switching Node 2"}, {"pin_number": "B3", "pin_name": "GPIO", "pin_description": "Multifunction GPIO: MAX77816A/B/C/F: General Purpose Input. An 800kΩ internal pulldown resistance to the GND. MAX77816D/E: Open-Drain Output. An external pullup resistor is required."}, {"pin_number": "B4, C4, D4", "pin_name": "LXBB1", "pin_description": "Buck-Boost Switching Node 1"}, {"pin_number": "B5, C5, D5", "pin_name": "INBB", "pin_description": "Buck-Boost Input. Bypass to PGNDBB with a 10µF capacitor."}, {"pin_number": "C1, D1", "pin_name": "OUTBB", "pin_description": "Buck<PERSON><PERSON><PERSON> Output"}, {"pin_number": "C3, D3", "pin_name": "PGNDBB", "pin_description": "Buck-Boost Power Ground. Star-ground connection to system GND."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77816", "family_comparison": "The MAX77816 family consists of variants A, B, C, D, E, and F, which differ primarily in their default output voltage and the function of the multifunction GPIO pin. MAX77816A/F: GPIO for FPWM Mode Enable. MAX77816B: GPIO for Inductor Peak Current-Limit selection. MAX77816C: GPIO for Output Voltage Selection. MAX77816D: GPIO as Power-OK indicator. MAX77816E: GPIO as Interrupt Indicator. Default VOUT for A is 3.4V, while B, C, D, E, F are 3.3V.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.3V", "max_output_voltage": "5.14V", "min_output_voltage": "2.6V", "max_output_current": "3A", "max_switch_frequency": "2.75MHz", "quiescent_current": "40µA", "high_side_mosfet_resistance": "34mΩ", "low_side_mosfet_resistance": "45mΩ", "over_current_protection_threshold": "5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "SKIP, FPWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "WLP", "pitch": "0.4", "length": "2.127", "width": "1.827", "pin_count": "40", "height": "0.4"}]}, {"part_number": "MAX77816E", "manufacturer": "Maxim Integrated", "country": "美国", "manufacturing_status": "Future product", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "High-Efficiency Buck-Boost Regulator with 5A Switches", "features": ["Buck and Boost Operation Including Seamless Transition between Buck and Boost Modes", "2.3V to 5.5V VIN Range", "2.60V to 5.14V VOUT with 20mV Step", "3A Minimum Continuous Output Current (VINBB ≥ 3.0V, VOUTBB = 3.3V)", "Burst Current: 3.6A Minimum Output Current for 800µs (VINBB ≥ 3.0V, VOUTBB = 3.3V)", "I2C Serial Interface Allows Dynamic VOUT Adjustment and Provides Design Flexibility", "97.5% Peak Efficiency", "40µA Quiescent Current", "Safety Features Enhance Device and System Reliability (Soft-Start, True Shutdown™, Thermal Shutdown and Short-Circuit Protection)", "Multifunction GPIO Pin (MAX77816A/F: FPWM Mode Enable, MAX77816B: Inductor Peak Current-Limit selection, MAX77816C: Output Voltage Selection, MAX77816D: Power-OK indicator, MAX77816E: Interrupt Indicator)", "Small Size: 1.827mm x 2.127mm, 20-Bump WLP, 0.4mm Pitch"], "description": "The MAX77816 is a high-current, high-efficiency buck-boost regulator targeting single-cell Li-ion battery-powered applications. It supports a wide output voltage range from 2.60V to 5.14V. The IC allows 5A (typ) maximum switch current. In buck mode, the output current can go as high as 4A, and in boost mode, the maximum output current can be 3A. A unique control algorithm allows high efficiency, outstanding line/load transient response, and seamless transition between buck and boost modes. The IC features an I2C-compatible serial interface. The I2C interface allows the output voltage to be dynamically adjusted, thus enabling finer control of system power consumption. The I2C interface also provides features such as enable control and device status monitoring. The multifunction GPIO pin is register settable to 5 different options, such as FPWM mode enable and inductor peak current level selection. These options provide design flexibility that allows the IC to cover a wide range of applications and use cases.", "applications": ["Smartphones and Tablets", "Wearable Devices", "Wireless Communication Devices", "RF Power Amplifiers", "Battery-Powered Applications"], "ordering_information": [{"part_number": "MAX77816E", "order_device": "MAX77816EEWP+T*", "package_type": "WLP", "package_drawing_code": "W201F2+1", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX77816E", "package_type": "WLP", "pins": [{"pin_number": "A1", "pin_name": "SYS", "pin_description": "System (Battery) Voltage Input. Bypass to GND with a 1µF capacitor."}, {"pin_number": "A2", "pin_name": "EN", "pin_description": "Active-High, Buck-Boost External Enable Input. An 800kΩ internal pulldown resistance to the GND."}, {"pin_number": "A3", "pin_name": "GND", "pin_description": "Quiet Ground. Star-ground connection to system GND."}, {"pin_number": "A4", "pin_name": "SDA", "pin_description": "I2C Data I/O (Hi-Z in OFF State). This pin requires a pullup resistor to I2C power supply. Connect to GND if not used."}, {"pin_number": "A5", "pin_name": "SCL", "pin_description": "I2C Clock Input (Hi-Z in OFF State). This pin requires a pullup resistor to I2C power supply. Connect to GND if not used."}, {"pin_number": "B1", "pin_name": "FB_BB", "pin_description": "Buck-Boost Output Voltage Feedback"}, {"pin_number": "B2, C2, D2", "pin_name": "LXBB2", "pin_description": "Buck-<PERSON><PERSON> Switching Node 2"}, {"pin_number": "B3", "pin_name": "GPIO", "pin_description": "Multifunction GPIO: MAX77816A/B/C/F: General Purpose Input. An 800kΩ internal pulldown resistance to the GND. MAX77816D/E: Open-Drain Output. An external pullup resistor is required."}, {"pin_number": "B4, C4, D4", "pin_name": "LXBB1", "pin_description": "Buck-Boost Switching Node 1"}, {"pin_number": "B5, C5, D5", "pin_name": "INBB", "pin_description": "Buck-Boost Input. Bypass to PGNDBB with a 10µF capacitor."}, {"pin_number": "C1, D1", "pin_name": "OUTBB", "pin_description": "Buck<PERSON><PERSON><PERSON> Output"}, {"pin_number": "C3, D3", "pin_name": "PGNDBB", "pin_description": "Buck-Boost Power Ground. Star-ground connection to system GND."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77816", "family_comparison": "The MAX77816 family consists of variants A, B, C, D, E, and F, which differ primarily in their default output voltage and the function of the multifunction GPIO pin. MAX77816A/F: GPIO for FPWM Mode Enable. MAX77816B: GPIO for Inductor Peak Current-Limit selection. MAX77816C: GPIO for Output Voltage Selection. MAX77816D: GPIO as Power-OK indicator. MAX77816E: GPIO as Interrupt Indicator. Default VOUT for A is 3.4V, while B, C, D, E, F are 3.3V.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.3V", "max_output_voltage": "5.14V", "min_output_voltage": "2.6V", "max_output_current": "3A", "max_switch_frequency": "2.75MHz", "quiescent_current": "40µA", "high_side_mosfet_resistance": "34mΩ", "low_side_mosfet_resistance": "45mΩ", "over_current_protection_threshold": "5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "SKIP, FPWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "WLP", "pitch": "0.4", "length": "2.127", "width": "1.827", "pin_count": "40", "height": "0.4"}]}, {"part_number": "MAX77816F", "manufacturer": "Maxim Integrated", "country": "美国", "manufacturing_status": "Future product", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "High-Efficiency Buck-Boost Regulator with 5A Switches", "features": ["Buck and Boost Operation Including Seamless Transition between Buck and Boost Modes", "2.3V to 5.5V VIN Range", "2.60V to 5.14V VOUT with 20mV Step", "3A Minimum Continuous Output Current (VINBB ≥ 3.0V, VOUTBB = 3.3V)", "Burst Current: 3.6A Minimum Output Current for 800µs (VINBB ≥ 3.0V, VOUTBB = 3.3V)", "I2C Serial Interface Allows Dynamic VOUT Adjustment and Provides Design Flexibility", "97.5% Peak Efficiency", "40µA Quiescent Current", "Safety Features Enhance Device and System Reliability (Soft-Start, True Shutdown™, Thermal Shutdown and Short-Circuit Protection)", "Multifunction GPIO Pin (MAX77816A/F: FPWM Mode Enable, MAX77816B: Inductor Peak Current-Limit selection, MAX77816C: Output Voltage Selection, MAX77816D: Power-OK indicator, MAX77816E: Interrupt Indicator)", "Small Size: 1.827mm x 2.127mm, 20-Bump WLP, 0.4mm Pitch"], "description": "The MAX77816 is a high-current, high-efficiency buck-boost regulator targeting single-cell Li-ion battery-powered applications. It supports a wide output voltage range from 2.60V to 5.14V. The IC allows 5A (typ) maximum switch current. In buck mode, the output current can go as high as 4A, and in boost mode, the maximum output current can be 3A. A unique control algorithm allows high efficiency, outstanding line/load transient response, and seamless transition between buck and boost modes. The IC features an I2C-compatible serial interface. The I2C interface allows the output voltage to be dynamically adjusted, thus enabling finer control of system power consumption. The I2C interface also provides features such as enable control and device status monitoring. The multifunction GPIO pin is register settable to 5 different options, such as FPWM mode enable and inductor peak current level selection. These options provide design flexibility that allows the IC to cover a wide range of applications and use cases.", "applications": ["Smartphones and Tablets", "Wearable Devices", "Wireless Communication Devices", "RF Power Amplifiers", "Battery-Powered Applications"], "ordering_information": [{"part_number": "MAX77816F", "order_device": "MAX77816FEWP+T*", "package_type": "WLP", "package_drawing_code": "W201F2+1", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX77816F", "package_type": "WLP", "pins": [{"pin_number": "A1", "pin_name": "SYS", "pin_description": "System (Battery) Voltage Input. Bypass to GND with a 1µF capacitor."}, {"pin_number": "A2", "pin_name": "EN", "pin_description": "Active-High, Buck-Boost External Enable Input. An 800kΩ internal pulldown resistance to the GND."}, {"pin_number": "A3", "pin_name": "GND", "pin_description": "Quiet Ground. Star-ground connection to system GND."}, {"pin_number": "A4", "pin_name": "SDA", "pin_description": "I2C Data I/O (Hi-Z in OFF State). This pin requires a pullup resistor to I2C power supply. Connect to GND if not used."}, {"pin_number": "A5", "pin_name": "SCL", "pin_description": "I2C Clock Input (Hi-Z in OFF State). This pin requires a pullup resistor to I2C power supply. Connect to GND if not used."}, {"pin_number": "B1", "pin_name": "FB_BB", "pin_description": "Buck-Boost Output Voltage Feedback"}, {"pin_number": "B2, C2, D2", "pin_name": "LXBB2", "pin_description": "Buck-<PERSON><PERSON> Switching Node 2"}, {"pin_number": "B3", "pin_name": "GPIO", "pin_description": "Multifunction GPIO: MAX77816A/B/C/F: General Purpose Input. An 800kΩ internal pulldown resistance to the GND. MAX77816D/E: Open-Drain Output. An external pullup resistor is required."}, {"pin_number": "B4, C4, D4", "pin_name": "LXBB1", "pin_description": "Buck-Boost Switching Node 1"}, {"pin_number": "B5, C5, D5", "pin_name": "INBB", "pin_description": "Buck-Boost Input. Bypass to PGNDBB with a 10µF capacitor."}, {"pin_number": "C1, D1", "pin_name": "OUTBB", "pin_description": "Buck<PERSON><PERSON><PERSON> Output"}, {"pin_number": "C3, D3", "pin_name": "PGNDBB", "pin_description": "Buck-Boost Power Ground. Star-ground connection to system GND."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77816", "family_comparison": "The MAX77816 family consists of variants A, B, C, D, E, and F, which differ primarily in their default output voltage and the function of the multifunction GPIO pin. MAX77816A/F: GPIO for FPWM Mode Enable. MAX77816B: GPIO for Inductor Peak Current-Limit selection. MAX77816C: GPIO for Output Voltage Selection. MAX77816D: GPIO as Power-OK indicator. MAX77816E: GPIO as Interrupt Indicator. Default VOUT for A is 3.4V, while B, C, D, E, F are 3.3V.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.3V", "max_output_voltage": "5.14V", "min_output_voltage": "2.6V", "max_output_current": "3A", "max_switch_frequency": "2.75MHz", "quiescent_current": "40µA", "high_side_mosfet_resistance": "34mΩ", "low_side_mosfet_resistance": "45mΩ", "over_current_protection_threshold": "5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "SKIP, FPWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "WLP", "pitch": "0.4", "length": "2.127", "width": "1.827", "pin_count": "40", "height": "0.4"}]}]