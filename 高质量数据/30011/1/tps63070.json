[{"part_number": "TPS63070", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "DC/DC 开关稳压器", "category_lv3": "降压-升压(<PERSON><PERSON><PERSON><PERSON>)转换器", "part_number_title": "TPS63070 具有 3.6A 开关电流的 2V 至 16V 降压-升压转换器", "features": ["输入电压范围: 2.0V 至 16V", "输出电压范围: 2.5V 至 9V", "效率高达 95%", "脉宽调制 (PWM) 模式下的直流精度为 +/-1%", "脉频调制 (PFM) 模式下的直流精度为 +3%/-1%", "降压模式下的输出电流为 2A", "升压模式下的输出电流为 2A (VIN = 4V; Vout = 5V)", "精密使能输入可实现用户定义的欠压闭锁和准确排序", "在降压和升压模式之间实现自动转换", "器件静态电流典型值: 50μA", "具有固定和可调输出电压选项", "具有输出放电选项", "省电模式可提高低输出功率时的效率", "2.4MHz 强制固定运行频率和同步选项", "电源正常输出", "可通过 VSEL 轻松更改输出电压", "关断期间负载断开", "过热保护", "输入/输出过压保护", "采用四方扁平无引线 (QFN) 封装"], "description": "TPS6307x 是一款具有低静态电流的高效降压-升压转换器，适用于那些输入电压可能高于或低于输出电压的应用。在升压或降压模式下，输出电流可高达 2A。此降压-升压转换器基于一个固定频率、脉宽调制 (PWM) 控制器，此控制器通过使用同步整流来获得最高效率。在低负载电流情况下，此转换器进入省电模式以在宽负载电流范围内保持高效率。转换器可被禁用以最大限度地减少电池消耗。在关断期间，负载从电池上断开。此器件采用 2.5mm x 3mm QFN 封装。", "applications": ["双节锂离子应用", "工业计量设备", "数码相机 (DSC) 和便携式摄像机", "笔记本电脑", "超便携移动个人计算机和移动互联网器件", "个人医疗产品"], "ordering_information": [{"part_number": "TPS63070", "order_device": "TPS63070RNMR", "package_type": "VQFN-HR", "package_drawing_code": "RNM0015A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63070", "order_device": "TPS63070RNMR.A", "package_type": "VQFN-HR", "package_drawing_code": "RNM0015A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63070", "order_device": "TPS63070RNMT", "package_type": "VQFN-HR", "package_drawing_code": "RNM0015A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63070", "order_device": "TPS63070RNMT.A", "package_type": "VQFN-HR", "package_drawing_code": "RNM0015A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63070", "package_type": "VQFN", "pins": [{"pin_number": "14", "pin_name": "EN", "pin_description": "使能输入。拉高以使能器件，拉低以禁用器件。"}, {"pin_number": "5", "pin_name": "FB", "pin_description": "可调版本的电压反馈，对于固定输出电压版本，必须连接到 VOUT。"}, {"pin_number": "4", "pin_name": "GND", "pin_description": "控制/逻辑地。"}, {"pin_number": "11", "pin_name": "L1", "pin_description": "电感连接端。"}, {"pin_number": "9", "pin_name": "L2", "pin_description": "电感连接端。"}, {"pin_number": "1", "pin_name": "PS/SYNC", "pin_description": "拉低强制PWM，拉高进入PWM/PFM（省电）模式。施加时钟信号以同步到外部频率。"}, {"pin_number": "2", "pin_name": "PG", "pin_description": "开漏电源正常输出。"}, {"pin_number": "10", "pin_name": "PGND", "pin_description": "功率地。"}, {"pin_number": "12, 13", "pin_name": "VIN", "pin_description": "功率级供电电压。"}, {"pin_number": "7, 8", "pin_name": "VOUT", "pin_description": "降压-升压转换器输出。"}, {"pin_number": "3", "pin_name": "VAUX", "pin_description": "内部电压调节器的电容连接端。此引脚不得外部加载。"}, {"pin_number": "15", "pin_name": "VSEL", "pin_description": "电压调节输入。此引脚上的高电平会使能一个将FB2引脚拉至GND的晶体管。"}, {"pin_number": "6", "pin_name": "FB2", "pin_description": "电压调节输出。连接一个从FB到FB2的电阻以更改反馈引脚上的分压比。VSEL上的逻辑高电平会将输出电压更改为更高值。不使用时，将引脚悬空或连接到GND。"}]}], "datasheet_cn": "ZHCSFB9B-JUNE 2016-REVISED MARCH 2019", "datasheet_en": "SLVSC58", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "2V", "max_output_voltage": "9V", "min_output_voltage": "2.5V", "max_output_current": "2A", "max_switch_frequency": "2.7MHz", "quiescent_current": "54μA", "high_side_mosfet_resistance": "50mΩ", "low_side_mosfet_resistance": "100mΩ", "over_current_protection_threshold": "3.6A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "自动重启", "input_under_voltage_protection": "欠压锁定", "output_over_voltage_protection": "自动重启", "output_under_voltage_protection": "无", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "间歇式", "over_temperature_protection": "自动重启", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.8V", "loop_control_mode": "平均电流模式"}, "package": [{"height": "1.0", "length": "3.1", "width": "2.6", "type": "OPTION", "pin_count": "2", "pitch": "1.0"}]}, {"part_number": "TPS630701", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "DC/DC 开关稳压器", "category_lv3": "降压-升压(<PERSON><PERSON><PERSON><PERSON>)转换器", "part_number_title": "TPS63070 具有 3.6A 开关电流的 2V 至 16V 降压-升压转换器", "features": ["输入电压范围: 2.0V 至 16V", "输出电压范围: 2.5V 至 9V", "效率高达 95%", "脉宽调制 (PWM) 模式下的直流精度为 +/-1%", "脉频调制 (PFM) 模式下的直流精度为 +3%/-1%", "降压模式下的输出电流为 2A", "升压模式下的输出电流为 2A (VIN = 4V; Vout = 5V)", "精密使能输入可实现用户定义的欠压闭锁和准确排序", "在降压和升压模式之间实现自动转换", "器件静态电流典型值: 50μA", "具有固定和可调输出电压选项", "具有输出放电选项", "省电模式可提高低输出功率时的效率", "2.4MHz 强制固定运行频率和同步选项", "电源正常输出", "可通过 VSEL 轻松更改输出电压", "关断期间负载断开", "过热保护", "输入/输出过压保护", "采用四方扁平无引线 (QFN) 封装"], "description": "TPS6307x 是一款具有低静态电流的高效降压-升压转换器，适用于那些输入电压可能高于或低于输出电压的应用。在升压或降压模式下，输出电流可高达 2A。此降压-升压转换器基于一个固定频率、脉宽调制 (PWM) 控制器，此控制器通过使用同步整流来获得最高效率。在低负载电流情况下，此转换器进入省电模式以在宽负载电流范围内保持高效率。转换器可被禁用以最大限度地减少电池消耗。在关断期间，负载从电池上断开。此器件采用 2.5mm x 3mm QFN 封装。", "applications": ["双节锂离子应用", "工业计量设备", "数码相机 (DSC) 和便携式摄像机", "笔记本电脑", "超便携移动个人计算机和移动互联网器件", "个人医疗产品"], "ordering_information": [{"part_number": "TPS630701", "order_device": "TPS630701RNMR", "package_type": "VQFN-HR", "package_drawing_code": "RNM0015A", "output_voltage": "5.0V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS630701", "order_device": "TPS630701RNMR.A", "package_type": "VQFN-HR", "package_drawing_code": "RNM0015A", "output_voltage": "5.0V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS630701", "order_device": "TPS630701RNMT", "package_type": "VQFN-HR", "package_drawing_code": "RNM0015A", "output_voltage": "5.0V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS630701", "order_device": "TPS630701RNMT.A", "package_type": "VQFN-HR", "package_drawing_code": "RNM0015A", "output_voltage": "5.0V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS630701", "package_type": "VQFN", "pins": [{"pin_number": "14", "pin_name": "EN", "pin_description": "使能输入。拉高以使能器件，拉低以禁用器件。"}, {"pin_number": "5", "pin_name": "FB", "pin_description": "可调版本的电压反馈，对于固定输出电压版本，必须连接到 VOUT。"}, {"pin_number": "4", "pin_name": "GND", "pin_description": "控制/逻辑地。"}, {"pin_number": "11", "pin_name": "L1", "pin_description": "电感连接端。"}, {"pin_number": "9", "pin_name": "L2", "pin_description": "电感连接端。"}, {"pin_number": "1", "pin_name": "PS/SYNC", "pin_description": "拉低强制PWM，拉高进入PWM/PFM（省电）模式。施加时钟信号以同步到外部频率。"}, {"pin_number": "2", "pin_name": "PG", "pin_description": "开漏电源正常输出。"}, {"pin_number": "10", "pin_name": "PGND", "pin_description": "功率地。"}, {"pin_number": "12, 13", "pin_name": "VIN", "pin_description": "功率级供电电压。"}, {"pin_number": "7, 8", "pin_name": "VOUT", "pin_description": "降压-升压转换器输出。"}, {"pin_number": "3", "pin_name": "VAUX", "pin_description": "内部电压调节器的电容连接端。此引脚不得外部加载。"}, {"pin_number": "15", "pin_name": "VSEL", "pin_description": "电压调节输入。此引脚上的高电平会使能一个将FB2引脚拉至GND的晶体管。"}, {"pin_number": "6", "pin_name": "FB2", "pin_description": "电压调节输出。连接一个从FB到FB2的电阻以更改反馈引脚上的分压比。VSEL上的逻辑高电平会将输出电压更改为更高值。不使用时，将引脚悬空或连接到GND。"}]}], "datasheet_cn": "ZHCSFB9B-JUNE 2016-REVISED MARCH 2019", "datasheet_en": "SLVSC58", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "2V", "max_output_voltage": "5V", "min_output_voltage": "5V", "max_output_current": "2A", "max_switch_frequency": "2.7MHz", "quiescent_current": "54μA", "high_side_mosfet_resistance": "50mΩ", "low_side_mosfet_resistance": "100mΩ", "over_current_protection_threshold": "3.6A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "自动重启", "input_under_voltage_protection": "欠压锁定", "output_over_voltage_protection": "自动重启", "output_under_voltage_protection": "无", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "间歇式", "over_temperature_protection": "自动重启", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "未找到", "loop_control_mode": "平均电流模式"}, "package": [{"height": "1.0", "length": "3.1", "width": "2.6", "type": "OPTION", "pin_count": "2", "pitch": "1.0"}]}, {"part_number": "TPS630702", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "DC/DC 开关稳压器", "category_lv3": "降压-升压(<PERSON><PERSON><PERSON><PERSON>)转换器", "part_number_title": "TPS63070 具有 3.6A 开关电流的 2V 至 16V 降压-升压转换器", "features": ["输入电压范围: 2.0V 至 16V", "输出电压范围: 2.5V 至 9V", "效率高达 95%", "脉宽调制 (PWM) 模式下的直流精度为 +/-1%", "脉频调制 (PFM) 模式下的直流精度为 +3%/-1%", "降压模式下的输出电流为 2A", "升压模式下的输出电流为 2A (VIN = 4V; Vout = 5V)", "精密使能输入可实现用户定义的欠压闭锁和准确排序", "在降压和升压模式之间实现自动转换", "器件静态电流典型值: 50μA", "具有固定和可调输出电压选项", "具有输出放电选项", "省电模式可提高低输出功率时的效率", "2.4MHz 强制固定运行频率和同步选项", "电源正常输出", "可通过 VSEL 轻松更改输出电压", "关断期间负载断开", "过热保护", "输入/输出过压保护", "采用四方扁平无引线 (QFN) 封装"], "description": "TPS6307x 是一款具有低静态电流的高效降压-升压转换器，适用于那些输入电压可能高于或低于输出电压的应用。在升压或降压模式下，输出电流可高达 2A。此降压-升压转换器基于一个固定频率、脉宽调制 (PWM) 控制器，此控制器通过使用同步整流来获得最高效率。在低负载电流情况下，此转换器进入省电模式以在宽负载电流范围内保持高效率。转换器可被禁用以最大限度地减少电池消耗。在关断期间，负载从电池上断开。此器件采用 2.5mm x 3mm QFN 封装。", "applications": ["双节锂离子应用", "工业计量设备", "数码相机 (DSC) 和便携式摄像机", "笔记本电脑", "超便携移动个人计算机和移动互联网器件", "个人医疗产品"], "ordering_information": [{"part_number": "TPS630702", "order_device": "TPS630702RNMR", "package_type": "VQFN-HR", "package_drawing_code": "RNM0015A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS630702", "order_device": "TPS630702RNMR.A", "package_type": "VQFN-HR", "package_drawing_code": "RNM0015A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS630702", "order_device": "TPS630702RNMT", "package_type": "VQFN-HR", "package_drawing_code": "RNM0015A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS630702", "order_device": "TPS630702RNMT.A", "package_type": "VQFN-HR", "package_drawing_code": "RNM0015A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS630702", "package_type": "VQFN", "pins": [{"pin_number": "14", "pin_name": "EN", "pin_description": "使能输入。拉高以使能器件，拉低以禁用器件。"}, {"pin_number": "5", "pin_name": "FB", "pin_description": "可调版本的电压反馈，对于固定输出电压版本，必须连接到 VOUT。"}, {"pin_number": "4", "pin_name": "GND", "pin_description": "控制/逻辑地。"}, {"pin_number": "11", "pin_name": "L1", "pin_description": "电感连接端。"}, {"pin_number": "9", "pin_name": "L2", "pin_description": "电感连接端。"}, {"pin_number": "1", "pin_name": "PS/SYNC", "pin_description": "拉低强制PWM，拉高进入PWM/PFM（省电）模式。施加时钟信号以同步到外部频率。"}, {"pin_number": "2", "pin_name": "PG", "pin_description": "开漏电源正常输出。"}, {"pin_number": "10", "pin_name": "PGND", "pin_description": "功率地。"}, {"pin_number": "12, 13", "pin_name": "VIN", "pin_description": "功率级供电电压。"}, {"pin_number": "7, 8", "pin_name": "VOUT", "pin_description": "降压-升压转换器输出。"}, {"pin_number": "3", "pin_name": "VAUX", "pin_description": "内部电压调节器的电容连接端。此引脚不得外部加载。"}, {"pin_number": "15", "pin_name": "VSEL", "pin_description": "电压调节输入。此引脚上的高电平会使能一个将FB2引脚拉至GND的晶体管。"}, {"pin_number": "6", "pin_name": "FB2", "pin_description": "电压调节输出。连接一个从FB到FB2的电阻以更改反馈引脚上的分压比。VSEL上的逻辑高电平会将输出电压更改为更高值。不使用时，将引脚悬空或连接到GND。"}]}], "datasheet_cn": "ZHCSFB9B-JUNE 2016-REVISED MARCH 2019", "datasheet_en": "SLVSC58", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "2V", "max_output_voltage": "9V", "min_output_voltage": "2.5V", "max_output_current": "2A", "max_switch_frequency": "2.7MHz", "quiescent_current": "54μA", "high_side_mosfet_resistance": "50mΩ", "low_side_mosfet_resistance": "100mΩ", "over_current_protection_threshold": "3.6A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "自动重启", "input_under_voltage_protection": "欠压锁定", "output_over_voltage_protection": "自动重启", "output_under_voltage_protection": "无", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "间歇式", "over_temperature_protection": "自动重启", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.8V", "loop_control_mode": "平均电流模式"}, "package": [{"height": "1.0", "length": "3.1", "width": "2.6", "type": "OPTION", "pin_count": "2", "pitch": "1.0"}]}]