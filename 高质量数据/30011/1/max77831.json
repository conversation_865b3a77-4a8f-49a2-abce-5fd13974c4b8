{"part_number": "MAX77831", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "2.5V to 16V Input, 7A Switching Current High-Efficiency Buck-Boost Converter", "features": ["Wide Input Voltage Range: 2.5V to 16V", "Default Output Voltage: 5V with Internal Feedback Resistors, 3V to 15V with External Feedback Resistors", "Default 1.8MHz Switching Frequency: 1.5MHz and 1.2MHz Selectable through the I2C Interface", "Forced-PWM (FPWM) Mode Operation Only", "I2C-Programmable Output Voltage After Startup: 4.5V to 15V (Internal Feedback Resistors), 3V to 15V (External Feedback Resistors, see Table 2)", "Maximum Output Current: Buck Mode: Up to 6A, Boost Mode: Up to 4A (Boost Ratio ≤ 1.3)", "7A Typical Switching Current", "RSEL Configuration: I2C Interface Slave Address, Switching Current Limit Threshold, Internal/External Feedback Resistors", "I2C Programming: Output Voltage (DVS), Slew Rate of Output Voltage Change, Switching Current Limit Threshold, Switching Frequency, Power-OK (POK) Status and Fault Interrupt Masks, Internal Compensation", "Soft-Start", "Output Active Discharge", "Open-Drain Power-OK (POK) Monitor/Fault Condition Interrupt", "Protection Features: Undervoltage Lockout (UVLO), Overcurrent Protection (OCP), Overvoltage Protection (OVP), Thermal Shutdown (THS)", "High Density Interconnect (HDI) PCB Not Required (See the PCB Layout Guideline Section)", "Available in 2.83mm x 2.03mm 35 WLP"], "description": "The MAX77831 is a high-efficiency, high-performance buck-boost converter targeted for systems requiring wide input voltage range (2.5V to 16V). It features a 7A switching current and can supply up to 6A output current in buck mode and up to 4A in boost mode (boost ratio ≤ 1.3). The IC is available in 5V default output voltage when using internal feedback resistors. The IC can also be configured to any default output voltages between 3V and 15V when using external feedback resistors. The output voltage is adjustable dynamically through the I2C serial interface (see the Output Voltage Configuration section). The IC only operates in forced-PWM (FPWM) mode. The SEL pin allows a single external resistor to program four different I2C interface slave addresses, four different switching-current limit thresholds, and selection between external/internal feedback resistors. The different switching-current limit thresholds allow the use of lower profile and smaller external components that are optimized for a particular application. The use of external feedback resistors allows for a wider output voltage range and customizable output voltages at startup. The I2C serial interface is optional and allows dynamically controlling the output voltage, the slew rate of the output voltage change, switching-current limit threshold, and switching frequency. The I2C-programmed settings have priority over the RSEL decoded settings. The MAX77831 is available in a 2.83mm x 2.03mm 35-bump wafer-level package (WLP).", "applications": ["Non-Battery Powered Applications up to 16V Input", "Battery Powered Applications up to 16V Input with EN Control"], "ordering_information": [{"part_number": "MAX77831", "order_device": "MAX77831BEWB+T", "package_type": "WLP", "package_drawing_code": "21-100367", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MAX77831", "package_type": "35 WLP", "pins": [{"pin_number": "A1", "pin_name": "SCL", "pin_description": "I2C Serial Interface Clock (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor when using the I2C interface or connect to AGND when the I2C interface is not in use."}, {"pin_number": "B1", "pin_name": "SDA", "pin_description": "I2C Serial Interface Data (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor when using the I2C interface or connect to AGND when the I2C interface is not in use."}, {"pin_number": "C2", "pin_name": "POKB/INTB", "pin_description": "Buck-Boost Output Power-OK Monitor or Fault Interrupt Active-Low Open Drain Output. Connect to VIO with a 15kΩ pullup resistor. See the Power-OK Monitor and Fault Interrupts section for more details. Do not connect to this pin if not in use."}, {"pin_number": "C1", "pin_name": "VL", "pin_description": "Low-Voltage Internal Supply. Powered from IN. Bypass to AGND with a 10V 2.2µF ceramic capacitor. Do not load this pin externally."}, {"pin_number": "D1", "pin_name": "SEL", "pin_description": "Configuration Selection. Connect a resistor between SEL and AGND. See Table 3 for resistor value and configurations."}, {"pin_number": "E1", "pin_name": "FB", "pin_description": "Using Internal Feedback Resistors: Output Voltage Sense Input. Connect to the output at the point-of-load (close to output capacitor). Using External Feedback Resistors: Output Voltage Feedback Input. Connect to the center tap of an external resistor divider from the output to AGND to set the output voltage."}, {"pin_number": "E2", "pin_name": "AGND", "pin_description": "Analog Ground. Connect to PGND on the PCB."}, {"pin_number": "E3", "pin_name": "BST2", "pin_description": "LX2 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST2 and LX2."}, {"pin_number": "D4, E4, E5, E6", "pin_name": "OUT", "pin_description": "Buck-Boost Output. Bypass to PGND with two 25V 22µF ceramic capacitor as close as possible."}, {"pin_number": "D5, D6, D7, E7", "pin_name": "LX2", "pin_description": "Buck-<PERSON><PERSON> Switching Node 2"}, {"pin_number": "C3, C4, C5, C6, C7, D2, D3", "pin_name": "PGND", "pin_description": "Power Ground. Connect to AGND on the PCB."}, {"pin_number": "A7, B5, B6, B7", "pin_name": "LX1", "pin_description": "Buck-Boost Switching Node 1"}, {"pin_number": "A4, A5, A6, B4", "pin_name": "IN", "pin_description": "Buck-Boost Input. Bypass to PGND with two 25V 10µF ceramic capacitors as close as possible."}, {"pin_number": "A3", "pin_name": "BST1", "pin_description": "LX1 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST1 and LX1."}, {"pin_number": "B2, B3", "pin_name": "EN", "pin_description": "Active-High Buck-Boost Enable Input. Compatible with the VIO voltage domain. Pulled down to AGND internally with 800kΩ resistance."}, {"pin_number": "A2", "pin_name": "VIO", "pin_description": "IO Voltage Supply. Connect to VIO and bypass to AGND with a 6.3V 0.47µF ceramic capacitor. Registers are held in reset and regulator remains disabled when this pin's voltage is invalid."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77831 Rev 3 (2022-11-01)", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "2.5V", "max_output_voltage": "15V", "min_output_voltage": "3V", "max_output_current": "6A", "max_switch_frequency": "1.8MHz", "quiescent_current": "5000µA", "high_side_mosfet_resistance": "20mΩ", "low_side_mosfet_resistance": "20mΩ", "over_current_protection_threshold": "0.98-7.0A(可调)", "operation_mode": "同步", "output_voltage_config_method": "固定/可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "FPWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.333V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.64", "length": "2.828", "width": "2.028", "type": "WLP", "pin_count": "2"}]}