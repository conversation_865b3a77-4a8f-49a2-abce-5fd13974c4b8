[{"part_number": "TPS55160-Q1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS5516x-Q1 36V、1A 输出、2MHz、单电感器、同步升压和降压稳压器", "features": ["符合汽车应用要求", "具有符合 AEC-Q100 标准的下列特性: - 器件温度等级 1：–40℃ 至 +125℃ 的工作环境温度范围 - 器件 HBM ESD 分类等级 2 - 器件 CDM ESD 分类等级 C4B", "提供功能安全: 可帮助进行功能安全系统设计的文档", "VOUT = 5V 时的输入电压范围为 2V 至 36V", "5V 或 12V 固定输出电压 (TPS55165-Q1)", "5.7V 至 9V 可调输出电压选项 (TPS55160-Q1 和 TPS55162-Q1)", "效率高达 85%", "VOUT = 5V 且 VIN ≥ 5.3V 时的输出电流为 1A", "VOUT = 5V 且 VIN ≥ 3.8V 时的输出电流为 0.8A", "VOUT = 5V 且 VIN ≥ 2.3V 时的输出电流为 0.4A", "在降压和升压模式之间自动转换", "低功耗模式，可在轻负载情况下提高效率 (TPS55160-Q1/TPS55165-Q1)", "在低功耗模式下器件静态电流小于 15µA (TPS55160-Q1/TPS55165-Q1)", "器件关断电流小于 3µA", "2MHz 强制固定运行频率", "可选展频 (TPS55160-Q1 和 TPS55165-Q1)", "通过具有电源锁存功能的 IGN 唤醒", "具有可配置延迟时间的智能电源正常输出", "过热保护和输出过压保护", "采用易于使用的 20 引脚 HTSSOP PowerPAD™ 封装"], "description": "TPS5516x-Q1 系列器件是一款高电压同步降压/升压直流/直流转换器。该器件通过多种不同的输入电源（如汽车电池）提供稳定的电源输出。降压/升压重叠控制可确保以最佳的效率在降压和升压模式之间自动转换。TPS55165-Q1 输出电压可以设置为 5V 或 12V 固定电平。TPS55160-Q1 和 TPS55162-Q1 器件具有通过外部电阻分压器设置的 5.7V 至 9V 可配置输出电压。对于常规汽车电池电压，输出电流可高至 1A，并且能够针对更低的输入电压（如用于实现常见电池启动曲线的电压）保持在 0.4A。该降压/升压转换器基于一个使用同步整流的固定频率、脉宽调制 (PWM) 控制电路来获得最高效率。开关频率设置为 2MHz（典型值），从而允许使用小型电感器（布板空间更少）。", "applications": ["启停敏感型汽车电源应用", "信息娱乐系统与仪表组", "车身电子装置和网关模块", "具有波动输入电压的工业应用", "太阳能和电池充电", "锂离子电池组"], "ordering_information": [{"part_number": "TPS55160-Q1", "order_device": "TPS55160QPWPRQ1", "package_type": "HTSSOP", "package_drawing_code": "PWP0020W", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS55160-Q1", "order_device": "TPS55160QPWPTQ1", "package_type": "HTSSOP", "package_drawing_code": "PWP0020W", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS5516x-Q1", "package_type": "HTSSOP", "pins": [{"pin_number": "1", "pin_name": "PGND", "pin_description": "Power-ground pin"}, {"pin_number": "2", "pin_name": "L1", "pin_description": "<PERSON> power-stage switch node. Connect an inductor with a nominal value of 4.7 µH between the L1 and L2 pins."}, {"pin_number": "3", "pin_name": "BST1", "pin_description": "Bootstrap node for the buck power stage. Connect a 100-nF capacitor between this pin and the L1 pin."}, {"pin_number": "4", "pin_name": "VINP", "pin_description": "Supply-power input voltage. Connect this pin to the input supply line."}, {"pin_number": "5", "pin_name": "VINL", "pin_description": "Supply-input voltage for internal biasing. Connect this pin to the input supply line."}, {"pin_number": "6", "pin_name": "IGN", "pin_description": "Ignition-enable input signal. The ignition is enabled when this pin is high (1) and is disabled when this pin is low (0)."}, {"pin_number": "7", "pin_name": "PS", "pin_description": "Logic-level input signal to enable and disable low-power mode. The power mode is low-power mode when this pin is high (1) and is normal mode when this pin is low (1)."}, {"pin_number": "8", "pin_name": "IGN_PWRL", "pin_description": "Logic-level IGN power-latch signal. The IGN pin is latched when this pin is high (1) and is not latched when this pin is low (0)."}, {"pin_number": "9", "pin_name": "SS_EN", "pin_description": "Configuration pin to enable and disable the spread-Spectrum. The spread-spectrum feature is enabled when this pin is open and disabled when this pin is low."}, {"pin_number": "10", "pin_name": "PG_DLY", "pin_description": "Configuration pin for power-good delay time. Connect this pin to a resistor with a value from 10kΩ to 100kΩ to configure the PG delay time from 0.5 ms to 40 ms. Connect this pin to ground for the default PG delay time which is 2 ms (typical)."}, {"pin_number": "11", "pin_name": "VREG_Q", "pin_description": "Quiet feedback pin for the gate-drive supply of the buck-boost power stages. This pin must be connected close to the top side of the 4.7-µF (typical) decoupling capacitor at the VREG output pin."}, {"pin_number": "12", "pin_name": "VREG", "pin_description": "Gate-drive supply for the buck-boost power stages. Apply a 4.7-µF (typical) decoupling capacitor at this pin to the power ground. The VREG pin cannot drive external loads in the application."}, {"pin_number": "13", "pin_name": "GND", "pin_description": "Analog ground"}, {"pin_number": "14", "pin_name": "VOS_FB", "pin_description": "For the TPS55160-Q1 and TPS55162-Q1 devices, this pin is used to adjust the VOUT configuration. Connect this pin to a resistive feedback network with less than 1-MΩ total resistance between the VOUT pin, FB pin, and GND pin (analog ground)."}, {"pin_number": "15", "pin_name": "PG", "pin_description": "Output power good pin. This pin is an open-drain pin. The status of the power-good output is good when this pin is high (1) and has a failure when this pin is low (0)."}, {"pin_number": "16", "pin_name": "VOUT_SENSE", "pin_description": "Sense pin for the buck-boost converter output voltage. This pin must be connected to the VOUT pin."}, {"pin_number": "17", "pin_name": "VOUT", "pin_description": "Buck-boost converter output voltage"}, {"pin_number": "18", "pin_name": "GND", "pin_description": "Analog ground"}, {"pin_number": "19", "pin_name": "BST2", "pin_description": "Bootstrap node for the boost power-stage. Connect a typical 100-nF capacitor between this pin and the L2 pin."}, {"pin_number": "20", "pin_name": "L2", "pin_description": "Boost power-stage switch node. Connect an inductor with a nominal value of 4.7 µH between the L1 and L2 pins."}, {"pin_number": "Thermal Pad", "pin_name": "PowerPAD", "pin_description": "The thermal pad must be soldered to the power ground to achieve the appropriate power dissipation through the analog ground plane."}]}], "datasheet_cn": "tps55160-q1.pdf", "datasheet_en": "https://www.ti.com/lit/ds/symlink/tps55160-q1.pdf", "family_comparison": "TPS55160-Q1: 可调输出电压 (5.7V-9V)，具有展频和低功耗模式。 TPS55162-Q1: 可调输出电压 (5.7V-9V)，无展频和低功耗模式。 TPS55165-Q1: 固定输出电压 (5V/12V)，具有展频和低功耗模式。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "3.6V", "max_output_voltage": "9V", "min_output_voltage": "5.7V", "max_output_current": "1A", "max_switch_frequency": "2.4MHz", "quiescent_current": "15µA", "high_side_mosfet_resistance": "150mΩ", "low_side_mosfet_resistance": "150mΩ", "over_current_protection_threshold": "3.5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.8V", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.65", "height": "1.2", "length": "6.6", "width": "4.5", "type": "OPTION", "pin_count": "2"}]}, {"part_number": "TPS55162-Q1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS5516x-Q1 36V、1A 输出、2MHz、单电感器、同步升压和降压稳压器", "features": ["符合汽车应用要求", "具有符合 AEC-Q100 标准的下列特性: - 器件温度等级 1：–40℃ 至 +125℃ 的工作环境温度范围 - 器件 HBM ESD 分类等级 2 - 器件 CDM ESD 分类等级 C4B", "提供功能安全: 可帮助进行功能安全系统设计的文档", "VOUT = 5V 时的输入电压范围为 2V 至 36V", "5V 或 12V 固定输出电压 (TPS55165-Q1)", "5.7V 至 9V 可调输出电压选项 (TPS55160-Q1 和 TPS55162-Q1)", "效率高达 85%", "VOUT = 5V 且 VIN ≥ 5.3V 时的输出电流为 1A", "VOUT = 5V 且 VIN ≥ 3.8V 时的输出电流为 0.8A", "VOUT = 5V 且 VIN ≥ 2.3V 时的输出电流为 0.4A", "在降压和升压模式之间自动转换", "低功耗模式，可在轻负载情况下提高效率 (TPS55160-Q1/TPS55165-Q1)", "在低功耗模式下器件静态电流小于 15µA (TPS55160-Q1/TPS55165-Q1)", "器件关断电流小于 3µA", "2MHz 强制固定运行频率", "可选展频 (TPS55160-Q1 和 TPS55165-Q1)", "通过具有电源锁存功能的 IGN 唤醒", "具有可配置延迟时间的智能电源正常输出", "过热保护和输出过压保护", "采用易于使用的 20 引脚 HTSSOP PowerPAD™ 封装"], "description": "TPS5516x-Q1 系列器件是一款高电压同步降压/升压直流/直流转换器。该器件通过多种不同的输入电源（如汽车电池）提供稳定的电源输出。降压/升压重叠控制可确保以最佳的效率在降压和升压模式之间自动转换。TPS55165-Q1 输出电压可以设置为 5V 或 12V 固定电平。TPS55160-Q1 和 TPS55162-Q1 器件具有通过外部电阻分压器设置的 5.7V 至 9V 可配置输出电压。对于常规汽车电池电压，输出电流可高至 1A，并且能够针对更低的输入电压（如用于实现常见电池启动曲线的电压）保持在 0.4A。该降压/升压转换器基于一个使用同步整流的固定频率、脉宽调制 (PWM) 控制电路来获得最高效率。开关频率设置为 2MHz（典型值），从而允许使用小型电感器（布板空间更少）。", "applications": ["启停敏感型汽车电源应用", "信息娱乐系统与仪表组", "车身电子装置和网关模块", "具有波动输入电压的工业应用", "太阳能和电池充电", "锂离子电池组"], "ordering_information": [{"part_number": "TPS55162-Q1", "order_device": "TPS55162QPWPRQ1", "package_type": "HTSSOP", "package_drawing_code": "PWP0020W", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS55162-Q1", "order_device": "TPS55162QPWPTQ1", "package_type": "HTSSOP", "package_drawing_code": "PWP0020W", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS5516x-Q1", "package_type": "HTSSOP", "pins": [{"pin_number": "1", "pin_name": "PGND", "pin_description": "Power-ground pin"}, {"pin_number": "2", "pin_name": "L1", "pin_description": "<PERSON> power-stage switch node. Connect an inductor with a nominal value of 4.7 µH between the L1 and L2 pins."}, {"pin_number": "3", "pin_name": "BST1", "pin_description": "Bootstrap node for the buck power stage. Connect a 100-nF capacitor between this pin and the L1 pin."}, {"pin_number": "4", "pin_name": "VINP", "pin_description": "Supply-power input voltage. Connect this pin to the input supply line."}, {"pin_number": "5", "pin_name": "VINL", "pin_description": "Supply-input voltage for internal biasing. Connect this pin to the input supply line."}, {"pin_number": "6", "pin_name": "IGN", "pin_description": "Ignition-enable input signal. The ignition is enabled when this pin is high (1) and is disabled when this pin is low (0)."}, {"pin_number": "7", "pin_name": "PS", "pin_description": "Logic-level input signal to enable and disable low-power mode. The power mode is low-power mode when this pin is high (1) and is normal mode when this pin is low (1)."}, {"pin_number": "8", "pin_name": "IGN_PWRL", "pin_description": "Logic-level IGN power-latch signal. The IGN pin is latched when this pin is high (1) and is not latched when this pin is low (0)."}, {"pin_number": "9", "pin_name": "SS_EN", "pin_description": "Configuration pin to enable and disable the spread-Spectrum. The spread-spectrum feature is enabled when this pin is open and disabled when this pin is low."}, {"pin_number": "10", "pin_name": "PG_DLY", "pin_description": "Configuration pin for power-good delay time. Connect this pin to a resistor with a value from 10kΩ to 100kΩ to configure the PG delay time from 0.5 ms to 40 ms. Connect this pin to ground for the default PG delay time which is 2 ms (typical)."}, {"pin_number": "11", "pin_name": "VREG_Q", "pin_description": "Quiet feedback pin for the gate-drive supply of the buck-boost power stages. This pin must be connected close to the top side of the 4.7-µF (typical) decoupling capacitor at the VREG output pin."}, {"pin_number": "12", "pin_name": "VREG", "pin_description": "Gate-drive supply for the buck-boost power stages. Apply a 4.7-µF (typical) decoupling capacitor at this pin to the power ground. The VREG pin cannot drive external loads in the application."}, {"pin_number": "13", "pin_name": "GND", "pin_description": "Analog ground"}, {"pin_number": "14", "pin_name": "VOS_FB", "pin_description": "For the TPS55160-Q1 and TPS55162-Q1 devices, this pin is used to adjust the VOUT configuration. Connect this pin to a resistive feedback network with less than 1-MΩ total resistance between the VOUT pin, FB pin, and GND pin (analog ground)."}, {"pin_number": "15", "pin_name": "PG", "pin_description": "Output power good pin. This pin is an open-drain pin. The status of the power-good output is good when this pin is high (1) and has a failure when this pin is low (0)."}, {"pin_number": "16", "pin_name": "VOUT_SENSE", "pin_description": "Sense pin for the buck-boost converter output voltage. This pin must be connected to the VOUT pin."}, {"pin_number": "17", "pin_name": "VOUT", "pin_description": "Buck-boost converter output voltage"}, {"pin_number": "18", "pin_name": "GND", "pin_description": "Analog ground"}, {"pin_number": "19", "pin_name": "BST2", "pin_description": "Bootstrap node for the boost power-stage. Connect a typical 100-nF capacitor between this pin and the L2 pin."}, {"pin_number": "20", "pin_name": "L2", "pin_description": "Boost power-stage switch node. Connect an inductor with a nominal value of 4.7 µH between the L1 and L2 pins."}, {"pin_number": "Thermal Pad", "pin_name": "PowerPAD", "pin_description": "The thermal pad must be soldered to the power ground to achieve the appropriate power dissipation through the analog ground plane."}]}], "datasheet_cn": "tps55160-q1.pdf", "datasheet_en": "https://www.ti.com/lit/ds/symlink/tps55160-q1.pdf", "family_comparison": "TPS55160-Q1: 可调输出电压 (5.7V-9V)，具有展频和低功耗模式。 TPS55162-Q1: 可调输出电压 (5.7V-9V)，无展频和低功耗模式。 TPS55165-Q1: 固定输出电压 (5V/12V)，具有展频和低功耗模式。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "3.6V", "max_output_voltage": "9V", "min_output_voltage": "5.7V", "max_output_current": "1A", "max_switch_frequency": "2.14MHz", "quiescent_current": "未找到", "high_side_mosfet_resistance": "150mΩ", "low_side_mosfet_resistance": "150mΩ", "over_current_protection_threshold": "3.5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.8V", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.65", "height": "1.2", "length": "6.6", "width": "4.5", "type": "OPTION", "pin_count": "2"}]}, {"part_number": "TPS55165-Q1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS5516x-Q1 36V、1A 输出、2MHz、单电感器、同步升压和降压稳压器", "features": ["符合汽车应用要求", "具有符合 AEC-Q100 标准的下列特性: - 器件温度等级 1：–40℃ 至 +125℃ 的工作环境温度范围 - 器件 HBM ESD 分类等级 2 - 器件 CDM ESD 分类等级 C4B", "提供功能安全: 可帮助进行功能安全系统设计的文档", "VOUT = 5V 时的输入电压范围为 2V 至 36V", "5V 或 12V 固定输出电压 (TPS55165-Q1)", "5.7V 至 9V 可调输出电压选项 (TPS55160-Q1 和 TPS55162-Q1)", "效率高达 85%", "VOUT = 5V 且 VIN ≥ 5.3V 时的输出电流为 1A", "VOUT = 5V 且 VIN ≥ 3.8V 时的输出电流为 0.8A", "VOUT = 5V 且 VIN ≥ 2.3V 时的输出电流为 0.4A", "在降压和升压模式之间自动转换", "低功耗模式，可在轻负载情况下提高效率 (TPS55160-Q1/TPS55165-Q1)", "在低功耗模式下器件静态电流小于 15µA (TPS55160-Q1/TPS55165-Q1)", "器件关断电流小于 3µA", "2MHz 强制固定运行频率", "可选展频 (TPS55160-Q1 和 TPS55165-Q1)", "通过具有电源锁存功能的 IGN 唤醒", "具有可配置延迟时间的智能电源正常输出", "过热保护和输出过压保护", "采用易于使用的 20 引脚 HTSSOP PowerPAD™ 封装"], "description": "TPS5516x-Q1 系列器件是一款高电压同步降压/升压直流/直流转换器。该器件通过多种不同的输入电源（如汽车电池）提供稳定的电源输出。降压/升压重叠控制可确保以最佳的效率在降压和升压模式之间自动转换。TPS55165-Q1 输出电压可以设置为 5V 或 12V 固定电平。TPS55160-Q1 和 TPS55162-Q1 器件具有通过外部电阻分压器设置的 5.7V 至 9V 可配置输出电压。对于常规汽车电池电压，输出电流可高至 1A，并且能够针对更低的输入电压（如用于实现常见电池启动曲线的电压）保持在 0.4A。该降压/升压转换器基于一个使用同步整流的固定频率、脉宽调制 (PWM) 控制电路来获得最高效率。开关频率设置为 2MHz（典型值），从而允许使用小型电感器（布板空间更少）。", "applications": ["启停敏感型汽车电源应用", "信息娱乐系统与仪表组", "车身电子装置和网关模块", "具有波动输入电压的工业应用", "太阳能和电池充电", "锂离子电池组"], "ordering_information": [{"part_number": "TPS55165-Q1", "order_device": "TPS55165QPWPRQ1", "package_type": "HTSSOP", "package_drawing_code": "PWP0020W", "output_voltage": "5V/12V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS55165-Q1", "order_device": "TPS55165QPWPTQ1", "package_type": "HTSSOP", "package_drawing_code": "PWP0020W", "output_voltage": "5V/12V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS5516x-Q1", "package_type": "HTSSOP", "pins": [{"pin_number": "1", "pin_name": "PGND", "pin_description": "Power-ground pin"}, {"pin_number": "2", "pin_name": "L1", "pin_description": "<PERSON> power-stage switch node. Connect an inductor with a nominal value of 4.7 µH between the L1 and L2 pins."}, {"pin_number": "3", "pin_name": "BST1", "pin_description": "Bootstrap node for the buck power stage. Connect a 100-nF capacitor between this pin and the L1 pin."}, {"pin_number": "4", "pin_name": "VINP", "pin_description": "Supply-power input voltage. Connect this pin to the input supply line."}, {"pin_number": "5", "pin_name": "VINL", "pin_description": "Supply-input voltage for internal biasing. Connect this pin to the input supply line."}, {"pin_number": "6", "pin_name": "IGN", "pin_description": "Ignition-enable input signal. The ignition is enabled when this pin is high (1) and is disabled when this pin is low (0)."}, {"pin_number": "7", "pin_name": "PS", "pin_description": "Logic-level input signal to enable and disable low-power mode. The power mode is low-power mode when this pin is high (1) and is normal mode when this pin is low (1)."}, {"pin_number": "8", "pin_name": "IGN_PWRL", "pin_description": "Logic-level IGN power-latch signal. The IGN pin is latched when this pin is high (1) and is not latched when this pin is low (0)."}, {"pin_number": "9", "pin_name": "SS_EN", "pin_description": "Configuration pin to enable and disable the spread-Spectrum. The spread-spectrum feature is enabled when this pin is open and disabled when this pin is low."}, {"pin_number": "10", "pin_name": "PG_DLY", "pin_description": "Configuration pin for power-good delay time. Connect this pin to a resistor with a value from 10kΩ to 100kΩ to configure the PG delay time from 0.5 ms to 40 ms. Connect this pin to ground for the default PG delay time which is 2 ms (typical)."}, {"pin_number": "11", "pin_name": "VREG_Q", "pin_description": "Quiet feedback pin for the gate-drive supply of the buck-boost power stages. This pin must be connected close to the top side of the 4.7-µF (typical) decoupling capacitor at the VREG output pin."}, {"pin_number": "12", "pin_name": "VREG", "pin_description": "Gate-drive supply for the buck-boost power stages. Apply a 4.7-µF (typical) decoupling capacitor at this pin to the power ground. The VREG pin cannot drive external loads in the application."}, {"pin_number": "13", "pin_name": "GND", "pin_description": "Analog ground"}, {"pin_number": "14", "pin_name": "VOS_FB", "pin_description": "For the TPS55165-Q1 device, this pin is used to select the output voltage. The output voltage is set to 5 V when this pin is connected to the GND pin. The output voltage is 12 V when this pin is connected to the VREG pin."}, {"pin_number": "15", "pin_name": "PG", "pin_description": "Output power good pin. This pin is an open-drain pin. The status of the power-good output is good when this pin is high (1) and has a failure when this pin is low (0)."}, {"pin_number": "16", "pin_name": "VOUT_SENSE", "pin_description": "Sense pin for the buck-boost converter output voltage. This pin must be connected to the VOUT pin."}, {"pin_number": "17", "pin_name": "VOUT", "pin_description": "Buck-boost converter output voltage"}, {"pin_number": "18", "pin_name": "GND", "pin_description": "Analog ground"}, {"pin_number": "19", "pin_name": "BST2", "pin_description": "Bootstrap node for the boost power-stage. Connect a typical 100-nF capacitor between this pin and the L2 pin."}, {"pin_number": "20", "pin_name": "L2", "pin_description": "Boost power-stage switch node. Connect an inductor with a nominal value of 4.7 µH between the L1 and L2 pins."}, {"pin_number": "Thermal Pad", "pin_name": "PowerPAD", "pin_description": "The thermal pad must be soldered to the power ground to achieve the appropriate power dissipation through the analog ground plane."}]}], "datasheet_cn": "tps55160-q1.pdf", "datasheet_en": "https://www.ti.com/lit/ds/symlink/tps55160-q1.pdf", "family_comparison": "TPS55160-Q1: 可调输出电压 (5.7V-9V)，具有展频和低功耗模式。 TPS55162-Q1: 可调输出电压 (5.7V-9V)，无展频和低功耗模式。 TPS55165-Q1: 固定输出电压 (5V/12V)，具有展频和低功耗模式。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "2V", "max_output_voltage": "12V", "min_output_voltage": "5V", "max_output_current": "1A", "max_switch_frequency": "2.4MHz", "quiescent_current": "15µA", "high_side_mosfet_resistance": "150mΩ", "low_side_mosfet_resistance": "150mΩ", "over_current_protection_threshold": "3.5A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "未找到", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.65", "height": "1.2", "length": "6.6", "width": "4.5", "type": "OPTION", "pin_count": "2"}]}]