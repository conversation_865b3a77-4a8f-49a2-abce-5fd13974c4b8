{"part_number": "MAX77847", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "5.5V Input, 4.5A Switching Current High Efficiency Buck-Boost Converter", "features": ["1.8V to 5.5V Input Voltage Range", "1.8V to 5.2V (50mV steps) Output Voltage Range", "4.5A/3.6A Switching Current Limit", "14μA Ultra-Low IQ", "I2C Interface", "Programmable Output Voltage", "Programmable Output Slew Rate (DVS)", "Forced PWM Mode Operation (FPWM)", "Output Active Discharge", "I2C configurable GPI Pin", "External FPWM Enable Input", "DVS Control Input", "SEL Pin to Configure Device", "Default Start-Up Voltage (16 Options)", "I2C Target Address (2 Options)", "Soft-Start", "Flexible System Integration"], "description": "The MAX77847 is a highly efficient high-performance buck-boost regulator with an industry-leading quiescent current of 14µA targeted for battery-powered applications. It supports an input voltage range from 1.8V to 5.5V and an output voltage range from 1.8V to 5.2V. The IC provides two programmable switching current limits to optimize external component sizing based on load requirements. Analog Devices' unique buck-boost controller technology provides high efficiency, excellent load and line transient performance, and a seamless mode transition across the input and output range. The device features a hardware configuration SEL pin which configures the default output voltage and I2C target address. A hardware dynamic voltage scaling (DVS) pin allows the user to change the output voltage between two output voltages without I2C interference. The I2C interface is optional and allows users to adjust the output voltage with 50mV steps. The MAX77847 is available in a 2.18mm x 1.66mm 15-bump wafer-level package (WLP).", "applications": ["5G PA Supply", "Battery Power Equipment", "Internet of Things (IoT) Devices", "System Power Pre-Regulation", "Smartphones ToF/Facial and Gesture Recognition"], "ordering_information": [{"part_number": "MAX77847", "order_device": "MAX77847AEWL+T", "package_type": "WLP", "package_drawing_code": "21-100642", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77847", "order_device": "MAX77847BEWL+T", "package_type": "WLP", "package_drawing_code": "21-100642", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MAX77847", "package_type": "WLP", "pins": [{"pin_number": "A1", "pin_name": "IN", "pin_description": "Buck-Boost Input. Bypass to PGND with 2x 10V 10µF X7R ceramic capacitor."}, {"pin_number": "A2", "pin_name": "LX1", "pin_description": "Input-Side Buck-<PERSON>ost Switching Node."}, {"pin_number": "A3, B3, C3", "pin_name": "PGND", "pin_description": "Power Ground."}, {"pin_number": "A4", "pin_name": "LX2", "pin_description": "Output-Side Buck-<PERSON><PERSON> Switching Node."}, {"pin_number": "A5", "pin_name": "OUT", "pin_description": "Buck-Boost Power Output. Bypass to PGND with 2x 10V X7R 10µF ceramic capacitor."}, {"pin_number": "B1", "pin_name": "BIAS", "pin_description": "Internal Bias Supply. Bypass to AGND with a 10V 2.2µF X7R ceramic capacitor. Do not load this pin externally."}, {"pin_number": "B2", "pin_name": "GPI", "pin_description": "General Purpose Input Pin. Forced PWM mode control input (default) or DVS control input."}, {"pin_number": "B4", "pin_name": "EN", "pin_description": "Buck-Boost Enable Input."}, {"pin_number": "B5", "pin_name": "OUTS", "pin_description": "Buck-Boost Output Voltage Sense Input. Connect to the output at the point of load."}, {"pin_number": "C1", "pin_name": "SCL", "pin_description": "I2C Clock Input (Hi-Z in OFF State). This pin requires a pullup resistor to the system IO supply voltage. Connect to AGND if not used."}, {"pin_number": "C2", "pin_name": "SDA", "pin_description": "I2C Data I/O (Hi-Z in OFF state). This pin requires a pullup resistor to system IO supply. Connect to AGND if not used."}, {"pin_number": "C4", "pin_name": "AGND", "pin_description": "Analog Ground."}, {"pin_number": "C5", "pin_name": "SEL", "pin_description": "Device Configuration Pin. Connect a resistor between this pin and AGND to configure output voltage and device target address. See SEL Pin Configuration section."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77847 Datasheet, Rev 0, 9/23", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.2V", "min_output_voltage": "1.8V", "max_output_current": "3A", "max_switch_frequency": "2.47MHz", "quiescent_current": "14µA", "high_side_mosfet_resistance": "55mΩ", "low_side_mosfet_resistance": "58mΩ", "over_current_protection_threshold": "4.5A/3.6A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "Skip Mode", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Latch", "pass_through_mode": "No", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "未找到", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.64", "length": "1.63", "width": "2.15", "type": "Information", "pin_count": "2"}]}