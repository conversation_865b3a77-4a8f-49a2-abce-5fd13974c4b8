{"part_number": "ISL81601", "manufacturer": "Renesas Electronics Corporation", "country": "Japan", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型控制器", "part_number_title": "60V Bidirectional 4-Switch Synchronous Buck-Boost Controller", "features": ["Single inductor 4-switch buck-boost controller", "On-the-fly bidirectional operation with independent control of voltage and current on both ends", "Proprietary algorithm for smoothest mode transition", "MOSFET drivers with adaptive shoot-through protection", "Wide input voltage range: 4.5V to 60V", "Wide output voltage range: 0.8V to 60V", "Supports pre-biased output with SR soft-start", "Programmable frequency: 100kHz to 600kHz", "Supports parallel operation current sharing with cascade phase interleaving", "External sync with clock out or frequency dithering", "External bias for higher efficiency supports 8V - 36V input", "Output and input current monitor", "Selectable PWM mode operation between PWM/DE/Burst modes", "Accurate EN/UVLO and PGOOD indicator", "Low shutdown current: 2.7μA", "Complete protection: OCP, SCP, OVP, OTP, and UVP", "Dual-level OCP protection with average current and pulse-by-pulse peak current limit", "Selectable OCP response with either hiccup or constant current mode", "Negative pulse-by-pulse peak current limit"], "description": "The ISL81601 is a true bidirectional 4-switch synchronous buck-boost controller with peak and average current sensing and monitoring at both ends. Its wide input and output voltage ranges make it suitable for industrial, telecommunication, and after-market automotive applications. The ISL81601 uses the proprietary buck-boost control algorithm with valley current modulation for Boost mode and peak current modulation for Buck mode control. The ISL81601 has four independent control loops for input and output voltages and currents. Inherent peak current sensing at both ends and cycle-by-cycle current limit of this family of products ensures high operational reliability by providing instant current limit in fast transient conditions at either ends and in both directions. It also has two current monitoring pins at both input and output to facilitate Constant Current (CC) limit and other system management functions. CC operation down to low voltages avoids any runaway condition at over load or short-circuit conditions. In addition to multilayer overcurrent protection, it also provides full protection features such as OVP, UVP, OTP, and average and peak current limit on both input and output to ensure high reliability in both unidirectional and bidirectional operation. The IC is packaged in a space conscious 32 Ld 5mm x 5mm QFN package or easy to assemble 4.4mmx9.7mm 38 Ld HTSSOP package. Both packages use an EPAD to improve thermal dissipation and noise immunity. Low pin count, fewer external components, and default internal values make the ISL81601 an ideal solution for quick to market simple power supply designs. The unique DE/Burst mode at light-load dramatically lowers standby power consumption with consistent output ripple over different load levels.", "applications": ["Battery backup", "UPS/storage systems", "Battery powered industrial applications", "Renewable energy", "Aftermarket automotive", "Redundant power supplies", "Robot and drones", "Medical equipment", "Building and industrial automation", "Security surveillance"], "ordering_information": [{"part_number": "ISL81601", "order_device": "ISL81601FRZ", "package_type": "QFN", "package_drawing_code": "L32.5x5B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ISL81601", "order_device": "ISL81601FRZ-T", "package_type": "QFN", "package_drawing_code": "L32.5x5B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ISL81601", "order_device": "ISL81601FRZ-T7A", "package_type": "QFN", "package_drawing_code": "L32.5x5B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ISL81601", "order_device": "ISL81601FVEZ", "package_type": "HTSSOP", "package_drawing_code": "M38.173C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ISL81601", "order_device": "ISL81601FVEZ-T", "package_type": "HTSSOP", "package_drawing_code": "M38.173C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ISL81601", "order_device": "ISL81601FVEZ-T7A", "package_type": "HTSSOP", "package_drawing_code": "M38.173C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "ISL81601", "package_type": "32 Ld 5x5 QFN", "pins": [{"pin_number": "1", "pin_name": "BSTEN", "pin_description": "DE Burst mode enable signal. Pulled up to 5V by an internal 250k resistor in PWM and DE mode. Pulled low in Burst mode. Connect together in multi-chip parallel operation to sync all chips for Burst mode operation."}, {"pin_number": "2", "pin_name": "FB_IN", "pin_description": "Input voltage feedback pin for reverse direction operation. Use a resistor divider to feed the input voltage back. Keep below 0.3V to disable reverse direction. Tie to VCC5V or SGND if not used to set phase shift for parallel operation."}, {"pin_number": "3", "pin_name": "VCC5V", "pin_description": "Output of the internal 5V linear regulator. Supplies bias for the IC. Decouple to SGND with a minimum of 4.7uF ceramic capacitor."}, {"pin_number": "4", "pin_name": "NC", "pin_description": "No connection pin."}, {"pin_number": "5", "pin_name": "RT/SYNC", "pin_description": "A resistor to ground adjusts the default switching frequency (100kHz to 600kHz). When an external clock is applied, the internal frequency is synchronized."}, {"pin_number": "6", "pin_name": "PLL_COMP", "pin_description": "Compensation pin for the internal PLL circuit. A compensation network is required."}, {"pin_number": "7", "pin_name": "CLKOUT/DITHER", "pin_description": "Dual function pin. Provides a clock signal for synchronization or enables frequency dither function with a capacitor."}, {"pin_number": "8", "pin_name": "SS/TRK", "pin_description": "Dual function pin for soft-start control with a capacitor or for tracking control with a resistor divider from a master supply."}, {"pin_number": "9", "pin_name": "COMP", "pin_description": "Voltage error GM amplifier output. Sets the reference of the inner current loop. Feedback compensation network is connected here."}, {"pin_number": "10", "pin_name": "FB_OUT", "pin_description": "Output voltage feedback input. Connect to a resistive voltage divider from the output to adjust the output voltage."}, {"pin_number": "11", "pin_name": "IMON_OUT", "pin_description": "Output current monitor. The current from this pin is proportional to the differential voltage between ISEN+ and ISEN-."}, {"pin_number": "12", "pin_name": "OV", "pin_description": "OVP comparator output signal. Pulled low in normal operation, pulled high when output OVP trips. Connect together in parallel operation for OVP sync."}, {"pin_number": "13", "pin_name": "ISEN-", "pin_description": "Output current sense signal negative input pin."}, {"pin_number": "14", "pin_name": "ISEN+", "pin_description": "Output current sense signal positive input pin."}, {"pin_number": "15", "pin_name": "PGOOD", "pin_description": "Open-drain logic output to indicate the status of output voltage. Pulled low when output is not within ±10% of nominal or EN is low."}, {"pin_number": "16", "pin_name": "UG2", "pin_description": "High-side MOSFET gate driver output controlled by the boost PWM signal."}, {"pin_number": "17", "pin_name": "PHASE2", "pin_description": "Phase node connection of the boost converter. Connects to the junction of the upper MOSFET's source, inductor, and lower MOSFET's drain."}, {"pin_number": "18", "pin_name": "BOOT2", "pin_description": "Bootstrap pin to provide bias for the boost high-side driver. Connect bootstrap capacitor and diode."}, {"pin_number": "19", "pin_name": "LG2/OC_MODE", "pin_description": "Low-side MOSFET gate driver output for boost and OCP mode set pin. A resistor to ground sets OCP to constant current or Hiccup mode."}, {"pin_number": "20", "pin_name": "VDD", "pin_description": "Output of the internal 8V linear regulator. Supplies bias for drivers. Decouple to PGND with a minimum of 4.7µF ceramic capacitor."}, {"pin_number": "21", "pin_name": "PGND", "pin_description": "Power ground connection. Connect to the sources of the lower MOSFETs."}, {"pin_number": "22", "pin_name": "LG1/PWM_MODE", "pin_description": "Low-side MOSFET gate driver output for buck and PWM mode set pin. A resistor to ground sets forced PWM or DE mode."}, {"pin_number": "23", "pin_name": "BOOT1", "pin_description": "Bootstrap pin to provide bias for the buck high-side driver. Connect bootstrap capacitor and diode."}, {"pin_number": "24", "pin_name": "PHASE1", "pin_description": "Phase node connection of the buck converter. Connects to the junction of the upper MOSFET's source, inductor, and lower MOSFET's drain."}, {"pin_number": "25", "pin_name": "UG1", "pin_description": "High-side MOSFET gate driver output controlled by the buck PWM signal."}, {"pin_number": "26", "pin_name": "EXTBIAS", "pin_description": "External bias input for the optional VDD LDO. Decouple with 10µF capacitor or tie to ground if unused."}, {"pin_number": "27", "pin_name": "VIN", "pin_description": "Tie to the input rail. Provides power to the internal LDO for VDD. Decouple with a small ceramic capacitor."}, {"pin_number": "28", "pin_name": "CS+", "pin_description": "Input current sense signal positive input pin."}, {"pin_number": "29", "pin_name": "CS-", "pin_description": "Input current sense signal negative input pin."}, {"pin_number": "30", "pin_name": "EN/UVLO", "pin_description": "Provides enable/disable and accurate UVLO functions. Floating enables the device by default."}, {"pin_number": "31", "pin_name": "IMON_IN", "pin_description": "Input current monitor. The current from this pin is proportional to the differential voltage between CS+ and CS-."}, {"pin_number": "32", "pin_name": "CLKEN", "pin_description": "DE mode burst operation off state enable signal. Pulled up in PWM/DE modes, pulled low in Burst mode off state. Connect together in parallel operation for sync."}, {"pin_number": "EPAD", "pin_name": "SGND EPAD", "pin_description": "Small-signal ground common to all control circuitries. EPAD is at ground potential and connected to SGND internally. Solder to ground plane for thermal performance."}]}, {"product_part_number": "ISL81601", "package_type": "38 Ld HTSSOP", "pins": [{"pin_number": "1", "pin_name": "VIN", "pin_description": "Tie to the input rail. Provides power to the internal LDO for VDD. Decouple with a small ceramic capacitor."}, {"pin_number": "2", "pin_name": "CS+", "pin_description": "Input current sense signal positive input pin."}, {"pin_number": "3", "pin_name": "CS-", "pin_description": "Input current sense signal negative input pin."}, {"pin_number": "4, 22, 24, 28, 33, 37", "pin_name": "NC", "pin_description": "No connection pin."}, {"pin_number": "5", "pin_name": "EN/UVLO", "pin_description": "Provides enable/disable and accurate UVLO functions. Floating enables the device by default."}, {"pin_number": "6", "pin_name": "IMON_IN", "pin_description": "Input current monitor. The current from this pin is proportional to the differential voltage between CS+ and CS-."}, {"pin_number": "7", "pin_name": "CLKEN", "pin_description": "DE mode burst operation off state enable signal. Pulled up in PWM/DE modes, pulled low in Burst mode off state. Connect together in parallel operation for sync."}, {"pin_number": "8", "pin_name": "BSTEN", "pin_description": "DE Burst mode enable signal. Pulled up to 5V by an internal 250k resistor in PWM and DE mode. Pulled low in Burst mode. Connect together in multi-chip parallel operation to sync all chips for Burst mode operation."}, {"pin_number": "9", "pin_name": "FB_IN", "pin_description": "Input voltage feedback pin for reverse direction operation. Use a resistor divider to feed the input voltage back. Keep below 0.3V to disable reverse direction. Tie to VCC5V or SGND if not used to set phase shift for parallel operation."}, {"pin_number": "10", "pin_name": "VCC5V", "pin_description": "Output of the internal 5V linear regulator. Supplies bias for the IC. Decouple to SGND with a minimum of 4.7uF ceramic capacitor."}, {"pin_number": "11", "pin_name": "SGND", "pin_description": "Small-signal ground common to all control circuitries. Route separately from PGND."}, {"pin_number": "12", "pin_name": "RT/SYNC", "pin_description": "A resistor to ground adjusts the default switching frequency (100kHz to 600kHz). When an external clock is applied, the internal frequency is synchronized."}, {"pin_number": "13", "pin_name": "PLL_COMP", "pin_description": "Compensation pin for the internal PLL circuit. A compensation network is required."}, {"pin_number": "14", "pin_name": "CLKOUT/DITHER", "pin_description": "Dual function pin. Provides a clock signal for synchronization or enables frequency dither function with a capacitor."}, {"pin_number": "15", "pin_name": "SS/TRK", "pin_description": "Dual function pin for soft-start control with a capacitor or for tracking control with a resistor divider from a master supply."}, {"pin_number": "16", "pin_name": "COMP", "pin_description": "Voltage error GM amplifier output. Sets the reference of the inner current loop. Feedback compensation network is connected here."}, {"pin_number": "17", "pin_name": "FB_OUT", "pin_description": "Output voltage feedback input. Connect to a resistive voltage divider from the output to adjust the output voltage."}, {"pin_number": "18", "pin_name": "IMON_OUT", "pin_description": "Output current monitor. The current from this pin is proportional to the differential voltage between ISEN+ and ISEN-."}, {"pin_number": "19", "pin_name": "OV", "pin_description": "OVP comparator output signal. Pulled low in normal operation, pulled high when output OVP trips. Connect together in parallel operation for OVP sync."}, {"pin_number": "20", "pin_name": "ISEN-", "pin_description": "Output current sense signal negative input pin."}, {"pin_number": "21", "pin_name": "ISEN+", "pin_description": "Output current sense signal positive input pin."}, {"pin_number": "23", "pin_name": "PGOOD", "pin_description": "Open-drain logic output to indicate the status of output voltage. Pulled low when output is not within ±10% of nominal or EN is low."}, {"pin_number": "25", "pin_name": "UG2", "pin_description": "High-side MOSFET gate driver output controlled by the boost PWM signal."}, {"pin_number": "26", "pin_name": "PHASE2", "pin_description": "Phase node connection of the boost converter. Connects to the junction of the upper MOSFET's source, inductor, and lower MOSFET's drain."}, {"pin_number": "27", "pin_name": "BOOT2", "pin_description": "Bootstrap pin to provide bias for the boost high-side driver. Connect bootstrap capacitor and diode."}, {"pin_number": "29", "pin_name": "LG2/OC_MODE", "pin_description": "Low-side MOSFET gate driver output for boost and OCP mode set pin. A resistor to ground sets OCP to constant current or Hiccup mode."}, {"pin_number": "30", "pin_name": "VDD", "pin_description": "Output of the internal 8V linear regulator. Supplies bias for drivers. Decouple to PGND with a minimum of 4.7µF ceramic capacitor."}, {"pin_number": "31", "pin_name": "PGND", "pin_description": "Power ground connection. Connect to the sources of the lower MOSFETs."}, {"pin_number": "32", "pin_name": "LG1/PWM_MODE", "pin_description": "Low-side MOSFET gate driver output for buck and PWM mode set pin. A resistor to ground sets forced PWM or DE mode."}, {"pin_number": "34", "pin_name": "BOOT1", "pin_description": "Bootstrap pin to provide bias for the buck high-side driver. Connect bootstrap capacitor and diode."}, {"pin_number": "35", "pin_name": "PHASE1", "pin_description": "Phase node connection of the buck converter. Connects to the junction of the upper MOSFET's source, inductor, and lower MOSFET's drain."}, {"pin_number": "36", "pin_name": "UG1", "pin_description": "High-side MOSFET gate driver output controlled by the buck PWM signal."}, {"pin_number": "38", "pin_name": "EXTBIAS", "pin_description": "External bias input for the optional VDD LDO. Decouple with 10µF capacitor or tie to ground if unused."}, {"pin_number": "EPAD", "pin_name": "SGND", "pin_description": "Small-signal ground common to all control circuitries. EPAD is at ground potential and connected to SGND internally. Solder to ground plane for thermal performance."}]}], "datasheet_cn": "未找到", "datasheet_en": "ISL81601 Datasheet v3.1", "family_comparison": "Compares ISL81601 with ISL81401 and ISL81401A on features like VIN, VDD, and current control.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "60V", "min_input_voltage": "4.5V", "max_output_voltage": "60V", "min_output_voltage": "1V", "max_output_current": "未找到", "max_switch_frequency": "0.6MHz", "quiescent_current": "4700μA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PWM/DE/Burst", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值/谷值电流模式"}, "package": [{"pitch": "0.5", "height": "0.9", "length": "9.7", "width": "4.4", "type": "or", "pin_count": "12"}]}