{"part_number": "TPS55287-Q1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS55287-Q1 具有I²C接口的36V、4A降压/升压转换器", "features": ["符合 AEC-Q100 标准: 器件温度等级 1: –40°C 至 +125°C 环境工作温度范围", "功能安全型: 可提供用于功能安全系统设计的文档", "可编程电源 (PPS) 支持 USB 供电 (USB PD): 宽输入电压范围: 3.0V 至 36V; 可编程输出电压范围: 0.8V 至 22V, 步长为 10mV; ±1% 基准电压精度; 对电缆压降提供可调输出电压补偿; 可编程输出电流限值, 步长为 50mA; ±5% 精密输出电流监测; I2C 接口", "在整个负载范围内具有高效率: VIN = 12V、VOUT = 20V 且 IOUT = 1.5A 时效率为 96.7%; 轻负载状态下的可编程 PFM 和 FPWM 模式", "避免频率干扰和串扰: 可选的时钟同步; 可编程开关频率范围为 200kHz 至 2.2MHz", "降低 EMI: 可选可编程扩展频谱; 无引线封装", "丰富的保护特性: 输出过压保护; 利用断续模式实现输出短路保护; 热关断保护; 4A 平均电感器电流限值", "小解决方案尺寸: 开关频率高达 2.2MHz (最大值); 3.0mm × 5.0mm HotRod™ QFN 封装"], "description": "TPS55287-Q1 同步降压/升压转换器经优化, 可将电池电压或适配器电压转换为电源轨。TPS55287-Q1 集成了四个 MOSFET 开关, 可为 USB 电力输送 (USB PD) 应用提供紧凑型解决方案。TPS55287-Q1 的输入电压高达 36V。通过 I2C 接口, TPS55287-Q1 的输出电压可以在 0.8V 至 22V 之间 (步长为 10mV) 进行编程。在升压模式下, 输入电压为 12V 时, 它可提供 35W 的功率。输入电压为 9V 时, 可提供 25W 的功率。TPS55287-Q1 采用平均电流模式控制方案。开关频率可通过外部电阻在 200kHz 至 2.2MHz 之间进行编程, 并且可与外部时钟同步。TPS55287-Q1 还提供展频选项, 从而更大限度地减少峰值 EMI。TPS55287-Q1 提供输出过压保护、平均电感器电流限值、逐周期峰值电流限值和输出短路保护。TPS55287-Q1 还可在持续过载情况下, 通过可选输出电流限值和断续模式保护来确保安全运行。TPS55287-Q1 可以使用具有高开关频率的小型电感器和电容器。它采用 3.0mm × 5.0mm QFN 封装。", "applications": ["汽车充电器", "USB PD", "无线充电器", "汽车信息娱乐系统与仪表组", "汽车尾部照明", "高级驾驶辅助系统 (ADAS)"], "ordering_information": [{"part_number": "TPS55287-Q1", "order_device": "TPS55287QWRYQRQ1", "status": "Active", "package_type": "VQFN-HR", "package_code": "RYQ", "package_drawing_code": "RYQ0021B", "carrier_description": "LARGE T&R", "carrier_quantity": "3000", "marking": "S55287Q", "pin_count": "21", "length": "5.0", "width": "3.0", "height": "1.0", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "可调", "application_grade": "Auto"}, {"part_number": "TPS55287-Q1", "order_device": "TPS55287QWRYQRQ1.A", "status": "Active", "package_type": "VQFN-HR", "package_code": "RYQ", "package_drawing_code": "RYQ0021B", "carrier_description": "LARGE T&R", "carrier_quantity": "3000", "marking": "S55287Q", "pin_count": "21", "length": "5.0", "width": "3.0", "height": "1.0", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "可调", "application_grade": "Auto"}], "pin_function": [{"product_part_number": "TPS55287-Q1", "package_type": "VQFN-HR", "pins": [{"pin_number": "1", "pin_name": "EN/UVLO", "pin_description": "启用逻辑输入和可编程输入电压欠压锁定(UVLO)输入。逻辑高电平可启用器件。逻辑低电平可禁用器件并将其转换为关断模式。EN/UVLO引脚上的电压高于1.15V的逻辑高电平电压后,该引脚可充当可编程 UVLO输入,具有1.23V的内部基准电压。"}, {"pin_number": "2", "pin_name": "MODE", "pin_description": "I2C 目标地址选择。当它连接至逻辑高电压时,I2C目标地址为74H。当它连接至逻辑低电压时,I2C目标地址为75H。"}, {"pin_number": "3", "pin_name": "SCL", "pin_description": "I2C接口的时钟。"}, {"pin_number": "4", "pin_name": "SDA", "pin_description": "I2C接口的数据。"}, {"pin_number": "5", "pin_name": "DITH/SYNC", "pin_description": "抖动频率设置和同步时钟输入。在该引脚和接地端之间,使用电容器来设置抖动频率。该引脚接地短路或拉至1.2V以上时,无抖动功能。可以在该引脚上应用外部时钟,来同步开关频率。"}, {"pin_number": "6", "pin_name": "FSW", "pin_description": "开关频率可通过该引脚和AGND 引脚之间的电阻进行编程。"}, {"pin_number": "7", "pin_name": "VIN", "pin_description": "降压/升压转换器的输入。"}, {"pin_number": "8", "pin_name": "SW1", "pin_description": "降压侧的开关节点引脚。它连接到内部降压低侧功率MOSFET的漏极,以及内部降压高侧功率MOSFET的源极。"}, {"pin_number": "9", "pin_name": "PGND", "pin_description": "器件的电源接地。"}, {"pin_number": "10", "pin_name": "SW2", "pin_description": "升压侧的开关节点引脚。它连接到内部升压低侧功率MOSFET的漏极,以及内部升压高侧功率MOSFET的源极。"}, {"pin_number": "11", "pin_name": "VOUT", "pin_description": "降压/升压转换器的输出。"}, {"pin_number": "12", "pin_name": "ISP", "pin_description": "电流检测放大器的正输入。在ISP 引脚和ISN引脚之间连接的可选电流检测电阻可以限制输出电流。如果检测到的电压达到寄存器中设置的电流限值,将激活慢速恒定电流控制环路,并开始调节 ISP 引脚和 ISN 引脚之间的电压。将ISP 引脚和ISN 引脚与VOUT 引脚连接到一起,可以禁用输出电流限制功能。它不得处于开路状态。"}, {"pin_number": "13", "pin_name": "ISN", "pin_description": "电流检测放大器的负输入。在ISP 引脚和ISN引脚之间连接的可选电流检测电阻可以限制输出电流。如果检测到的电压达到寄存器中设置的电流限值,将激活慢速恒定电流控制环路,并开始调节 ISP 引脚和ISN 引脚之间的电压。将 ISP 引脚和ISN 引脚与VOUT 引脚连接到一起,可以禁用输出电流限制功能。它不得处于开路状态。"}, {"pin_number": "14", "pin_name": "FB/INT", "pin_description": "当器件设置为使用外部输出电压反馈时,连接到电阻分压器的中心抽头以对输出电压进行编程。当器件设置为使用内部反馈时,该引脚是故障指示灯开漏输出。当发生内部故障时,该引脚输出逻辑低电平。"}, {"pin_number": "15", "pin_name": "COMP", "pin_description": "内部误差放大器的输出。在该引脚和AGND 引脚之间连接环路补偿网络。"}, {"pin_number": "16", "pin_name": "CDC", "pin_description": "电压输出与 ISP 引脚和 ISN 引脚之间检测到的电压成正比。在该引脚和AGND 之间使用一个电阻器来增加输出电压,以补偿电缆上由电缆电阻引起的压降。如果使用内部电缆压降补偿,则该引脚可保持开路。"}, {"pin_number": "17", "pin_name": "AGND", "pin_description": "器件的信号接地。"}, {"pin_number": "18", "pin_name": "VCC", "pin_description": "内部稳压器的输出。在此引脚和AGND 引脚之间需要一个大于4.7μF的陶瓷电容器。"}, {"pin_number": "19", "pin_name": "BOOT2", "pin_description": "升压侧高侧 MOSFET 栅极驱动器的电源。必须在此引脚和 SW2引脚之间连接一个0.1μF的陶瓷电容器。"}, {"pin_number": "20", "pin_name": "BOOT1", "pin_description": "降压侧高侧 MOSFET 栅极驱动器的电源。必须在此引脚和 SW1引脚之间连接一个0.1μF 的陶瓷电容器。"}, {"pin_number": "21", "pin_name": "EXTVCC", "pin_description": "为VCC 选择内部LDO或外部5V。将它连接至VCC 引脚逻辑高电压或者保持悬空时,可选择内部LDO。将它连接至逻辑低电压时,可为VCC 选择外部5V。"}]}], "datasheet_cn": "ZHCSVK5", "datasheet_en": "SLVSHT1", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "3.0V", "max_output_voltage": "22V", "min_output_voltage": "1V", "max_output_current": "4A", "max_switch_frequency": "2.2MHz", "quiescent_current": "760µA", "high_side_mosfet_resistance": "14mΩ", "low_side_mosfet_resistance": "22mΩ", "over_current_protection_threshold": "4A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "无", "input_under_voltage_protection": "欠压锁定", "output_over_voltage_protection": "自动重启", "output_under_voltage_protection": "无", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "间歇式", "over_temperature_protection": "热关断", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "1.129V", "loop_control_mode": "平均电流模式"}, "package": [{"type": "OPTION", "length": "5.0", "width": "3.0", "pin_count": "2", "pitch": "0.5", "height": "1.0"}]}