{"part_number": "MP8860", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "2.8 - 22V VI<PERSON>, 1A Ιουτ, 4-<PERSON><PERSON>, <PERSON> Buck-Boost Converter with I2C Interface", "features": ["Wide 2.8V to 22V Operating Input Voltage Range", "1V to 20.47V Output Voltage Range (5V Default) with 10mV Resolution through I²C", "1A Output Current or 4A Input Current", "Four Low RDS(ON) Internal Buck Power MOSFETS", "Adjustable Accurate CC Output Current Limit with Internal Sensing MOSFET via I²C", "500kHz Switching Frequency", "Output Over-Voltage Protection (OVP) Hiccup", "Output Short-Circuit Protection (SCP) with Hiccup", "Over-Temperature Warning and Shutdown", "I2C Interface with ALT Pin", "Four Programmable I²C Addresses", "One-Time Programmable (OTP) Non-Volatile Memory", "I2C Programmable Line Drop Compensation, PFM/PWM Mode, Soft Start, OCP, etc.", "EN Shutdown Discharge Programmable", "Available in a QFN-16 (3mmx3mm) Package"], "description": "The MP8860 is a synchronous, 4-switch, integrated buck-boost converter capable of regulating the output voltage from a 2.8V to 22V wide input voltage range with high efficiency.\nThe MP8860 uses constant-on-time (COT) control in buck mode and constant-off-time control in boost mode, providing fast load transient response and smooth buck-boost mode transient. The MP8860 provides auto PFM/PWM or forced PWM switching modes, and programmable output constant current (CC) current limit, which supports flexible design for different applications.\nFull protection features include over-current protection (OCP), over-voltage protection (OVP), under-voltage protection (UVP), programmable soft start, and thermal shutdown.\nThe MP8860 is available in a 16-pin QFN (3mmx3mm) package.", "applications": ["Power Supply for Motor", "Buck-Boost Bus Supplies", "Industrial Systems", "Personal Medical Products", "DSLR Cameras"], "ordering_information": [{"part_number": "MP8860", "order_device": "MP8860GQ-0000", "package_type": "QFN-16", "package_drawing_code": "MO-220", "output_voltage": "5V (<PERSON><PERSON><PERSON>)", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MP8860", "order_device": "MP8860GQ-xxxx", "package_type": "QFN-16", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MP8860", "order_device": "MP8860GQ-xxxx-Z", "package_type": "QFN-16", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MP8860", "package_type": "QFN-16", "pins": [{"pin_number": "1", "pin_name": "IN", "pin_description": "Supply voltage. IN is the drain of the internal power device and provides power to the entire chip. The MP8860 operates from a 2.8V to 22V input voltage. A capacitor (CIN) is required to prevent large voltage spikes from appearing at the input. Place CIN as close to the IC as possible."}, {"pin_number": "2, 11", "pin_name": "GND", "pin_description": "Power ground. GND is the reference ground of the regulated output voltage. GND requires extra care during PCB layout. Connect GND with copper traces and vias."}, {"pin_number": "3", "pin_name": "EN", "pin_description": "On/off control for entire chip. Drive EN high to turn on the chip. Drive EN low or float EN to turn off the device. EN has internal 2MΩ pull-down resistor to ground."}, {"pin_number": "4", "pin_name": "ADD", "pin_description": "I2C slave addresses program pin. Connect a resistor divider from VCC to ADD to set four different I2C slave addresses."}, {"pin_number": "5", "pin_name": "SCL", "pin_description": "Clock pin of the I²C interface. SCL can support an I²C clock up to 3.4MHz."}, {"pin_number": "6", "pin_name": "SDA", "pin_description": "Data pin of the I²C interface."}, {"pin_number": "7", "pin_name": "OC", "pin_description": "Output constant current limit set pin."}, {"pin_number": "8", "pin_name": "ALT", "pin_description": "Alert output. ALT pulling to logic low indicates that a fault or warning has occurred."}, {"pin_number": "9", "pin_name": "VCC", "pin_description": "Internal 3.6V LDO regulator output. Decouple VCC with a 1µF capacitor."}, {"pin_number": "10", "pin_name": "AGND", "pin_description": "Analog ground. Connect AGND to GND."}, {"pin_number": "12", "pin_name": "OUT", "pin_description": "Output power pin. Place the output capacitor close to OUT and GND."}, {"pin_number": "13", "pin_name": "BST2", "pin_description": "Bootstrap. Connect a 0.1µF capacitor between SW2 and BST2 to form a floating supply across the high-side switch driver."}, {"pin_number": "14", "pin_name": "SW2", "pin_description": "Switching node of the second half bridge. Connect one end of the inductor to SW2 for the current to run through the bridge."}, {"pin_number": "15", "pin_name": "SW1", "pin_description": "Switching node of the first half bridge. Connect one end of the inductor to SW1 for the current to run through the bridge."}, {"pin_number": "16", "pin_name": "BST1", "pin_description": "Bootstrap. Connect a 0.1µF capacitor between SW1 and BST1 to form a floating supply across the high-side switch driver."}]}], "datasheet_cn": "未找到", "datasheet_en": "MP8860 Datasheet", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "22V", "min_input_voltage": "2.8V", "max_output_voltage": "20.47V", "min_output_voltage": "1V", "max_output_current": "1A", "max_switch_frequency": "0.5MHz", "quiescent_current": "1000µA", "high_side_mosfet_resistance": "50mΩ", "low_side_mosfet_resistance": "42mΩ", "over_current_protection_threshold": "Adjustable (0A to 3A)", "operation_mode": "同步", "pass_through_mode": "No", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM/PWM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Hiccup", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "未找到", "loop_control_mode": "Constant On Time Control"}, "package": [{"pitch": "0.50", "height": "1.00", "length": "3.6", "width": "2.8", "type": "Information", "pin_count": "3"}]}