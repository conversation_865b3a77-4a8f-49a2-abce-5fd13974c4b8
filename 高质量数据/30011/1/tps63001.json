[{"part_number": "TPS63000", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "升降压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "TPS6300x High-Efficient Single Inductor Buck-Boost Converter With 1.8-A Switches", "features": ["Input Voltage Range: 1.8 V to 5.5 V", "Fixed and Adjustable Output Voltage Options from 1.2 V to 5.5 V", "Up to 96% Efficiency", "1200-mA Output Current at 3.3 V in Step-Down Mode (VIN = 3.6 V to 5.5 V)", "Up to 800-mA Output Current at 3.3 V in Boost Mode (VIN > 2.4 V)", "Automatic Transition Between Step-Down and Boost Mode", "Device Quiescent Current less than 50 μA", "Power-Save Mode for Improved Efficiency at Low Output Power", "Forced Fixed Frequency Operation and Synchronization Possible", "Load Disconnect During Shutdown", "Overtemperature Protection", "Available in a Small 3-mm × 3-mm 10-Pin VSON Package (QFN)"], "description": "The TPS6300x devices provide a power supply solution for products powered by either a two-cell or three-cell alkaline, NiCd or NiMH battery, or a one-cell Li-ion or Li-polymer battery. Output currents can go as high as 1200 mA while using a single-cell Li-ion or Li-polymer battery, and discharge it down to 2.5 V or lower. The buck-boost converter is based on a fixed frequency, pulse width modulation (PWM) controller using synchronous rectification to obtain maximum efficiency. At low load currents, the converter enters power-save mode to maintain high efficiency over a wide load current range. The power-save mode can be disabled, forcing the converter to operate at a fixed switching frequency. The maximum average current in the switches is limited to a typical value of 1800 mA. The output voltage is programmable using an external resistor divider, or is fixed internally on the chip. The converter can be disabled to minimize battery drain. During shutdown, the load is disconnected from the battery. The TPS6300x devices operate over a free air temperature range of –40°C to 85°C. The devices are packaged in a 10-pin VSON package (QFN) measuring 3 mm × 3 mm (DRC).", "applications": ["All Two-Cell and Three-Cell Alkaline, NiCd or NiMH or Single-Cell Li Battery Powered Products", "Portable Audio Players", "Smart Phones", "Personal Medical Products", "White LEDs"], "ordering_information": [{"part_number": "TPS63000", "order_device": "TPS63000DRCR", "package_type": "VSON", "package_drawing_code": "DRC0010J", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63000", "order_device": "TPS63000DRCT", "package_type": "VSON", "package_drawing_code": "DRC0010J", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63000", "package_type": "VSON (DRC)", "pins": [{"pin_number": "1", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}, {"pin_number": "2", "pin_name": "L2", "pin_description": "Connection for inductor"}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power ground"}, {"pin_number": "4", "pin_name": "L1", "pin_description": "Connection for inductor"}, {"pin_number": "5", "pin_name": "VIN", "pin_description": "Supply voltage for power stage"}, {"pin_number": "6", "pin_name": "EN", "pin_description": "Enable input (1 enabled, 0 disabled)"}, {"pin_number": "7", "pin_name": "PS/SYNC", "pin_description": "Enable / disable power-save mode (1 disabled, 0 enabled, clock signal for synchronization)"}, {"pin_number": "8", "pin_name": "VINA", "pin_description": "Supply voltage for control stage"}, {"pin_number": "9", "pin_name": "GND", "pin_description": "Control / logic ground"}, {"pin_number": "10", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT on fixed output voltage versions"}, {"pin_number": "Exposed Thermal Pad", "pin_name": "Exposed Thermal Pad", "pin_description": "The exposed thermal pad is connected to PGND."}]}], "datasheet_cn": "未找到", "datasheet_en": "TPS63000, TPS63001, TPS63002", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.5V", "min_output_voltage": "1.2V", "max_output_current": "1.2A", "max_switch_frequency": "1.5MHz", "quiescent_current": "40µA", "high_side_mosfet_resistance": "100mΩ", "low_side_mosfet_resistance": "100mΩ", "over_current_protection_threshold": "1.8A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "自动重启", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.5V", "loop_control_mode": "平均电流模式"}, "package": [{"type": "VSON", "height": "1.8", "length": "3", "width": "2.5", "pin_count": "10", "pitch": "0.5"}]}, {"part_number": "TPS63001", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "升降压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "TPS6300x High-Efficient Single Inductor Buck-Boost Converter With 1.8-A Switches", "features": ["Input Voltage Range: 1.8 V to 5.5 V", "Fixed and Adjustable Output Voltage Options from 1.2 V to 5.5 V", "Up to 96% Efficiency", "1200-mA Output Current at 3.3 V in Step-Down Mode (VIN = 3.6 V to 5.5 V)", "Up to 800-mA Output Current at 3.3 V in Boost Mode (VIN > 2.4 V)", "Automatic Transition Between Step-Down and Boost Mode", "Device Quiescent Current less than 50 μA", "Power-Save Mode for Improved Efficiency at Low Output Power", "Forced Fixed Frequency Operation and Synchronization Possible", "Load Disconnect During Shutdown", "Overtemperature Protection", "Available in a Small 3-mm × 3-mm 10-Pin VSON Package (QFN)"], "description": "The TPS6300x devices provide a power supply solution for products powered by either a two-cell or three-cell alkaline, NiCd or NiMH battery, or a one-cell Li-ion or Li-polymer battery. Output currents can go as high as 1200 mA while using a single-cell Li-ion or Li-polymer battery, and discharge it down to 2.5 V or lower. The buck-boost converter is based on a fixed frequency, pulse width modulation (PWM) controller using synchronous rectification to obtain maximum efficiency. At low load currents, the converter enters power-save mode to maintain high efficiency over a wide load current range. The power-save mode can be disabled, forcing the converter to operate at a fixed switching frequency. The maximum average current in the switches is limited to a typical value of 1800 mA. The output voltage is programmable using an external resistor divider, or is fixed internally on the chip. The converter can be disabled to minimize battery drain. During shutdown, the load is disconnected from the battery. The TPS6300x devices operate over a free air temperature range of –40°C to 85°C. The devices are packaged in a 10-pin VSON package (QFN) measuring 3 mm × 3 mm (DRC).", "applications": ["All Two-Cell and Three-Cell Alkaline, NiCd or NiMH or Single-Cell Li Battery Powered Products", "Portable Audio Players", "Smart Phones", "Personal Medical Products", "White LEDs"], "ordering_information": [{"part_number": "TPS63001", "order_device": "TPS63001DRCR", "package_type": "VSON", "package_drawing_code": "DRC0010J", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63001", "order_device": "TPS63001DRCT", "package_type": "VSON", "package_drawing_code": "DRC0010J", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63001", "package_type": "VSON (DRC)", "pins": [{"pin_number": "1", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}, {"pin_number": "2", "pin_name": "L2", "pin_description": "Connection for inductor"}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power ground"}, {"pin_number": "4", "pin_name": "L1", "pin_description": "Connection for inductor"}, {"pin_number": "5", "pin_name": "VIN", "pin_description": "Supply voltage for power stage"}, {"pin_number": "6", "pin_name": "EN", "pin_description": "Enable input (1 enabled, 0 disabled)"}, {"pin_number": "7", "pin_name": "PS/SYNC", "pin_description": "Enable / disable power-save mode (1 disabled, 0 enabled, clock signal for synchronization)"}, {"pin_number": "8", "pin_name": "VINA", "pin_description": "Supply voltage for control stage"}, {"pin_number": "9", "pin_name": "GND", "pin_description": "Control / logic ground"}, {"pin_number": "10", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT on fixed output voltage versions"}, {"pin_number": "Exposed Thermal Pad", "pin_name": "Exposed Thermal Pad", "pin_description": "The exposed thermal pad is connected to PGND."}]}], "datasheet_cn": "未找到", "datasheet_en": "TPS63000, TPS63001, TPS63002", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "3.3V", "min_output_voltage": "3.3V", "max_output_current": "1.2A", "max_switch_frequency": "1.5MHz", "quiescent_current": "40µA", "high_side_mosfet_resistance": "100mΩ", "low_side_mosfet_resistance": "100mΩ", "over_current_protection_threshold": "1.8A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "自动重启", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "未找到", "output_reference_voltage": "未找到", "loop_control_mode": "平均电流模式"}, "package": [{"type": "VSON", "height": "1.8", "length": "3", "width": "2.5", "pin_count": "10", "pitch": "0.5"}]}, {"part_number": "TPS63002", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "升降压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "TPS6300x High-Efficient Single Inductor Buck-Boost Converter With 1.8-A Switches", "features": ["Input Voltage Range: 1.8 V to 5.5 V", "Fixed and Adjustable Output Voltage Options from 1.2 V to 5.5 V", "Up to 96% Efficiency", "1200-mA Output Current at 3.3 V in Step-Down Mode (VIN = 3.6 V to 5.5 V)", "Up to 800-mA Output Current at 3.3 V in Boost Mode (VIN > 2.4 V)", "Automatic Transition Between Step-Down and Boost Mode", "Device Quiescent Current less than 50 μA", "Power-Save Mode for Improved Efficiency at Low Output Power", "Forced Fixed Frequency Operation and Synchronization Possible", "Load Disconnect During Shutdown", "Overtemperature Protection", "Available in a Small 3-mm × 3-mm 10-Pin VSON Package (QFN)"], "description": "The TPS6300x devices provide a power supply solution for products powered by either a two-cell or three-cell alkaline, NiCd or NiMH battery, or a one-cell Li-ion or Li-polymer battery. Output currents can go as high as 1200 mA while using a single-cell Li-ion or Li-polymer battery, and discharge it down to 2.5 V or lower. The buck-boost converter is based on a fixed frequency, pulse width modulation (PWM) controller using synchronous rectification to obtain maximum efficiency. At low load currents, the converter enters power-save mode to maintain high efficiency over a wide load current range. The power-save mode can be disabled, forcing the converter to operate at a fixed switching frequency. The maximum average current in the switches is limited to a typical value of 1800 mA. The output voltage is programmable using an external resistor divider, or is fixed internally on the chip. The converter can be disabled to minimize battery drain. During shutdown, the load is disconnected from the battery. The TPS6300x devices operate over a free air temperature range of –40°C to 85°C. The devices are packaged in a 10-pin VSON package (QFN) measuring 3 mm × 3 mm (DRC).", "applications": ["All Two-Cell and Three-Cell Alkaline, NiCd or NiMH or Single-Cell Li Battery Powered Products", "Portable Audio Players", "Smart Phones", "Personal Medical Products", "White LEDs"], "ordering_information": [{"part_number": "TPS63002", "order_device": "TPS63002DRCR", "package_type": "VSON", "package_drawing_code": "DRC0010J", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63002", "order_device": "TPS63002DRCT", "package_type": "VSON", "package_drawing_code": "DRC0010J", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63002", "package_type": "VSON (DRC)", "pins": [{"pin_number": "1", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}, {"pin_number": "2", "pin_name": "L2", "pin_description": "Connection for inductor"}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power ground"}, {"pin_number": "4", "pin_name": "L1", "pin_description": "Connection for inductor"}, {"pin_number": "5", "pin_name": "VIN", "pin_description": "Supply voltage for power stage"}, {"pin_number": "6", "pin_name": "EN", "pin_description": "Enable input (1 enabled, 0 disabled)"}, {"pin_number": "7", "pin_name": "PS/SYNC", "pin_description": "Enable / disable power-save mode (1 disabled, 0 enabled, clock signal for synchronization)"}, {"pin_number": "8", "pin_name": "VINA", "pin_description": "Supply voltage for control stage"}, {"pin_number": "9", "pin_name": "GND", "pin_description": "Control / logic ground"}, {"pin_number": "10", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT on fixed output voltage versions"}, {"pin_number": "Exposed Thermal Pad", "pin_name": "Exposed Thermal Pad", "pin_description": "The exposed thermal pad is connected to PGND."}]}], "datasheet_cn": "未找到", "datasheet_en": "TPS63000, TPS63001, TPS63002", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5V", "min_output_voltage": "5V", "max_output_current": "1.2A", "max_switch_frequency": "1.5MHz", "quiescent_current": "40µA", "high_side_mosfet_resistance": "100mΩ", "low_side_mosfet_resistance": "100mΩ", "over_current_protection_threshold": "1.8A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "自动重启", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "未找到", "output_reference_voltage": "未找到", "loop_control_mode": "平均电流模式"}, "package": [{"type": "VSON", "height": "1.8", "length": "3", "width": "2.5", "pin_count": "10", "pitch": "0.5"}]}]