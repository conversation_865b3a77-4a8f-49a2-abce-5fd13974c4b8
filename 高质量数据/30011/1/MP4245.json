{"part_number": "MP4245", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "36V, 6A Peak, Buck<PERSON><PERSON><PERSON> Converter with I²C Interface for Power Delivery", "features": ["Supports 60W Buck-Boost or 6A Peak Ιουτ", "Wide 4V to 36V Operating Input Voltage Range", "1V to 23V Output Voltage Range", "250kHz, 350kHz, or 420kHz Selectable Frequency or SYNC Input", "12mΩ/24mΩ/14mΩ/14mΩ Low RDS(ON) for Switches A, B, C, and D", "Selectable Frequency Spread Spectrum", "Line Drop Compensation", "Accurate Constant Current (CC) Output Current Limit", "I2C Interface and MTP (PMBus Compatible): PFM/PWM Mode, Current Limit, Output Voltage, Frequency Spread Spectrum, and Line Drop Compensation; CRC Calculation for MTP Integrity", "Load-<PERSON><PERSON>", "EN Shutdown Active Discharge", "Available in a QFN-21 (4mmx5mm) Package with Wettable Flanks"], "description": "The MP4245 is a buck-boost converter with four integrated power switches. The device can deliver up to 6A of output current at certain input-voltage supply ranges, with excellent load and line regulation. The MP4245 is suitable for USB power delivery (USB PD) applications. It can work with an external USB PD controller through the I2C interface. The I2C interface and two-time programmable multiple-time programmable (MTP) memory provide flexible features. Fault condition protections includes constant current (CC) limiting, output over-voltage protection (OVP), and thermal shutdown (TSD). The MP4245 requires a minimal number of readily available, standard external components, and is available in a QFN-21 (4mmx5mm) package.", "applications": ["USB Type-C with PD Charging Only Ports", "12V Bus Voltage Supplies", "Wireless Charging"], "ordering_information": [{"part_number": "MP4245", "order_device": "MP4245GVE-0000", "package_type": "QFN-21", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MP4245", "order_device": "MP4245GVE-0001", "package_type": "QFN-21", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MP4245", "order_device": "MP4245GVE-xxxx-Z", "package_type": "QFN-21", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MP4245", "package_type": "QFN-21 (4mmx5mm)", "pins": [{"pin_number": "1", "pin_name": "VIN", "pin_description": "Supply voltage. The MP4245 operates from a 4V to 36V input voltage. An input capacitor (CIN) prevents large voltage spikes at the input. Place CIN as close to the IC as possible. VIN is the drain of the first half-bridge's internal power device. VIN supplies power to the entire chip."}, {"pin_number": "2", "pin_name": "PGND", "pin_description": "Power ground. PGND requires extra consideration during PCB layout. Connect PGND to GND with copper traces and vias."}, {"pin_number": "3", "pin_name": "OUT", "pin_description": "Buck-boost mode output pin."}, {"pin_number": "4", "pin_name": "ISENS-", "pin_description": "Negative node of the current-sense signal input. Place a current-sense resistor between the PGND pin and the USB port's GND pin. Connect the ISENS- pin to the PGND side. The sensed signal is used for buck-boost constant current (CC) limiting."}, {"pin_number": "5", "pin_name": "ISENS+", "pin_description": "Positive node of current-sense signal input. Place a current-sense resistor between the PGND pin and the USB port's GND pin. Connect the ISENS+ pin to the USB port's GND. The sensed signal is used for buck-boost constant current (CC) limiting."}, {"pin_number": "6", "pin_name": "SDA", "pin_description": "I2C data line."}, {"pin_number": "7", "pin_name": "SCL", "pin_description": "I2C clock signal input."}, {"pin_number": "8", "pin_name": "ALT", "pin_description": "PMBus alert pin. Open drain output, active low. Pull ALT up to an external supply with a 10kΩ resistor."}, {"pin_number": "9", "pin_name": "ADD", "pin_description": "I2C slave address set pin. Connect a different resistor from the ADD pin to ground to set eight different I2C addresses. The internal ADC reads this pin's voltage to lock the I2C address during start-up. ADD has an internal 20µA current source."}, {"pin_number": "10", "pin_name": "GATE", "pin_description": "Gate drive pin to drive external MOSFET."}, {"pin_number": "11", "pin_name": "BST2", "pin_description": "Bootstrap. A 220nF capacitor is connected between SW2 and BST2 to form a floating supply across the high-side switch driver."}, {"pin_number": "12", "pin_name": "SW2", "pin_description": "Switch 2 output. Use a wide PCB trace to make the SW2 connection."}, {"pin_number": "13", "pin_name": "SW1", "pin_description": "Switch 1 output. Use a wide PCB trace to make the SW1 connection."}, {"pin_number": "14", "pin_name": "BST1", "pin_description": "Bootstrap. A 220nF capacitor is connected between SW1 and BST1 to form a floating supply across the high-side switch driver."}, {"pin_number": "15", "pin_name": "VCC", "pin_description": "Internal 5V LDO regulator output. Decouple VCC with a 1µF capacitor."}, {"pin_number": "16", "pin_name": "AGND", "pin_description": "Analog ground. Connect AGND to PGND, then connect AGND to the VCC capacitor's ground node."}, {"pin_number": "17", "pin_name": "FREQ", "pin_description": "Frequency selection pin. Float the FREQ pin to set the frequency to 350kHz. Pull FREQ to ground set the frequency to 250kHz. Pull FREQ to 5V to set the frequency to 420kHz."}, {"pin_number": "18", "pin_name": "DRV", "pin_description": "5.5V to 6.5V adjustable LDO output. 1mA load capability. DRV starts up at the same time as VCC. Add a 1µF decoupling capacitor to DRV."}, {"pin_number": "19", "pin_name": "SYNCI", "pin_description": "SYNC clock input. Apply a clock on this pin to sync the switching frequency to the external clock. The allowable frequency for the external clock is 250kHz, 350kHz, or 420kHz. If SYNC is not used, it is recommended to connect SYNCI to GND."}, {"pin_number": "20", "pin_name": "EN", "pin_description": "Enable control pin. Apply a logic high voltage on this pin to enable the IC, pull EN to logic low to disable the IC. EN has an internal 2MΩ pull-down resistor."}, {"pin_number": "21", "pin_name": "FB", "pin_description": "Feedback pin. Connect FB to the tap of an external resistor divider from the output to GND to set the output voltage."}]}], "datasheet_cn": "未找到", "datasheet_en": "MP4245.pdf", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "4V", "max_output_voltage": "23V", "min_output_voltage": "1V", "max_output_current": "6A", "max_switch_frequency": "420kHz", "quiescent_current": "300µA", "high_side_mosfet_resistance": "12mΩ", "low_side_mosfet_resistance": "24mΩ", "over_current_protection_threshold": "1-6.35A(可调)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C, PMBus", "enable_function": "Yes", "light_load_mode": "PFM/PWM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.5% to ±2%", "output_reference_voltage": "0.1V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "with", "height": "1.0", "width": "4.0", "length": "5.0", "pitch": "4245.", "pin_count": "4245"}]}