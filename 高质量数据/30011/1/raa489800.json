{"part_number": "RAA489800", "manufacturer": "Renesas", "country": "Japan", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "RAA489800 Bidirectional Buck-Boost Voltage Regulator", "features": ["Bidirectional buck, boost, and buck-boost operation", "Configurable for 4-switch buck-boost or 2-switch buck operation", "Input voltage range: 3.8V to 23V (no dead zone)", "Output voltage: up to 21V", "Up to 1MHz switching frequency", "Pin programmable soft-start time", "LDO output for VDD and VDDP", "System FAULT status ALERT function", "Input/output internal discharge function", "Active switching for negative voltage transitions", "Pass Through mode in both directions", "Forward and Reverse mode enable pins", "OCP, OVP, UVP, and OTP protection", "Absolute overvoltage protection", "SMBus and auto-increment I2C compatible", "Pb-free (RoHS compliant)", "32 Ld 4x4 TQFN package", "UL 2367, IEC 62368-1: File No. E520109"], "description": "The RAA489800 is a bidirectional, buck-boost voltage regulator that provides buck-boost voltage regulation and protection features. The advanced Renesas R3™ Technology provides high light-load efficiency, fast transient response, and seamless DCM/CCM transitions. The RAA489800 takes input power from a wide range of DC power sources up to 23V (such as conventional AC/DC adapters (ADP), USB PD ports, travel ADP) and safely converts it to a regulated voltage up to 21V. The RAA489800 can also convert a wide range DC power source connected at its output (system side) to a regulated voltage to its input (ADP side). This bidirectional buck-boost regulation feature makes the RAA489800's application very flexible. In addition to 4-switch buck-boost configuration, it can also support 2-switch Buck mode operation. The RAA489800 includes various system operation functions such as the Forward mode enable pin, Reverse mode enable pin, programmable soft-start time, and adjustable forward and reverse VOUT. It also has forward and reverse power-good indicators. The protection functionalities include OCP, OVP, UVP, and OTP. The RAA489800 has serial communication through SMBus/I2C that allows programming of many critical parameters to deliver a customized solution. These programming parameters include, but are not limited to: output current limit, input current limit, and output voltage setting.", "applications": ["Tablets", "Ultrabooks", "power banks", "mobile devices", "USB-C"], "ordering_information": [{"part_number": "RAA489800", "order_device": "RAA489800ARGNP#AAO", "package_type": "TQFN", "package_drawing_code": "L32.4x4D", "output_voltage": "未找到", "min_operation_temp": "-10", "max_operation_temp": "100"}, {"part_number": "RAA489800", "order_device": "RAA489800ARGNP#HA0", "package_type": "TQFN", "package_drawing_code": "L32.4x4D", "output_voltage": "未找到", "min_operation_temp": "-10", "max_operation_temp": "100"}, {"part_number": "RAA489800", "order_device": "RAA489800A2GNP#AAO", "package_type": "TQFN", "package_drawing_code": "L32.4x4D", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "RAA489800", "order_device": "RAA489800A2GNP#HA0", "package_type": "TQFN", "package_drawing_code": "L32.4x4D", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "RAA489800", "package_type": "TQFN", "pins": [{"pin_number": "Bottom Pad", "pin_name": "GND", "pin_description": "Signal common to the IC. Unless otherwise stated, signals are referenced to the GND pin. GND should also be used as the thermal pad for heat dissipation."}, {"pin_number": "1", "pin_name": "CSON", "pin_description": "Forward VOUT current sense negative input. Connect to the VOUT current resistor negative input. Place a 0.1µF ceramic capacitor between CSOP and CSON to provide differential mode filtering."}, {"pin_number": "2", "pin_name": "CSOP", "pin_description": "Forward VOUT current sense positive input. Connect to the VOUT current resistor positive input. Place a 0.1µF ceramic capacitor between CSOP and CSON to provide differential mode filtering."}, {"pin_number": "3", "pin_name": "VOUTS", "pin_description": "Forward VSYS feedback voltage. Use an optional resistor divider externally to configure forward VSYS voltage."}, {"pin_number": "4", "pin_name": "BOOT2", "pin_description": "High-side MOSFET Q4 gate driver supply. Connect an MLCC capacitor across the BOOT2 pin and the PHASE2 pin. The boot capacitor is charged through an internal boot diode connected from the VDDP pin to the BOOT2 pin when the PHASE2 pin drops below VDDP minus the voltage drop across the internal boot diode."}, {"pin_number": "5", "pin_name": "UGATE2", "pin_description": "High-side MOSFET Q4 gate drive."}, {"pin_number": "6", "pin_name": "PHASE2", "pin_description": "Current return path for the high-side MOSFET Q4 gate drive. Connect this pin to the node consisting of the high-side MOSFET Q4 source, the low-side MOSFET Q3 drain, and the inductor output terminal."}, {"pin_number": "7", "pin_name": "LGATE2", "pin_description": "Low-side MOSFET Q3 gate drive."}, {"pin_number": "8", "pin_name": "VDDP", "pin_description": "Power supply to the gate drivers. Connect to the VDD pin through a 4.7Ω resistor and connect a 2.2µF ceramic capacitor to GND. The effective capacitance at 5V must be at least 0.4µF after derating."}, {"pin_number": "9", "pin_name": "LGATE1", "pin_description": "Low-side MOSFET Q2 gate drive."}, {"pin_number": "10", "pin_name": "PHASE1", "pin_description": "Current return path for the high-side MOSFET Q1 gate drive. Connect this pin to the node consisting of the high-side MOSFET Q1 source, the low-side MOSFET Q2 drain, and the input terminal of the inductor."}, {"pin_number": "11", "pin_name": "UGATE1", "pin_description": "High-side MOSFET Q1 gate drive."}, {"pin_number": "12", "pin_name": "BOOT1", "pin_description": "High-side MOSFET Q1 gate driver supply. Connect an MLCC capacitor across the BOOT1 pin and the PHASE1 pin. The boot capacitor is charged through an internal boot diode connected from the VDDP pin to the BOOT1 pin when the PHASE1 pin drops below VDDP minus the voltage drop across the internal boot diode."}, {"pin_number": "13", "pin_name": "ADPS", "pin_description": "Reverse output voltage feedback. Use a resistor divider externally to configure the reverse output voltage."}, {"pin_number": "14", "pin_name": "CSIN", "pin_description": "ADP current sense negative input."}, {"pin_number": "15", "pin_name": "CSIP", "pin_description": "ADP current sense positive input. The modulator also uses this for sensing input voltage in Forward mode and output voltage in Reverse mode."}, {"pin_number": "16", "pin_name": "ADP", "pin_description": "Senses ADP voltage. Forward mode can be enabled when the ADP voltage is higher than 4.1V. The ADP pin is also one of the two internal low power LDO inputs."}, {"pin_number": "17", "pin_name": "DCIN", "pin_description": "Internal LDO input providing power to the IC. Connect a diode OR from the ADP and system outputs. Connect a 4.7µF ceramic capacitor to GND. The effective capacitance at 20V must be at least 0.4µF after derating."}, {"pin_number": "18", "pin_name": "VDD", "pin_description": "Internal LDO output that provides the bias power for the internal analog and digital circuit. Connect a 2.2µF ceramic capacitor to GND. The effective capacitance at 5V must be at least 0.4µF after derating. If VDD is pulled below 2.7V, the RAA489800 resets all the SMBus registers to their default values."}, {"pin_number": "19", "pin_name": "FRWEN", "pin_description": "Forward mode enable, analog signal input. Forward mode is valid if the FRWEN pin voltage is greater than 0.6V."}, {"pin_number": "20", "pin_name": "RVSEN", "pin_description": "Reverse mode enable, digital signal input. Reverse mode is valid if the signal is '1' (logic high), otherwise, Reverse mode is disabled."}, {"pin_number": "21", "pin_name": "SDA", "pin_description": "SMBus data I/O. Connect to the data line from the host controller. Connect a 10k pull-up resistor according to the SMBus specification."}, {"pin_number": "22", "pin_name": "SCL", "pin_description": "SMBus clock I/O. Connect to the clock line from the host controller. Connect a 10k pull-up resistor according to the SMBus specification."}, {"pin_number": "23", "pin_name": "ALERT#", "pin_description": "Open-drain output. If WOC, OV, UV, OTP, or ADPOV faults are detected, ALERT# is pulled low. Clear ALERT# with Control4<1>. If ALERT# is asserted, Table 9 on page 26 shows which fault information bits are set. Configure ALERT# behavior with Control2<10:9>, Control4<7>, and Control4<1>."}, {"pin_number": "24", "pin_name": "FRWPG", "pin_description": "Open-drain output. Indicator output to indicate the forward modulator is enabled and in regulation."}, {"pin_number": "25", "pin_name": "ADDR0", "pin_description": "Address setting pin for the IC. The IC address is set by the ADDR0 and ADDR1 logic voltage levels."}, {"pin_number": "26", "pin_name": "RVSPG", "pin_description": "Open-drain output. Indicator output to indicate the reverse modulator is enabled and in regulation."}, {"pin_number": "27", "pin_name": "PROG", "pin_description": "A resistor from the PROG pin to GND sets the default forward system output voltage."}, {"pin_number": "28", "pin_name": "COMPF", "pin_description": "Forward mode error amplifier output. Connect a compensation network externally from COMPF to GND."}, {"pin_number": "29", "pin_name": "REF", "pin_description": "Output voltage soft-start reference. It also sets the slew rate for output voltage changes. A ceramic capacitor from REF to GND is set to the desired soft-start time. The forward output voltage (VOUTS) reference soft-start time is set in Forward mode. The reverse output voltage (ADPS) reference soft-start time is set in Reverse mode."}, {"pin_number": "30", "pin_name": "COMPR", "pin_description": "Reverse mode error amplifier output. Connect a compensation network externally from COMPR to GND."}, {"pin_number": "31", "pin_name": "VOUT", "pin_description": "Forward VOUT sense voltage for modulator and PHASE 2 zero-current comparator."}, {"pin_number": "32", "pin_name": "ADDR1", "pin_description": "Address setting pin for the IC. The IC address is set by the ADDR0 and ADDR1 logic voltage levels."}]}], "datasheet_cn": "未找到", "datasheet_en": "RAA489800 Datasheet Rev.1.02 (2021-10-21)", "family_comparison": "| Part Number | Absolute Overvoltage Protection | Buck-Boost Efficiency Improvement | Pin #23 Function | Input/ Output Current PROCHOT# | CC/CV Loop ALERT# | 2-FET Buck Only Mode | Forced Buck-Boost and Boost Mode | VSYS Low Shutdown Feature | Default Input Current Limit Setting Trough PROG pin |\n|---|---|---|---|---|---|---|---|---|---|\n| RAA489800 | Yes | 5% | ALERT# | No | Yes | Yes | No | Yes | Yes |\n| RAA489801 | Yes | 5% | PROCHOT# | Yes | No | Yes | Yes | No | Yes |\n| ISL95338 | No | N/A | PROCHOT# | Yes | No | No | Yes | No | No |", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "23V", "min_input_voltage": "3.8V", "max_output_voltage": "21V", "min_output_voltage": "2V", "max_output_current": "6.08A", "max_switch_frequency": "1MHz", "quiescent_current": "120µA", "high_side_mosfet_resistance": "不适用(外部MOS)", "low_side_mosfet_resistance": "不适用(外部MOS)", "over_current_protection_threshold": "Programmable up to 6.08A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "SMBus, I2C", "enable_function": "Yes", "light_load_mode": "Diode Emulation Mode (DEM)", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "True", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "未找到", "loop_control_mode": "R3 (<PERSON>ust Ripple Regulator)"}, "package": [{"pitch": "0.5", "height": "0.75", "length": "32.4", "width": "2.7", "type": "Outline", "pin_count": "2"}]}