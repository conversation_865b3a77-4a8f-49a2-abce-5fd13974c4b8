{"part_number": "TPS55285", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "升降压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "TPS55285 具有 I2C 接口的 22V、8A 降压/升压转换器", "features": ["可编程电源 (PPS) 支持 USB 供电 (USB PD)", "宽输入电压范围: 2.4V 至 22V", "启动时的最小输入电压为 3.0V", "可编程输出电压范围: 0.8V 至 22V, 步长为 10mV", "±1% 基准电压精度", "针对电缆上压降的可调输出电压补偿", "可编程输出电流限值高达 6.35A, 步长为 50mA", "在整个负载范围内具有高效率 (VIN = 20V、VOUT = 5V 且 IOUT = 5A 时效率为 92.0%; VIN = 12V、VOUT = 20V 且 IOUT = 3A 时效率为 96.0%)", "I2C 编程 (输出使能 (OE) 开/关, 输出电压变化的转换率, 开关频率: 400kHz、800kHz、1.6MHz、2.2MHz, 轻负载状态下的可编程 PFM 和 FPWM 模式, 展频启用/禁用, 输出放电启用/禁用)", "丰富的保护特性 (输入过压保护, 输出绝对过压保护, 输出相对过压保护, 利用断续模式实现输出短路保护, 热关断保护, 8A 平均电感器电流限值)", "小尺寸解决方案 (四个低 RDS(on) 内部 MOSFET, 开关频率高达 2.2MHz (最大值), 2.5mm x 3.5mm HotRod™ WQFN 封装)"], "description": "TPS55285 完全集成式同步降压/升压转换器经优化，可将电池电压、USB 电源传输 (USB PD) 或适配器电压转换为电源轨。TPS55285 集成了四个 15mΩ MOSFET，以提供高效率和小尺寸解决方案。TPS55285 具有 2.4V (3.0V 上升) 至 22V 的宽输入电压范围，并且能够输出 0.8V 至 22V 电压 (步长为 10mV)，以支持多种应用。它具有 8A 平均电感器电流限制，并可在降压模式下提供高达 7A 的输出电流。在升压模式下，输入电压为 12V 时可提供 60W 的功率，输入电压为 5V 时可提供 30W 的功率。通过 I²C 接口，可以动态地对 TPS55285 的输出电压进行编程。启用器件时，默认输出电压为 5V。I²C 接口允许配置输出电压变化的转换率、开关频率，强制 PWM 模式运行。TPS55285 提供输入和输出过压保护、平均电感器电流限值、逐周期峰值电流限值和输出短路保护。TPS55285 还确保了在持续过载情况下，可通过输出电流限制安全运行，而无需外部输出电流检测电阻器和断续模式保护。TPS55285 可以使用具有高开关频率的小型电感器和电容器。它采用 2.5mm × 3.5mm QFN 封装。", "applications": ["USB PD", "无线充电器", "扩展坞", "笔记本电脑", "固态硬盘"], "ordering_information": [{"part_number": "TPS55285", "order_device": "XTPS55285VALR", "package_type": "WQFN-HR", "package_drawing_code": "未找到", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS55285", "order_device": "XTPS55285VALR.A", "package_type": "WQFN-HR", "package_drawing_code": "未找到", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS55285", "package_type": "WQFN-HR", "pins": [{"pin_number": "1", "pin_name": "MODE", "pin_description": "通过在该引脚和 AGND 之间放置一个电阻器来选择 TPS55285 默认输出使能 (OE) 位。"}, {"pin_number": "2", "pin_name": "SCL", "pin_description": "I2C 接口的时钟"}, {"pin_number": "3", "pin_name": "SDA", "pin_description": "I2C 接口的数据"}, {"pin_number": "4", "pin_name": "EN/UVLO", "pin_description": "启用逻辑输入和可编程输入电压欠压锁定 (UVLO) 输入。逻辑高电平可启用器件。逻辑低电平可禁用器件并将其转换为关断模式。EN/UVLO 引脚上的电压高于 1.125V 的逻辑高电平电压后，该引脚可充当可编程 UVLO 输入，具有 1.23V 的内部基准电压。"}, {"pin_number": "5", "pin_name": "VIN", "pin_description": "降压/升压转换器的输入"}, {"pin_number": "6", "pin_name": "SW1", "pin_description": "降压侧的开关节点引脚。它连接到内部降压低侧功率 MOSFET 的漏极，以及内部降压高侧功率 MOSFET 的源极。"}, {"pin_number": "7", "pin_name": "PGND", "pin_description": "器件的电源接地"}, {"pin_number": "8", "pin_name": "SW2", "pin_description": "升压侧的开关节点引脚。它连接到内部升压低侧功率 MOSFET 的漏极，以及内部升压高侧功率 MOSFET 的源极。"}, {"pin_number": "9", "pin_name": "VOUT", "pin_description": "降压/升压转换器的输出"}, {"pin_number": "10", "pin_name": "FB/INT", "pin_description": "当器件设置为使用外部输出电压反馈时，连接到电阻分压器的中心抽头以对输出电压进行编程。当器件设置为使用内部反馈时，该引脚是故障指示灯开漏输出。当发生内部故障时，该引脚输出逻辑低电平。"}, {"pin_number": "11", "pin_name": "COMP", "pin_description": "内部误差放大器的输出。在该引脚和 AGND 引脚之间连接环路补偿网络。"}, {"pin_number": "12", "pin_name": "AGND", "pin_description": "器件的信号接地。"}, {"pin_number": "13", "pin_name": "VCC", "pin_description": "内部稳压器的输出。在此引脚和 AGND 引脚之间需要一个大于 4.7μF 的陶瓷电容器。"}, {"pin_number": "14", "pin_name": "BOOT2", "pin_description": "升压侧高侧 MOSFET 栅极驱动器的电源。必须在此引脚和 SW2 引脚之间连接一个 0.1μF 的陶瓷电容器。"}, {"pin_number": "15", "pin_name": "BOOT1", "pin_description": "降压侧高侧 MOSFET 栅极驱动器的电源。必须在此引脚和 SW1 引脚之间连接一个 0.1μF 的陶瓷电容器。"}]}], "datasheet_cn": "TPS55285", "datasheet_en": "SLVS172", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "22V", "min_input_voltage": "2.4V", "max_output_voltage": "22V", "min_output_voltage": "1V", "max_output_current": "7A", "max_switch_frequency": "2.2MHz", "quiescent_current": "770µA", "high_side_mosfet_resistance": "14.5mΩ", "low_side_mosfet_resistance": "15.5mΩ", "over_current_protection_threshold": "8A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "1.129V", "loop_control_mode": "平均电流模式"}, "package": [{"length": "2.5", "width": "3.5", "type": "OPTION", "pin_count": "10", "pitch": "3.5", "height": "3.5"}]}