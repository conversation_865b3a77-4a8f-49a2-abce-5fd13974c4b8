{"part_number": "TPS63802", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "采用DFN封装的 TPS63802 2A、高效率、低 IQ 降压/升压转换器", "features": ["输入电压范围: 1.3V 至 5.5V", "器件启动时输入电压大于 1.8V", "输出电压范围: 1.8V 至 5.2V (可调节)", "V₁ ≥ 2.3V、Vo = 3.3V 时, 输出电流为 2A", "在整个负载范围内具有高效率", "11µA 工作静态电流", "具有省电模式和强制 PWM 模式", "峰值电流降压/升压模式架构", "可在降压、降压/升压和升压操作模式之间定义切换点", "正向和反向电流运行", "启动至预偏置输出", "安全、可靠运行的特性", "集成软启动", "过热和过压保护", "带负载断开功能的真正关断功能", "正向和反向电流限制", "21.5mm² 的小解决方案尺寸", "小型 SON/DFN 封装 (类似于 QFN)", "小型 0.47µH 电感器", "与 22µF 最小输出电容器配合使用", "使用 TPS63802 并借助 WEBENCH® Power Designer 创建定制设计方案"], "description": "TPS63802 是一款高效率、高输出电流降压/升压转换器。根据输入电压不同，当输入电压近似等于输出电压时，它会自动以升压、降压或全新的 4 周期降压/升压模式运行。在定义的阈值内进行模式切换，避免不必要的模式内切换，以减少输出电压纹波。这类器件的输出电压可在较宽输出电压范围内通过电阻式分压器进行单独调整。静态电流为 11µA，可在超小甚至空载条件下实现出色效率。", "applications": ["系统前置稳压器（跟踪和远程信息处理、便携式 POS、家庭自动化、IP 网络摄像头）", "负载点调节（有线传感器、端口/电缆适配器和加密狗、电子智能锁、物联网）", "蓄电池备用电源（电表、数据集中器、电能质量监测仪）", "热电器件电源（TEC、光纤模块）", "通用电压稳定器和转换器"], "ordering_information": [{"part_number": "TPS63802", "order_device": "TPS63802DLAR", "package_type": "VSON-HR", "package_drawing_code": "DLA0010A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63802", "order_device": "TPS63802DLAR.A", "package_type": "VSON-HR", "package_drawing_code": "DLA0010A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63802", "order_device": "TPS63802DLARG4.A", "package_type": "VSON-HR", "package_drawing_code": "DLA0010A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63802", "order_device": "TPS63802DLAT", "package_type": "VSON-HR", "package_drawing_code": "DLA0010A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63802", "order_device": "TPS63802DLAT.A", "package_type": "VSON-HR", "package_drawing_code": "DLA0010A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63802", "package_type": "VSON-HR (DLA)", "pins": [{"pin_number": "1", "pin_name": "EN", "pin_description": "Device Enable input. Set HIGH to enable and LOW to disable. It must not be left floating."}, {"pin_number": "2", "pin_name": "MODE", "pin_description": "PFM/PWM mode selection. Set LOW for power save mode, set HIGH for forced PWM mode. It must not be left floating."}, {"pin_number": "3", "pin_name": "AGND", "pin_description": "Analog ground"}, {"pin_number": "4", "pin_name": "FB", "pin_description": "Voltage feedback sensing pin"}, {"pin_number": "5", "pin_name": "PG", "pin_description": "Power good indicator, open-drain output"}, {"pin_number": "6", "pin_name": "VOUT", "pin_description": "Power stage output"}, {"pin_number": "7", "pin_name": "L2", "pin_description": "Connection for inductor"}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Power ground"}, {"pin_number": "9", "pin_name": "L1", "pin_description": "Connection for inductor"}, {"pin_number": "10", "pin_name": "VIN", "pin_description": "Supply voltage input"}]}], "datasheet_cn": "ZHCSJ11D (Version D, 2021-01)", "datasheet_en": "SLVSEU9", "family_comparison": "与TPS63805相比，封装为VSON而非WCSP。与TPS63810/TPS63811相比，输出电压可调而非固定或I2C可编程，静态电流更低(11µA vs 15µA)，开关电流限制更低(4A vs 5.2A)。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.3V", "max_output_voltage": "5.2V", "min_output_voltage": "1.8V", "max_output_current": "2A", "max_switch_frequency": "2.1MHz", "quiescent_current": "11µA", "high_side_mosfet_resistance": "47mΩ", "low_side_mosfet_resistance": "30mΩ", "over_current_protection_threshold": "4A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "True", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.5V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "Outline", "pitch": "0.5", "height": "8.5", "length": "3", "width": "2", "pin_count": "1"}]}