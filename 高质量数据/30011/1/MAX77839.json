{"part_number": "MAX77839", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "5.5V Input, 4.4A/3.6A Switching Current 6μA IQ Buck-Boost Converter", "features": ["Flexible System Integration", "1.8V to 5.5V Input Voltage Range", "2.3V to 5.3V Single Resistor Adjustable Output", "3A Maximum Output Current", "4.4A ILIM, See TOC 24 (\"A\" and \"B\" Options)", "3.6A ILIM, See TOC 23 (\"C\" and \"D\" Options)", "96% Peak Efficiency (VIN = 3.6V, VOUT = 3.3V)", "Optional GPIO Pin (FPWM Input, POK Output)", "Low Supply Current that Extends Battery Life", "Skip Mode that Reduces Supply Current at Light Loads", "6μA Ultra-Low IQ", "2.2MHz (typ) Switching Frequency", "Integrated Protections that Provide System Robustness", "Undervoltage Lockout (UVLO)", "Overvoltage Protection (OVP)", "Cycle-by-Cycle Inductor Peak Current Limit", "Thermal Shutdown (TSHDN)", "Active Output Discharge", "Small Solution Size", "2.07mm x 1.51mm, 0.4mm Pitch, 15-Bump WLP", "2.5mm x 2.0mm, 0.5mm Pitch, 11-Pin FC2QFN"], "description": "The MAX77839 is a highly-efficient buck-boost regulator with an industry leading quiescent current of 6µA targeted for one-cell Li-ion and down to any battery chemistry with 1.8V discharge voltage. It supports input voltages of 1.8V to 5.5V and an output voltage range of 2.3V to 5.3V. The IC provides two different switching current limits to optimize external component sizing based on given load current requirements (\"A\" and \"B\" = 4.4A (typ), \"C\" and \"D\" = 3.6A (typ)), and two different GPIO pin configurations (\"A\" and \"C\" = FPWM pin,”B” and “D” = POK pin). These options provide design flexibility that allow the IC to cover a wide range of applications and use cases while minimizing board space.\nThe IC features a single-resistor adjustable output voltage from 2.3V to 5.3V. A configurable GPIO pin allows to select either a FPWM mode control input or a POK open drain output, depending on the system requirements. Maxim's unique buck-boost controller technology provides high efficiency, excellent load and line transient response, and a seamless transition between buck and boost modes of operation.\nThe MAX77839 is available in both a 2.07mm x 1.51mm, 15-bump wafer-level package (WLP), and a 2.5mm x 2.0mm, 11-lead FC2QFN package.", "applications": ["Asset Tracking/Fleet Management", "5G/2G/GSM Cellular Power", "RF Amplifier", "Smartphones ToF/Facial and Gesture Recognition", "System Power Pre-Regulation", "Single-Cell Li-ion Battery Powered Devices"], "ordering_information": [{"part_number": "MAX77839", "order_device": "MAX77839AEWL+T", "package_type": "WLP", "package_drawing_code": "21-100441", "pin_count": "15", "length": "2.07", "width": "1.51", "pitch": "0.4", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77839", "order_device": "MAX77839AEFQ+T", "package_type": "FC2QFN", "package_drawing_code": "21-100431", "pin_count": "11", "length": "2.5", "width": "2.0", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77839", "order_device": "MAX77839BEWL+T", "package_type": "WLP", "package_drawing_code": "21-100441", "pin_count": "15", "length": "2.07", "width": "1.51", "pitch": "0.4", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77839", "order_device": "MAX77839BEFQ+T", "package_type": "FC2QFN", "package_drawing_code": "21-100431", "pin_count": "11", "length": "2.5", "width": "2.0", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77839", "order_device": "MAX77839CEWL+T", "package_type": "WLP", "package_drawing_code": "21-100441", "pin_count": "15", "length": "2.07", "width": "1.51", "pitch": "0.4", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77839", "order_device": "MAX77839CEFQ+T", "package_type": "FC2QFN", "package_drawing_code": "21-100431", "pin_count": "11", "length": "2.5", "width": "2.0", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77839", "order_device": "MAX77839DEWL+T", "package_type": "WLP", "package_drawing_code": "21-100441", "pin_count": "15", "length": "2.07", "width": "1.51", "pitch": "0.4", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77839", "order_device": "MAX77839DEFQ+T", "package_type": "FC2QFN", "package_drawing_code": "21-100431", "pin_count": "11", "length": "2.5", "width": "2.0", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MAX77839", "package_type": "WLP", "pins": [{"pin_number": "A5, B5", "pin_name": "IN", "pin_description": "Buck-Boost Input. Bypass to PGND with two 16V 10µF X7R ceramic capacitors."}, {"pin_number": "C5", "pin_name": "BIAS", "pin_description": "Internal Bias Supply. Bypass to AGND with a 10V 2.2µF X7R ceramic capacitor. Do not load this pin externally."}, {"pin_number": "A4, B4", "pin_name": "LX1", "pin_description": "Input-Side Buck-Boost Switching Node"}, {"pin_number": "C4", "pin_name": "GPIO", "pin_description": "GPIO pin. For A, C options, forced-PWM mode digital input. Apply high for FPWM mode, apply low for auto skip mode operation. For B, D options, POK open drain output. Pull up with 15kΩ resistor to IO voltage."}, {"pin_number": "A3, B3", "pin_name": "PGND", "pin_description": "Power Ground"}, {"pin_number": "C3", "pin_name": "SEL", "pin_description": "Output Voltage Selection Input. Connect a resistor between this pin and ground to configure the output voltage. Consult Output Voltage Configuration for a table of recommended resistors and corresponding output voltages."}, {"pin_number": "A2, B2", "pin_name": "LX2", "pin_description": "Output-Side Buck-<PERSON>ost Switching Node"}, {"pin_number": "C2", "pin_name": "AGND", "pin_description": "Analog Ground"}, {"pin_number": "A1", "pin_name": "OUT", "pin_description": "Buck-Boost Power Output. Bypass to PGND with two 16V X7R 10µF ceramic capacitors."}, {"pin_number": "B1", "pin_name": "EN", "pin_description": "Buck-Boost Enable Input"}, {"pin_number": "C1", "pin_name": "OUTS", "pin_description": "Buck-Boost Output Voltage Sense Input. Connect to the output at the point-of-load."}]}, {"product_part_number": "MAX77839", "package_type": "FC2QFN", "pins": [{"pin_number": "11", "pin_name": "IN", "pin_description": "Buck-Boost Input. Bypass to PGND with two 16V 10µF X7R ceramic capacitors."}, {"pin_number": "10", "pin_name": "BIAS", "pin_description": "Internal Bias Supply. Bypass to AGND with a 10V 2.2µF X7R ceramic capacitor. Do not load this pin externally."}, {"pin_number": "1", "pin_name": "LX1", "pin_description": "Input-Side Buck-Boost Switching Node"}, {"pin_number": "9", "pin_name": "GPIO", "pin_description": "GPIO pin. For A, C options, forced-PWM mode digital input. Apply high for FPWM mode, apply low for auto skip mode operation. For B, D options, POK open drain output. Pull up with 15kΩ resistor to IO voltage."}, {"pin_number": "2", "pin_name": "PGND", "pin_description": "Power Ground"}, {"pin_number": "8", "pin_name": "SEL", "pin_description": "Output Voltage Selection Input. Connect a resistor between this pin and ground to configure the output voltage. Consult Output Voltage Configuration for a table of recommended resistors and corresponding output voltages."}, {"pin_number": "3", "pin_name": "LX2", "pin_description": "Output-Side Buck-<PERSON>ost Switching Node"}, {"pin_number": "7", "pin_name": "AGND", "pin_description": "Analog Ground"}, {"pin_number": "4", "pin_name": "OUT", "pin_description": "Buck-Boost Power Output. Bypass to PGND with two 16V X7R 10µF ceramic capacitors."}, {"pin_number": "5", "pin_name": "EN", "pin_description": "Buck-Boost Enable Input"}, {"pin_number": "6", "pin_name": "OUTS", "pin_description": "Buck-Boost Output Voltage Sense Input. Connect to the output at the point-of-load."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77839 Datasheet", "family_comparison": "The device has four options based on current limit and GPIO function: Options A and B have a 4.4A typical current limit (ILIM). Options C and D have a 3.6A typical current limit. Options A and C configure the GPIO pin as a Forced-PWM (FPWM) input. Options B and D configure the GPIO pin as a Power-OK (POK) output.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.3V", "min_output_voltage": "2.3V", "max_output_current": "3A", "max_switch_frequency": "2.464MHz", "quiescent_current": "6µA", "high_side_mosfet_resistance": "50mΩ", "low_side_mosfet_resistance": "58mΩ", "over_current_protection_threshold": "4.4A (A/B options), 3.6A (C/D options)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Skip Mode", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "未找到", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "length": "2.07", "width": "1.51", "type": "Information", "pin_count": "3", "height": "1.8"}]}