{"part_number": "ADPL76030", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "55 V, 1.4 MHz Synchronous 4-Switch Buck-Boost Controller with Spread Spectrum", "features": ["4-Switch Single Inductor Architecture Allows VIN Above, Below, or Equal to VOUT", "Up to 95% Efficiency at 1.4 MHz", "Proprietary Peak-Buck, Peak-Boost Current-Mode", "Wide VIN Range: 4 V to 55 V", "±2% Output Voltage Accuracy: 1 V ≤ VOUT ≤ 55 V", "±3% Input or Output Current Accuracy with Monitor", "Spread Spectrum Frequency Modulation for Low EMI", "No Top MOSFET Refresh Noise in Buck or Boost", "Adjustable and Synchronizable: 600 kHz to 1.4 MHz", "VOUT Disconnected from VIN During Shutdown"], "description": "The ADPL76030 is a synchronous 4-switch buck-boost DC/DC controller that regulates output voltage, and input or output current from an input voltage above, below, or equal to the output voltage. The proprietary peak-buck, peak-boost current-mode control scheme allows adjustable and synchronizable 600 kHz to 1.4 MHz fixed frequency operation, or internal 25% triangle spread spectrum frequency modulation for low EMI. With a 4 V to 55 V input voltage range, 0 V to 55 V output voltage capability, and seamless low noise transitions between operation regions, the ADPL76030 is ideal for voltage regulators, and battery and supercapacitor charger applications in industrial, telecom, and even battery-powered systems.\nThe ADPL76030 provides input or output current monitor and power good flag. Fault protection is also provided to detect output short-circuit condition, during which the ADPL76030 retries, latches off, or keeps running.", "applications": ["Industrial, Telecom Systems", "High Frequency, Battery-Powered System"], "ordering_information": [{"part_number": "ADPL76030", "order_device": "ADPL76030EUFD#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1712 Rev C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ADPL76030", "order_device": "ADPL76030EUFD#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1712 Rev C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "ADPL76030", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "TG1", "pin_description": "Buck Side Top Gate Drive. Drives the gate of buck side top N-channel MOSFET with a voltage swing from SW1 to BST1."}, {"pin_number": "2", "pin_name": "LSP", "pin_description": "Positive Terminal of the Buck Side Inductor Current-Sense Resistor (RSENSE). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "3", "pin_name": "LSN", "pin_description": "Negative Terminal of the Buck Side Inductor Current-Sense Resistor (RSENSE). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "4", "pin_name": "VIN", "pin_description": "Input Supply. The VIN pin must be tied to the power input to determine the buck, buck-boost, or boost operation regions. Locally bypass this pin to ground with a minimum 1 µF ceramic capacitor."}, {"pin_number": "5", "pin_name": "INTVCC", "pin_description": "Internal 5 V Linear Regulator Output. The INTVcc linear regulator is supplied from the VIN pin, and powers the internal control circuitry and gate drivers. Locally bypass this pin to ground with a minimum 4.7 µF ceramic capacitor."}, {"pin_number": "6", "pin_name": "EN/UVLO", "pin_description": "Enable and Undervoltage Lockout. Force the pin below 0.3 V to shut down the part and reduce Vin quiescent current below 2 μA. Force the pin above 1.233 V for normal operation. The accurate 1.220 V falling threshold can be used to program an undervoltage lockout (UVLO) threshold with a resistor divider from VIN to ground. An accurate 2.5 μA pull-down current allows the programming of VIN UVLO hysteresis. If neither function is used, tie this pin directly to VIN."}, {"pin_number": "7", "pin_name": "TEST1", "pin_description": "Factory Test Pin. This pin is used for testing purpose only and must be directly connected to ground for the part to operate properly."}, {"pin_number": "8", "pin_name": "TEST2", "pin_description": "Factory Test Pin. This pin is used for testing purposes only and must be tied to VREF for the part to operate properly."}, {"pin_number": "9", "pin_name": "VREF", "pin_description": "Voltage Reference Output. The VREF pin provides an accurate 2 V reference capable of supplying 1 mA current. Locally bypass this pin to ground with a 0.47 µF ceramic capacitor."}, {"pin_number": "10", "pin_name": "CTRL", "pin_description": "Control Input for ISP/ISN Current-Sense Threshold. The CTRL pin is used to program the ISP/ISN current limit."}, {"pin_number": "11", "pin_name": "ISP", "pin_description": "Positive Terminal of the ISP/ISN Current-Sense Resistor (RIS). Ensure accurate current sense with Kelvin connection."}, {"pin_number": "12", "pin_name": "ISN", "pin_description": "Negative Terminal of the ISP/ISN Current-Sense Resistor (RIS). Ensure accurate current sense with Kelvin connection."}, {"pin_number": "13", "pin_name": "ISMON", "pin_description": "ISP/ISN Current-Sense Monitor Output. The ISMON pin generates a voltage equal to ten times V(ISP-ISN) plus 0.25 V offset voltage."}, {"pin_number": "14", "pin_name": "PGOOD", "pin_description": "Power Good Open Drain Output. The PGOOD pin is pulled low when the FB pin is within ±10% of the final regulation voltage. To function, the pin requires an external pull-up resistor."}, {"pin_number": "15", "pin_name": "SS", "pin_description": "Soft-Start Timer Setting. The SS pin is used to set the soft-start timer by connecting a capacitor to ground. It is also used to set fault protection modes."}, {"pin_number": "16", "pin_name": "FB", "pin_description": "Voltage Loop Feedback Input. The FB pin is used for constant-voltage regulation and output fault protection."}, {"pin_number": "17", "pin_name": "Vc", "pin_description": "Error Amplifier Output to Set Inductor Current Comparator Threshold. The Vc pin is used to compensate the control loop with an external RC network."}, {"pin_number": "18", "pin_name": "RT", "pin_description": "Switching Frequency Setting. Connect a resistor from this pin to ground to set the internal oscillator frequency from 600 kHz to 1.4 MHz."}, {"pin_number": "19", "pin_name": "SYNC/SPRD", "pin_description": "Switching Frequency Synchronization or Spread Spectrum. Ground this pin for switching at internal oscillator frequency. Apply a clock signal for external frequency synchronization. Tie to INTVCC for 25% triangle spread spectrum."}, {"pin_number": "20", "pin_name": "DNC", "pin_description": "Do not Connect. This pin must be left floating for the part to operate properly."}, {"pin_number": "21", "pin_name": "VOUT", "pin_description": "Output Supply. The VOUT pin must be tied to the power output to determine the buck, buck-boost, or boost operation regions. Locally bypass this pin to ground with a minimum 1 µF ceramic capacitor."}, {"pin_number": "22", "pin_name": "TG2", "pin_description": "Boost Side Top Gate Drive. Drives the gate of boost side top N-Channel MOSFET with a voltage swing from SW2 to BST2."}, {"pin_number": "23", "pin_name": "SW2", "pin_description": "Boost Side Switch Node. The SW2 pin swings from a <PERSON>hottky diode voltage drop below ground to VOUT."}, {"pin_number": "24", "pin_name": "BST2", "pin_description": "Boost Side Bootstrap Floating Driver Supply. The BST2 pin has an integrated bootstrap Schottky diode from the INTVcc pin and requires an external bootstrap capacitor to the SW2 pin."}, {"pin_number": "25", "pin_name": "BG2", "pin_description": "Boost Side Bottom Gate Drive. Drives the gate of boost side bottom N-channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "26", "pin_name": "BG1", "pin_description": "Buck Side Bottom Gate Drive. Drives the gate of buck side bottom N-channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "27", "pin_name": "BST1", "pin_description": "Buck Side Bootstrap Floating Driver Supply. The BST1 pin has an integrated bootstrap Schottky diode from the INTVcc pin and requires an external bootstrap capacitor to the SW1 pin."}, {"pin_number": "28", "pin_name": "SW1", "pin_description": "Buck Side Switch Node. The SW1 pin swings from a Schottky diode voltage drop below ground up to VIN."}, {"pin_number": "29", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Solder the exposed pad directly to the ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "ADPL76030.pdf", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "55V", "min_input_voltage": "4V", "max_output_voltage": "55V", "min_output_voltage": "1V", "max_output_current": "4A", "max_switch_frequency": "1.4MHz", "quiescent_current": "2100µA", "high_side_mosfet_resistance": "不适用(外部器件)", "low_side_mosfet_resistance": "不适用(外部器件)", "over_current_protection_threshold": "100mV", "operation_mode": "同步", "pass_through_mode": "True", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM, PSM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "1V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.50", "height": "0.75", "length": "5.00", "width": "4.00", "type": "DESCRIPTION", "pin_count": "1"}]}