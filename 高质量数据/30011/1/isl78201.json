{"part_number": "ISL78201", "manufacturer": "Renesas", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压-升压芯片(<PERSON><PERSON><PERSON><PERSON>)", "part_number_title": "40V 2.5A Regulator with Integrated High-Side MOSFET for Synchronous Buck or Boost Buck Converter", "features": ["Buck mode: input voltage range 3V to 40V", "Boost mode expands operating input voltage lower than 2.5V", "Selectable forced PWM mode or PFM mode", "300µA IC quiescent current (PFM, no load); 180µA input quiescent current (PFM, no load, VOUT tied to AUXVCC)", "Less than 5μA (MAX) shutdown input current (IC disabled)", "Operational topologies: Synchronous buck, Non-synchronous buck, Two-stage boost buck, Noninverting single inductor buck boost", "Programmable frequency from 200kHz to 2.2MHz and frequency synchronization capability", "±1% Tight voltage regulation accuracy", "Reliable cycle-by-cycle overcurrent protection: Temperature compensated current sense, Programmable OC limit, Frequency foldback and hiccup mode protection", "20 Ld HTSSOP package", "AEC-Q100 qualified", "Pb-free (RoHS compliant)"], "description": "The ISL78201 is an AEC-Q100 qualified 40V, 2.5A synchronous buck or boost buck controller with a high-side MOSFET and low-side driver integrated. In Buck mode, the ISL78201 supports a wide input range of 3V to 40V. In Boost-Buck mode, the input range can be extended down to 2.5V and output regulation can be maintained when Vin drops below VOUT, enabling sensitive electronics to remain on during cold-cranking and start-stop applications. The ISL78201 has a flexible selection of operation modes including forced PWM mode and an optional switch to PFM mode for light loads. In PFM mode, the quiescent input current is as low as 300µA and can be further reduced to 180μA with AUXVCC connected to Vout under 12V VIN and 5V VOUT application. The load boundary between PFM and PWM can be programmed to cover wide applications. The low-side driver can be either used to drive an external low-side MOSFET for a synchronous buck, or left unused for a standard non-synchronous buck. The low-side driver can also be used to drive a boost converter as a preregulator that greatly expands the operating input voltage range down to 2.5V or lower. The ISL78201 offers the most robust current protections. It uses peak current mode control with cycle-by-cycle current limiting. It is implemented with frequency foldback undercurrent limit condition; in addition, the hiccup overcurrent mode is also implemented to guarantee reliable operations under harsh short conditions. The ISL78201 has comprehensive protections against various faults including overvoltage and over-temperature protections, etc.", "applications": ["Automotive applications", "General purpose power regulator", "24V Bus power", "Battery power", "Embedded processor and I/O supplies"], "ordering_information": [{"part_number": "ISL78201", "order_device": "ISL78201AVEZ", "package_type": "HTSSOP", "package_drawing_code": "M20.173A", "marking": "78201 AVEZ", "min_operation_temp": "-40", "max_operation_temp": "105", "output_voltage": "Adjustable"}, {"part_number": "ISL78201", "order_device": "ISL78201AVEZ-T", "package_type": "HTSSOP", "package_drawing_code": "M20.173A", "carrier_description": "Ta<PERSON> and Reel", "carrier_quantity": "2500", "marking": "78201 AVEZ", "min_operation_temp": "-40", "max_operation_temp": "105", "output_voltage": "Adjustable"}, {"part_number": "ISL78201", "order_device": "ISL78201AVEZ-T7A", "package_type": "HTSSOP", "package_drawing_code": "M20.173A", "carrier_description": "Ta<PERSON> and Reel", "carrier_quantity": "250", "marking": "78201 AVEZ", "min_operation_temp": "-40", "max_operation_temp": "105", "output_voltage": "Adjustable"}], "pin_function": [{"product_part_number": "ISL78201", "package_type": "HTSSOP", "pins": [{"pin_number": "1", "pin_name": "PGND", "pin_description": "This pin is used as the ground connection of the power flow including driver."}, {"pin_number": "2", "pin_name": "BOOT", "pin_description": "This pin provides bias voltage to the high-side MOSFET driver. A bootstrap circuit is used to create a voltage suitable to drive the internal N-channel MOSFET. The boot charge circuitries are integrated inside the IC. No external boot diode is needed. A 1μF ceramic capacitor is recommended to be used between BOOT and PHASE pin."}, {"pin_number": "3, 4", "pin_name": "VIN", "pin_description": "Connect the input rail to these pins that are connected to the drain of the integrated high-side MOSFET, as well as the source for the internal linear regulator that provides the bias of the IC. Range: 3V to 40V."}, {"pin_number": "5", "pin_name": "SGND", "pin_description": "This pin provides the return path for the control and monitor portions of the IC."}, {"pin_number": "6", "pin_name": "VCC", "pin_description": "This pin is the output of the internal linear regulator that supplies the bias for the IC including the driver. A minimum 4.7µF decoupling ceramic capacitor is recommended between VCC to ground."}, {"pin_number": "7", "pin_name": "AUXVCC", "pin_description": "This pin is the input of the auxiliary internal linear regulator, which can be supplied by the regulator output after power-up. With such a configuration, the power dissipation inside the IC is reduced. The input range for this LDO is 3V to 20V. In Boost mode operation, this pin works as boost output overvoltage detection pin."}, {"pin_number": "8", "pin_name": "EN", "pin_description": "The controller is enabled when this pin is pulled HIGH or left floating. The IC is disabled when this pin is pulled LOW. Range: 0V to 5.5V."}, {"pin_number": "9", "pin_name": "FS", "pin_description": "To connect this pin to VCC, or GND, or left open will force the IC to have 500kHz switching frequency. The oscillator switching frequency can also be programmed by adjusting the resistor from this pin to GND."}, {"pin_number": "10", "pin_name": "SS", "pin_description": "Connect a capacitor from this pin to ground. This capacitor, along with an internal 5µA current source, sets the soft-start interval of the converter. Also this pin can be used to track a ramp on this pin."}, {"pin_number": "11", "pin_name": "FB", "pin_description": "This pin is the inverting input of the voltage feedback error amplifier. With a properly selected resistor divider connected from VOUT to FB, the output voltage can be set to any voltage between the input rail (reduced by maximum duty cycle and voltage drop) and the 0.8V reference. Loop compensation is achieved by connecting an RC network across COMP and FB. The FB pin is also monitored for overvoltage events."}, {"pin_number": "12", "pin_name": "COMP", "pin_description": "Output of the voltage feedback error amplifier."}, {"pin_number": "13", "pin_name": "ILIMIT", "pin_description": "Programmable current limit pin. With this pin connected to the VCC pin, or to GND, or left open, the current limit threshold is set to default 3.6A; the current limit threshold can be programmed with a resistor from this pin to GND."}, {"pin_number": "14", "pin_name": "MODE", "pin_description": "Mode selection pin. Pull this pin to GND for forced PWM mode; to have it floating or connected to VCC will enable PFM mode when the peak inductor current is below the default threshold of 700mA. The current boundary threshold between PFM and PWM can also be programmed with a resistor at this pin to ground."}, {"pin_number": "15", "pin_name": "PGOOD", "pin_description": "PGOOD is an open-drain output and pull-up this pin with a resistor to VCC for proper function. PGOOD will be pulled low under the events when the output is out of regulation (OV or UV) or EN pin is pulled low. PGOOD rising has a fixed 128 cycles delay."}, {"pin_number": "16, 17", "pin_name": "PHASE", "pin_description": "These pins are the PHASE nodes that should be connected to the output inductor. These pins are connected to the source of the high-side N channel MOSFET."}, {"pin_number": "18", "pin_name": "EXT_BOOST", "pin_description": "This pin is used to set Boost mode and monitor the battery voltage that is the input of the boost converter. After VCC POR, the controller will detect the voltage on this pin, if voltage on this pin is below 200mV, the controller is set in Synchronous/Non-synchronous Buck mode and latch in this state unless Vcc is below the POR falling threshold; if the voltage on this pin after VCC POR is above 200mV, the controller is set in Boost mode and latch in this state."}, {"pin_number": "19", "pin_name": "SYNC", "pin_description": "This pin can be used to synchronize two or more ISL78201 controllers. Multiple ISL78201s can be synchronized with their SYNC pins connected together. An 180° phase shift is automatically generated between the master and slave ICs. The internal oscillator can also lock to an external frequency source applied on this pin with square pulse waveform. This pin should be left floating if not used. Range: 0V to 5.5V."}, {"pin_number": "20", "pin_name": "LGATE", "pin_description": "In Synchronous Buck mode, this pin is used to drive the lower side MOSFET to improve efficiency. In Non-synchronous Buck mode when a diode is used as the bottom-side power device, this pin should be connected to VCC through a resistor to have low-side driver (LGATE) disabled. In Boost mode, it can be used to drive the boost power MOSFET."}, {"pin_number": "21", "pin_name": "PAD", "pin_description": "Bottom thermal pad. It is not connected to any electrical potential of the IC. In layout it must be connected to PCB ground copper plane with an area as large as possible to effectively reduce the thermal impedance."}]}], "datasheet_cn": "未找到", "datasheet_en": "ISL78201", "family_comparison": "ISL78201: Topology=Bo<PERSON> Buck with Internal High-Side MOSFET, BOOST=Yes, AUXVCC=Yes, PFM=Yes, Package=20 Ld HTSSOP. ISL78206: Topology=Buck with Internal High-Side MOSFET, BOOST=No, AUXVCC=No, PFM=No, Package=20 Ld HTSSOP.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "40V", "min_input_voltage": "3V", "max_output_voltage": "未找到", "min_output_voltage": "1V", "max_output_current": "2.5A", "max_switch_frequency": "2.2MHz", "quiescent_current": "300µA", "high_side_mosfet_resistance": "127mΩ", "low_side_mosfet_resistance": "不适用(外部低边MOSFET)", "over_current_protection_threshold": "3.6A", "operation_mode": "Synchronous", "output_voltage_config_method": "Adjustable", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.8V", "loop_control_mode": "Peak Current Mode"}, "package": [{"pitch": "0.65", "height": "1.2", "length": "6.5", "width": "4.4", "type": "Outline", "pin_count": "11"}]}