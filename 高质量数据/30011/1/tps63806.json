[{"part_number": "TPS63805", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "升降压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "具有小解决方案尺寸的TPS6380x高效率、低IQ降压/升压转换器", "features": ["三个引脚对引脚器件选项:TPS63805、TPS63806和 TPS63807具有特定的应用重点", "输入电压范围:1.3V 至5.5V, 器件启动时输入电压大于 1.8V", "输出电压范围: 1.8V 至5.2V (可调节)", "在整个负载范围内具有高效率, 具有省电模式和强制 PWM 模式", "峰值电流降压/升压模式架构, 可在降压、降压/升压和升压操作模式之间定义切换点", "正向和反向电流运行", "启动至预偏置输出", "安全、可靠运行的特性: 集成软启动, 过热和过压保护, 带负载断开功能的真正关断功能, 正向和反向电流限制", "经优化可实现 18.5 mm² 的最小解决方案尺寸 (与22µF 最小输出电容器配合使用)", "V₁ ≥ 2.3V、Vo = 3.3V时,输出电流为2A", "11µA 工作静态电流"], "description": "TPS63805、TPS63806 和 TPS63807 是高效率、高输出电流降压/升压转换器。根据输入电压不同，当输入电压近似等于输出电压时，它们会自动以升压、降压或全新的 4 周期降压/升压模式运行。在定义的阈值内进行模式切换，避免不必要的模式内切换，以减少输出电压纹波。这类器件的输出电压可在较宽输出电压范围内通过电阻式分压器进行单独调整。TPS63805 和 TPS63807 以很少的物料清单实现了超小的解决方案尺寸。静态电流为 11μA，可在超小甚至空载条件下实现出色效率。TPS63805、TPS63806 和 TPS63807采用 1.4mm × 2.3mm 封装。该器件可与微型无源组件配套使用，从而使整体解决方案尺寸保持小巧。", "applications": ["系统前置稳压器（智能手机、平板电脑、EFT 终端和远程信息处理）", "负载点调节（有线传感器、端口/电缆适配器和加密狗）"], "ordering_information": [{"part_number": "TPS63805", "order_device": "TPS63805YFFR", "package_type": "DSBGA", "package_drawing_code": "YFF0015", "output_voltage": "可调节", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63805", "order_device": "TPS63805YFFT", "package_type": "DSBGA", "package_drawing_code": "YFF0015", "output_voltage": "可调节", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63805", "package_type": "WCSP", "pins": [{"pin_number": "A2, A3", "pin_name": "VIN", "pin_description": "Supply voltage"}, {"pin_number": "B2, B3", "pin_name": "L1", "pin_description": "Connection for inductor"}, {"pin_number": "A1", "pin_name": "EN", "pin_description": "Device Enable input. Set HIGH to enable and LOW to disable. It must not be left floating."}, {"pin_number": "C2, C3", "pin_name": "GND", "pin_description": "Power ground"}, {"pin_number": "B1", "pin_name": "MODE", "pin_description": "PFM/PWM mode selection. Set LOW for power save mode, set HIGH for forced PWM mode. It must not be left floating."}, {"pin_number": "C1", "pin_name": "AGND", "pin_description": "Analog ground"}, {"pin_number": "D2, D3", "pin_name": "L2", "pin_description": "Connection for inductor"}, {"pin_number": "E2, E3", "pin_name": "VOUT", "pin_description": "Power stage output"}, {"pin_number": "D1", "pin_name": "FB", "pin_description": "Voltage feedback sensing pin"}, {"pin_number": "E1", "pin_name": "PG", "pin_description": "Power good indicator, open drain output. If not used can be left floating."}]}], "datasheet_cn": "ZHCSIG5E", "datasheet_en": "SLVSDS9", "family_comparison": "TPS63805/TPS63807与TPS63806在静态电流、最小有效输出电容和负载瞬态响应方面存在差异。TPS63805/TPS63807针对最小解决方案尺寸和低静态电流进行了优化。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.3V", "max_output_voltage": "5.2V", "min_output_voltage": "1.8V", "max_output_current": "2A", "max_switch_frequency": "2.1MHz", "quiescent_current": "11µA", "high_side_mosfet_resistance": "47mΩ", "low_side_mosfet_resistance": "30mΩ", "over_current_protection_threshold": "5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "1%", "output_reference_voltage": "0.5V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.625", "length": "2.285", "width": "1.374", "type": "OPTION", "pin_count": "1"}]}, {"part_number": "TPS63806", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "升降压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "具有小解决方案尺寸的TPS6380x高效率、低IQ降压/升压转换器", "features": ["三个引脚对引脚器件选项:TPS63805、TPS63806和 TPS63807具有特定的应用重点", "输入电压范围:1.3V 至5.5V, 器件启动时输入电压大于 1.8V", "输出电压范围: 1.8V 至5.2V (可调节)", "在整个负载范围内具有高效率, 具有省电模式和强制 PWM 模式", "峰值电流降压/升压模式架构, 可在降压、降压/升压和升压操作模式之间定义切换点", "正向和反向电流运行", "启动至预偏置输出", "安全、可靠运行的特性: 集成软启动, 过热和过压保护, 带负载断开功能的真正关断功能, 正向和反向电流限制", "经优化可实现最佳负载阶跃响应（电流阶跃为 2A 时可实现 180mV 负载阶跃响应）", "高达2.5A的瞬态输出电流", "13µA 工作静态电流"], "description": "TPS63805、TPS63806 和 TPS63807 是高效率、高输出电流降压/升压转换器。根据输入电压不同，当输入电压近似等于输出电压时，它们会自动以升压、降压或全新的 4 周期降压/升压模式运行。在定义的阈值内进行模式切换，避免不必要的模式内切换，以减少输出电压纹波。这类器件的输出电压可在较宽输出电压范围内通过电阻式分压器进行单独调整。TPS63806 针对存在重负载曲线下负载阶跃响应问题的应用进行了优化。TPS63805、TPS63806 和 TPS63807采用 1.4mm × 2.3mm 封装。该器件可与微型无源组件配套使用，从而使整体解决方案尺寸保持小巧。", "applications": ["飞行时间摄像头传感器（智能手机、电子智能锁和IP 网络摄像头）", "宽带网络无线电或 SoC 电源（物联网、跟踪、家居自动化和 EPOS）", "热电器件电源（TEC、光纤模块）", "通用电压稳定器"], "ordering_information": [{"part_number": "TPS63806", "order_device": "TPS63806YFFR", "package_type": "DSBGA", "package_drawing_code": "YFF0015", "output_voltage": "可调节", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63806", "package_type": "WCSP", "pins": [{"pin_number": "A2, A3", "pin_name": "VIN", "pin_description": "Supply voltage"}, {"pin_number": "B2, B3", "pin_name": "L1", "pin_description": "Connection for inductor"}, {"pin_number": "A1", "pin_name": "EN", "pin_description": "Device Enable input. Set HIGH to enable and LOW to disable. It must not be left floating."}, {"pin_number": "C2, C3", "pin_name": "GND", "pin_description": "Power ground"}, {"pin_number": "B1", "pin_name": "MODE", "pin_description": "PFM/PWM mode selection. Set LOW for power save mode, set HIGH for forced PWM mode. It must not be left floating."}, {"pin_number": "C1", "pin_name": "AGND", "pin_description": "Analog ground"}, {"pin_number": "D2, D3", "pin_name": "L2", "pin_description": "Connection for inductor"}, {"pin_number": "E2, E3", "pin_name": "VOUT", "pin_description": "Power stage output"}, {"pin_number": "D1", "pin_name": "FB", "pin_description": "Voltage feedback sensing pin"}, {"pin_number": "E1", "pin_name": "PG", "pin_description": "Power good indicator, open drain output. If not used can be left floating."}]}], "datasheet_cn": "ZHCSIG5E", "datasheet_en": "SLVSDS9", "family_comparison": "TPS63805/TPS63807与TPS63806在静态电流、最小有效输出电容和负载瞬态响应方面存在差异。TPS63806针对最佳负载阶跃响应进行了优化。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.3V", "max_output_voltage": "5.2V", "min_output_voltage": "1.8V", "max_output_current": "2.5A", "max_switch_frequency": "2.1MHz", "quiescent_current": "13µA", "high_side_mosfet_resistance": "47mΩ", "low_side_mosfet_resistance": "30mΩ", "over_current_protection_threshold": "5.5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "1%", "output_reference_voltage": "0.5V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.625", "length": "2.285", "width": "1.374", "type": "OPTION", "pin_count": "1"}]}, {"part_number": "TPS63807", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "升降压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "具有小解决方案尺寸的TPS6380x高效率、低IQ降压/升压转换器", "features": ["三个引脚对引脚器件选项:TPS63805、TPS63806和 TPS63807具有特定的应用重点", "输入电压范围:1.3V 至5.5V, 器件启动时输入电压大于 1.8V", "输出电压范围: 1.8V 至5.2V (可调节)", "在整个负载范围内具有高效率, 具有省电模式和强制 PWM 模式", "峰值电流降压/升压模式架构, 可在降压、降压/升压和升压操作模式之间定义切换点", "正向和反向电流运行", "启动至预偏置输出", "安全、可靠运行的特性: 集成软启动, 过热和过压保护, 带负载断开功能的真正关断功能, 正向和反向电流限制", "经优化可实现 18.5 mm² 的最小解决方案尺寸 (与22µF 最小输出电容器配合使用)", "V₁ ≥ 2.3V、Vo = 3.3V时,输出电流为2A", "11µA 工作静态电流", "EN 为低电平时的输出放电功能", "480µs Tramp,对于启动期间的小浪涌电流"], "description": "TPS63805、TPS63806 和 TPS63807 是高效率、高输出电流降压/升压转换器。根据输入电压不同，当输入电压近似等于输出电压时，它们会自动以升压、降压或全新的 4 周期降压/升压模式运行。在定义的阈值内进行模式切换，避免不必要的模式内切换，以减少输出电压纹波。这类器件的输出电压可在较宽输出电压范围内通过电阻式分压器进行单独调整。TPS63805 和 TPS63807 以很少的物料清单实现了超小的解决方案尺寸。静态电流为 11μA，可在超小甚至空载条件下实现出色效率。TPS63805、TPS63806 和 TPS63807采用 1.4mm × 2.3mm 封装。该器件可与微型无源组件配套使用，从而使整体解决方案尺寸保持小巧。", "applications": ["需要输出放电功能的应用"], "ordering_information": [{"part_number": "TPS63807", "order_device": "TPS63807YFFR", "package_type": "DSBGA", "package_drawing_code": "YFF0015", "output_voltage": "可调节", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63807", "package_type": "WCSP", "pins": [{"pin_number": "A2, A3", "pin_name": "VIN", "pin_description": "Supply voltage"}, {"pin_number": "B2, B3", "pin_name": "L1", "pin_description": "Connection for inductor"}, {"pin_number": "A1", "pin_name": "EN", "pin_description": "Device Enable input. Set HIGH to enable and LOW to disable. It must not be left floating."}, {"pin_number": "C2, C3", "pin_name": "GND", "pin_description": "Power ground"}, {"pin_number": "B1", "pin_name": "MODE", "pin_description": "PFM/PWM mode selection. Set LOW for power save mode, set HIGH for forced PWM mode. It must not be left floating."}, {"pin_number": "C1", "pin_name": "AGND", "pin_description": "Analog ground"}, {"pin_number": "D2, D3", "pin_name": "L2", "pin_description": "Connection for inductor"}, {"pin_number": "E2, E3", "pin_name": "VOUT", "pin_description": "Power stage output"}, {"pin_number": "D1", "pin_name": "FB", "pin_description": "Voltage feedback sensing pin"}, {"pin_number": "E1", "pin_name": "PG", "pin_description": "Power good indicator, open drain output. If not used can be left floating."}]}], "datasheet_cn": "ZHCSIG5E", "datasheet_en": "SLVSDS9", "family_comparison": "TPS63805/TPS63807与TPS63806在静态电流、最小有效输出电容和负载瞬态响应方面存在差异。TPS63805/TPS63807针对最小解决方案尺寸和低静态电流进行了优化。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.3V", "max_output_voltage": "5.2V", "min_output_voltage": "1.8V", "max_output_current": "2A", "max_switch_frequency": "2.1MHz", "quiescent_current": "11µA", "high_side_mosfet_resistance": "47mΩ", "low_side_mosfet_resistance": "30mΩ", "over_current_protection_threshold": "5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "1%", "output_reference_voltage": "0.5V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.625", "length": "2.285", "width": "1.374", "type": "OPTION", "pin_count": "1"}]}]