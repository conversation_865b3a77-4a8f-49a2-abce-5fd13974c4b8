[{"part_number": "TPS63030", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS6303x High Efficiency Single Inductor Buck-Boost Converter With 1-A Switches", "features": ["Input voltage range: 1.8 V to 5.5 V", "Fixed and adjustable output voltage options from 1.2 V to 5.5 V", "Up to 96% efficiency", "800-mA Output current at 3.3 V in step-down mode (VIN = 3.6 V to 5.5 V)", "Up to 500-mA output current at 3.3 V in boost mode (VIN > 2.4 V)", "Automatic transition between step-down and boost mode", "Device quiescent current less than 50 μA", "Power-save mode for improved efficiency at low-output power", "Forced fixed frequency operation and synchronization possible", "Load disconnect during shutdown", "Overtemperature protection", "Available in a small 2.5-mm × 2.5-mm 10-pin VSON package (QFN)"], "description": "The TPS6303x devices provide a power supply solution for products powered by either a two-cell or three-cell alkaline, NiCd or NiMH battery, or a one-cell Li-ion or Li-polymer battery. Output currents can go as high as 600 mA while using a single-cell Li-ion or Li-polymer battery, and discharge it down to 2.5 V or lower. The buck-boost converter is based on a fixed-frequency, pulse width modulation (PWM) controller using synchronous rectification to obtain maximum efficiency. At low-load currents, the converter enters power-save mode to maintain high efficiency over a wide load current range. The power-save mode can be disabled, forcing the converter to operate at a fixed switching frequency. The maximum average current in the switches is limited to a typical value of 1000 mA. The output voltage is programmable using an external resistor divider, or is fixed internally on the chip. The converter can be disabled to minimize battery drain. During shutdown, the load is disconnected from the battery. The TPS6303x devices operate over a free air temperature range of –40°C to 85°C. The devices are packaged in a 10-pin VSON package measuring 2.5-mm x 2.5-mm (DSK).", "applications": ["All two-cell and three-cell alkaline, NiCd or NiMH, or single-cell Li battery powered products", "Smartphone", "Portable media player", "IP network camera", "Blood glucose monitor", "Portable data terminal"], "ordering_information": [{"part_number": "TPS63030", "order_device": "TPS63030DSKR", "package_type": "SON", "package_drawing_code": "DSK0010A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63030", "order_device": "TPS63030DSKT", "package_type": "SON", "package_drawing_code": "DSK0010A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63030", "package_type": "VSON", "pins": [{"pin_number": "6", "pin_name": "EN", "pin_description": "Enable input (1 enabled, 0 disabled)"}, {"pin_number": "10", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT on fixed output voltage versions"}, {"pin_number": "9", "pin_name": "GND", "pin_description": "Control / logic ground"}, {"pin_number": "4", "pin_name": "L1", "pin_description": "Connection for inductor"}, {"pin_number": "2", "pin_name": "L2", "pin_description": "Connection for inductor"}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power ground"}, {"pin_number": "7", "pin_name": "PS/SYNC", "pin_description": "Enable / disable power save mode (1 disabled, 0 enabled, clock signal for synchronization)"}, {"pin_number": "5", "pin_name": "VIN", "pin_description": "Supply voltage for power stage"}, {"pin_number": "8", "pin_name": "VINA", "pin_description": "Supply voltage for control stage"}, {"pin_number": "1", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}, {"pin_number": "Exposed Thermal Pad", "pin_name": "Exposed Thermal Pad", "pin_description": "The exposed thermal pad is connected to PGND."}]}], "datasheet_cn": "未找到", "datasheet_en": "TPS63030, TPS63031 SLVS696D – APRIL 2020", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.5V", "min_output_voltage": "1.2V", "max_output_current": "0.8A", "max_switch_frequency": "2.6MHz", "quiescent_current": "25µA", "high_side_mosfet_resistance": "200mΩ", "low_side_mosfet_resistance": "200mΩ", "over_current_protection_threshold": "1A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.5V", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.5", "height": "0.8", "length": "2.5", "width": "2.5", "type": "OPTION", "pin_count": "10"}]}, {"part_number": "TPS63031", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS6303x High Efficiency Single Inductor Buck-Boost Converter With 1-A Switches", "features": ["Input voltage range: 1.8 V to 5.5 V", "Fixed and adjustable output voltage options from 1.2 V to 5.5 V", "Up to 96% efficiency", "800-mA Output current at 3.3 V in step-down mode (VIN = 3.6 V to 5.5 V)", "Up to 500-mA output current at 3.3 V in boost mode (VIN > 2.4 V)", "Automatic transition between step-down and boost mode", "Device quiescent current less than 50 μA", "Power-save mode for improved efficiency at low-output power", "Forced fixed frequency operation and synchronization possible", "Load disconnect during shutdown", "Overtemperature protection", "Available in a small 2.5-mm × 2.5-mm 10-pin VSON package (QFN)"], "description": "The TPS6303x devices provide a power supply solution for products powered by either a two-cell or three-cell alkaline, NiCd or NiMH battery, or a one-cell Li-ion or Li-polymer battery. Output currents can go as high as 600 mA while using a single-cell Li-ion or Li-polymer battery, and discharge it down to 2.5 V or lower. The buck-boost converter is based on a fixed-frequency, pulse width modulation (PWM) controller using synchronous rectification to obtain maximum efficiency. At low-load currents, the converter enters power-save mode to maintain high efficiency over a wide load current range. The power-save mode can be disabled, forcing the converter to operate at a fixed switching frequency. The maximum average current in the switches is limited to a typical value of 1000 mA. The output voltage is programmable using an external resistor divider, or is fixed internally on the chip. The converter can be disabled to minimize battery drain. During shutdown, the load is disconnected from the battery. The TPS6303x devices operate over a free air temperature range of –40°C to 85°C. The devices are packaged in a 10-pin VSON package measuring 2.5-mm x 2.5-mm (DSK).", "applications": ["All two-cell and three-cell alkaline, NiCd or NiMH, or single-cell Li battery powered products", "Smartphone", "Portable media player", "IP network camera", "Blood glucose monitor", "Portable data terminal"], "ordering_information": [{"part_number": "TPS63031", "order_device": "TPS63031DSKR", "package_type": "SON", "package_drawing_code": "DSK0010A", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63031", "order_device": "TPS63031DSKT", "package_type": "SON", "package_drawing_code": "DSK0010A", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63031", "package_type": "VSON", "pins": [{"pin_number": "6", "pin_name": "EN", "pin_description": "Enable input (1 enabled, 0 disabled)"}, {"pin_number": "10", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT on fixed output voltage versions"}, {"pin_number": "9", "pin_name": "GND", "pin_description": "Control / logic ground"}, {"pin_number": "4", "pin_name": "L1", "pin_description": "Connection for inductor"}, {"pin_number": "2", "pin_name": "L2", "pin_description": "Connection for inductor"}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power ground"}, {"pin_number": "7", "pin_name": "PS/SYNC", "pin_description": "Enable / disable power save mode (1 disabled, 0 enabled, clock signal for synchronization)"}, {"pin_number": "5", "pin_name": "VIN", "pin_description": "Supply voltage for power stage"}, {"pin_number": "8", "pin_name": "VINA", "pin_description": "Supply voltage for control stage"}, {"pin_number": "1", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}, {"pin_number": "Exposed Thermal Pad", "pin_name": "Exposed Thermal Pad", "pin_description": "The exposed thermal pad is connected to PGND."}]}], "datasheet_cn": "未找到", "datasheet_en": "TPS63030, TPS63031 SLVS696D – APRIL 2020", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "3.3V", "min_output_voltage": "3.3V", "max_output_current": "0.8A", "max_switch_frequency": "2.6MHz", "quiescent_current": "25µA", "high_side_mosfet_resistance": "200mΩ", "low_side_mosfet_resistance": "200mΩ", "over_current_protection_threshold": "1A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "不适用", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.5", "height": "0.8", "length": "2.5", "width": "2.5", "type": "OPTION", "pin_count": "10"}]}]