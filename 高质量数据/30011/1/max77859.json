[{"part_number": "MAX77859A", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "2.5V to 22V Input, 7.8A Switching Current High-Efficiency Buck-Boost Converter for USB-PD/PPS Applications", "features": ["Wide Input Voltage Range: 2.5V to 22V", "Programmable Output Voltage: 3.2V to 16V with Internal Feedback Resistors, 3.0V to 20V with External Feedback Resistors", "USB Type-C® Power Delivery (PD)/Programmable Power Supply (PPS): 20mV Output Voltage Step Size, 50mA Output Current Limit Step Size", "Maximum Output Current: Buck Mode: Up to 6A, Boost Mode: Up to 4A (VIN = 3.7V, VOUT = 5V)", "7.8A Typical Switching Current", "Automatic SKIP Mode and Forced-PWM Mode", "RSEL Configuration: I2C Interface Target Address, Switching Current Limit Threshold, Internal/External Feedback Resistors", "I2C Programming: Output Voltage (DVS), Slew Rate of Output Voltage Change, Output Current Limit Threshold, Switching Current Limit Threshold, Switching Frequency, Forced-PWM Mode Operation (FPWM), Loop Compensation, Power-OK (POK) and Fault Status/Interrupts", "Output Active Discharge", "Open-Drain Status/Interrupts Pin", "Available in a 3.01mm x 2.78mm 42 Wafer-Level Packaging (WLP) or 4.0mm x 4.0mm 19 FC2QFN"], "description": "The MAX77859A is a high-efficiency, high-performance buck-boost converter targeted for systems requiring a wide input voltage range (2.5V to 22V). The IC can supply up to 6A of output current in buck mode and up to 4A in boost mode (VIN = 3.7V, VOUT = 5V). The IC allows systems to change the output voltage dynamically through I2C serial interface. MAX77859A features I2C-adjustable output current limit with resolutions of 50mA/step (with 10mΩ sense resistance) to support USB-C PPS requirements. Systems equipped with MAX77859 can provide fast-charging peripheral devices with higher output voltage, minimizing power loss across cable/connector and reducing charging time. The IC operates either in SKIP mode or in forced-PWM (FPWM) mode, depending on the operating conditions, to optimize efficiency. The default output voltage is 5V when using internal feedback resistors. The IC can also be configured to any default output voltages between 3V and 20V when using external feedback resistors. The output voltage is adjustable dynamically (DVS) between 3.2V and 16V in 20mV steps when using internal feedback resistors or between 3V to 20V when using external feedback resistors by programming the internal reference voltage through I2C serial interface. Using a 10mΩ sense resistor, the output current limit threshold is adjustable dynamically between 1A and 5A in 50mA steps through I2C serial interface, with a 3A default value.", "applications": ["USB PD 3.0 (PPS) Dynamically-Reconfigurable Processor (DRP) Applications", "Space-Constrained Applications"], "ordering_information": [{"part_number": "MAX77859A", "order_device": "MAX77859AEWO+T", "package_type": "WLP", "package_drawing_code": "21-100632", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "MAX77859A", "order_device": "MAX77859AEFS+T*", "package_type": "FC2QFN", "package_drawing_code": "未找到", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX77859A", "package_type": "42 WLP", "pins": [{"pin_number": "A1, B1, B2, B3", "pin_name": "LX2", "pin_description": "<PERSON><PERSON><PERSON><PERSON> Switching Node 2."}, {"pin_number": "B5, C1, C2, C3, C4, C5, D1, D2, D3, D4, D5, E5", "pin_name": "PGND", "pin_description": "Power Ground. Connect to AGND on the PCB."}, {"pin_number": "E1, E2, E3, F1", "pin_name": "LX1", "pin_description": "Buck-<PERSON><PERSON> Switching Node 1."}, {"pin_number": "F5", "pin_name": "BST1", "pin_description": "LX1 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST1 and LX1."}, {"pin_number": "E4, F2, F3, F4", "pin_name": "IN", "pin_description": "Buck-Boost Input. Bypass to PGND with two 35V 22µF ceramic capacitors as close as possible."}, {"pin_number": "C7", "pin_name": "VL", "pin_description": "Low-Voltage Internal Supply. Powered from IN. Bypass to AGND with a 10V 2.2µF ceramic capacitor."}, {"pin_number": "D7", "pin_name": "AGND", "pin_description": "Analog Ground. Connect to PGND on the PCB."}, {"pin_number": "F6", "pin_name": "Vio", "pin_description": "IO Voltage Supply. Bypass to AGND with a 6.3V 0.47µF ceramic capacitor."}, {"pin_number": "E7", "pin_name": "SCL", "pin_description": "I2C Serial Interface Clock (High-Z in OFF State). Connect to Vio with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "F7", "pin_name": "SDA", "pin_description": "I2C Serial Interface Data (High-Z in OFF State). Connect to Vio with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "E6", "pin_name": "EN", "pin_description": "Active-High Buck-Boost Enable Input. Compatible with the Vio voltage domain."}, {"pin_number": "A6", "pin_name": "POKB/INTB", "pin_description": "Active-Low Open Drain Status/Interrupts Output. Connect to Vio with a 15kΩ pullup resistor."}, {"pin_number": "D6", "pin_name": "FPWM", "pin_description": "Active-High Forced-PWM Mode Control Input."}, {"pin_number": "C6", "pin_name": "SEL", "pin_description": "Configuration Selection. Connect a resistor between SEL and AGND."}, {"pin_number": "B6", "pin_name": "SRN", "pin_description": "Sense Resistor Negative Input."}, {"pin_number": "B7", "pin_name": "SRP", "pin_description": "Sense Resistor Positive Input."}, {"pin_number": "A7", "pin_name": "FB", "pin_description": "Output Voltage Sense Input (Internal Feedback) or Output Voltage Feedback Input (External Feedback)."}, {"pin_number": "A2, A3, A4, B4", "pin_name": "OUT", "pin_description": "Buck-Boost Output. Bypass to PGND with two 25V 22µF ceramic capacitors as close as possible."}, {"pin_number": "A5", "pin_name": "BST2", "pin_description": "LX2 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST2 and LX2."}]}, {"product_part_number": "MAX77859A", "package_type": "19 FC2QFN", "pins": [{"pin_number": "1", "pin_name": "LX2", "pin_description": "<PERSON><PERSON><PERSON><PERSON> Switching Node 2."}, {"pin_number": "2", "pin_name": "PGND", "pin_description": "Power Ground. Connect to AGND on the PCB."}, {"pin_number": "3", "pin_name": "LX1", "pin_description": "Buck-<PERSON><PERSON> Switching Node 1."}, {"pin_number": "4", "pin_name": "BST1", "pin_description": "LX1 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST1 and LX1."}, {"pin_number": "5", "pin_name": "IN", "pin_description": "Buck-Boost Input. Bypass to PGND with two 35V 22µF ceramic capacitors as close as possible."}, {"pin_number": "6", "pin_name": "VL", "pin_description": "Low-Voltage Internal Supply. Powered from IN. Bypass to AGND with a 10V 2.2µF ceramic capacitor."}, {"pin_number": "7", "pin_name": "AGND", "pin_description": "Analog Ground. Connect to PGND on the PCB."}, {"pin_number": "8", "pin_name": "Vio", "pin_description": "IO Voltage Supply. Bypass to AGND with a 6.3V 0.47µF ceramic capacitor."}, {"pin_number": "9", "pin_name": "SCL", "pin_description": "I2C Serial Interface Clock (High-Z in OFF State). Connect to Vio with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "10", "pin_name": "SDA", "pin_description": "I2C Serial Interface Data (High-Z in OFF State). Connect to Vio with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "11", "pin_name": "EN", "pin_description": "Active-High Buck-Boost Enable Input. Compatible with the Vio voltage domain."}, {"pin_number": "12", "pin_name": "POKB/INTB", "pin_description": "Active-Low Open Drain Status/Interrupts Output. Connect to Vio with a 15kΩ pullup resistor."}, {"pin_number": "13", "pin_name": "FPWM", "pin_description": "Active-High Forced-PWM Mode Control Input."}, {"pin_number": "14", "pin_name": "SEL", "pin_description": "Configuration Selection. Connect a resistor between SEL and AGND."}, {"pin_number": "15", "pin_name": "SRN", "pin_description": "Sense Resistor Negative Input."}, {"pin_number": "16", "pin_name": "SRP", "pin_description": "Sense Resistor Positive Input."}, {"pin_number": "17", "pin_name": "FB", "pin_description": "Output Voltage Sense Input (Internal Feedback) or Output Voltage Feedback Input (External Feedback)."}, {"pin_number": "18", "pin_name": "OUT", "pin_description": "Buck-Boost Output. Bypass to PGND with two 25V 22µF ceramic capacitors as close as possible."}, {"pin_number": "19", "pin_name": "BST2", "pin_description": "LX2 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST2 and LX2."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77859, Rev 1, 05/23", "family_comparison": "MAX77859A supports PPS (Programmable Power Supply) with an adjustable output current limit. MAX77859B is a non-PPS version optimized for low quiescent current. Both are available in 42-pin WLP and 19-pin FC2QFN packages.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "22V", "min_input_voltage": "2.5V", "max_output_voltage": "20V", "min_output_voltage": "3V", "max_output_current": "6A", "max_switch_frequency": "2.1MHz", "quiescent_current": "300µA", "high_side_mosfet_resistance": "20mΩ", "low_side_mosfet_resistance": "20mΩ", "over_current_protection_threshold": "1.2-7.8A(可调)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "SKIP Mode", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "0.30518V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.64", "length": "3.01", "width": "2.78", "type": "Information", "pin_count": "2"}]}, {"part_number": "MAX77859B", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Future product", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "2.5V to 22V Input, 7.8A Switching Current High-Efficiency Buck-Boost Converter for USB-PD/PPS Applications", "features": ["Wide Input Voltage Range: 2.5V to 22V", "Programmable Output Voltage: 3.2V to 16V with Internal Feedback Resistors, 3.0V to 20V with External Feedback Resistors", "Maximum Output Current: Buck Mode: Up to 6A, Boost Mode: Up to 4A (VIN = 3.7V, VOUT = 5V)", "7.8A Typical Switching Current", "Automatic SKIP Mode and Forced-PWM Mode", "RSEL Configuration: I2C Interface Target Address, Switching Current Limit Threshold, Internal/External Feedback Resistors", "I2C Programming: Output Voltage (DVS), Slew Rate of Output Voltage Change, Switching Current Limit Threshold, Switching Frequency, Forced-PWM Mode Operation (FPWM), Loop Compensation, Power-OK (POK) and Fault Status/Interrupts", "Output Active Discharge", "Open-Drain Status/Interrupts Pin", "Available in a 3.01mm x 2.78mm 42 Wafer-Level Packaging (WLP) or 4.0mm x 4.0mm 19 FC2QFN"], "description": "The MAX77859B is a high-efficiency, high-performance buck-boost converter targeted for systems requiring a wide input voltage range (2.5V to 22V). The IC can supply up to 6A of output current in buck mode and up to 4A in boost mode (VIN = 3.7V, VOUT = 5V). The IC allows systems to change the output voltage dynamically through I2C serial interface. MAX77859B is a non-PPS version and is optimized for low quiescent current. Systems equipped with MAX77859 can provide fast-charging peripheral devices with higher output voltage, minimizing power loss across cable/connector and reducing charging time. The IC operates either in SKIP mode or in forced-PWM (FPWM) mode, depending on the operating conditions, to optimize efficiency. The default output voltage is 5V when using internal feedback resistors. The IC can also be configured to any default output voltages between 3V and 20V when using external feedback resistors. The output voltage is adjustable dynamically (DVS) between 3.2V and 16V in 20mV steps when using internal feedback resistors or between 3V to 20V when using external feedback resistors by programming the internal reference voltage through I2C serial interface.", "applications": ["USB PD 3.0 (PPS) Dynamically-Reconfigurable Processor (DRP) Applications", "Space-Constrained Applications"], "ordering_information": [{"part_number": "MAX77859B", "order_device": "MAX77859BEWO+T*", "package_type": "WLP", "package_drawing_code": "21-100632", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "MAX77859B", "order_device": "MAX77859BEFS+T*", "package_type": "FC2QFN", "package_drawing_code": "未找到", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX77859B", "package_type": "42 WLP", "pins": [{"pin_number": "A1, B1, B2, B3", "pin_name": "LX2", "pin_description": "<PERSON><PERSON><PERSON><PERSON> Switching Node 2."}, {"pin_number": "B5, C1, C2, C3, C4, C5, D1, D2, D3, D4, D5, E5", "pin_name": "PGND", "pin_description": "Power Ground. Connect to AGND on the PCB."}, {"pin_number": "E1, E2, E3, F1", "pin_name": "LX1", "pin_description": "Buck-<PERSON><PERSON> Switching Node 1."}, {"pin_number": "F5", "pin_name": "BST1", "pin_description": "LX1 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST1 and LX1."}, {"pin_number": "E4, F2, F3, F4", "pin_name": "IN", "pin_description": "Buck-Boost Input. Bypass to PGND with two 35V 22µF ceramic capacitors as close as possible."}, {"pin_number": "C7", "pin_name": "VL", "pin_description": "Low-Voltage Internal Supply. Powered from IN. Bypass to AGND with a 10V 2.2µF ceramic capacitor."}, {"pin_number": "D7", "pin_name": "AGND", "pin_description": "Analog Ground. Connect to PGND on the PCB."}, {"pin_number": "F6", "pin_name": "Vio", "pin_description": "IO Voltage Supply. Bypass to AGND with a 6.3V 0.47µF ceramic capacitor."}, {"pin_number": "E7", "pin_name": "SCL", "pin_description": "I2C Serial Interface Clock (High-Z in OFF State). Connect to Vio with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "F7", "pin_name": "SDA", "pin_description": "I2C Serial Interface Data (High-Z in OFF State). Connect to Vio with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "E6", "pin_name": "EN", "pin_description": "Active-High Buck-Boost Enable Input. Compatible with the Vio voltage domain."}, {"pin_number": "A6", "pin_name": "POKB/INTB", "pin_description": "Active-Low Open Drain Status/Interrupts Output. Connect to Vio with a 15kΩ pullup resistor."}, {"pin_number": "D6", "pin_name": "FPWM", "pin_description": "Active-High Forced-PWM Mode Control Input."}, {"pin_number": "C6", "pin_name": "SEL", "pin_description": "Configuration Selection. Connect a resistor between SEL and AGND."}, {"pin_number": "B6", "pin_name": "SRN", "pin_description": "Sense Resistor Negative Input. When a sense resistor is not used, short to SRP pin."}, {"pin_number": "B7", "pin_name": "SRP", "pin_description": "Sense Resistor Positive Input. When a sense resistor is not used, short to SRN pin."}, {"pin_number": "A7", "pin_name": "FB", "pin_description": "Output Voltage Sense Input (Internal Feedback) or Output Voltage Feedback Input (External Feedback)."}, {"pin_number": "A2, A3, A4, B4", "pin_name": "OUT", "pin_description": "Buck-Boost Output. Bypass to PGND with two 25V 22µF ceramic capacitors as close as possible."}, {"pin_number": "A5", "pin_name": "BST2", "pin_description": "LX2 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST2 and LX2."}]}, {"product_part_number": "MAX77859B", "package_type": "19 FC2QFN", "pins": [{"pin_number": "1", "pin_name": "LX2", "pin_description": "<PERSON><PERSON><PERSON><PERSON> Switching Node 2."}, {"pin_number": "2", "pin_name": "PGND", "pin_description": "Power Ground. Connect to AGND on the PCB."}, {"pin_number": "3", "pin_name": "LX1", "pin_description": "Buck-<PERSON><PERSON> Switching Node 1."}, {"pin_number": "4", "pin_name": "BST1", "pin_description": "LX1 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST1 and LX1."}, {"pin_number": "5", "pin_name": "IN", "pin_description": "Buck-Boost Input. Bypass to PGND with two 35V 22µF ceramic capacitors as close as possible."}, {"pin_number": "6", "pin_name": "VL", "pin_description": "Low-Voltage Internal Supply. Powered from IN. Bypass to AGND with a 10V 2.2µF ceramic capacitor."}, {"pin_number": "7", "pin_name": "AGND", "pin_description": "Analog Ground. Connect to PGND on the PCB."}, {"pin_number": "8", "pin_name": "Vio", "pin_description": "IO Voltage Supply. Bypass to AGND with a 6.3V 0.47µF ceramic capacitor."}, {"pin_number": "9", "pin_name": "SCL", "pin_description": "I2C Serial Interface Clock (High-Z in OFF State). Connect to Vio with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "10", "pin_name": "SDA", "pin_description": "I2C Serial Interface Data (High-Z in OFF State). Connect to Vio with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "11", "pin_name": "EN", "pin_description": "Active-High Buck-Boost Enable Input. Compatible with the Vio voltage domain."}, {"pin_number": "12", "pin_name": "POKB/INTB", "pin_description": "Active-Low Open Drain Status/Interrupts Output. Connect to Vio with a 15kΩ pullup resistor."}, {"pin_number": "13", "pin_name": "FPWM", "pin_description": "Active-High Forced-PWM Mode Control Input."}, {"pin_number": "14", "pin_name": "SEL", "pin_description": "Configuration Selection. Connect a resistor between SEL and AGND."}, {"pin_number": "15", "pin_name": "SRN", "pin_description": "Sense Resistor Negative Input. When a sense resistor is not used, short to SRP pin."}, {"pin_number": "16", "pin_name": "SRP", "pin_description": "Sense Resistor Positive Input. When a sense resistor is not used, short to SRN pin."}, {"pin_number": "17", "pin_name": "FB", "pin_description": "Output Voltage Sense Input (Internal Feedback) or Output Voltage Feedback Input (External Feedback)."}, {"pin_number": "18", "pin_name": "OUT", "pin_description": "Buck-Boost Output. Bypass to PGND with two 25V 22µF ceramic capacitors as close as possible."}, {"pin_number": "19", "pin_name": "BST2", "pin_description": "LX2 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST2 and LX2."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77859, Rev 1, 05/23", "family_comparison": "MAX77859A supports PPS (Programmable Power Supply) with an adjustable output current limit. MAX77859B is a non-PPS version optimized for low quiescent current. Both are available in 42-pin WLP and 19-pin FC2QFN packages.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "22V", "min_input_voltage": "2.5V", "max_output_voltage": "20V", "min_output_voltage": "3V", "max_output_current": "6A", "max_switch_frequency": "2.1MHz", "quiescent_current": "60µA", "high_side_mosfet_resistance": "20mΩ", "low_side_mosfet_resistance": "20mΩ", "over_current_protection_threshold": "1.2-7.8A(可调)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "SKIP Mode", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "No", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "0.30518V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.64", "length": "3.01", "width": "2.78", "type": "Information", "pin_count": "2"}]}]