[{"part_number": "MAX77655", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Custom", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "SIMO", "part_number_title": "Low IQ SIMO PMIC with 4-Outputs Delivering up to 700mA Total Output Current", "features": ["4x Buck-Boost Regulators, 1x Inductor", "2.5V to 5.5V Input Voltage Range", "0.5V to 4.0V Output Voltage Range", "700mA Total Output Current (3.7VIN, 1.8VOUT)", "Single-Inductor Multiple-Outputs (SIMO)", "Up to 90% Efficiency", "6.9µA Typical IQ with Two Outputs Enabled in Low-Power Mode", "I2C Interface and Dedicated Enable Pin", "Flexible Power Sequencer (FPS)", "1.99mm x 1.99mm, 16-Bumps, 0.5mm Pitch Wafer-Level Package (WLP)", "< 40mm² Solution Size"], "description": "The MAX77655 is a highly efficient, complete power supply for low-power, ultra-compact applications. It provides four programmable buck-boost switching regulator outputs using only one inductor. Operating from a single Li-ion battery, the MAX77655 delivers a total of 700mA output current (3.7VIN, 1.8VOUT) in less than 40mm2 solution size. An integrated sequencer controls full startup while an I2C interface allows the MAX77655 to be dynamically configured and monitored. This device is part of the single-inductor multiple-output (SIMO) product family.", "applications": ["TWS Bluetooth™ Headphones/Hearables", "Fitness, Health, Activity Monitors, and Smart Watches", "Portable Devices", "Sensors Nodes and Consumer Internet of Things (IoT)"], "ordering_information": [{"part_number": "MAX77655", "order_device": "MAX77655EWE+T", "package_type": "WLP", "package_drawing_code": "21-100374", "output_voltage": "Programmable", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX77655", "package_type": "WLP", "pins": [{"pin_number": "C2", "pin_name": "nEN", "pin_description": "Active-Low Enable Input. EN supports push-button, slide-switch, or logic configurations. If not used, connect nEN to IN."}, {"pin_number": "B2", "pin_name": "nIRQ", "pin_description": "Active-Low, Open-Drain Interrupt Pin. Connect a 100kΩ pullup resistor to nIRQ."}, {"pin_number": "B1", "pin_name": "SCL", "pin_description": "I2C Clock"}, {"pin_number": "C1", "pin_name": "SDA", "pin_description": "I2C Data"}, {"pin_number": "D1", "pin_name": "IN", "pin_description": "Input Voltage Connection. Bypass to GND with a 22µF ceramic capacitor."}, {"pin_number": "A1", "pin_name": "GND", "pin_description": "Quiet Ground. Connect GND to PGND and the low-impedance ground plane of the PCB."}, {"pin_number": "A2", "pin_name": "VDD", "pin_description": "Device Power Input. Connect to PVDD."}, {"pin_number": "D4", "pin_name": "PVDD", "pin_description": "1.8V Internal Supply. Bypass this pin with a 10µF ceramic capacitor and connect to VDD. Do not connect anything else to this pin. If pullup resistors must be connected to PVDD, ensure on the layout the connection points are as close as possible to the capacitor and not the pin."}, {"pin_number": "A3", "pin_name": "SBB0", "pin_description": "SIMO Buck-Boost Output 0. SBBO is the power output for channel 0 of the SIMO buck-boost. Bypass SBBO to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "A4", "pin_name": "SBB1", "pin_description": "SIMO Buck-Boost Output 1. SBB1 is the power output for channel 1 of the SIMO buck-boost. Bypass SBB1 to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "B4", "pin_name": "SBB2", "pin_description": "SIMO Buck-Boost Output 2. SBB2 is the power output for channel 2 of the SIMO buck-boost. Bypass SBB2 to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "C4", "pin_name": "SBB3", "pin_description": "SIMO Buck-Boost Output 3. SBB3 is the power output for channel 3 of the SIMO buck-boost. Bypass SBB3 to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "B3", "pin_name": "BST", "pin_description": "SIMO Power Input for the High-Side Output NMOS Drivers. Connect a 10nF ceramic capacitor between BST and LXB."}, {"pin_number": "C3", "pin_name": "LXB", "pin_description": "Switching Node B. LXB is driven between PGND and SBBx when SBBx is enabled. LXB is driven to PGND when all SIMO channels are disabled."}, {"pin_number": "D2", "pin_name": "LXA", "pin_description": "Switching Node A. LXA is driven between PGND and IN when any SIMO channel is enabled. LXA is driven to PGND when all SIMO channels are disabled."}, {"pin_number": "D3", "pin_name": "PGND", "pin_description": "Power Ground for the SIMO Low-Side FETs. Connect PGND to GND, and the low-impedance ground plane of the PCB."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77655.pdf", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "5.5V", "min_input_voltage": "2.5V", "max_output_voltage": "4.0V", "min_output_voltage": "1V", "max_output_current": "0.7A", "max_switch_frequency": "未找到", "quiescent_current": "6.9µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "1.2A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM/PSM/DCM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "Latch", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "Information", "pin_count": "2", "pitch": "0.5", "height": "0.64", "width": "1.99", "length": "1.99"}]}, {"part_number": "MAX77655A", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "SIMO", "part_number_title": "Low IQ SIMO PMIC with 4-Outputs Delivering up to 700mA Total Output Current", "features": ["4x Buck-Boost Regulators, 1x Inductor", "2.5V to 5.5V Input Voltage Range", "0.5V to 4.0V Output Voltage Range", "700mA Total Output Current (3.7VIN, 1.8VOUT)", "Single-Inductor Multiple-Outputs (SIMO)", "Up to 90% Efficiency", "6.9µA Typical IQ with Two Outputs Enabled in Low-Power Mode", "I2C Interface and Dedicated Enable Pin", "Flexible Power Sequencer (FPS)", "1.99mm x 1.99mm, 16-Bumps, 0.5mm Pitch Wafer-Level Package (WLP)", "< 40mm² Solution Size"], "description": "The MAX77655 is a highly efficient, complete power supply for low-power, ultra-compact applications. It provides four programmable buck-boost switching regulator outputs using only one inductor. Operating from a single Li-ion battery, the MAX77655 delivers a total of 700mA output current (3.7VIN, 1.8VOUT) in less than 40mm2 solution size. An integrated sequencer controls full startup while an I2C interface allows the MAX77655 to be dynamically configured and monitored. This device is part of the single-inductor multiple-output (SIMO) product family.", "applications": ["TWS Bluetooth™ Headphones/Hearables", "Fitness, Health, Activity Monitors, and Smart Watches", "Portable Devices", "Sensors Nodes and Consumer Internet of Things (IoT)"], "ordering_information": [{"part_number": "MAX77655A", "order_device": "MAX77655AEWE+T", "package_type": "WLP", "package_drawing_code": "21-100374", "output_voltage": "Programmable", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX77655", "package_type": "WLP", "pins": [{"pin_number": "C2", "pin_name": "nEN", "pin_description": "Active-Low Enable Input. EN supports push-button, slide-switch, or logic configurations. If not used, connect nEN to IN."}, {"pin_number": "B2", "pin_name": "nIRQ", "pin_description": "Active-Low, Open-Drain Interrupt Pin. Connect a 100kΩ pullup resistor to nIRQ."}, {"pin_number": "B1", "pin_name": "SCL", "pin_description": "I2C Clock"}, {"pin_number": "C1", "pin_name": "SDA", "pin_description": "I2C Data"}, {"pin_number": "D1", "pin_name": "IN", "pin_description": "Input Voltage Connection. Bypass to GND with a 22µF ceramic capacitor."}, {"pin_number": "A1", "pin_name": "GND", "pin_description": "Quiet Ground. Connect GND to PGND and the low-impedance ground plane of the PCB."}, {"pin_number": "A2", "pin_name": "VDD", "pin_description": "Device Power Input. Connect to PVDD."}, {"pin_number": "D4", "pin_name": "PVDD", "pin_description": "1.8V Internal Supply. Bypass this pin with a 10µF ceramic capacitor and connect to VDD. Do not connect anything else to this pin. If pullup resistors must be connected to PVDD, ensure on the layout the connection points are as close as possible to the capacitor and not the pin."}, {"pin_number": "A3", "pin_name": "SBB0", "pin_description": "SIMO Buck-Boost Output 0. SBBO is the power output for channel 0 of the SIMO buck-boost. Bypass SBBO to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "A4", "pin_name": "SBB1", "pin_description": "SIMO Buck-Boost Output 1. SBB1 is the power output for channel 1 of the SIMO buck-boost. Bypass SBB1 to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "B4", "pin_name": "SBB2", "pin_description": "SIMO Buck-Boost Output 2. SBB2 is the power output for channel 2 of the SIMO buck-boost. Bypass SBB2 to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "C4", "pin_name": "SBB3", "pin_description": "SIMO Buck-Boost Output 3. SBB3 is the power output for channel 3 of the SIMO buck-boost. Bypass SBB3 to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "B3", "pin_name": "BST", "pin_description": "SIMO Power Input for the High-Side Output NMOS Drivers. Connect a 10nF ceramic capacitor between BST and LXB."}, {"pin_number": "C3", "pin_name": "LXB", "pin_description": "Switching Node B. LXB is driven between PGND and SBBx when SBBx is enabled. LXB is driven to PGND when all SIMO channels are disabled."}, {"pin_number": "D2", "pin_name": "LXA", "pin_description": "Switching Node A. LXA is driven between PGND and IN when any SIMO channel is enabled. LXA is driven to PGND when all SIMO channels are disabled."}, {"pin_number": "D3", "pin_name": "PGND", "pin_description": "Power Ground for the SIMO Low-Side FETs. Connect PGND to GND, and the low-impedance ground plane of the PCB."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77655.pdf", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "5.5V", "min_input_voltage": "2.5V", "max_output_voltage": "4.0V", "min_output_voltage": "1V", "max_output_current": "0.7A", "max_switch_frequency": "未找到", "quiescent_current": "6.9µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "1.2A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM/PSM/DCM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "Latch", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "Information", "pin_count": "2", "pitch": "0.5", "height": "0.64", "width": "1.99", "length": "1.99"}]}, {"part_number": "MAX77655B", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "SIMO", "part_number_title": "Low IQ SIMO PMIC with 4-Outputs Delivering up to 700mA Total Output Current", "features": ["4x Buck-Boost Regulators, 1x Inductor", "2.5V to 5.5V Input Voltage Range", "0.5V to 4.0V Output Voltage Range", "700mA Total Output Current (3.7VIN, 1.8VOUT)", "Single-Inductor Multiple-Outputs (SIMO)", "Up to 90% Efficiency", "6.9µA Typical IQ with Two Outputs Enabled in Low-Power Mode", "I2C Interface and Dedicated Enable Pin", "Flexible Power Sequencer (FPS)", "1.99mm x 1.99mm, 16-Bumps, 0.5mm Pitch Wafer-Level Package (WLP)", "< 40mm² Solution Size"], "description": "The MAX77655 is a highly efficient, complete power supply for low-power, ultra-compact applications. It provides four programmable buck-boost switching regulator outputs using only one inductor. Operating from a single Li-ion battery, the MAX77655 delivers a total of 700mA output current (3.7VIN, 1.8VOUT) in less than 40mm2 solution size. An integrated sequencer controls full startup while an I2C interface allows the MAX77655 to be dynamically configured and monitored. This device is part of the single-inductor multiple-output (SIMO) product family.", "applications": ["TWS Bluetooth™ Headphones/Hearables", "Fitness, Health, Activity Monitors, and Smart Watches", "Portable Devices", "Sensors Nodes and Consumer Internet of Things (IoT)"], "ordering_information": [{"part_number": "MAX77655B", "order_device": "MAX77655BEWE+T", "package_type": "WLP", "package_drawing_code": "21-100374", "output_voltage": "Programmable", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX77655", "package_type": "WLP", "pins": [{"pin_number": "C2", "pin_name": "nEN", "pin_description": "Active-Low Enable Input. EN supports push-button, slide-switch, or logic configurations. If not used, connect nEN to IN."}, {"pin_number": "B2", "pin_name": "nIRQ", "pin_description": "Active-Low, Open-Drain Interrupt Pin. Connect a 100kΩ pullup resistor to nIRQ."}, {"pin_number": "B1", "pin_name": "SCL", "pin_description": "I2C Clock"}, {"pin_number": "C1", "pin_name": "SDA", "pin_description": "I2C Data"}, {"pin_number": "D1", "pin_name": "IN", "pin_description": "Input Voltage Connection. Bypass to GND with a 22µF ceramic capacitor."}, {"pin_number": "A1", "pin_name": "GND", "pin_description": "Quiet Ground. Connect GND to PGND and the low-impedance ground plane of the PCB."}, {"pin_number": "A2", "pin_name": "VDD", "pin_description": "Device Power Input. Connect to PVDD."}, {"pin_number": "D4", "pin_name": "PVDD", "pin_description": "1.8V Internal Supply. Bypass this pin with a 10µF ceramic capacitor and connect to VDD. Do not connect anything else to this pin. If pullup resistors must be connected to PVDD, ensure on the layout the connection points are as close as possible to the capacitor and not the pin."}, {"pin_number": "A3", "pin_name": "SBB0", "pin_description": "SIMO Buck-Boost Output 0. SBBO is the power output for channel 0 of the SIMO buck-boost. Bypass SBBO to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "A4", "pin_name": "SBB1", "pin_description": "SIMO Buck-Boost Output 1. SBB1 is the power output for channel 1 of the SIMO buck-boost. Bypass SBB1 to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "B4", "pin_name": "SBB2", "pin_description": "SIMO Buck-Boost Output 2. SBB2 is the power output for channel 2 of the SIMO buck-boost. Bypass SBB2 to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "C4", "pin_name": "SBB3", "pin_description": "SIMO Buck-Boost Output 3. SBB3 is the power output for channel 3 of the SIMO buck-boost. Bypass SBB3 to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "B3", "pin_name": "BST", "pin_description": "SIMO Power Input for the High-Side Output NMOS Drivers. Connect a 10nF ceramic capacitor between BST and LXB."}, {"pin_number": "C3", "pin_name": "LXB", "pin_description": "Switching Node B. LXB is driven between PGND and SBBx when SBBx is enabled. LXB is driven to PGND when all SIMO channels are disabled."}, {"pin_number": "D2", "pin_name": "LXA", "pin_description": "Switching Node A. LXA is driven between PGND and IN when any SIMO channel is enabled. LXA is driven to PGND when all SIMO channels are disabled."}, {"pin_number": "D3", "pin_name": "PGND", "pin_description": "Power Ground for the SIMO Low-Side FETs. Connect PGND to GND, and the low-impedance ground plane of the PCB."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77655.pdf", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "5.5V", "min_input_voltage": "2.5V", "max_output_voltage": "4.0V", "min_output_voltage": "1V", "max_output_current": "0.7A", "max_switch_frequency": "未找到", "quiescent_current": "6.9µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "1.2A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM/PSM/DCM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "Latch", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "Information", "pin_count": "2", "pitch": "0.5", "height": "0.64", "width": "1.99", "length": "1.99"}]}]