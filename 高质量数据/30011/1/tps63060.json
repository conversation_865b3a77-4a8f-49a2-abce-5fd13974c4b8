[{"part_number": "TPS63060", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS6306x 具有 2A 开关电流的高输入电压降压/升压转换器", "features": ["输入电压范围: 2.5V 至 12V", "效率: 高达 93%", "5V 时的输出电流 (Vin < 10V): 降压模式下为 2A", "5V 时的输出电流 (VIN > 4V): 升压模式下为 1.3A", "在降压和升压模式之间自动转换", "器件典型静态电流: ≤30 μA", "输出电压可以是固定的，也可以调节，范围是 2.5V 至 8V", "省电模式，改善低输出功率时的效率", "2.4MHz 强制固定频率运行，可实现同步", "电源正常状态输出", "<PERSON><PERSON><PERSON><PERSON> Control™", "关断期间负载断开", "提供过热保护", "过压保护"], "description": "TPS6306x 器件可以为由 3 节到 6 节碱性、镍镉或镍氢电池或单节、两节锂离子或锂聚合物电池供电的产品提供电源解决方案。使用两节双锂离子或者锂聚合物电池时，输出电流可升高至 2A，放电电压可达 5V 或者更低。此升压/降压转换器基于一个频率固定的脉宽调制 (PWM) 控制器。该控制器可通过同步整流实现效率最大化。在负载电流较低的情况下，该转换器会进入节能模式，以在宽负载电流范围内保持高效率。禁用省电模式则会强制转换器以固定开关频率运行。开关的最大平均电流为 2.25A (典型值)。输出电压可通过外部电阻分频器进行编程，或者在内部芯片上固定。可通过禁用转换器来最大限度地减少电池消耗。在关断期间，负载从电池断开。这些器件采用 3mm×3mm 10 引脚 WSON (DSC) 封装。", "applications": ["双锂离子电池应用", "数码相机 (DSC) 和便携式摄像机", "笔记本电脑", "工业计量设备", "超便携电脑和移动互联网设备", "个人医疗产品", "大功率 LED"], "ordering_information": [{"part_number": "TPS63060", "order_device": "TPS63060DSCR", "package_type": "WSON", "package_drawing_code": "DSC", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63060", "order_device": "TPS63060DSCT", "package_type": "WSON", "package_drawing_code": "DSC", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63060", "package_type": "WSON", "pins": [{"pin_number": "1", "pin_name": "L1", "pin_description": "Connection for inductor"}, {"pin_number": "2", "pin_name": "VIN", "pin_description": "Supply voltage for power stage"}, {"pin_number": "3", "pin_name": "EN", "pin_description": "Enable input (1 enabled, 0 disabled)"}, {"pin_number": "4", "pin_name": "PS/SYNC", "pin_description": "Enable / disable power save mode (1 disabled, 0 enabled, clock signal for synchronization)"}, {"pin_number": "5", "pin_name": "PG", "pin_description": "Output power good (1 good, 0 failure, open drain)"}, {"pin_number": "6", "pin_name": "VAUX", "pin_description": "Connection for capacitor"}, {"pin_number": "7", "pin_name": "GND", "pin_description": "Control and logic ground"}, {"pin_number": "8", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT on fixed output voltage versions"}, {"pin_number": "9", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}, {"pin_number": "10", "pin_name": "L2", "pin_description": "Connection for inductor"}, {"pin_number": "Exposed Thermal Pad", "pin_name": "Exposed Thermal Pad", "pin_description": "Must be soldered to achieve the appropriate power dissipation. Must be connected to PGND."}]}], "datasheet_cn": "ZHCS705C (Version C, 2020-09)", "datasheet_en": "SLVSA92", "family_comparison": "TPS63060是可调输出版本，TPS63061是5V固定输出版本。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "12V", "min_input_voltage": "2.5V", "max_output_voltage": "8V", "min_output_voltage": "2.5V", "max_output_current": "2A", "max_switch_frequency": "2.6MHz", "quiescent_current": "30μA", "high_side_mosfet_resistance": "90mΩ", "low_side_mosfet_resistance": "95mΩ", "over_current_protection_threshold": "2.25A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±0.6%", "output_reference_voltage": "0.5V", "loop_control_mode": "平均电流模式"}, "package": [{"type": "OPTION", "pitch": "0.5", "height": "0.8", "length": "3.0", "width": "3.0", "pin_count": "10"}]}, {"part_number": "TPS63061", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS6306x 具有 2A 开关电流的高输入电压降压/升压转换器", "features": ["输入电压范围: 2.5V 至 12V", "效率: 高达 93%", "5V 时的输出电流 (Vin < 10V): 降压模式下为 2A", "5V 时的输出电流 (VIN > 4V): 升压模式下为 1.3A", "在降压和升压模式之间自动转换", "器件典型静态电流: ≤30 μA", "输出电压可以是固定的，也可以调节，范围是 2.5V 至 8V", "省电模式，改善低输出功率时的效率", "2.4MHz 强制固定频率运行，可实现同步", "电源正常状态输出", "<PERSON><PERSON><PERSON><PERSON> Control™", "关断期间负载断开", "提供过热保护", "过压保护"], "description": "TPS6306x 器件可以为由 3 节到 6 节碱性、镍镉或镍氢电池或单节、两节锂离子或锂聚合物电池供电的产品提供电源解决方案。使用两节双锂离子或者锂聚合物电池时，输出电流可升高至 2A，放电电压可达 5V 或者更低。此升压/降压转换器基于一个频率固定的脉宽调制 (PWM) 控制器。该控制器可通过同步整流实现效率最大化。在负载电流较低的情况下，该转换器会进入节能模式，以在宽负载电流范围内保持高效率。禁用省电模式则会强制转换器以固定开关频率运行。开关的最大平均电流为 2.25A (典型值)。输出电压可通过外部电阻分频器进行编程，或者在内部芯片上固定。可通过禁用转换器来最大限度地减少电池消耗。在关断期间，负载从电池断开。这些器件采用 3mm×3mm 10 引脚 WSON (DSC) 封装。", "applications": ["双锂离子电池应用", "数码相机 (DSC) 和便携式摄像机", "笔记本电脑", "工业计量设备", "超便携电脑和移动互联网设备", "个人医疗产品", "大功率 LED"], "ordering_information": [{"part_number": "TPS63061", "order_device": "TPS63061DSCR", "package_type": "WSON", "package_drawing_code": "DSC", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63061", "order_device": "TPS63061DSCT", "package_type": "WSON", "package_drawing_code": "DSC", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63061", "package_type": "WSON", "pins": [{"pin_number": "1", "pin_name": "L1", "pin_description": "Connection for inductor"}, {"pin_number": "2", "pin_name": "VIN", "pin_description": "Supply voltage for power stage"}, {"pin_number": "3", "pin_name": "EN", "pin_description": "Enable input (1 enabled, 0 disabled)"}, {"pin_number": "4", "pin_name": "PS/SYNC", "pin_description": "Enable / disable power save mode (1 disabled, 0 enabled, clock signal for synchronization)"}, {"pin_number": "5", "pin_name": "PG", "pin_description": "Output power good (1 good, 0 failure, open drain)"}, {"pin_number": "6", "pin_name": "VAUX", "pin_description": "Connection for capacitor"}, {"pin_number": "7", "pin_name": "GND", "pin_description": "Control and logic ground"}, {"pin_number": "8", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT on fixed output voltage versions"}, {"pin_number": "9", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}, {"pin_number": "10", "pin_name": "L2", "pin_description": "Connection for inductor"}, {"pin_number": "Exposed Thermal Pad", "pin_name": "Exposed Thermal Pad", "pin_description": "Must be soldered to achieve the appropriate power dissipation. Must be connected to PGND."}]}], "datasheet_cn": "ZHCS705C (Version C, 2020-09)", "datasheet_en": "SLVSA92", "family_comparison": "TPS63060是可调输出版本，TPS63061是5V固定输出版本。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "12V", "min_input_voltage": "2.5V", "max_output_voltage": "5V", "min_output_voltage": "5V", "max_output_current": "2A", "max_switch_frequency": "2.6MHz", "quiescent_current": "30μA", "high_side_mosfet_resistance": "90mΩ", "low_side_mosfet_resistance": "95mΩ", "over_current_protection_threshold": "2.25A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±0.6%", "output_reference_voltage": "不适用", "loop_control_mode": "平均电流模式"}, "package": [{"type": "OPTION", "pitch": "0.5", "height": "0.8", "length": "3.0", "width": "3.0", "pin_count": "10"}]}]