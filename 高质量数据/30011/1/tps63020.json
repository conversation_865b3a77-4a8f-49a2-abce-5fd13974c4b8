[{"part_number": "TPS63020", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "升降压(<PERSON><PERSON><PERSON><PERSON>)", "part_number_title": "TPS6302x High Efficiency Single Inductor Buck-boost Converter with 4-A Switches", "features": ["Input voltage range: 1.8 V to 5.5 V", "Adjustable output voltage: 1.2 V to 5.5 V", "Output current for VIN > 2.5 V, VOUT = 3.3 V: 2 A", "High efficiency over the entire load range", "Operating quiescent current: 25 μA", "Power save mode with mode selection", "Average current mode buck-boost architecture", "Automatic transition between modes", "Fixed frequency operation at 2.4 MHz", "Synchronization possible", "Power good output", "Safety and robust operation features", "Overtemperature, overvoltage protection", "Load disconnect during shutdown"], "description": "The TPS6302x devices provide a power supply solution for products powered by either a two-cell or three-cell alkaline, NiCd or NiMH battery, a one-cell Li-ion or Li-polymer battery, supercapacitors or other supply rails. Output currents up to 3 A are supported. When using batteries, they can be discharged down to below 2 V. The buck-boost converter is based on a fixed frequency, pulse width modulation (PWM) controller using synchronous rectification to obtain maximum efficiency. At low load currents, the converter enters power save mode to maintain high efficiency over a wide load current range. The power save mode can be disabled, forcing the converter to operate at a fixed switching frequency. The maximum average current in the switches is limited to a typical value of 4 A. The output voltage is programmable using an external resistor divider, or is fixed internally on the chip. The converter can be disabled to minimize battery drain. During shutdown, the load is disconnected from the battery. The TPS6302x devices operate over a free air temperature range of -40°C to 85°C. The devices are packaged in a 14-pin VSON package measuring 3 mm x 4 mm (DSJ).", "applications": ["Pre-regulation in battery-powered devices: EPOS (portable data terminal, barcode scanner), e-cigarette, single board computer, IP network camera, video doorbell, land mobile radios", "Voltage stabilizer: wired communication, wireless communication, PLC, optical module", "Backup supercapacitor supply: electricity meter, solid state drive (SSD) - enterprise"], "ordering_information": [{"part_number": "TPS63020", "order_device": "TPS63020DSJR", "package_type": "VSON", "package_drawing_code": "DSJ", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63020", "order_device": "TPS63020DSJT", "package_type": "VSON", "package_drawing_code": "DSJ", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63020", "package_type": "VSON (DSJ)", "pins": [{"pin_number": "1", "pin_name": "VINA", "pin_description": "Supply voltage for control stage"}, {"pin_number": "2", "pin_name": "GND", "pin_description": "Control / logic ground"}, {"pin_number": "3", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT on fixed output voltage versions"}, {"pin_number": "4, 5", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}, {"pin_number": "6, 7", "pin_name": "L2", "pin_description": "Connection for inductor"}, {"pin_number": "8, 9", "pin_name": "L1", "pin_description": "Connection for inductor"}, {"pin_number": "10, 11", "pin_name": "VIN", "pin_description": "Supply voltage for power stage"}, {"pin_number": "12", "pin_name": "EN", "pin_description": "Enable input (1 enabled, 0 disabled), must not be left open"}, {"pin_number": "13", "pin_name": "PS/SYNC", "pin_description": "Enable / disable power save mode (1 disabled, 0 enabled, clock signal for synchronization), must not be left open"}, {"pin_number": "14", "pin_name": "PG", "pin_description": "Output power good (1 good, 0 failure; open-drain), can be left open"}, {"pin_number": "Exposed Thermal Pad", "pin_name": "PGND", "pin_description": "The exposed thermal pad is connected to PGND."}]}], "datasheet_cn": "未找到", "datasheet_en": "slvs916i.pdf", "family_comparison": "TPS63020: Adjustable, TPS63021: 3.3V", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.5V", "min_output_voltage": "1.2V", "max_output_current": "3A", "max_switch_frequency": "2.6MHz", "quiescent_current": "25µA", "high_side_mosfet_resistance": "50mΩ", "low_side_mosfet_resistance": "50mΩ", "over_current_protection_threshold": "4A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Fold Back", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "1%", "output_reference_voltage": "0.5V", "loop_control_mode": "平均电流模式"}, "package": [{"type": "VSON", "pitch": "0.5", "length": "4.0", "width": "3.0", "pin_count": "14", "height": "0.5"}]}, {"part_number": "TPS63021", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "升降压(<PERSON><PERSON><PERSON><PERSON>)", "part_number_title": "TPS6302x High Efficiency Single Inductor Buck-boost Converter with 4-A Switches", "features": ["Input voltage range: 1.8 V to 5.5 V", "Adjustable output voltage: 1.2 V to 5.5 V", "Output current for VIN > 2.5 V, VOUT = 3.3 V: 2 A", "High efficiency over the entire load range", "Operating quiescent current: 25 μA", "Power save mode with mode selection", "Average current mode buck-boost architecture", "Automatic transition between modes", "Fixed frequency operation at 2.4 MHz", "Synchronization possible", "Power good output", "Safety and robust operation features", "Overtemperature, overvoltage protection", "Load disconnect during shutdown"], "description": "The TPS6302x devices provide a power supply solution for products powered by either a two-cell or three-cell alkaline, NiCd or NiMH battery, a one-cell Li-ion or Li-polymer battery, supercapacitors or other supply rails. Output currents up to 3 A are supported. When using batteries, they can be discharged down to below 2 V. The buck-boost converter is based on a fixed frequency, pulse width modulation (PWM) controller using synchronous rectification to obtain maximum efficiency. At low load currents, the converter enters power save mode to maintain high efficiency over a wide load current range. The power save mode can be disabled, forcing the converter to operate at a fixed switching frequency. The maximum average current in the switches is limited to a typical value of 4 A. The output voltage is programmable using an external resistor divider, or is fixed internally on the chip. The converter can be disabled to minimize battery drain. During shutdown, the load is disconnected from the battery. The TPS6302x devices operate over a free air temperature range of -40°C to 85°C. The devices are packaged in a 14-pin VSON package measuring 3 mm x 4 mm (DSJ).", "applications": ["Pre-regulation in battery-powered devices: EPOS (portable data terminal, barcode scanner), e-cigarette, single board computer, IP network camera, video doorbell, land mobile radios", "Voltage stabilizer: wired communication, wireless communication, PLC, optical module", "Backup supercapacitor supply: electricity meter, solid state drive (SSD) - enterprise"], "ordering_information": [{"part_number": "TPS63021", "order_device": "TPS63021DSJR", "package_type": "VSON", "package_drawing_code": "DSJ", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63021", "order_device": "TPS63021DSJT", "package_type": "VSON", "package_drawing_code": "DSJ", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63021", "package_type": "VSON (DSJ)", "pins": [{"pin_number": "1", "pin_name": "VINA", "pin_description": "Supply voltage for control stage"}, {"pin_number": "2", "pin_name": "GND", "pin_description": "Control / logic ground"}, {"pin_number": "3", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT on fixed output voltage versions"}, {"pin_number": "4, 5", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}, {"pin_number": "6, 7", "pin_name": "L2", "pin_description": "Connection for inductor"}, {"pin_number": "8, 9", "pin_name": "L1", "pin_description": "Connection for inductor"}, {"pin_number": "10, 11", "pin_name": "VIN", "pin_description": "Supply voltage for power stage"}, {"pin_number": "12", "pin_name": "EN", "pin_description": "Enable input (1 enabled, 0 disabled), must not be left open"}, {"pin_number": "13", "pin_name": "PS/SYNC", "pin_description": "Enable / disable power save mode (1 disabled, 0 enabled, clock signal for synchronization), must not be left open"}, {"pin_number": "14", "pin_name": "PG", "pin_description": "Output power good (1 good, 0 failure; open-drain), can be left open"}, {"pin_number": "Exposed Thermal Pad", "pin_name": "PGND", "pin_description": "The exposed thermal pad is connected to PGND."}]}], "datasheet_cn": "未找到", "datasheet_en": "slvs916i.pdf", "family_comparison": "TPS63020: Adjustable, TPS63021: 3.3V", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "3.3V", "min_output_voltage": "3.3V", "max_output_current": "3A", "max_switch_frequency": "2.6MHz", "quiescent_current": "25µA", "high_side_mosfet_resistance": "50mΩ", "low_side_mosfet_resistance": "50mΩ", "over_current_protection_threshold": "4A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Fold Back", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "1%", "output_reference_voltage": "未找到", "loop_control_mode": "平均电流模式"}, "package": [{"type": "VSON", "pitch": "0.5", "length": "4.0", "width": "3.0", "pin_count": "14", "height": "0.5"}]}]