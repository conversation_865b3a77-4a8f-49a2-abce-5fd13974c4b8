[{"part_number": "TPS63810", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "升降压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "TPS63810 和 TPS63811 - 具有 I²C 接口的 2.5A 降压/升压转换器", "features": ["输入电压范围: 2.2V 至 5.5V", "输出电压范围: 1.8V 至 5.2V", "运行和关断期间, I²C 可配置", "VSEL 引脚用于在两个输出电压预设之间切换", "输出电流: 当 V₁ ≥ 2.5V、Vo = 3.3V 时可达 2.5A; 当 V₁ ≥ 2.8V、V◦ = 3.5V 时可达 2.5A", "在整个负载范围内具有高效率", "13µA 低工作静态电流", "自动节电模式和强制 PWM 模式 (I²C 可配置)", "峰值电流降压/升压模式架构", "可在降压、降压/升压和升压操作模式之间定义转换", "正向和反向电流运行", "启动至预偏置输出", "安全、可靠运行特性", "集成软启动", "过热和过压保护", "关断期间的真正负载断开", "正向和反向电流限制", "TPS63810: 预编程输出电压 (3.3V、3.45V)", "解决方案尺寸小于 20 mm², 仅有四个外部器件"], "description": "TPS63810 和 TPS63811 是完全可编程 (通过 I²C) 的高效率、高输出电流降压/升压转换器。根据输入电压不同, 当输入电压近似等于输出电压时, 它们会自动以升压、降压或全新的 4 周期降压/升压模式运行。在定义的阈值内进行模式切换, 避免不必要的模式内切换, 以减少输出电压纹波。两个可通过 I²C 访问的寄存器用于设置输出电压, VSEL 引脚用于选择哪个输出电压寄存器处于激活状态。这样, 这些器件就能够支持动态电压调节。如果输出电压寄存器在运行过程中发生了更改或切换了 VSEL 引脚, 则器件将以定义的可编程斜坡速率转换运行模式。", "applications": ["系统前置稳压器 (智能手机、平板电脑、跟踪和远程信息处理、EPOS、TWS 耳机、医用助听器)", "负载点调节 (飞行时间摄像头传感器、端口/电缆适配器和加密狗)", "热电器件电源 (TEC、光纤模块)", "宽带网络无线电或 SoC 电源 (物联网、家庭自动化、EPOS)"], "ordering_information": [{"part_number": "TPS63810", "order_device": "TPS63810YFFR", "package_type": "DSBGA", "package_drawing_code": "YFF0015", "output_voltage": "3.3V/3.45V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63810", "order_device": "TPS63810YFFR.A", "package_type": "DSBGA", "package_drawing_code": "YFF0015", "output_voltage": "3.3V/3.45V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63810", "package_type": "DSBGA", "pins": [{"pin_number": "A1", "pin_name": "EN", "pin_description": "器件使能。此引脚上的高逻辑电平可使能器件；低逻辑电平可禁用器件。"}, {"pin_number": "A2, A3", "pin_name": "VIN", "pin_description": "功率级电源电压"}, {"pin_number": "B1", "pin_name": "VSEL", "pin_description": "此引脚选择哪个 VOUT 寄存器处于激活状态。当此引脚上施加低逻辑电平时，VOUT1 寄存器设置输出电压。当此引脚上施加高逻辑电平时，VOUT2 寄存器设置输出电压。"}, {"pin_number": "B2, B3", "pin_name": "LX1", "pin_description": "电感器连接"}, {"pin_number": "C1", "pin_name": "AGND", "pin_description": "模拟接地"}, {"pin_number": "C2, C3", "pin_name": "GND", "pin_description": "电源接地"}, {"pin_number": "D1", "pin_name": "SCL", "pin_description": "I2C 串行接口时钟。使用电阻器或电流源将此引脚上拉至 I2C 总线电压。"}, {"pin_number": "D2, D3", "pin_name": "LX2", "pin_description": "电感器连接"}, {"pin_number": "E1", "pin_name": "SDA", "pin_description": "I2C 串行接口数据。使用电阻器或电流源将此引脚上拉至 I2C 总线电压。"}, {"pin_number": "E2, E3", "pin_name": "VOUT", "pin_description": "转换器输出"}]}], "datasheet_cn": "ZHCSKE6C-JULY 2019-REVISED FEBRUARY 2020", "datasheet_en": "SLVSEK4", "family_comparison": "TPS63810为预设启动电压版本，TPS63811为启动时可编程版本。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.2V", "max_output_voltage": "5.2V", "min_output_voltage": "1.8V", "max_output_current": "2.5A", "max_switch_frequency": "2.6MHz", "quiescent_current": "13µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "4.3A", "operation_mode": "同步", "pass_through_mode": "False", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "自动重启", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "自动重启", "output_under_voltage_protection": "No", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "电流限制", "over_temperature_protection": "自动重启", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.625", "length": "2.3", "width": "1.4", "type": "OPTION", "pin_count": "2"}]}, {"part_number": "TPS63811", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "升降压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "TPS63810 和 TPS63811 - 具有 I²C 接口的 2.5A 降压/升压转换器", "features": ["输入电压范围: 2.2V 至 5.5V", "输出电压范围: 1.8V 至 5.2V", "运行和关断期间, I²C 可配置", "VSEL 引脚用于在两个输出电压预设之间切换", "输出电流: 当 V₁ ≥ 2.5V、Vo = 3.3V 时可达 2.5A; 当 V₁ ≥ 2.8V、V◦ = 3.5V 时可达 2.5A", "在整个负载范围内具有高效率", "13µA 低工作静态电流", "自动节电模式和强制 PWM 模式 (I²C 可配置)", "峰值电流降压/升压模式架构", "可在降压、降压/升压和升压操作模式之间定义转换", "正向和反向电流运行", "启动至预偏置输出", "安全、可靠运行特性", "集成软启动", "过热和过压保护", "关断期间的真正负载断开", "正向和反向电流限制", "TPS63811: 启动前的程序输出电压", "解决方案尺寸小于 20 mm², 仅有四个外部器件"], "description": "TPS63810 和 TPS63811 是完全可编程 (通过 I²C) 的高效率、高输出电流降压/升压转换器。根据输入电压不同, 当输入电压近似等于输出电压时, 它们会自动以升压、降压或全新的 4 周期降压/升压模式运行。在定义的阈值内进行模式切换, 避免不必要的模式内切换, 以减少输出电压纹波。两个可通过 I²C 访问的寄存器用于设置输出电压, VSEL 引脚用于选择哪个输出电压寄存器处于激活状态。这样, 这些器件就能够支持动态电压调节。如果输出电压寄存器在运行过程中发生了更改或切换了 VSEL 引脚, 则器件将以定义的可编程斜坡速率转换运行模式。", "applications": ["系统前置稳压器 (智能手机、平板电脑、跟踪和远程信息处理、EPOS、TWS 耳机、医用助听器)", "负载点调节 (飞行时间摄像头传感器、端口/电缆适配器和加密狗)", "热电器件电源 (TEC、光纤模块)", "宽带网络无线电或 SoC 电源 (物联网、家庭自动化、EPOS)"], "ordering_information": [{"part_number": "TPS63811", "order_device": "TPS63811YFFR", "package_type": "DSBGA", "package_drawing_code": "YFF0015", "output_voltage": "Programmable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63811", "order_device": "TPS63811YFFR.A", "package_type": "DSBGA", "package_drawing_code": "YFF0015", "output_voltage": "Programmable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63811", "package_type": "DSBGA", "pins": [{"pin_number": "A1", "pin_name": "EN", "pin_description": "器件使能。此引脚上的高逻辑电平可使能器件；低逻辑电平可禁用器件。"}, {"pin_number": "A2, A3", "pin_name": "VIN", "pin_description": "功率级电源电压"}, {"pin_number": "B1", "pin_name": "VSEL", "pin_description": "此引脚选择哪个 VOUT 寄存器处于激活状态。当此引脚上施加低逻辑电平时，VOUT1 寄存器设置输出电压。当此引脚上施加高逻辑电平时，VOUT2 寄存器设置输出电压。"}, {"pin_number": "B2, B3", "pin_name": "LX1", "pin_description": "电感器连接"}, {"pin_number": "C1", "pin_name": "AGND", "pin_description": "模拟接地"}, {"pin_number": "C2, C3", "pin_name": "GND", "pin_description": "电源接地"}, {"pin_number": "D1", "pin_name": "SCL", "pin_description": "I2C 串行接口时钟。使用电阻器或电流源将此引脚上拉至 I2C 总线电压。"}, {"pin_number": "D2, D3", "pin_name": "LX2", "pin_description": "电感器连接"}, {"pin_number": "E1", "pin_name": "SDA", "pin_description": "I2C 串行接口数据。使用电阻器或电流源将此引脚上拉至 I2C 总线电压。"}, {"pin_number": "E2, E3", "pin_name": "VOUT", "pin_description": "转换器输出"}]}], "datasheet_cn": "ZHCSKE6C-JULY 2019-REVISED FEBRUARY 2020", "datasheet_en": "SLVSEK4", "family_comparison": "TPS63810为预设启动电压版本，TPS63811为启动时可编程版本。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.2V", "max_output_voltage": "5.2V", "min_output_voltage": "1.8V", "max_output_current": "2.5A", "max_switch_frequency": "2.6MHz", "quiescent_current": "13µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "4.3A", "operation_mode": "同步", "pass_through_mode": "False", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "自动重启", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "自动重启", "output_under_voltage_protection": "No", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "电流限制", "over_temperature_protection": "自动重启", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.625", "length": "2.3", "width": "1.4", "type": "OPTION", "pin_count": "2"}]}]