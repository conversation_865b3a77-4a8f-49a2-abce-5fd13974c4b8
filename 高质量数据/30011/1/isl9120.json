[{"part_number": "ISL9120IRTAZ", "manufacturer": "RENESAS", "country": "日本", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "ISL9120IR Compact High Efficiency Low Power Buck-Boost Regulator", "features": ["Accepts input voltages above or below regulated output voltage", "Automatic bypass mode functionality", "Automatic and seamless transitions between buck and boost modes", "Input voltage range: 1.8V to 5.5V", "Selectable forced bypass power saving mode", "Adaptive multilevel current limit scheme to optimize efficiency at low and high currents", "Output current: up to 800mA (VIN = 2.5V, VOUT = 3.3V)", "High efficiency: up to 98%", "41µA quiescent current maximizes light-load efficiency", "Fully protected for over-temperature and undervoltage", "Small 3mmx3mm 12 Ld TQFN package"], "description": "The ISL9120IR is a highly integrated buck-boost switching regulator that accepts input voltages either above or below the regulated output voltage. This regulator automatically transitions between buck and boost modes without significant output disturbance. The ISL9120IR also has automatic bypass functionality for when the input voltage is generally within 1% to 2% of the output voltage, there will be a direct bypass connection between the VIN and VOUT pins. In addition to the automatic bypass functionality, the ISL9120IR also has a forced bypass functionality with the use of the BYPS pin. This device is capable of delivering up to 800mA of output current (VIN = 2.5V, VOUT = 3.3V) and provides excellent efficiency due to its adaptive current limit pulse frequency modulation (PFM) control architecture. The ISL9120IR is designed for stand-alone applications and supports a 3.3V fixed output voltage or variable output voltages with an external resistor divider. The forced bypass power saving mode can be chosen if voltage regulation is not required. The device consumes less than 3.5µA of current over the operating temperature range in the forced bypass mode. The ISL9120IR requires only a single inductor and very few external components. Power supply solution size is minimized by a 3mmx3mm 12 Ld TQFN package.", "applications": ["Smartphones and tablets", "Portable consumer and wearable devices"], "ordering_information": [{"part_number": "ISL9120IRTAZ", "order_device": "ISL9120IRTAZ", "package_type": "12 Ld 3x3 TQFN", "package_drawing_code": "L12.3x3A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85", "carrier_description": "Ta<PERSON> and Reel"}], "pin_function": [{"product_part_number": "ISL9120IR", "package_type": "12 Ld TQFN", "pins": [{"pin_number": "1, 12", "pin_name": "LX2", "pin_description": "Inductor connection, output side."}, {"pin_number": "2", "pin_name": "PGND", "pin_description": "Power ground for high switching current."}, {"pin_number": "3, 4", "pin_name": "LX1", "pin_description": "Inductor connection, input side."}, {"pin_number": "5, 6", "pin_name": "VIN", "pin_description": "Power supply input. Range: 1.8V to 5.5V. Connect 1x 10µF capacitor to PGND."}, {"pin_number": "7", "pin_name": "BYPS", "pin_description": "Bypass mode enable pin. HIGH for bypass mode. LOW for buck-boost mode."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Analog ground pin."}, {"pin_number": "9", "pin_name": "EN", "pin_description": "Logic input, drive HIGH to enable device."}, {"pin_number": "10", "pin_name": "FB", "pin_description": "Voltage feedback pin."}, {"pin_number": "11", "pin_name": "VOUT", "pin_description": "Buck-boost output. Connect 22µF or 47µF capacitor to PGND."}, {"pin_number": "EPAD", "pin_name": "GND", "pin_description": "Exposed Pad, connect to GND."}]}], "datasheet_cn": null, "datasheet_en": "FN8743 Rev 1.00, February 2, 2016", "family_comparison": "Key differences between ISL9120 and ISL9120IR: Both have Buck-boost regulation and Bypass functionality. The package for ISL9120 is 1.41x1.41mm 9-Bump WLCSP, while for ISL9120IR it is 3x3mm 12Ld TQFN.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.2V", "min_output_voltage": "1V", "max_output_current": "0.8A", "max_switch_frequency": "PFM控制，无固定频率", "quiescent_current": "41µA", "high_side_mosfet_resistance": "63mΩ", "low_side_mosfet_resistance": "63mΩ", "over_current_protection_threshold": "2A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "True", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±4%", "output_reference_voltage": "0.8V", "loop_control_mode": "迟滞模式控制 (PFM)"}, "package": [{"type": "Outline", "pitch": "0.5", "height": "0.75", "width": "3", "length": "12.3", "pin_count": "2"}]}, {"part_number": "ISL9120IRTNZ", "manufacturer": "RENESAS", "country": "日本", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "ISL9120IR Compact High Efficiency Low Power Buck-Boost Regulator", "features": ["Accepts input voltages above or below regulated output voltage", "Automatic bypass mode functionality", "Automatic and seamless transitions between buck and boost modes", "Input voltage range: 1.8V to 5.5V", "Selectable forced bypass power saving mode", "Adaptive multilevel current limit scheme to optimize efficiency at low and high currents", "Output current: up to 800mA (VIN = 2.5V, VOUT = 3.3V)", "High efficiency: up to 98%", "41µA quiescent current maximizes light-load efficiency", "Fully protected for over-temperature and undervoltage", "Small 3mmx3mm 12 Ld TQFN package"], "description": "The ISL9120IR is a highly integrated buck-boost switching regulator that accepts input voltages either above or below the regulated output voltage. This regulator automatically transitions between buck and boost modes without significant output disturbance. The ISL9120IR also has automatic bypass functionality for when the input voltage is generally within 1% to 2% of the output voltage, there will be a direct bypass connection between the VIN and VOUT pins. In addition to the automatic bypass functionality, the ISL9120IR also has a forced bypass functionality with the use of the BYPS pin. This device is capable of delivering up to 800mA of output current (VIN = 2.5V, VOUT = 3.3V) and provides excellent efficiency due to its adaptive current limit pulse frequency modulation (PFM) control architecture. The ISL9120IR is designed for stand-alone applications and supports a 3.3V fixed output voltage or variable output voltages with an external resistor divider. The forced bypass power saving mode can be chosen if voltage regulation is not required. The device consumes less than 3.5µA of current over the operating temperature range in the forced bypass mode. The ISL9120IR requires only a single inductor and very few external components. Power supply solution size is minimized by a 3mmx3mm 12 Ld TQFN package.", "applications": ["Smartphones and tablets", "Portable consumer and wearable devices"], "ordering_information": [{"part_number": "ISL9120IRTNZ", "order_device": "ISL9120IRTNZ", "package_type": "12 Ld 3x3 TQFN", "package_drawing_code": "L12.3x3A", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85", "carrier_description": "Ta<PERSON> and Reel"}], "pin_function": [{"product_part_number": "ISL9120IR", "package_type": "12 Ld TQFN", "pins": [{"pin_number": "1, 12", "pin_name": "LX2", "pin_description": "Inductor connection, output side."}, {"pin_number": "2", "pin_name": "PGND", "pin_description": "Power ground for high switching current."}, {"pin_number": "3, 4", "pin_name": "LX1", "pin_description": "Inductor connection, input side."}, {"pin_number": "5, 6", "pin_name": "VIN", "pin_description": "Power supply input. Range: 1.8V to 5.5V. Connect 1x 10µF capacitor to PGND."}, {"pin_number": "7", "pin_name": "BYPS", "pin_description": "Bypass mode enable pin. HIGH for bypass mode. LOW for buck-boost mode."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Analog ground pin."}, {"pin_number": "9", "pin_name": "EN", "pin_description": "Logic input, drive HIGH to enable device."}, {"pin_number": "10", "pin_name": "FB", "pin_description": "Voltage feedback pin."}, {"pin_number": "11", "pin_name": "VOUT", "pin_description": "Buck-boost output. Connect 22µF or 47µF capacitor to PGND."}, {"pin_number": "EPAD", "pin_name": "GND", "pin_description": "Exposed Pad, connect to GND."}]}], "datasheet_cn": null, "datasheet_en": "FN8743 Rev 1.00, February 2, 2016", "family_comparison": "Key differences between ISL9120 and ISL9120IR: Both have Buck-boost regulation and Bypass functionality. The package for ISL9120 is 1.41x1.41mm 9-Bump WLCSP, while for ISL9120IR it is 3x3mm 12Ld TQFN.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "3.3V", "min_output_voltage": "3.3V", "max_output_current": "0.8A", "max_switch_frequency": "PFM控制，无固定频率", "quiescent_current": "41µA", "high_side_mosfet_resistance": "63mΩ", "low_side_mosfet_resistance": "63mΩ", "over_current_protection_threshold": "2A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "True", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±4%", "output_reference_voltage": "不适用", "loop_control_mode": "迟滞模式控制 (PFM)"}, "package": [{"type": "Outline", "pitch": "0.5", "height": "0.75", "width": "3", "length": "12.3", "pin_count": "2"}]}]