{"part_number": "TPS63900", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS63900 1.8V 到 5.5V, 75nA IQ 降压/升压转换器, 具有输入电流限制和 DVS", "features": ["输入电压范围: 1.8V 至 5.5V", "输出电压范围: 1.8V 至 5V (100mV 阶跃)", "可使用外部电阻器进行编程", "SEL 引脚用于在两个输出电压预设之间切换", "VI ≥ 2.0V、VO = 3.3V 时, 输出电流 > 400mA (峰值开关电流限制典型值 1.45A)", "可堆叠: 并联多个器件以获得更高的输出电流", "负载电流为 10µA 时, 效率 > 90%", "静态电流为 75nA", "60nA 关断电流", "单模式运行", "无需在降压、降压/升压和升压模式之间转换", "低输出波纹", "出色的瞬态性能", "安全、可靠运行的特性", "集成软启动", "可编程输入电流限制, 具有八个设置 (1mA 至 100mA 和无限制)", "输出短路和过热保护", "21mm² 的微小解决方案尺寸", "小型 2.2µH 电感器, 单个 22µF 输出电容器", "10 引脚、2.5mm × 2.5mm、0.5mm 间距 WSON 封装"], "description": "TPS63900 器件是一款具有超低静态电流 (典型值为 75nA) 的高效同步降压/升压转换器。该器件具有 32 个用户可编程的输出电压设置, 范围为 1.8V 至 5V。动态电压调节特性使各项应用可于运行期间在两个输出电压之间进行切换; 例如, 在待机运行期间, 可通过降低系统电源电压来降低功耗。凭借其宽电源电压范围和可编程的输入电流限制 (1mA 至 100 mA 和无限制), 该器件非常适合与 3 节碱性电池、1 节锂二氧化锰 (Li-MnO2) 或 1 节锂亚硫酰氯 (Li-SOCl2) 等各种一次电池以及二次电池搭配使用。高输出电流功能支持 sub-1GHz、BLE、LoRa、wM-Bus 和 NB-IoT 等常用射频标准。", "applications": ["智能仪表和传感器节点", "电子智能锁", "医疗传感器贴片和患者监护仪", "可穿戴电子产品", "资产跟踪", "工业物联网 (智能传感器)/窄带物联网"], "ordering_information": [{"part_number": "TPS63900", "order_device": "TPS63900DSKR", "package_type": "SON", "package_drawing_code": "DSK0010A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63900", "order_device": "TPS63900DSKR.A", "package_type": "SON", "package_drawing_code": "DSK0010A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63900", "order_device": "TPS63900DSKRG4.A", "package_type": "SON", "package_drawing_code": "DSK0010A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63900", "package_type": "WSON", "pins": [{"pin_number": "1", "pin_name": "EN", "pin_description": "Device enable. A high level applied to this pin enables the device and a low level disables it. It must not be left open."}, {"pin_number": "2", "pin_name": "SEL", "pin_description": "Output voltage select. Selects Vo(2) when a high level is applied to this pin. Selects Vo(1) when a low level is applied to this pin. It must not be left open."}, {"pin_number": "3", "pin_name": "CFG1", "pin_description": "Configuration pin 1. Connect a resistor between this pin and ground to set Vo(2) and input current limit, must not be left open."}, {"pin_number": "4", "pin_name": "CFG2", "pin_description": "Configuration pin 2. Connect a resistor between this pin and ground to set Vo(2) and input current limit. Must not be left open."}, {"pin_number": "5", "pin_name": "CFG3", "pin_description": "Configuration pin 3. Connect a resistor between this pin and ground to set Vo(1). Must not be left open."}, {"pin_number": "6", "pin_name": "VOUT", "pin_description": "Output voltage"}, {"pin_number": "7", "pin_name": "LX2", "pin_description": "Switching node of the boost stage"}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Ground"}, {"pin_number": "9", "pin_name": "LX1", "pin_description": "Switching node of the buck stage"}, {"pin_number": "10", "pin_name": "VIN", "pin_description": "Supply voltage"}, {"pin_number": "Thermal Pad", "pin_name": "Thermal Pad", "pin_description": "Connect this pin to ground for correct operation."}]}], "datasheet_cn": "ZHCSL70D", "datasheet_en": "SLVSET3", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5V", "min_output_voltage": "1.8V", "max_output_current": "0.4A", "max_switch_frequency": "未找到", "quiescent_current": "0.075µA", "high_side_mosfet_resistance": "155mΩ", "low_side_mosfet_resistance": "155mΩ", "over_current_protection_threshold": "1.45A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "不适用", "loop_control_mode": "梯形电流模式"}, "package": [{"type": "Outline", "pitch": "0.5", "height": "0.8", "length": "2.5", "width": "2.5", "pin_count": "1"}]}