{"part_number": "ISL78263", "manufacturer": "Renesas", "country": "日本", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "DC-DC控制器", "part_number_title": "Automotive 42V Dual Synchronous Boost and Low-Iq Buck Controllers with Integrated Drivers", "features": ["VIN Operating range: 2.1 to 42V", "Low quiescent current: 6µA typical, buck channel", "Switching frequency: 200kHz to 2.2MHz", "Boost frequency at 1x or 0.2x the buck frequency", "Dropout mode (buck) for high duty-cycle operation", "25ns on-times for low duty-cycle operation", "External synchronization", "Programmable spread spectrum clocking", "2A Sourcing / 3A sinking MOSFET drivers", "Boot UV and programmable boot refresh time", "Extensive protection mechanisms for OV/UV/OC/OT"], "description": "The ISL78263 offers synchronous boost and buck controllers. The boost controller operates either as an independent channel or as a pre-boost function that supplies the buck controller. The low-Iq buck controller uses only 6µA quiescent current, allowing it to support low-power, always-on operation. Both controllers support wide duty-cycles for switching frequencies from 200kHz to 2.2MHz. Both devices can be synchronized to an external clock and offer programmable spread spectrum clocking to mitigate EMI. In pre-boost buck mode, the boost of the ISL78263 can be set to activate during falling VIN transients and continues to operate with an input voltage down to 2.1V. This allows the buck output to maintain regulation, even as the VIN voltage falls below its output. To support the increase in input current that occurs when VIN falls, the ISL78263 integrates robust MOSFET drivers to allow the use of multiple FETs in parallel. ISL78263 is qualified to AEC-Q100 Grade 1 and is specified to operate across an ambient temperature range of -40°C to 125°C. The device is available in a 5mmx5mm, 32 Ld WFQFN (Wettable Flank) package. The features of the ISL78263 make it ideally suited as a front-end regulator for automotive systems that must maintain always-on operation and support severe cold-cranking transients.", "applications": ["Automotive battery supplied application", "In cabin systems", "ADAS: Advanced Driver Assist Systems", "Start-stop protected systems (such as head unit, cluster, e-Mirror)"], "ordering_information": [{"part_number": "ISL78263", "order_device": "ISL78263ARZ", "package_type": "QFN", "package_drawing_code": "L32.5x5H", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ISL78263", "order_device": "ISL78263ARZ-T", "package_type": "QFN", "package_drawing_code": "L32.5x5H", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ISL78263", "order_device": "ISL78263ARZ-T7A", "package_type": "QFN", "package_drawing_code": "L32.5x5H", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "ISL78263", "package_type": "WFQFN", "pins": [{"pin_number": "1", "pin_name": "CNT2", "pin_description": "This pin sources a current at startup and reads the voltage across a resistor to ground to program internal boot refresh on-time of the low-side switch for 180ns or 360ns, and also allows selection of Channel 2 boost frequency as either 1xfsw or 1/5xfsw of channel 1 buck (See Table 5)."}, {"pin_number": "2", "pin_name": "FB1", "pin_description": "Feedback pin for Channel 1 buck. For fixed output voltages of 5.0V or 3.3V, this pin is connected directly to the output of Channel 1 buck. For adjustable output, this pin connects to a resistive divider from Channel 1 output to ground, and the FB1 voltage is regulated to 0.8V."}, {"pin_number": "3", "pin_name": "VSEL", "pin_description": "This pin sources a current at startup and reads the voltage across a resistor to ground to program the Channel 1 buck output to a fixed voltage of 5.0V or 3.3 V, or as an adjustable voltage in the range of 0.8V to 5.0V. For each of these selections, the boost converter can be configured as Cold Crank or Individual Boost operation. (See Table 3)."}, {"pin_number": "4", "pin_name": "EN2", "pin_description": "Enable control pin of Channel 2 with a logic high voltage enabling operation of Channel 2."}, {"pin_number": "5", "pin_name": "FB2", "pin_description": "Feedback pin for Channel 2 boost regulator with adjustable output. This pin connects to a resistive divider from Channel 2 output to ground, and the FB2 voltage is regulated to 0.8V."}, {"pin_number": "6", "pin_name": "COMP1", "pin_description": "Loop compensation pin for Channel 1 with a resistor/capacitor network connected to ground to provide control loop compensation for Channel 1 buck regulator."}, {"pin_number": "7", "pin_name": "COMP2", "pin_description": "Loop compensation pin for Channel 2 with a resistor/capacitor network connected to ground to provide control loop compensation for Channel 2 boost regulator."}, {"pin_number": "8", "pin_name": "RT", "pin_description": "A resistor from RT to GND programs the switching frequency for Channel 1 and Channel 2, with Channel 2 shifted 180° in phase from Channel 1 to minimize input ripple current. This pin is pulled to ground while in ECM and is otherwise 0.5V."}, {"pin_number": "9", "pin_name": "ISEN1N", "pin_description": "The output current sense pin connected to the negative terminal of the current sense resistor, also connected to the output voltage of Channel 1."}, {"pin_number": "10", "pin_name": "ISEN1P", "pin_description": "The output current sense pin connected to the junction of the positive terminal of the current sense resistor and the power inductor of Channel 1."}, {"pin_number": "11", "pin_name": "SGND", "pin_description": "Analog GND for the IC, connected to the PGND pin in the top copper trace under the IC."}, {"pin_number": "12", "pin_name": "PGOOD1", "pin_description": "Power-good pin for Channel 1 with an open-drain output, producing a low output if the Channel 1 output is not within ±7% (typical) of the programmed output voltage, and a logic high output if the output is within regulation."}, {"pin_number": "13", "pin_name": "SYNC", "pin_description": "Connect SYNC to an external clock in the range of 200kHz to 2.4MHz to synchronize the internal clock with operation in FCCM. Connect to VCC to force the part into Fixed frequency Continuous Conduction Mode (FCCM) operation using the internal oscillator. Connect to GND to allow the controller to automatically switch between Continuous Conduction Mode, and Diode Emulation Mode (DEM), or ECM mode depending on load current level. In DEM and CCM the device will use the internal oscillator programmed by RT pin. The pin can be switched during operation (VCC to GND, or GND to VCC) to change the mode of operation."}, {"pin_number": "14", "pin_name": "CNT", "pin_description": "This pin sources a current at startup and reads the voltage across a resistor to ground to program the spread spectrum (ON/OFF, and frequency variation) and dead time (See Table 4 on page 19)."}, {"pin_number": "15", "pin_name": "BOOT1", "pin_description": "Provides connection point for a ceramic boot capacitor providing high-side gate voltage supply for Channel 1. The capacitor is charged through an external diode connected to VCC through an R-C filter."}, {"pin_number": "16", "pin_name": "HS1", "pin_description": "The output of Channel 1 high-side MOSFET gate driver."}, {"pin_number": "17", "pin_name": "LX1", "pin_description": "Connected to the Channel 1 switch node, providing the return path for the high-side MOSFET gate driver back to BOOT1."}, {"pin_number": "18", "pin_name": "LS1", "pin_description": "The output of Channel 1 low-side MOSFET gate driver swinging between VCC and GND."}, {"pin_number": "19", "pin_name": "VCC", "pin_description": "Bias supply (5V typical) for the IC and MOSFET gate drivers, and should be decoupled with a ceramic capacitor of 10µF. This pin is supplied by internal LDO during start-up and can be powered from EXTSUP after initial start-up, using the automatic switchover function."}, {"pin_number": "20", "pin_name": "EXTSUP", "pin_description": "EXTSUP accepts external bias input of 5V typical that can be supplied from Channel 1 output of 5.0V, or an independent supply derived from other sources. The external bias should not be applied until VIN has exceeded initial start-up voltage; however, a voltage such that EXTSUP - VIN ≤ 0.5V would be allowed."}, {"pin_number": "21", "pin_name": "PGND", "pin_description": "Connection point for power ground of the switching circuits for Channel 1 and Channel 2, and serves as the return path for the low-side MOSFET gate drive."}, {"pin_number": "22", "pin_name": "LS2", "pin_description": "The output of Channel 2 low-side MOSFET gate driver swinging between VCC and GND."}, {"pin_number": "23", "pin_name": "LX2", "pin_description": "Connected to the Channel 2 switch node, providing the return path for the high-side MOSFET gate driver back to BOOT2."}, {"pin_number": "24", "pin_name": "HS2", "pin_description": "The output of Channel 2 high-side MOSFET gate driver."}, {"pin_number": "25", "pin_name": "BOOT2", "pin_description": "Provides connection point for a ceramic boot capacitor providing high-side gate voltage supply for Channel 2. The capacitor is charged through an external diode connected to VCC."}, {"pin_number": "26", "pin_name": "VIN", "pin_description": "Connected to the high voltage input supply for the buck regulators, and is normally supplied from a battery. This pin is decoupled using a 0.1µF or larger ceramic capacitor."}, {"pin_number": "27", "pin_name": "ISEN2N", "pin_description": "Input current sense pin of Channel 2 boost, connected to the negative terminal of the current sense resistor and the inductor."}, {"pin_number": "28", "pin_name": "ISEN2P", "pin_description": "Input current sense pin connected to the positive terminal of the current sense resistor at the input of the Channel 2 boost."}, {"pin_number": "29", "pin_name": "EN1", "pin_description": "Enable control pin of Channel 1 with a logic high voltage enabling operation of Channel 1. For initial start-up, this pin may be connected to the VIN supply through a 100kΩ resistor."}, {"pin_number": "30", "pin_name": "TERM", "pin_description": "This is the GND disconnect terminal for the bottom of the buck feedback resistor. The external resistive divider connected to the TERM pin should have sufficient resistance to limit the current into the TERM pin to < 25mA at maximum VBOOST."}, {"pin_number": "31", "pin_name": "NC", "pin_description": "Not internally connected; leave unconnected or tie to GND."}, {"pin_number": "32", "pin_name": "PGOOD2", "pin_description": "Power-good pin for Channel 2 with an open-drain output, producing a low output if the Channel 2 output is not within ±7% (typical) of the programmed output voltage, and a logic high output if the output is within regulation."}, {"pin_number": "33", "pin_name": "EPAD", "pin_description": "The bottom pad of the IC, to be connected to PGND and SGND under the IC. Connect to internal PCB GND layers using multiple vias."}]}], "datasheet_cn": "未找到", "datasheet_en": "R33DS0003EU0200", "family_comparison": "Part of the ISL7826x family. Key difference is Channel 2: ISL78263 has a Boost converter, ISL78264 has a Buck converter.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 2, "max_input_voltage": "42V", "min_input_voltage": "2.1V", "max_output_voltage": "40V", "min_output_voltage": "1V", "max_output_current": "由外部元件决定", "max_switch_frequency": "2.2MHz", "quiescent_current": "6µA", "high_side_mosfet_resistance": "不适用(外部MOSFET)", "low_side_mosfet_resistance": "不适用(外部MOSFET)", "over_current_protection_threshold": "可调", "operation_mode": "同步", "output_voltage_config_method": "Fixed and Adjustable", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "ECM, DEM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "True", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "Outline", "length": "32.5", "width": "5", "pin_count": "1", "pitch": "0.5", "height": "0.85"}]}