{"part_number": "SM72441", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "MPPT控制器", "part_number_title": "Programmable Maximum Power Point Tracking Controller for Photovoltaic Solar Panels", "features": ["Renewable Energy Grade", "Programmable Maximum Power Point Tracking", "Photovoltaic Solar Panel Voltage and Current Diagnostic", "Single Inductor Four Switch Buck-boost Converter Control", "VOUT Overvoltage Protection", "Over-Current Protection"], "description": "The SM72441 is a programmable MPPT controller capable of controlling four PWM gate drive signals for a 4-switch buck-boost converter. Along with SM72295 (Photovoltaic Full Bridge Driver) it creates a solution for an MPPT configured DC-DC converter with efficiencies up to 98.5%. Integrated into the chip is an 8-channel, 12 bit A/D converter used to sense input and output voltage and current, as well as board configuration. Externally programmable values include maximum output voltage and current as well as different settings on slew rate, and soft-start.", "applications": ["Photovoltaic Solar Panels"], "ordering_information": [{"part_number": "SM72441", "order_device": "SM72441MT/NOPB", "package_type": "TSSOP", "package_drawing_code": "PW0028A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "105"}, {"part_number": "SM72441", "order_device": "SM72441MT/NOPB.A", "package_type": "TSSOP", "package_drawing_code": "PW0028A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "105"}, {"part_number": "SM72441", "order_device": "SM72441MTE/NOPB", "package_type": "TSSOP", "package_drawing_code": "PW0028A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "105"}, {"part_number": "SM72441", "order_device": "SM72441MTE/NOPB.A", "package_type": "TSSOP", "package_drawing_code": "PW0028A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "105"}, {"part_number": "SM72441", "order_device": "SM72441MTX/NOPB", "package_type": "TSSOP", "package_drawing_code": "PW0028A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "105"}, {"part_number": "SM72441", "order_device": "SM72441MTX/NOPB.A", "package_type": "TSSOP", "package_drawing_code": "PW0028A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "105"}], "pin_function": [{"product_part_number": "SM72441", "package_type": "TSSOP-28", "pins": [{"pin_number": "1", "pin_name": "RST", "pin_description": "Active low signal. External reset input signal to the digital circuit."}, {"pin_number": "2", "pin_name": "NC1", "pin_description": "No Connect. This pin should be grounded."}, {"pin_number": "3", "pin_name": "VDDD", "pin_description": "Digital supply voltage. This pin should be connected to a 5V supply, and bypassed to VSSD with a 0.1uF monolithic ceramic capacitor."}, {"pin_number": "4", "pin_name": "VSSD", "pin_description": "Digital ground. The ground return for the digital supply and signals."}, {"pin_number": "5", "pin_name": "NC2", "pin_description": "No Connect. This pin should be pulled up to the 5V supply using 10k resistor."}, {"pin_number": "6", "pin_name": "NC3", "pin_description": "No Connect. This pin should be grounded using a 10k resistor."}, {"pin_number": "7", "pin_name": "NC4", "pin_description": "No Connect. This pin should be grounded using a 10k resistor."}, {"pin_number": "8", "pin_name": "NC5", "pin_description": "No Connect. This pin should be pulled up to 5V supply using 10k resistor."}, {"pin_number": "9", "pin_name": "NC6", "pin_description": "No Connect. This pin should be pulled up to 5V supply using 10k resistor."}, {"pin_number": "10", "pin_name": "NC7", "pin_description": "No Connect. This pin should be grounded."}, {"pin_number": "11", "pin_name": "LED", "pin_description": "LED pin outputs a pulse during normal operation."}, {"pin_number": "12", "pin_name": "VDDA", "pin_description": "Analog supply voltage. This voltage is also used as the reference voltage. This pin should be connected to a 5V supply, and bypassed to VSSA with a 1uF and 0.1uF monolithic ceramic capacitor."}, {"pin_number": "13", "pin_name": "VSSA", "pin_description": "Analog ground. The ground return for the analog supply and signals."}, {"pin_number": "14", "pin_name": "A0", "pin_description": "A/D Input Channel 0. Connect a resistor divider to 5V supply to set the maximum output voltage. Please refer to application section for more information on setting the resistor value."}, {"pin_number": "15", "pin_name": "AVIN", "pin_description": "A/D Input to sense input voltage."}, {"pin_number": "16", "pin_name": "A2", "pin_description": "A/D Input Channel 2. Connect a resistor divider to 5V supply to set MPPT update rate. Please refer to application section for more information on setting the resistor value."}, {"pin_number": "17", "pin_name": "AVOUT", "pin_description": "A/D Input to sense the output voltage."}, {"pin_number": "18", "pin_name": "A4", "pin_description": "A/D Input Channel 4. Connect a resistor divider to 5V supply to set the maximum output current. Please refer to application section for more information on setting the resistor value."}, {"pin_number": "19", "pin_name": "AIIN", "pin_description": "A/D Input to sense input current."}, {"pin_number": "20", "pin_name": "A6", "pin_description": "A/D Input Channel 6. Connect a resistor divider to 5V supply to set the maximum output voltage slew rate. Please refer to application section for more information on setting the resistor value."}, {"pin_number": "21", "pin_name": "AIOUT", "pin_description": "A/D Input to sense the output current."}, {"pin_number": "22", "pin_name": "NC8", "pin_description": "No Connect. This pin should be grounded using a 10k resistor."}, {"pin_number": "23", "pin_name": "NC9", "pin_description": "No Connect. This pin should be connected with 150k pull-up resistor to 5V supply."}, {"pin_number": "24", "pin_name": "LIB", "pin_description": "Low side boost PWM output."}, {"pin_number": "25", "pin_name": "HIB", "pin_description": "High side boost PWM output."}, {"pin_number": "26", "pin_name": "HIA", "pin_description": "High side buck PWM output."}, {"pin_number": "27", "pin_name": "LIA", "pin_description": "Low side buck PWM output."}, {"pin_number": "28", "pin_name": "OVP", "pin_description": "Overvoltage Protection Pin. Active Low. SM72441 will reset once voltage on this pin drops below its threshold voltage."}]}], "datasheet_cn": "未找到", "datasheet_en": "SM72441 SNOSB64G", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "可编程", "min_input_voltage": "可编程", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "可调", "max_switch_frequency": "0.21MHz", "quiescent_current": "15000µA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "外部电阻分压", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Standby Mode", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "No", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±0.1%", "output_reference_voltage": "可编程", "loop_control_mode": "未找到"}, "package": [{"type": "OPTION", "length": "9.7", "width": "4.4", "pin_count": "2", "pitch": "0.65", "height": "1.2"}]}