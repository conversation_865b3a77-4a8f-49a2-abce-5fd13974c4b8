{"part_number": "TPIC74101-Q1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPIC74101-Q1 降压和升压开关模式稳压器", "features": ["符合汽车应用要求", "具有符合AEC-Q100的下列结果:", "器件人体模型 (HBM) 静电放电 (ESD) 分类等级 1B: 引脚 7 (L2), 引脚 8 (VOUT), 引脚 9 (5Vg)", "器件 HBM ESD 分类等级 2: 引脚 1–6 以及引脚 10–20", "器件充电器件模型 (CDM) ESD 分类等级 C4B", "开关模式稳压器", "5V±2%, 降压模式", "5V±3%, 低功耗、升压或升压/降压切换模式", "开关频率: 380kHz (典型值)", "输入工作范围: 1.5V 至 40V (Vdriver)", "1A 负载电流能力", "200mA 负载电流能力, 低至 2V 输入 (Vdriver)时", "120mA 负载电流能力, 低至 1.5V 输入 (Vdriver)时", "使能功能", "低功耗工作模式", "开关式 5V 稳压输出 5Vg, 具有限流功能", "可编程的转换率和频率调制, 应对电磁干扰 (EMI) 问题", "复位功能, 具有去毛刺脉冲定时器和可编程延迟", "警报功能, 用于欠压检测和指示", "耐热增强型封装, 可实现高效的热管理"], "description": "TPIC74101-Q1 是一款开关模式稳压器, 通过集成开关实现电压模式控制。此器件具有宽输入电压范围, 可借助外部元件 (LC 组合) 将输出稳压至 5V±2%。\nTPIC74101-Q1 具有复位功能, 可检测并指示 5V 输出电源轨何时超出规定容限。此复位延迟可通过 RESET 引脚上的外部定时电容进行编程。此外, 该器件还具备警报 (AOUT) 功能, 当输入电源轨 Vdriver 低于预定值 (通过 AIN 引脚设置) 时会激活此功能。\nTPIC74101-Q1 提供了一种频率调制方案, 可最大程度降低 EMI。该器件通过时钟调制器对开关频率进行调制, 以降低频段中的干扰能量。\n5Vg 输出是一种开关式 5V 稳压输出, 其内置限流功能, 当通过电源线为容性负载供电时, 可防止 RESET 引脚被置为有效。此功能通过 5Vg_ENABLE 引脚进行控制。如果此输出 (5Vg 输出) 接地短路, 则会进入斩波模式以进行自我保护。但在此故障期间, VOUT 上的输出纹波电压会有所增加。", "applications": ["车用信息娱乐 & 仪表板", "车身电子装置"], "ordering_information": [{"part_number": "TPIC74101-Q1", "order_device": "TPIC74101QPWPRQ1", "package_type": "HTSSOP", "package_drawing_code": "PWP (R-PDSO-G20)", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPIC74101-Q1", "order_device": "TPIC74101QPWPRQ1.A", "package_type": "HTSSOP", "package_drawing_code": "PWP (R-PDSO-G20)", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPIC74101-Q1", "package_type": "HTSSOP", "pins": [{"pin_number": "1", "pin_name": "SCR1", "pin_description": "Programmable slew-rate control"}, {"pin_number": "2", "pin_name": "Cboot2", "pin_description": "External bootstrap capacitor"}, {"pin_number": "3", "pin_name": "Cboot1", "pin_description": "External bootstrap capacitor"}, {"pin_number": "4", "pin_name": "Vdriver", "pin_description": "Input voltage source"}, {"pin_number": "5", "pin_name": "L1", "pin_description": "Inductor input (an external <PERSON><PERSON><PERSON><PERSON> diode to GND must be connected to L1)"}, {"pin_number": "6", "pin_name": "PGND", "pin_description": "Power ground"}, {"pin_number": "7", "pin_name": "L2", "pin_description": "Inductor output"}, {"pin_number": "8", "pin_name": "VOUT", "pin_description": "5-V regulated output"}, {"pin_number": "9", "pin_name": "5Vg", "pin_description": "Switched 5-V supply"}, {"pin_number": "10", "pin_name": "AIN", "pin_description": "Programmable alarm setting"}, {"pin_number": "11", "pin_name": "CLP", "pin_description": "Low-power operation mode (digital input)"}, {"pin_number": "12", "pin_name": "RESET", "pin_description": "Reset function (open drain)"}, {"pin_number": "13", "pin_name": "AOUT", "pin_description": "Alarm output (open drain)"}, {"pin_number": "14", "pin_name": "REST", "pin_description": "Programmable reset timer delay"}, {"pin_number": "15", "pin_name": "Rmod", "pin_description": "Main switching frequency modulation setting to minimize EMI"}, {"pin_number": "16", "pin_name": "GND", "pin_description": "Ground"}, {"pin_number": "17", "pin_name": "Vlogic", "pin_description": "Supply decoupling output (may be used as a 5-V supply for logic-level inputs)"}, {"pin_number": "18", "pin_name": "ENABLE", "pin_description": "Switch-mode regulator enable/disable"}, {"pin_number": "19", "pin_name": "5Vg_ENABLE", "pin_description": "Switched 5-V voltage regulator output enable/disable"}, {"pin_number": "20", "pin_name": "SCR0", "pin_description": "Programmable slew-rate control"}]}], "datasheet_cn": "TPIC74101-Q1_ZHCS483A.pdf", "datasheet_en": "SLIS140", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "24V", "min_input_voltage": "1.5V", "max_output_voltage": "5V", "min_output_voltage": "5V", "max_output_current": "1A", "max_switch_frequency": "0.59MHz", "quiescent_current": "110µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "1.75A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "自动重启", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "间歇式", "over_temperature_protection": "热关断", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "不适用", "loop_control_mode": "电压模式"}, "package": [{"pitch": "0.65", "height": "1.2", "length": "6.5", "width": "4.4", "type": "OPTION", "pin_count": "18"}]}