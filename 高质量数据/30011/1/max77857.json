[{"part_number": "MAX77857A", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压-升压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "2.5V to 16V Input, 7A Switching Current High-Efficiency Buck-Boost Converter", "features": ["Wide Input Voltage Range: 2.5V to 16V", "Default Output Voltage: 5V with Internal Feedback Resistors, 3V to 15V with External Feedback Resistors", "I2C-Programmable Output Voltage after Startup: 4.5V to 15V with Internal Feedback Resistors, 3.0V to 15V with External Feedback Resistors", "Maximum Output Current: Buck Mode: Up to 6A, Boost Mode: Up to 4A (Boost Ratio ≤ 1.3)", "7A Typical Switching Current", "RSEL Configuration: I2C Interface Slave Address, Switching Current Limit Threshold, Internal/External Feedback Resistors", "I2C Programming: Output Voltage (DVS), Slew Rate of Output Voltage Change, Switching Current Limit Threshold, Switching Frequency, Forced PWM Mode Operation (FPWM), Power-OK (POK) Status and Fault Interrupt Masks", "Soft-Start", "Output Active Discharge", "Open-Drain Power-OK (POK) Monitor and Fault Condition Interrupt (MAX77857B/C Only)", "Protection Features: Undervoltage Lockout (UVLO), Overcurrent Protection (OCP), Overvoltage Protection (OVP), Thermal Shutdown (THS)", "High Density Interconnect (HDI) PCB not Required", "Available in 2.83mm x 2.03mm 35 WLP or 31 WLP or 3.5mm x 3.5mm 16 FC2QFN"], "description": "The MAX77857 is a high-efficiency, high-performance buck-boost converter targeted for systems requiring a wide input voltage range (2.5V to 16V). It features 7A switching current and can supply up to 6A output current in buck mode and up to 4A in boost mode (Boost Ratio ≤ 1.3). It operates in PWM mode and implements an automatic SKIP mode to improve light-load efficiency. The default output voltage is 5V when using internal feedback resistors. It can also be configured to any default output voltages between 3V and 15V when using external feedback resistors. The output voltage is adjustable dynamically through the I2C serial interface. The SEL pin allows a single external resistor to program four different I2C interface slave addresses, four different switching current limit thresholds, and selection between external/internal feedback resistors. The different switching current limit thresholds allow the use of lower profile and smaller external components that are optimized for a particular application. The use of external feedback resistors allows for a wider output voltage range and customizable output voltages at startup. The I2C serial interface is optional and allows for dynamically controlling the output voltage, slew rate of the output voltage change, switching-current limit threshold, switching frequency, and forced PWM mode operation. The I2C-programmed settings have priority over the RSEL decoded settings. The MAX77857 is available in a 2.83mm x 2.03mm, 35- bump and 31-bump wafer-level package (WLP) and a 3.5mm x 3.5mm, 16-lead Flip Chip QFN package (FC2QFN).", "applications": ["USB Power Delivery (USB-PD) OTG", "Qualcomm® Quick Charge™", "USB VBUS Supply and DRP (Dual Role Power) Ports", "DSLR, DSLR Lens", "Display Power", "Up to 3-Cell Li-Ion Battery Applications", "Notebook Computer, Tablet PC"], "ordering_information": [{"part_number": "MAX77857A", "order_device": "MAX77857AEWQ+T", "package_type": "WLP", "package_drawing_code": "21-100641", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MAX77857A", "package_type": "31 WLP", "pins": [{"pin_number": "A1", "pin_name": "SCL", "pin_description": "I2C Serial Interface Clock (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "B1", "pin_name": "SDA", "pin_description": "I2C Serial Interface Data (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "C1", "pin_name": "VL", "pin_description": "Low-Voltage Internal Supply. Powered from IN. Bypass to AGND with a 10V 2.2µF ceramic capacitor. Do not load this pin externally except for usage stated in the Non-I2C and Standalone Operation section."}, {"pin_number": "D1", "pin_name": "SEL", "pin_description": "Configuration Selection. Connect a resistor between SEL and AGND."}, {"pin_number": "E1", "pin_name": "FB", "pin_description": "Using Internal Feedback Resistors: Output Voltage Sense Input. Connect to the output at the point-of-load (close to output capacitor). Using External Feedback Resistors: Output Voltage Feedback Input. Connect to the center tap of an external resistor divider from OUT to AGND to set the output voltage."}, {"pin_number": "E2", "pin_name": "AGND", "pin_description": "Analog Ground. Connect to PGND on the PCB."}, {"pin_number": "E3", "pin_name": "BST2", "pin_description": "LX2 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST2 and LX2."}, {"pin_number": "D4, E4, E5, E6", "pin_name": "OUT", "pin_description": "Buck-Boost Output. Bypass to PGND with two 25V 22µF ceramic capacitors as close as possible."}, {"pin_number": "D5, D6, D7, E7", "pin_name": "LX2", "pin_description": "Buck-<PERSON><PERSON> Switching Node 2"}, {"pin_number": "C4, C5, C6, C7", "pin_name": "PGND", "pin_description": "Power Ground. Connect to AGND on the PCB."}, {"pin_number": "A7, B5, B6, B7", "pin_name": "LX1", "pin_description": "Buck-Boost Switching Node 1"}, {"pin_number": "A4, A5, A6, B4", "pin_name": "IN", "pin_description": "Buck-Boost Input. Bypass to PGND with two 25V 10µF ceramic capacitors as close as possible."}, {"pin_number": "A3", "pin_name": "BST1", "pin_description": "LX1 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST1 and LX1."}, {"pin_number": "B2, C2", "pin_name": "EN", "pin_description": "Active-High Buck-Boost Enable Input. Compatible with the VIO voltage domain. Pulldown internally with 0.1µA current source."}, {"pin_number": "A2", "pin_name": "VIO", "pin_description": "IO Voltage Supply. Bypass to AGND with a 6.3V 0.47µF ceramic capacitor. Registers are held in reset and regulator remains disabled when this pin's voltage is invalid."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77857 Rev 3; 9/22", "family_comparison": "MAX77857A is the base version without POKB/INTB and startup latch-off. MAX77857B adds the POKB/INTB feature. MAX77857C adds both POKB/INTB and startup latch-off features.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "2.5V", "max_output_voltage": "15V", "min_output_voltage": "3V", "max_output_current": "6A", "max_switch_frequency": "2.27MHz", "quiescent_current": "50µA", "high_side_mosfet_resistance": "20mΩ", "low_side_mosfet_resistance": "22mΩ", "over_current_protection_threshold": "7A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "SKIP", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Latch", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.333V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.64", "length": "2.828", "width": "2.028", "type": "Information", "pin_count": "2"}]}, {"part_number": "MAX77857B", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压-升压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "2.5V to 16V Input, 7A Switching Current High-Efficiency Buck-Boost Converter", "features": ["Wide Input Voltage Range: 2.5V to 16V", "Default Output Voltage: 5V with Internal Feedback Resistors, 3V to 15V with External Feedback Resistors", "I2C-Programmable Output Voltage after Startup: 4.5V to 15V with Internal Feedback Resistors, 3.0V to 15V with External Feedback Resistors", "Maximum Output Current: Buck Mode: Up to 6A, Boost Mode: Up to 4A (Boost Ratio ≤ 1.3)", "7A Typical Switching Current", "RSEL Configuration: I2C Interface Slave Address, Switching Current Limit Threshold, Internal/External Feedback Resistors", "I2C Programming: Output Voltage (DVS), Slew Rate of Output Voltage Change, Switching Current Limit Threshold, Switching Frequency, Forced PWM Mode Operation (FPWM), Power-OK (POK) Status and Fault Interrupt Masks", "Soft-Start", "Output Active Discharge", "Open-Drain Power-OK (POK) Monitor and Fault Condition Interrupt (MAX77857B/C Only)", "Protection Features: Undervoltage Lockout (UVLO), Overcurrent Protection (OCP), Overvoltage Protection (OVP), Thermal Shutdown (THS)", "High Density Interconnect (HDI) PCB not Required", "Available in 2.83mm x 2.03mm 35 WLP or 31 WLP or 3.5mm x 3.5mm 16 FC2QFN"], "description": "The MAX77857 is a high-efficiency, high-performance buck-boost converter targeted for systems requiring a wide input voltage range (2.5V to 16V). It features 7A switching current and can supply up to 6A output current in buck mode and up to 4A in boost mode (Boost Ratio ≤ 1.3). It operates in PWM mode and implements an automatic SKIP mode to improve light-load efficiency. The default output voltage is 5V when using internal feedback resistors. It can also be configured to any default output voltages between 3V and 15V when using external feedback resistors. The output voltage is adjustable dynamically through the I2C serial interface. The SEL pin allows a single external resistor to program four different I2C interface slave addresses, four different switching current limit thresholds, and selection between external/internal feedback resistors. The different switching current limit thresholds allow the use of lower profile and smaller external components that are optimized for a particular application. The use of external feedback resistors allows for a wider output voltage range and customizable output voltages at startup. The I2C serial interface is optional and allows for dynamically controlling the output voltage, slew rate of the output voltage change, switching-current limit threshold, switching frequency, and forced PWM mode operation. The I2C-programmed settings have priority over the RSEL decoded settings. The MAX77857 is available in a 2.83mm x 2.03mm, 35- bump and 31-bump wafer-level package (WLP) and a 3.5mm x 3.5mm, 16-lead Flip Chip QFN package (FC2QFN).", "applications": ["USB Power Delivery (USB-PD) OTG", "Qualcomm® Quick Charge™", "USB VBUS Supply and DRP (Dual Role Power) Ports", "DSLR, DSLR Lens", "Display Power", "Up to 3-Cell Li-Ion Battery Applications", "Notebook Computer, Tablet PC"], "ordering_information": [{"part_number": "MAX77857B", "order_device": "MAX77857BEWB+T", "package_type": "WLP", "package_drawing_code": "21-100367", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77857B", "order_device": "MAX77857BEFE+T", "package_type": "FC2QFN", "package_drawing_code": "21-100410", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MAX77857B", "package_type": "35 WLP", "pins": [{"pin_number": "A1", "pin_name": "SCL", "pin_description": "I2C Serial Interface Clock (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "B1", "pin_name": "SDA", "pin_description": "I2C Serial Interface Data (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "C2", "pin_name": "POKB/INTB", "pin_description": "Buck-Boost Output Power-OK Monitor or Fault Interrupt Active-Low Open-Drain Output. Connect to VIO with a 15kΩ pullup resistor. Do not connect to this pin if not in use."}, {"pin_number": "C1", "pin_name": "VL", "pin_description": "Low-Voltage Internal Supply. Powered from IN. Bypass to AGND with a 10V 2.2µF ceramic capacitor. Do not load this pin externally except for usage stated in the Non-I2C and Standalone Operation section."}, {"pin_number": "D1", "pin_name": "SEL", "pin_description": "Configuration Selection. Connect a resistor between SEL and AGND."}, {"pin_number": "E1", "pin_name": "FB", "pin_description": "Using Internal Feedback Resistors: Output Voltage Sense Input. Connect to the output at the point-of-load (close to output capacitor). Using External Feedback Resistors: Output Voltage Feedback Input. Connect to the center tap of an external resistor divider from OUT to AGND to set the output voltage."}, {"pin_number": "E2", "pin_name": "AGND", "pin_description": "Analog Ground. Connect to PGND on the PCB."}, {"pin_number": "E3", "pin_name": "BST2", "pin_description": "LX2 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST2 and LX2."}, {"pin_number": "D4, E4, E5, E6", "pin_name": "OUT", "pin_description": "Buck-Boost Output. Bypass to PGND with two 25V 22µF ceramic capacitors as close as possible."}, {"pin_number": "D5, D6, D7, E7", "pin_name": "LX2", "pin_description": "Buck-<PERSON><PERSON> Switching Node 2"}, {"pin_number": "C3, C4, C5, C6, C7, D2, D3", "pin_name": "PGND", "pin_description": "Power Ground. Connect to AGND on the PCB."}, {"pin_number": "A7, B5, B6, B7", "pin_name": "LX1", "pin_description": "Buck-Boost Switching Node 1"}, {"pin_number": "A4, A5, A6, B4", "pin_name": "IN", "pin_description": "Buck-Boost Input. Bypass to PGND with two 25V 10µF ceramic capacitors as close as possible."}, {"pin_number": "A3", "pin_name": "BST1", "pin_description": "LX1 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST1 and LX1."}, {"pin_number": "B2, B3", "pin_name": "EN", "pin_description": "Active-High Buck-Boost Enable Input. Compatible with the VIO voltage domain. Pulldown internally with 0.1µA current source."}, {"pin_number": "A2", "pin_name": "VIO", "pin_description": "IO Voltage Supply. Bypass to AGND with a 6.3V 0.47µF ceramic capacitor. Registers are held in reset and regulator remains disabled when this pin's voltage is invalid."}]}, {"product_part_number": "MAX77857B", "package_type": "16 FC2QFN", "pins": [{"pin_number": "1", "pin_name": "SCL", "pin_description": "I2C Serial Interface Clock (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "2", "pin_name": "SDA", "pin_description": "I2C Serial Interface Data (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "3", "pin_name": "POKB/INTB", "pin_description": "Buck-Boost Output Power-OK Monitor or Fault Interrupt Active-Low Open-Drain Output. Connect to VIO with a 15kΩ pullup resistor. Do not connect to this pin if not in use."}, {"pin_number": "4", "pin_name": "VL", "pin_description": "Low-Voltage Internal Supply. Powered from IN. Bypass to AGND with a 10V 2.2µF ceramic capacitor. Do not load this pin externally except for usage stated in the Non-I2C and Standalone Operation section."}, {"pin_number": "5", "pin_name": "SEL", "pin_description": "Configuration Selection. Connect a resistor between SEL and AGND."}, {"pin_number": "6", "pin_name": "FB", "pin_description": "Using Internal Feedback Resistors: Output Voltage Sense Input. Connect to the output at the point-of-load. Using External Feedback Resistors: Output Voltage Feedback Input. Connect to the center tap of an external resistor divider from OUT to AGND to set the output voltage."}, {"pin_number": "7", "pin_name": "AGND", "pin_description": "Analog Ground. Connect to PGND on the PCB."}, {"pin_number": "8", "pin_name": "BST2", "pin_description": "LX2 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST2 and LX2."}, {"pin_number": "9", "pin_name": "OUT", "pin_description": "Buck-Boost Output. Bypass to PGND with two 25V 22µF ceramic capacitors as close as possible."}, {"pin_number": "10", "pin_name": "LX2", "pin_description": "Buck-<PERSON><PERSON> Switching Node 2"}, {"pin_number": "11", "pin_name": "PGND", "pin_description": "Power Ground. Connect to AGND on the PCB."}, {"pin_number": "12", "pin_name": "LX1", "pin_description": "Buck-Boost Switching Node 1"}, {"pin_number": "13", "pin_name": "IN", "pin_description": "Buck-Boost Input. Bypass to PGND with two 25V 10µF ceramic capacitors as close as possible."}, {"pin_number": "14", "pin_name": "BST1", "pin_description": "LX1 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST1 and LX1."}, {"pin_number": "15", "pin_name": "EN", "pin_description": "Active-High Buck-Boost Enable Input. Compatible with the VIO voltage domain. Pulldown internally with 0.1µA current source."}, {"pin_number": "16", "pin_name": "VIO", "pin_description": "IO Voltage Supply. Bypass to AGND with a 6.3V 0.47µF ceramic capacitor. Registers are held in reset and regulator remains disabled when this pin's voltage is invalid."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77857 Rev 3; 9/22", "family_comparison": "MAX77857A is the base version without POKB/INTB and startup latch-off. MAX77857B adds the POKB/INTB feature. MAX77857C adds both POKB/INTB and startup latch-off features.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "2.5V", "max_output_voltage": "15V", "min_output_voltage": "3V", "max_output_current": "6A", "max_switch_frequency": "2.27MHz", "quiescent_current": "50µA", "high_side_mosfet_resistance": "20mΩ", "low_side_mosfet_resistance": "22mΩ", "over_current_protection_threshold": "7A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "SKIP", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Latch", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.333V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.64", "length": "2.828", "width": "2.028", "type": "Information", "pin_count": "2"}]}, {"part_number": "MAX77857C", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压-升压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "2.5V to 16V Input, 7A Switching Current High-Efficiency Buck-Boost Converter", "features": ["Wide Input Voltage Range: 2.5V to 16V", "Default Output Voltage: 5V with Internal Feedback Resistors, 3V to 15V with External Feedback Resistors", "I2C-Programmable Output Voltage after Startup: 4.5V to 15V with Internal Feedback Resistors, 3.0V to 15V with External Feedback Resistors", "Maximum Output Current: Buck Mode: Up to 6A, Boost Mode: Up to 4A (Boost Ratio ≤ 1.3)", "7A Typical Switching Current", "RSEL Configuration: I2C Interface Slave Address, Switching Current Limit Threshold, Internal/External Feedback Resistors", "I2C Programming: Output Voltage (DVS), Slew Rate of Output Voltage Change, Switching Current Limit Threshold, Switching Frequency, Forced PWM Mode Operation (FPWM), Power-OK (POK) Status and Fault Interrupt Masks", "Soft-Start", "Output Active Discharge", "Open-Drain Power-OK (POK) Monitor and Fault Condition Interrupt (MAX77857B/C Only)", "Protection Features: Undervoltage Lockout (UVLO), Overcurrent Protection (OCP), Overvoltage Protection (OVP), Thermal Shutdown (THS)", "High Density Interconnect (HDI) PCB not Required", "Available in 2.83mm x 2.03mm 35 WLP or 31 WLP or 3.5mm x 3.5mm 16 FC2QFN"], "description": "The MAX77857 is a high-efficiency, high-performance buck-boost converter targeted for systems requiring a wide input voltage range (2.5V to 16V). It features 7A switching current and can supply up to 6A output current in buck mode and up to 4A in boost mode (Boost Ratio ≤ 1.3). It operates in PWM mode and implements an automatic SKIP mode to improve light-load efficiency. The default output voltage is 5V when using internal feedback resistors. It can also be configured to any default output voltages between 3V and 15V when using external feedback resistors. The output voltage is adjustable dynamically through the I2C serial interface. The SEL pin allows a single external resistor to program four different I2C interface slave addresses, four different switching current limit thresholds, and selection between external/internal feedback resistors. The different switching current limit thresholds allow the use of lower profile and smaller external components that are optimized for a particular application. The use of external feedback resistors allows for a wider output voltage range and customizable output voltages at startup. The I2C serial interface is optional and allows for dynamically controlling the output voltage, slew rate of the output voltage change, switching-current limit threshold, switching frequency, and forced PWM mode operation. The I2C-programmed settings have priority over the RSEL decoded settings. The MAX77857 is available in a 2.83mm x 2.03mm, 35- bump and 31-bump wafer-level package (WLP) and a 3.5mm x 3.5mm, 16-lead Flip Chip QFN package (FC2QFN).", "applications": ["USB Power Delivery (USB-PD) OTG", "Qualcomm® Quick Charge™", "USB VBUS Supply and DRP (Dual Role Power) Ports", "DSLR, DSLR Lens", "Display Power", "Up to 3-Cell Li-Ion Battery Applications", "Notebook Computer, Tablet PC"], "ordering_information": [{"part_number": "MAX77857C", "order_device": "MAX77857CEWB+T", "package_type": "WLP", "package_drawing_code": "21-100367", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77857C", "order_device": "MAX77857CEFE+T", "package_type": "FC2QFN", "package_drawing_code": "21-100410", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MAX77857C", "package_type": "35 WLP", "pins": [{"pin_number": "A1", "pin_name": "SCL", "pin_description": "I2C Serial Interface Clock (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "B1", "pin_name": "SDA", "pin_description": "I2C Serial Interface Data (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "C2", "pin_name": "POKB/INTB", "pin_description": "Buck-Boost Output Power-OK Monitor or Fault Interrupt Active-Low Open-Drain Output. Connect to VIO with a 15kΩ pullup resistor. Do not connect to this pin if not in use."}, {"pin_number": "C1", "pin_name": "VL", "pin_description": "Low-Voltage Internal Supply. Powered from IN. Bypass to AGND with a 10V 2.2µF ceramic capacitor. Do not load this pin externally except for usage stated in the Non-I2C and Standalone Operation section."}, {"pin_number": "D1", "pin_name": "SEL", "pin_description": "Configuration Selection. Connect a resistor between SEL and AGND."}, {"pin_number": "E1", "pin_name": "FB", "pin_description": "Using Internal Feedback Resistors: Output Voltage Sense Input. Connect to the output at the point-of-load (close to output capacitor). Using External Feedback Resistors: Output Voltage Feedback Input. Connect to the center tap of an external resistor divider from OUT to AGND to set the output voltage."}, {"pin_number": "E2", "pin_name": "AGND", "pin_description": "Analog Ground. Connect to PGND on the PCB."}, {"pin_number": "E3", "pin_name": "BST2", "pin_description": "LX2 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST2 and LX2."}, {"pin_number": "D4, E4, E5, E6", "pin_name": "OUT", "pin_description": "Buck-Boost Output. Bypass to PGND with two 25V 22µF ceramic capacitors as close as possible."}, {"pin_number": "D5, D6, D7, E7", "pin_name": "LX2", "pin_description": "Buck-<PERSON><PERSON> Switching Node 2"}, {"pin_number": "C3, C4, C5, C6, C7, D2, D3", "pin_name": "PGND", "pin_description": "Power Ground. Connect to AGND on the PCB."}, {"pin_number": "A7, B5, B6, B7", "pin_name": "LX1", "pin_description": "Buck-Boost Switching Node 1"}, {"pin_number": "A4, A5, A6, B4", "pin_name": "IN", "pin_description": "Buck-Boost Input. Bypass to PGND with two 25V 10µF ceramic capacitors as close as possible."}, {"pin_number": "A3", "pin_name": "BST1", "pin_description": "LX1 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST1 and LX1."}, {"pin_number": "B2, B3", "pin_name": "EN", "pin_description": "Active-High Buck-Boost Enable Input. Compatible with the VIO voltage domain. Pulldown internally with 0.1µA current source."}, {"pin_number": "A2", "pin_name": "VIO", "pin_description": "IO Voltage Supply. Bypass to AGND with a 6.3V 0.47µF ceramic capacitor. Registers are held in reset and regulator remains disabled when this pin's voltage is invalid."}]}, {"product_part_number": "MAX77857C", "package_type": "16 FC2QFN", "pins": [{"pin_number": "1", "pin_name": "SCL", "pin_description": "I2C Serial Interface Clock (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "2", "pin_name": "SDA", "pin_description": "I2C Serial Interface Data (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor."}, {"pin_number": "3", "pin_name": "POKB/INTB", "pin_description": "Buck-Boost Output Power-OK Monitor or Fault Interrupt Active-Low Open-Drain Output. Connect to VIO with a 15kΩ pullup resistor. Do not connect to this pin if not in use."}, {"pin_number": "4", "pin_name": "VL", "pin_description": "Low-Voltage Internal Supply. Powered from IN. Bypass to AGND with a 10V 2.2µF ceramic capacitor. Do not load this pin externally except for usage stated in the Non-I2C and Standalone Operation section."}, {"pin_number": "5", "pin_name": "SEL", "pin_description": "Configuration Selection. Connect a resistor between SEL and AGND."}, {"pin_number": "6", "pin_name": "FB", "pin_description": "Using Internal Feedback Resistors: Output Voltage Sense Input. Connect to the output at the point-of-load. Using External Feedback Resistors: Output Voltage Feedback Input. Connect to the center tap of an external resistor divider from OUT to AGND to set the output voltage."}, {"pin_number": "7", "pin_name": "AGND", "pin_description": "Analog Ground. Connect to PGND on the PCB."}, {"pin_number": "8", "pin_name": "BST2", "pin_description": "LX2 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST2 and LX2."}, {"pin_number": "9", "pin_name": "OUT", "pin_description": "Buck-Boost Output. Bypass to PGND with two 25V 22µF ceramic capacitors as close as possible."}, {"pin_number": "10", "pin_name": "LX2", "pin_description": "Buck-<PERSON><PERSON> Switching Node 2"}, {"pin_number": "11", "pin_name": "PGND", "pin_description": "Power Ground. Connect to AGND on the PCB."}, {"pin_number": "12", "pin_name": "LX1", "pin_description": "Buck-Boost Switching Node 1"}, {"pin_number": "13", "pin_name": "IN", "pin_description": "Buck-Boost Input. Bypass to PGND with two 25V 10µF ceramic capacitors as close as possible."}, {"pin_number": "14", "pin_name": "BST1", "pin_description": "LX1 High-Side FET Driver Supply. Connect a 25V 0.22µF ceramic capacitor between BST1 and LX1."}, {"pin_number": "15", "pin_name": "EN", "pin_description": "Active-High Buck-Boost Enable Input. Compatible with the VIO voltage domain. Pulldown internally with 0.1µA current source."}, {"pin_number": "16", "pin_name": "VIO", "pin_description": "IO Voltage Supply. Bypass to AGND with a 6.3V 0.47µF ceramic capacitor. Registers are held in reset and regulator remains disabled when this pin's voltage is invalid."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77857 Rev 3; 9/22", "family_comparison": "MAX77857A is the base version without POKB/INTB and startup latch-off. MAX77857B adds the POKB/INTB feature. MAX77857C adds both POKB/INTB and startup latch-off features.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "2.5V", "max_output_voltage": "15V", "min_output_voltage": "3V", "max_output_current": "6A", "max_switch_frequency": "2.27MHz", "quiescent_current": "50µA", "high_side_mosfet_resistance": "20mΩ", "low_side_mosfet_resistance": "22mΩ", "over_current_protection_threshold": "7A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "SKIP", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Latch", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.333V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.64", "length": "2.828", "width": "2.028", "type": "Information", "pin_count": "2"}]}]