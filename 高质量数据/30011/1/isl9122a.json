{"part_number": "ISL9122A", "manufacturer": "Renesas", "country": "日本", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "ISL9122A Ultra-Low IQ Buck-Boost Regulator With Bypass", "features": ["1300nA quiescent current", "84% efficiency at 10µA load (VIN = 3.6V, VOUT = 3.3V)", "97% peak efficiency (VIN = 3.6V, VOUT = 3.3V)", "Input voltage range: 1.8V to 5.5V", "Output voltage range: 1.8V to 5.375V", "Output current: up to 500mA (VIN > VOUT > 2.5V)", "Selectable Forced and Auto Bypass power saving modes", "Seamless PWM/PFM and buck/boost transition", "I2C control and voltage adjustability", "Hysteretic controller", "Small 1.8mm×1.0mm 8 Bump WLCSP and 3.0mm×2.0mm 8 Ld DFN packages"], "description": "The ISL9122A is a highly integrated non-inverting buck-boost switching regulator that accepts input voltages both above or below the regulated output voltage. It features an extremely low quiescent current consumption of 1300nA in Regulation mode, 120nA in Forced Bypass mode, and 8nA in Shutdown mode. It supports input voltages from 1.8V to 5.5V. The ISL9122A has automatic bypass functionality for situations in which the input voltage is close to the output voltage, and it automatically transitions between Buck and Boost modes without significant output disturbance. In addition to the automatic bypass functionality, the Forced Bypass power saving mode can be chosen if voltage regulation is not required. Forced Bypass power saving mode is accessible using the I2C interface bus. The ISL9122A is capable of delivering up to 500mA of output current (VIN > VOUT > 2.5V) and provides excellent efficiency because of its adaptive frequency hysteretic control architecture. The ISL9122A is designed for stand-alone applications and supports a default output voltage at Power-On Reset (POR). After POR, the output voltage can be adjusted in the range of 1.8V to 5.375V by using the I2C interface bus. Specific default output voltages are available upon request. The ISL9122A requires only a single EIA 0603 size inductor and a minimum of two external capacitors. Power supply solution size is minimized by a 1.8mm×1.0mm 8 Bump WLCSP and it is also available in a 3.0mm×2.0mm 8 Ld plastic DFN.", "applications": ["Smart watches and wristband devices", "Wireless earphones", "Internet of Things (IoT) devices", "Water, gas, and oil meters", "Portable medical devices", "Hearing aid devices"], "ordering_information": [{"part_number": "ISL9122A", "order_device": "ISL9122AIINZ-T", "package_type": "WLCSP", "package_drawing_code": "W2x4.8", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9122A", "order_device": "ISL9122AIINZ-T7A", "package_type": "WLCSP", "package_drawing_code": "W2x4.8", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9122A", "order_device": "ISL9122AIRNZ-T", "package_type": "DFN", "package_drawing_code": "L8.2x3", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9122A", "order_device": "ISL9122AIRNZ-T7A", "package_type": "DFN", "package_drawing_code": "L8.2x3", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "ISL9122A", "package_type": "WLCSP", "pins": [{"pin_number": "B2", "pin_name": "VOUT", "pin_description": "Buck-boost output"}, {"pin_number": "A2", "pin_name": "LX2", "pin_description": "Inductor connection, output side"}, {"pin_number": "A1", "pin_name": "GND", "pin_description": "Ground connection"}, {"pin_number": "B1", "pin_name": "LX1", "pin_description": "Inductor connection, input side"}, {"pin_number": "C1", "pin_name": "VIN", "pin_description": "Power supply input"}, {"pin_number": "C2", "pin_name": "EN", "pin_description": "Logic input, drive HIGH to enable device. Do not leave floating."}, {"pin_number": "D2", "pin_name": "SDA", "pin_description": "I2C data input. Pull down to GND if not being used. Do not leave floating."}, {"pin_number": "D1", "pin_name": "SCL", "pin_description": "I2C clock input. Pull down to GND if not being used. Do not leave floating."}]}, {"product_part_number": "ISL9122A", "package_type": "DFN", "pins": [{"pin_number": "4", "pin_name": "VOUT", "pin_description": "Buck-boost output"}, {"pin_number": "3", "pin_name": "LX2", "pin_description": "Inductor connection, output side"}, {"pin_number": "2", "pin_name": "GND", "pin_description": "Ground connection"}, {"pin_number": "1", "pin_name": "LX1", "pin_description": "Inductor connection, input side"}, {"pin_number": "8", "pin_name": "VIN", "pin_description": "Power supply input"}, {"pin_number": "5", "pin_name": "EN", "pin_description": "Logic input, drive HIGH to enable device. Do not leave floating."}, {"pin_number": "6", "pin_name": "SDA", "pin_description": "I2C data input. Pull down to GND if not being used. Do not leave floating."}, {"pin_number": "7", "pin_name": "SCL", "pin_description": "I2C clock input. Pull down to GND if not being used. Do not leave floating."}, {"pin_number": "9", "pin_name": "EPAD", "pin_description": "Exposed Pad. Must be soldered to PCB GND."}]}], "datasheet_cn": "未找到", "datasheet_en": "FN8947 Rev.1.02", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.375V", "min_output_voltage": "1.8V", "max_output_current": "0.5A", "max_switch_frequency": "2.5MHz", "quiescent_current": "1.3µA", "high_side_mosfet_resistance": "52mΩ", "low_side_mosfet_resistance": "50mΩ", "over_current_protection_threshold": "2.5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "True", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2.5%", "output_reference_voltage": "未找到", "loop_control_mode": "迟滞模式控制"}, "package": [{"type": "WLCSP", "pin_count": "122", "pitch": "0.4", "height": "0.5", "width": "1.0", "length": "1.8"}]}