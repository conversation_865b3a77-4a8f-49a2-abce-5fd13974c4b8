{"part_number": "MAX77813", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "5.5V Input, 2A, High-Efficiency Buck-Boost Converter", "features": ["VIN Range: 2.30V to 5.5V", "VOUT Range: 2.60V to 5.14V (I2C Programmable in 20mV Steps)", "Up to 2A Output Current in Boost Mode (VIN = 3.0V, VOUT = 3.4V, ILIM = High)", "Up to 3A Output Current in Buck Mode (ILIM = High)", "Up to 97% Peak Efficiency", "SKIP Mode for Optimal Light Load Efficiency", "55μA (Typ) Low Quiescent Current", "3.4MHz High Speed I2C Serial Interface", "Inductor Switching Current Limit Selection Pin", "Power-OK Output", "2.5MHz Switching Frequency", "Protection Features: Soft-Start, Thermal Shutdown, Overvoltage Protection, Overcurrent Protection", "2.13mm x 1.83mm, 20-Bump WLP"], "description": "The MAX77813 is a high-efficiency step-up/step-down (buck-boost) converter targeted for single-cell Li+/Li-ion battery powered applications. The device maintains a regulated output voltage from 2.6V to 5.14V across an input voltage range of 2.3V to 5.5V. The device supports up to 2A of output current in boost mode and up to 3A in buck mode. The device seamlessly transitions between buck and boost modes. A unique control algorithm allows high-efficiency, outstanding load, and line transient response. Dedicated enable and power-OK pins allow simple hardware control. An I2C serial interface is optionally used for dynamic voltage scaling, system power optimization, and fault read-back. The device supports two inductor switching current limit options selected by the ILIM pin. The MAX77813 is available in a 20-bump, 2.13mm x 1.83mm wafer-level package (WLP).", "applications": ["Single-Cell Li+/Li-ion Battery Powered Devices", "Handheld Scanners, Mobile Payment Terminals, Security Cameras", "AR/VR Headsets"], "ordering_information": [{"part_number": "MAX77813", "order_device": "MAX77813EWP33+T", "package_type": "WLP", "package_drawing_code": "21-0771", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77813", "order_device": "MAX77813EWP+T", "package_type": "WLP", "package_drawing_code": "21-0771", "output_voltage": "3.4V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77813", "order_device": "MAX77813EWP45+T", "package_type": "WLP", "package_drawing_code": "21-0771", "output_voltage": "4.56V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MAX77813", "package_type": "20 WLP", "pins": [{"pin_number": "A1", "pin_name": "VSYS", "pin_description": "System (Battery) Voltage Input. Bypass to AGND with a 10V 1μF capacitor."}, {"pin_number": "A2", "pin_name": "ILIM", "pin_description": "Inductor Switching Current Limit Selection Input. See the Peak Inductor Current Limit Selection (ILIM) section. Do not leave this pin unconnected."}, {"pin_number": "A3, B3", "pin_name": "AGND", "pin_description": "Analog Ground. Connect to PGND on the PCB. See the PCB Layout Guidelines."}, {"pin_number": "A4", "pin_name": "SDA", "pin_description": "I2C Serial Interface Data (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor. Connect to AGND if not used."}, {"pin_number": "A5", "pin_name": "SCL", "pin_description": "I2C Serial Interface Clock (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor. Connect to AGND if not used."}, {"pin_number": "B1", "pin_name": "FB", "pin_description": "Output Voltage Sense"}, {"pin_number": "B2", "pin_name": "POK", "pin_description": "Open-Drain Power-OK Output. Asserts high (high-Z) when buck-boost output reaches 80% of target."}, {"pin_number": "B4", "pin_name": "EN", "pin_description": "Active-High Enable Input. This pin has an 800kΩ internal pulldown to AGND."}, {"pin_number": "B5", "pin_name": "VIO", "pin_description": "I2C Supply Voltage Input. Bypass to AGND with a 0.1μF capacitor. Connect to AGND if not used."}, {"pin_number": "C1, D1", "pin_name": "OUT", "pin_description": "Output. Bypass to PGND with a 10V 47μF ceramic capacitor."}, {"pin_number": "C2, D2", "pin_name": "LX2", "pin_description": "Switching Node 2"}, {"pin_number": "C3, D3", "pin_name": "PGND", "pin_description": "Power Ground. Connect to AGND on the PCB. See the PCB Layout Guidelines."}, {"pin_number": "C4, D4", "pin_name": "LX1", "pin_description": "Switching Node 1"}, {"pin_number": "C5, D5", "pin_name": "IN", "pin_description": "Input. Bypass to PGND with a 10V 10μF ceramic capacitor."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77813.pdf (Rev 5, 2023-01)", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.3V", "max_output_voltage": "5.14V", "min_output_voltage": "2.6V", "max_output_current": "3A", "max_switch_frequency": "2.75MHz", "quiescent_current": "55µA", "high_side_mosfet_resistance": "40mΩ", "low_side_mosfet_resistance": "55mΩ", "over_current_protection_threshold": "4.5A (ILIM=High)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "SKIP", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.0%", "output_reference_voltage": "未找到", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "length": "2.13", "width": "1.83", "type": "Information", "pin_count": "2", "height": "0.4"}]}