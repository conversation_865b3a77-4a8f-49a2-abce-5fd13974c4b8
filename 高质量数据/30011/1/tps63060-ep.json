{"part_number": "TPS63060-EP", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "开关电流为 2A 的高输入电压降压 - 升压转换器", "features": ["效率高达 93%", "5V 降压模式下的输出电流为 2A/1A", "5V 升压模式 (VIN > 4V) 下的输出电流为 1.3A", "降压和升压模式间的自动转换", "典型器件静态电流少于 30µA", "输入电压范围: 2.5V 至 12V", "2.5V 至 8V 固定和可调输出电压选项", "用于改进低输出功率时效率的省电模式", "2.4MHz 强制固定运行频率并可实现同步", "电源正常输出", "<PERSON><PERSON><PERSON><PERSON> Control™", "关断期间负载断开", "过热保护", "过压保护", "采用 3mm x 3mm, 小外形尺寸无引线 (SON)-10 封装"], "description": "TPS63060 器件为由 3 节到最多 6 节碱性电池、镍镉电池 (NiCd) 或者镍氢电池 (NiMH) 电池、或者单节或双节锂离子或者锂聚合物电池供电的产品提供了一个电源解决方案。当使用一个双锂离子或者锂聚合物电池时,输出电流可升高至 2A 并将电池电压放电至 5V 或者更低。此降压-升压转换器基于一个使用同步整流的固定频率、脉宽调制 (PWM) 控制器以获得最高效率。在低负载电流情况下,此转换器进入省电模式以在宽负载电流范围内保持高效率。省电模式可被禁用,从而强制转换器运行在固定的开关频率下。开关内的最大平均电流被限制在 2.25A (典型值)。使用一个外部电阻器分压器可对输出电压进行编程,或者在芯片上对输出电压进行内部固定。转换器可被禁用以大大减少电池消耗。在关断期间,负载从电池上断开。此器件封装在一个 10 引脚 SON PowerPAD™ 3mm x 3mm 封装内 (DSC)。", "applications": ["双锂离子电池应用", "数码相机和摄像放像机", "笔记本电脑", "工业计量设备", "超便携移动个人计算机和移动互联网器件", "个人医疗产品", "高功率发光二极管 (LED)", "支持国防、航空航天、和医疗应用"], "ordering_information": [{"part_number": "TPS63060-EP", "order_device": "TPS63060MDSCTEP", "package_type": "WSON", "package_drawing_code": "S-PWSON-N10", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "125"}, {"part_number": "TPS63060-EP", "order_device": "V62/14602-01XE", "package_type": "WSON", "package_drawing_code": "S-PWSON-N10", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63060-EP", "package_type": "DSC", "pins": [{"pin_number": "3", "pin_name": "EN", "pin_description": "Enable input. (1 enabled, 0 disabled)"}, {"pin_number": "8", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT on fixed output voltage versions"}, {"pin_number": "7", "pin_name": "GND", "pin_description": "Control / logic ground"}, {"pin_number": "1", "pin_name": "L1", "pin_description": "Connection for Inductor"}, {"pin_number": "10", "pin_name": "L2", "pin_description": "Connection for Inductor"}, {"pin_number": "4", "pin_name": "PS/SYNC", "pin_description": "Enable / disable power save mode (1 disabled, 0 enabled, clock signal for synchronization)"}, {"pin_number": "5", "pin_name": "PG", "pin_description": "Output power good (1 good, 0 failure; open drain)"}, {"pin_number": "PowerPAD™", "pin_name": "PGND", "pin_description": "Power ground"}, {"pin_number": "2", "pin_name": "VIN", "pin_description": "Supply voltage for power stage"}, {"pin_number": "9", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}, {"pin_number": "6", "pin_name": "VAUX", "pin_description": "Connection for Capacitor"}, {"pin_number": "PowerPAD™", "pin_name": "PowerPAD™", "pin_description": "Must be soldered to achieve appropriate power dissipation. Must be connected to PGND."}]}], "datasheet_cn": "ZHCSBZ6", "datasheet_en": "SLVSC92", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "12V", "min_input_voltage": "2.5V", "max_output_voltage": "8V", "min_output_voltage": "2.5V", "max_output_current": "2A", "max_switch_frequency": "3MHz", "quiescent_current": "30µA", "high_side_mosfet_resistance": "90mΩ", "low_side_mosfet_resistance": "95mΩ", "over_current_protection_threshold": "2.25A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "Internal", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.5V", "loop_control_mode": "平均电流模式"}, "package": [{"type": "OPTION", "length": "3.0", "width": "3.0", "pin_count": "10", "pitch": "0.5", "height": "0.8"}]}