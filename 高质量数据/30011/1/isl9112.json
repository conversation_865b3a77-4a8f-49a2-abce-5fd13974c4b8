[{"part_number": "ISL9110", "manufacturer": "RENESAS", "country": "日本", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "ISL9110, ISL9112 1.2A High Efficiency Buck-Boost Regulators", "features": ["Accepts input voltages above or below regulated output voltage", "Automatic and seamless transitions between Buck and <PERSON><PERSON> modes", "Input voltage range: 1.8V to 5.5V", "Output current: Up to 1.2A", "High efficiency: Up to 95%", "35µA quiescent current maximizes light-load efficiency", "2.5MHz switching frequency minimizes external component size", "Selectable Forced PWM mode and external synchronization", "Fully protected for overcurrent, over-temperature, and undervoltage", "Small 3mmx3mm TDFN Package"], "description": "The ISL9110 and ISL9112 are highly-integrated buck-boost switching regulators that accept input voltages either above or below the regulated output voltage. Unlike other Buck-Boost regulators, these regulators automatically transition between operating modes without significant output disturbance. Both parts are capable of delivering up to 1.2A output current, and provide excellent efficiency due to their fully synchronous 4-switch architecture. No-load quiescent current of only 35μA also optimizes efficiency under light-load conditions. Forced PWM and/or synchronization to an external clock may also be selected for noise sensitive applications. The ISL9110 is designed for standalone applications and supports 3.3V and 5V fixed output voltages or variable output voltages with an external resistor divider. Output voltages as low as 1V, or as high as 5.2V are supported using an external resistor divider.", "applications": ["Regulated 3.3V from a single Li-ion battery", "Smart phones and tablet computers", "Handheld devices", "Point-of-load regulators"], "ordering_information": [{"part_number": "ISL9110", "order_device": "ISL9110IRTNZ", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9110", "order_device": "ISL9110IRTNZ-T", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9110", "order_device": "ISL9110IRTNZ-T7A", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9110", "order_device": "ISL9110IRT7Z", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "5.0V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9110", "order_device": "ISL9110IRT7Z-T", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "5.0V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9110", "order_device": "ISL9110IRT7Z-T7A", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "5.0V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9110", "order_device": "ISL9110IRTAZ", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9110", "order_device": "ISL9110IRTAZ-T", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9110", "order_device": "ISL9110IRTAZ-T7A", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9110", "order_device": "ISL9110BIRTAZ", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9110", "order_device": "ISL9110BIRTAZ-T", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "ISL9110", "package_type": "12 LD TDFN", "pins": [{"pin_number": "1", "pin_name": "VOUT", "pin_description": "Buck/boost output. Connect a 10µF capacitor to PGND."}, {"pin_number": "2", "pin_name": "LX2", "pin_description": "Inductor connection, output side."}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power ground for high switching current."}, {"pin_number": "4", "pin_name": "LX1", "pin_description": "Inductor connection, input side."}, {"pin_number": "5", "pin_name": "PVIN", "pin_description": "Power input. Range: 1.8V to 5.5V. Connect a 10µF capacitor to PGND."}, {"pin_number": "6", "pin_name": "VIN", "pin_description": "Supply input. Range: 1.8V to 5.5V."}, {"pin_number": "7", "pin_name": "PG", "pin_description": "Open-drain output. Provides output power-good status."}, {"pin_number": "8", "pin_name": "BAT", "pin_description": "Open drain output. Provides input-power-good status."}, {"pin_number": "9", "pin_name": "EN", "pin_description": "Logic input, drive high to enable device."}, {"pin_number": "10", "pin_name": "MODE/SYNC", "pin_description": "Logic input, high for auto PFM mode. Low for forced PWM operation. External clock sync input. Range: 2.75MHz to 3.25MHz."}, {"pin_number": "11", "pin_name": "GND", "pin_description": "Analog ground pin."}, {"pin_number": "12", "pin_name": "FB", "pin_description": "Voltage feedback pin."}, {"pin_number": "PAD", "pin_name": "PAD", "pin_description": "Exposed pad; connect to PGND."}]}], "datasheet_cn": "未找到", "datasheet_en": "ISL9110, ISL9112 Datasheet, FN7649 Rev.3.00, Jul 26, 2018", "family_comparison": "ISL9110 is designed for standalone applications with status outputs (PG, BAT), while the ISL9112 uses an I2C interface for programmable features like dynamic voltage scaling and slew rate control.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.2V", "min_output_voltage": "1.0V", "max_output_current": "1.2A", "max_switch_frequency": "2.75MHz", "quiescent_current": "35µA", "high_side_mosfet_resistance": "120mΩ", "low_side_mosfet_resistance": "100mΩ", "over_current_protection_threshold": "2.4A", "operation_mode": "同步", "pass_through_mode": "False", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.75", "length": "12.3", "width": "3", "type": "TDFN", "pin_count": "3"}]}, {"part_number": "ISL9112", "manufacturer": "RENESAS", "country": "日本", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "ISL9110, ISL9112 1.2A High Efficiency Buck-Boost Regulators", "features": ["Accepts input voltages above or below regulated output voltage", "Automatic and seamless transitions between Buck and <PERSON><PERSON> modes", "Input voltage range: 1.8V to 5.5V", "Output current: Up to 1.2A", "High efficiency: Up to 95%", "35µA quiescent current maximizes light-load efficiency", "2.5MHz switching frequency minimizes external component size", "Selectable Forced PWM mode and external synchronization", "I2C Interface (ISL9112)", "Fully protected for overcurrent, over-temperature, and undervoltage", "Small 3mmx3mm TDFN Package"], "description": "The ISL9110 and ISL9112 are highly-integrated buck-boost switching regulators that accept input voltages either above or below the regulated output voltage. Unlike other Buck-Boost regulators, these regulators automatically transition between operating modes without significant output disturbance. The ISL9112 supports a broader set of programmable features that may be accessed using an I2C bus interface. With a programmable output voltage range of 1.9V to 5V, the ISL9112 is ideal for applications requiring dynamically changing supply voltages. A programmable slew rate can be selected to provide smooth transitions between output voltage settings.", "applications": ["Regulated 3.3V from a single Li-ion battery", "Smart phones and tablet computers", "Handheld devices", "Point-of-load regulators"], "ordering_information": [{"part_number": "ISL9112", "order_device": "ISL9112IRTNZ", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9112", "order_device": "ISL9112IRTNZ-T", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9112", "order_device": "ISL9112IRTNZ-T7A", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9112", "order_device": "ISL9112IRT7Z", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "5.0V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9112", "order_device": "ISL9112IRT7Z-T", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "5.0V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL9112", "order_device": "ISL9112IRT7Z-T7A", "package_type": "TDFN", "package_drawing_code": "L12.3x3C", "output_voltage": "5.0V", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "ISL9112", "package_type": "12 LD TDFN", "pins": [{"pin_number": "1", "pin_name": "VOUT", "pin_description": "Buck/boost output. Connect a 10µF capacitor to PGND."}, {"pin_number": "2", "pin_name": "LX2", "pin_description": "Inductor connection, output side."}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power ground for high switching current."}, {"pin_number": "4", "pin_name": "LX1", "pin_description": "Inductor connection, input side."}, {"pin_number": "5", "pin_name": "PVIN", "pin_description": "Power input. Range: 1.8V to 5.5V. Connect a 10µF capacitor to PGND."}, {"pin_number": "6", "pin_name": "VIN", "pin_description": "Supply input. Range: 1.8V to 5.5V."}, {"pin_number": "7", "pin_name": "SCL", "pin_description": "Logic input, I2C clock."}, {"pin_number": "8", "pin_name": "SDA", "pin_description": "Logic I/O, open drain, I2C data."}, {"pin_number": "9", "pin_name": "EN", "pin_description": "Logic input, drive high to enable device."}, {"pin_number": "10", "pin_name": "MODE/SYNC", "pin_description": "Logic input, high for auto PFM mode. Low for forced PWM operation. External clock sync input. Range: 2.75MHz to 3.25MHz."}, {"pin_number": "11", "pin_name": "GND", "pin_description": "Analog ground pin."}, {"pin_number": "12", "pin_name": "FB", "pin_description": "Voltage feedback pin."}, {"pin_number": "PAD", "pin_name": "PAD", "pin_description": "Exposed pad; connect to PGND."}]}], "datasheet_cn": "未找到", "datasheet_en": "ISL9110, ISL9112 Datasheet, FN7649 Rev.3.00, Jul 26, 2018", "family_comparison": "ISL9110 is designed for standalone applications with status outputs (PG, BAT), while the ISL9112 uses an I2C interface for programmable features like dynamic voltage scaling and slew rate control.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.0V", "min_output_voltage": "1.9V", "max_output_current": "1.2A", "max_switch_frequency": "2.75MHz", "quiescent_current": "35µA", "high_side_mosfet_resistance": "120mΩ", "low_side_mosfet_resistance": "100mΩ", "over_current_protection_threshold": "2.4A", "operation_mode": "同步", "pass_through_mode": "False", "output_voltage_config_method": "I2C", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.75", "length": "12.3", "width": "3", "type": "TDFN", "pin_count": "3"}]}]