[{"part_number": "MAX77646", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PMIC", "category_lv3": "多通道PMIC", "part_number_title": "Ultra-Configurable SIMO PMIC Featuring 3-Output Buck-Boost, 1-LDO for Long Battery Life Primary Cell Applications", "features": ["1.8V to 3.3V Input Range", "Highly Integrated", "3x Output, Single Inductor Multiple Output (SIMO) Buck-Boost Regulators, Supports Wide Output Voltage Range from 0.5V to 5.1V for all SIMO Channels", "1x 150mA LDO, 100mA in LSW Mode", "2x GPIOs (MAX77647)", "Watchdog Timer (MAX77647)", "Ultra Low-Power SIMO", "1.3μA Operating Current (3x SIMO Channels)", "0.1μA Shutdown Current", "85% Efficiency in Buck-Only Mode (VIN = 2.4V, VOUT = 0.8V, 10mA, IPK = 0.5A)", "86% Efficiency in Buck-Boost Mode (VIN = 2.4V, VOUT = 1.8V, 10mA, IPK = 0.5A)", "Less Than 20mVpp Output Ripple at VOUT = 1.8V", "Automatic Low-Power Mode to Normal-Power Mode Transition", "Flexible and Configurable", "Ultra-Configurable Resistor Programmable Output Voltages (MAX77646)", "I2C-Programmable Output Voltages (MAX77647)", "Small Size", "5mm2 Wafer-Level Package (WLP)", "20-Bump, 0.5mm Pitch, 5 x 4 Array"], "description": "The MAX77646/MAX77647 provides power supply solutions for low-power applications where size and efficiency are critical. The IC features a Single Inductor Multiple Output (SIMO) buck-boost regulator that provides three independently programmable power rails from a single inductor to minimize total solution size. A 150mA LDO provides ripple rejection for audio and other noise-sensitive applications. The LDO can also be configured as a load switch to manage power consumption by disconnecting external blocks when not required. The MAX77646/MAX77647 operates with battery voltages from 1.8V with coin-cell, dual silver oxide, dual alkaline, and LiSOCL2 primary batteries. The MAX77646 (RSEL variant) SIMO and LDO output voltages are individually programmable through resistors. A peak current limit input is used to set both the inductor's peak current limits of the device with a single resistor. Individual enable pins combined with the flexible resistor programmability allows the device to be tailored for many applications.", "applications": ["GNSS Module", "IoT Enabled Wireless Sensor Nodes", "Home Safety and Security Monitors", "Factory Safety and Security Monitors", "Fitness, Health, and Activity Trackers"], "ordering_information": [{"part_number": "MAX77646", "order_device": "MAX77646ANP+T", "package_type": "WLP", "package_drawing_code": "21-100601", "output_voltage": "可调", "min_operation_temp": "-20", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX77646", "package_type": "20-<PERSON><PERSON>", "pins": [{"pin_number": "A1", "pin_name": "RSEL_SBB2", "pin_description": "Select Resistor Pin SBB2. Connect a resistor from this pin to GND using the value to configure the output voltage of SBB2."}, {"pin_number": "A2", "pin_name": "RSEL_SBB1", "pin_description": "Select Resistor Pin SBB1. Connect a resistor from this pin to GND using the value to configure the output voltage of SBB1."}, {"pin_number": "A3", "pin_name": "RSEL_SBBO", "pin_description": "Select Resistor Pin SBB0. Connect a resistor from this pin to GND using the value to configure the output voltage of SBB0."}, {"pin_number": "A4", "pin_name": "LDO", "pin_description": "Linear Regulator Output. Bypass with a 1.0µF ceramic capacitor to GND. If not used, disable LDO and connect this pin to ground or leave unconnected."}, {"pin_number": "A5", "pin_name": "IN_LDO", "pin_description": "Linear Regulator Input. If connected to a SIMO output with a short trace, IN_LDO can share the output's capacitor. Otherwise, bypass with a 2.2µF ceramic capacitor to ground. If not used, connect to ground or leave unconnected."}, {"pin_number": "B1", "pin_name": "RSEL_LDO", "pin_description": "Select Resistor Pin LDO. Connect a resistor from this pin to GND using the value to configure the output voltage of LDO."}, {"pin_number": "B2", "pin_name": "RSEL_IPK", "pin_description": "Select Resistor Pin IPK. Connect a resistor from this pin to GND using the value to configure the peak inductor current."}, {"pin_number": "B3", "pin_name": "SBB1", "pin_description": "SIMO Buck-Boost Output 1. SBB1 is the power output for channel 1 of the SIMO buck-boost. Bypass SBB1 to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "B4", "pin_name": "LXB", "pin_description": "Switching Node B. LXB is driven between PGND and SBBx when SBBx is enabled. LXB is driven to PGND when all SIMO channels are disabled. Connect a 1.5µH inductor between LXA and LXB."}, {"pin_number": "B5", "pin_name": "SBBO", "pin_description": "SIMO Buck-Boost Output 0. SBBO is the power output for channel 0 of the SIMO buck-boost. Bypass SBB0 to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "C1", "pin_name": "EN1", "pin_description": "Enable Input for SBB1. Hold high to enable output regulation. Hold low to disable the output."}, {"pin_number": "C2", "pin_name": "ENO", "pin_description": "Enable Input for SBB0. Hold high to enable output regulation. Hold low to disable the output."}, {"pin_number": "C3", "pin_name": "SBB2", "pin_description": "SIMO Buck-Boost Output 2. SBB2 is the power output for channel 2 of the SIMO buck-boost. Bypass SBBO to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "C4", "pin_name": "LXB", "pin_description": "Switching Node B. LXB is driven between PGND and SBBx when SBBx is enabled. LXB is driven to PGND when all SIMO channels are disabled. Connect a 1.5µH inductor between LXA and LXB."}, {"pin_number": "C5", "pin_name": "BST", "pin_description": "SIMO Power Input for the High-Side Output NMOS Drivers. Connect a 10nF ceramic capacitor between BST and LXB."}, {"pin_number": "D1", "pin_name": "EN2", "pin_description": "Enable Input for SBB2 and LDO. Hold high to enable output regulation. Hold low to disable the output."}, {"pin_number": "D2", "pin_name": "GND", "pin_description": "Quiet Ground. Connect GND to PGND, and the low-impedance ground plane of the PCB."}, {"pin_number": "D3", "pin_name": "IN_SBB", "pin_description": "SIMO Power Input. Bypass to PGND with a minimum of 10µF ceramic capacitor as close as possible to the IN_SBB pin."}, {"pin_number": "D4", "pin_name": "LXA", "pin_description": "Switching Node A. LXA is driven between PGND and IN_SBB when any SIMO channel is enabled. LXA is driven to PGND when all SIMO channels are disabled. Connect a 1.5µH inductor between LXA and LXB."}, {"pin_number": "D5", "pin_name": "PGND", "pin_description": "Power Ground for the SIMO Low-Side FETs. Connect PGND to GND and the low-impedance ground plane of the PCB."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77646/MAX77647", "family_comparison": "MAX77646: Resistor-programmable (RSEL pins), has individual enable pins for outputs, does not have I2C, GPIOs, programmable power sequencer, or watchdog timer. MAX77647: I2C-programmable, includes 2 GPIOs, a programmable flexible power sequencer, and a watchdog timer. Uses a single nEN pin for system enable instead of individual enable pins.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "3.3V", "min_input_voltage": "1.8V", "max_output_voltage": "5.1V", "min_output_voltage": "1V", "max_output_current": "0.5A", "max_switch_frequency": "未找到", "quiescent_current": "1.3µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "0.91A", "operation_mode": "同步", "output_voltage_config_method": "外部可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "不适用", "loop_control_mode": "脉冲频率调制(PFM)"}, "package": [{"type": "Diagram", "pitch": "0.5", "height": "0.5", "width": "1.968", "length": "2.468", "pin_count": "84"}]}, {"part_number": "MAX77647", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PMIC", "category_lv3": "多通道PMIC", "part_number_title": "Ultra-Configurable SIMO PMIC Featuring 3-Output Buck-Boost, 1-LDO for Long Battery Life Primary Cell Applications", "features": ["1.8V to 3.3V Input Range", "Highly Integrated", "3x Output, Single Inductor Multiple Output (SIMO) Buck-Boost Regulators, Supports Wide Output Voltage Range from 0.5V to 5.1V for all SIMO Channels", "1x 150mA LDO, 100mA in LSW Mode", "2x GPIOs (MAX77647)", "Watchdog Timer (MAX77647)", "Ultra Low-Power SIMO", "1.3μA Operating Current (3x SIMO Channels)", "0.1μA Shutdown Current", "85% Efficiency in Buck-Only Mode (VIN = 2.4V, VOUT = 0.8V, 10mA, IPK = 0.5A)", "86% Efficiency in Buck-Boost Mode (VIN = 2.4V, VOUT = 1.8V, 10mA, IPK = 0.5A)", "Less Than 20mVpp Output Ripple at VOUT = 1.8V", "Automatic Low-Power Mode to Normal-Power Mode Transition", "Flexible and Configurable", "Ultra-Configurable Resistor Programmable Output Voltages (MAX77646)", "I2C-Programmable Output Voltages (MAX77647)", "Small Size", "5mm2 Wafer-Level Package (WLP)", "20-Bump, 0.5mm Pitch, 5 x 4 Array"], "description": "The MAX77646/MAX77647 provides power supply solutions for low-power applications where size and efficiency are critical. The IC features a Single Inductor Multiple Output (SIMO) buck-boost regulator that provides three independently programmable power rails from a single inductor to minimize total solution size. A 150mA LDO provides ripple rejection for audio and other noise-sensitive applications. The LDO can also be configured as a load switch to manage power consumption by disconnecting external blocks when not required. The MAX77646/MAX77647 operates with battery voltages from 1.8V with coin-cell, dual silver oxide, dual alkaline, and LiSOCL2 primary batteries. The MAX77647 (I2C variant) SIMO and LDO output voltages are individually programmable through I2C and in addition, includes two GPIOs with alternate modes for scalability. A bidirectional I2C serial interface allows for configuring and checking the status of the devices. An internal on/off controller provides a controlled startup sequence for the regulators and provides supervisory functionality while they are on. Numerous factory programmable options allow the device to be tailored for many applications, enabling faster time to market.", "applications": ["GNSS Module", "IoT Enabled Wireless Sensor Nodes", "Home Safety and Security Monitors", "Factory Safety and Security Monitors", "Fitness, Health, and Activity Trackers"], "ordering_information": [{"part_number": "MAX77647", "order_device": "MAX77647AANP+T", "package_type": "WLP", "package_drawing_code": "21-100601", "output_voltage": "可调", "min_operation_temp": "-20", "max_operation_temp": "85"}, {"part_number": "MAX77647", "order_device": "MAX77647ANP+", "package_type": "WLP", "package_drawing_code": "未找到", "output_voltage": "可调", "min_operation_temp": "-20", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX77647", "package_type": "20-<PERSON><PERSON>", "pins": [{"pin_number": "A1", "pin_name": "Vio", "pin_description": "I2C Interface and GPIO Driver Power"}, {"pin_number": "A2", "pin_name": "SCL", "pin_description": "I2C Clock"}, {"pin_number": "A3", "pin_name": "SDA", "pin_description": "I2C Data"}, {"pin_number": "A4", "pin_name": "LDO", "pin_description": "Linear Regulator Output. Bypass with a 1.0µF ceramic capacitor to GND. If not used, disable LDO and connect this pin to ground or leave unconnected."}, {"pin_number": "A5", "pin_name": "IN_LDO", "pin_description": "Linear Regulator Input. If connected to a SIMO output with a short trace, IN_LDO can share the output's capacitor. Otherwise, bypass with a 2.2µF ceramic capacitor to ground. If not used, connect to ground or leave unconnected."}, {"pin_number": "B1", "pin_name": "GPIO0", "pin_description": "General Purpose Input/Output. The GPIO I/O stage is internally biased with Vio."}, {"pin_number": "B2", "pin_name": "GPIO1", "pin_description": "General Purpose Input/Output. The GPIO I/O stage is internally biased with Vio."}, {"pin_number": "B3", "pin_name": "SBB1", "pin_description": "SIMO Buck-Boost Output 1. SBB1 is the power output for channel 1 of the SIMO buck-boost. Bypass SBB1 to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "B4", "pin_name": "LXB", "pin_description": "Switching Node B. LXB is driven between PGND and SBBx when SBBx is enabled. LXB is driven to PGND when all SIMO channels are disabled. Connect a 1.5µH inductor between LXA and LXB."}, {"pin_number": "B5", "pin_name": "SBBO", "pin_description": "SIMO Buck-Boost Output 0. SBBO is the power output for channel 0 of the SIMO buck-boost. Bypass SBB0 to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "C1", "pin_name": "nIRQ", "pin_description": "Active-Low, Open-Drain Interrupt Output. Connect a 100kΩ pullup resistor between nIRQ and a voltage equal to or less than VIN_SBB."}, {"pin_number": "C2", "pin_name": "nEN", "pin_description": "Active-Low Enable Input. nEN supports push-button or slide-switch configurations. If not used, connect nEN to SYS and use the CNFG_SBBx_B.EN_SBBx[2:0] and CNFG_LDO_B.EN_LDO[2:0] bitfields to enable channels."}, {"pin_number": "C3", "pin_name": "SBB2", "pin_description": "SIMO Buck-Boost Output 2. SBB2 is the power output for channel 2 of the SIMO buck-boost. Bypass SBBO to PGND with a 22µF ceramic capacitor. If not used, see the Unused Outputs section."}, {"pin_number": "C4", "pin_name": "LXB", "pin_description": "Switching Node B. LXB is driven between PGND and SBBx when SBBx is enabled. LXB is driven to PGND when all SIMO channels are disabled. Connect a 1.5µH inductor between LXA and LXB."}, {"pin_number": "C5", "pin_name": "BST", "pin_description": "SIMO Power Input for the High-Side Output NMOS Drivers. Connect a 10nF ceramic capacitor between BST and LXB."}, {"pin_number": "D1", "pin_name": "nRST", "pin_description": "Active-Low, Open-Drain Reset Output. Connect a 100kΩ pullup resistor between nRST and a voltage equal to or less than VIN_SBB."}, {"pin_number": "D2", "pin_name": "GND", "pin_description": "Quiet Ground. Connect GND to PGND, and the low-impedance ground plane of the PCB."}, {"pin_number": "D3", "pin_name": "IN_SBB", "pin_description": "SIMO Power Input. Bypass to PGND with a minimum of 10µF ceramic capacitor as close as possible to the IN_SBB pin."}, {"pin_number": "D4", "pin_name": "LXA", "pin_description": "Switching Node A. LXA is driven between PGND and IN_SBB when any SIMO channel is enabled. LXA is driven to PGND when all SIMO channels are disabled. Connect a 1.5µH inductor between LXA and LXB."}, {"pin_number": "D5", "pin_name": "PGND", "pin_description": "Power Ground for the SIMO Low-Side FETs. Connect PGND to GND and the low-impedance ground plane of the PCB."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77646/MAX77647", "family_comparison": "MAX77646: Resistor-programmable (RSEL pins), has individual enable pins for outputs, does not have I2C, GPIOs, programmable power sequencer, or watchdog timer. MAX77647: I2C-programmable, includes 2 GPIOs, a programmable flexible power sequencer, and a watchdog timer. Uses a single nEN pin for system enable instead of individual enable pins.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "3.3V", "min_input_voltage": "1.8V", "max_output_voltage": "5.1V", "min_output_voltage": "1V", "max_output_current": "0.5A", "max_switch_frequency": "未找到", "quiescent_current": "1.3µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "0.91A", "operation_mode": "同步", "output_voltage_config_method": "通信接口可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2%", "output_reference_voltage": "不适用", "loop_control_mode": "脉冲频率调制(PFM)"}, "package": [{"type": "Diagram", "pitch": "0.5", "height": "0.5", "width": "1.968", "length": "2.468", "pin_count": "84"}]}]