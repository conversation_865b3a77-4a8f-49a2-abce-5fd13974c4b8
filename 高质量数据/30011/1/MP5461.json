{"part_number": "MP5461", "manufacturer": "Monolithic Power Systems", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "Dual Input, 4-Switch Integrated Buck-Boost Converter with Input ORing and Selection", "features": ["Dual Input ORing Switches:", "4.2V to 5.5V Input Voltage Range for VIN1", "Supports 22V Voltage Stress for VIN1", "5.75V OVP Shutdown for VIN1", "2.5V to 5.5V Input Voltage Range for VIN2", "Fast Reverse Block within 2μs", "1A Current Capability for Each Channel", "Soft-Start Control", "Fast SCP (Short-Circuit Protection) on OR_OUT", "Power-Path Selection Input", "Power-Path Status Indication", "<PERSON><PERSON><PERSON><PERSON> Converter:", "1.8MHz Switching Frequency for CCM", "3.3V Fixed Output Voltage", "500mA Continuous Output Current", "1ms Soft-Start Time", "Auto PFM/PWM Mode", "Output Over-Voltage Protection", "Hiccup Over-Current Protection", "1μA Shutdown Current", "200μA Quiescent Current", "Active Low System EN Pin", "EN to OR_OUT Start-Up Delay 300μs", "Over-Temperature Shutdown", "Available in a Wafer Level Chip Scale Packaging: CSP-12(1.4mmx1.8mm)"], "description": "The MP5461 is a dual input, 4-switch, integrated buck-boost converter. It is capable of regulating the output voltage from 4.2V to 5.5V VIN1 and 2.5V to 5.5V VIN2. The VIN1 can support up to 22V input voltage but is not functional after >5.75V. The MP5461 has two auto-ORing switches from VIN1 and VIN2 to achieve a stable input for the buck-boost converter. The two sets of ORing MOSFETs are integrated. If one channel power source falls, the fast turn-off protection minimizes the reverse current. The buck-boost converter can operate from an input voltage above, equal to, or below the output voltage. It uses current-mode control with 1.8MHz fixed PWM frequency to optimize stability and transient response. In a light-load condition, it enters PFM mode to get high light-load efficiency. Integrated MOSFETs minimize the solution size while maintaining high efficiency. Fault protection includes VIN1 OVP shutdown, output hiccup current limiting, and thermal shutdown. The MP5461 is available in a tiny CSP-12 (1.4mmx1.8mm) package.", "applications": ["USB-C Cable", "VCONN Powered USB Device"], "ordering_information": [{"part_number": "MP5461", "order_device": "MP5461GC", "package_type": "CSP-12", "package_drawing_code": "MO-211", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MP5461", "order_device": "MP5461GC-Z", "package_type": "CSP-12", "package_drawing_code": "MO-211", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MP5461", "package_type": "CSP-12", "pins": [{"pin_number": "A1", "pin_name": "IN1", "pin_description": "VIN1 supply voltage. The MP5461 operates from a 4.2V to 5.5V VIN1 voltage and supports a 22V input voltage, but it is not functional >5.75V. CIN1 prevents large voltage spikes at the input. Place CIN1 as close to the IC as possible."}, {"pin_number": "A2", "pin_name": "OR_OUT", "pin_description": "IN1, IN2 ORing output. Also functions as the buck-boost input pin. Use a 10µF or larger capacitor for decoupling."}, {"pin_number": "A3", "pin_name": "SW1", "pin_description": "Switch1. The first half-bridge switch node is connected to SW1. Connect an inductor between SW1 and SW2."}, {"pin_number": "B1", "pin_name": "IN2", "pin_description": "VIN2 supply voltage. The MP5461 operates from a 2.5V to 5.5V VIN2 voltage. CIN2 prevents large voltage spikes at the input. Place CIN2 as close to the IC as possible."}, {"pin_number": "B2", "pin_name": "AGND", "pin_description": "Analog ground. Connect AGND to VCC capacitor's GND node by a Kelvin sense trace."}, {"pin_number": "B3", "pin_name": "GND", "pin_description": "Power ground. Reference ground of the regulated output voltage. GND requires extra care during PCB layout. Connect to GND with copper traces and vias."}, {"pin_number": "C1", "pin_name": "VCC", "pin_description": "Internal 5V LDO regulator output. Decouple with a 1µF capacitor."}, {"pin_number": "C2", "pin_name": "EN", "pin_description": "On/off control for entire chip. EN is active low. Drive EN high to turn off the chip. Drive EN low or float to turn on the device. It has an internal 600kΩ pull-down resistor to ground."}, {"pin_number": "C3", "pin_name": "SW2", "pin_description": "Switch2. The internal second half-bridge switch node is connected to SW2. Connect an inductor between SW1 and SW2."}, {"pin_number": "D1", "pin_name": "SEL", "pin_description": "Power path select input. If SEL=Low or is floated, VIN1 is selected; If SEL=High, VIN2 is selected. The MP5461 will auto select the available power path if only one supply is available. It has an internal 600kΩ pull-down resistor to ground."}, {"pin_number": "D2", "pin_name": "STATUS", "pin_description": "Status indication. Open drain output. Indicates if the VIN1 or VIN2 channel is selected. Refer to the truth table."}, {"pin_number": "D3", "pin_name": "OUT", "pin_description": "Output pin."}]}], "datasheet_cn": "未找到", "datasheet_en": "MP5461_r1.0.pdf", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.5V", "max_output_voltage": "3.3V", "min_output_voltage": "3.3V", "max_output_current": "0.5A", "max_switch_frequency": "2.15MHz", "quiescent_current": "210µA", "high_side_mosfet_resistance": "90mΩ", "low_side_mosfet_resistance": "80mΩ", "over_current_protection_threshold": "2.5A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "5.75V", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "115% Vout", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.63", "length": "1.4", "width": "1.8", "type": "Information", "pin_count": "200"}]}