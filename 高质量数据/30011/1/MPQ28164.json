{"part_number": "MPQ28164", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "升降压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "High-Efficiency, Single-Inductor, Buck-Boost Converter with 4.2A Switches", "features": ["1.8V Minimum Start-Up Input Voltage", "1.2V to 5.5V Input Work Range", "1.5V to 5V Output Range", "4.2A Switching Current Limit", "3.3V/2A Load Capability from a 2.5V to 5.5V Input Supply", "2MHz Fixed or External Synchronous Switching Frequency", "Selectable PSM and PWM Mode", "Typical 25µA Quiescent Current", "High Efficiency up to 95%", "Load Disconnect during Shutdown", "Internal Soft Start (SS) and Compensation", "Power Good Indicator", "Hiccup Mode for Short-Circuit Protection (SCP)", "Over-Temperature Protection (OTP)", "Available in a Small QFN-11 (2mmx3mm) Package"], "description": "The MPQ28164 is a high-efficiency, low-quiescent current, buck-boost converter that operates from an input voltage above, equal to, or below the output voltage. The MPQ28164 provides a compact solution for products powered by one-cell Lithium-ion or multi-cell alkaline batteries where the output voltage is within the battery voltage range. The MPQ28164 uses current-mode control with a fixed PWM frequency for optimal stability and transient response. The fixed 2MHz switching frequency and integrated low RDS(ON) MOSFETs minimize the solution footprint while maintaining high efficiency. To ensure the longest possible battery life, the MPQ28164 uses an optional pulse-skipping mode that reduces the switching frequency under light-load conditions. For other low-noise applications where pulse-skipping mode may cause interference, a high-logic input on the MODE/SYNC pin guarantees fixed-frequency PWM operation under all load conditions. The MPQ28164 operates with an input voltage from 1.2V to 5.5V to provide an adjustable output voltage from 1.5V to 5V. With an input from 2.5V to 5.5V, the device can supply 2A of current to the load with a 3.3V output voltage. The MPQ28164 is available in a small QFN-11 (2mmx3mm) package.", "applications": ["Battery-Powered Devices", "Portable Instruments", "Tablet PCs", "Super-Cap Chargers"], "ordering_information": [{"part_number": "MPQ28164", "order_device": "MPQ28164GD", "package_type": "QFN-11 (2mmx3mm)", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "Tray"}, {"part_number": "MPQ28164", "order_device": "MPQ28164GD-Z", "package_type": "QFN-11 (2mmx3mm)", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "Tape & Reel"}], "pin_function": [{"product_part_number": "MPQ28164", "package_type": "QFN-11 (2mmx3mm)", "pins": [{"pin_number": "1", "pin_name": "EN", "pin_description": "On/off control. Pull EN high to enable the MPQ28164. Pull EN low or leave EN floating to disable all of the internal circuits. EN is pulled down to AGND with a 1.5MΩ resistor internally."}, {"pin_number": "2", "pin_name": "MODE/SYNC", "pin_description": "Operation mode selection. If MODE/SYNC is low, the MPQ28164 switches between PSM and fixed frequency PWM automatically according to the load level. If MODE/SYNC is high, the MPQ28164 works in fixed-frequency PWM mode continuously. An external clock can be applied to MODE/SYNC for switching frequency synchronization. MODE/SYNC is pulled down to AGND with a 1MΩ resistor internally. MODE/SYNC should be pulled high or low through a resistor smaller than 10kΩ."}, {"pin_number": "3", "pin_name": "PG", "pin_description": "Power good indicator. PG switches high and low based on the feedback voltage."}, {"pin_number": "4", "pin_name": "VCC", "pin_description": "Supply voltage for control stage. VCC is powered by the higher value of either VIN or VOUT. Decouple VCC with a 1μF capacitor."}, {"pin_number": "5", "pin_name": "AGND", "pin_description": "Signal ground."}, {"pin_number": "6", "pin_name": "FB", "pin_description": "Output voltage feedback. Keep FB and its associated traces far away from noise sources like SW."}, {"pin_number": "7", "pin_name": "VOUT", "pin_description": "Buck-boost converter output. An output capacitor should be placed close to VOUT and PGND."}, {"pin_number": "8", "pin_name": "SW2", "pin_description": "Switch. Internal switches are connected to SW2. Connect an inductor between SW1 and SW2."}, {"pin_number": "9", "pin_name": "PGND", "pin_description": "Power ground."}, {"pin_number": "10", "pin_name": "SW1", "pin_description": "Switch. Internal switches are connected to SW1. Connect an inductor between SW1 and SW2."}, {"pin_number": "11", "pin_name": "VIN", "pin_description": "Supply voltage for the power stage."}]}], "datasheet_cn": "未找到", "datasheet_en": "MPQ28164 Rev. 1.0", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.2V", "max_output_voltage": "5V", "min_output_voltage": "1.5V", "max_output_current": "2A", "max_switch_frequency": "2.3MHz", "quiescent_current": "25µA", "high_side_mosfet_resistance": "27.5mΩ", "low_side_mosfet_resistance": "22mΩ", "over_current_protection_threshold": "4.2A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PSM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "0.5V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.5", "height": "0.9", "length": "3.0", "width": "2.0", "type": "INFORMATION", "pin_count": "6"}]}