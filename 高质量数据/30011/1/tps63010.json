[{"part_number": "TPS63010", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压-升压(<PERSON><PERSON><PERSON><PERSON>)稳压器", "part_number_title": "TPS6301x Highly Efficient, Single Inductor Buck-Boost Converter With 2-A Switches", "features": ["Up to 96% Efficiency", "1200-mA Output Current at 3.3 V in Step-Down Mode (VIN = 3.6 V to 5.5 V)", "Up to 800-mA Output Current at 3.3 V in Boost Mode (VIN > 2.4 V)", "Automatic Transition Between Step-Down and Boost Mode", "Device Quiescent Current less than 50 μA", "Input Voltage Range: 2 V to 5.5 V", "Fixed and Adjustable Output Voltage Options from 1.2 V to 5.5 V", "Power Save Mode for Improved Efficiency at Low-Output Power", "Forced Fixed Frequency Operation and Synchronization Possible", "Load Disconnect During Shutdown", "Output Overvoltage Protection", "Overtemperature Protection", "Available in Small 20-Pin, 2.126 mm × 1.922 mm, DSBGA Package"], "description": "The TPS6301x devices provide a power supply solution for products powered by either a two-cell or three-cell alkaline, NiCd or NiMH battery, or a one-cell Li-Ion or Li-polymer battery. Output currents can go as high as 1200 mA while using a single-cell Li-Ion or Li-Polymer Battery, and discharge it down to 2.5 V or lower. The buck-boost converter is based on a fixed-frequency, pulse-width-modulation (PWM) controller using synchronous rectification to obtain maximum efficiency. At low load currents, the converter enters power save mode to maintain high efficiency over a wide load current range. The power save mode can be disabled, forcing the converter to operate at a fixed switching frequency. The maximum average current in the switches is limited to a typical value of 2200 mA. The output voltage is programmable using an external resistor divider, or is fixed internally on the chip. The converter can be disabled to minimize battery drain. During shutdown, the load is disconnected from the battery. The device is packaged in a 20-pin DSBGA package measuring 2.126 mm x 1.922 mm (YFF).", "applications": ["All Two-Cell and Three-Cell Alkaline, NiCd or NiMH, or Single-Cell Li Battery-Powered Products", "Portable Audio Players", "PDAs", "Cellular Phones", "Personal Medical Products", "White LEDs"], "ordering_information": [{"part_number": "TPS63010", "order_device": "TPS63010YFFR", "package_type": "DSBGA", "package_drawing_code": "R-XBGA-N20", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63010", "order_device": "TPS63010YFFT", "package_type": "DSBGA", "package_drawing_code": "R-XBGA-N20", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63010, TPS63011, TPS63012", "package_type": "DSBGA", "pins": [{"pin_number": "A4", "pin_name": "EN", "pin_description": "Enable input. (1 enabled, 0 disabled)"}, {"pin_number": "E3", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT at fixed output voltage versions"}, {"pin_number": "C3, D3, E4", "pin_name": "GND", "pin_description": "Control and logic ground"}, {"pin_number": "B1,B2", "pin_name": "L1", "pin_description": "Connection for Inductor"}, {"pin_number": "D1,D2", "pin_name": "L2", "pin_description": "Connection for Inductor"}, {"pin_number": "C1,C2", "pin_name": "PGND", "pin_description": "Power ground"}, {"pin_number": "C4", "pin_name": "PS", "pin_description": "Enable and disable power save mode (1 disabled, 0 enabled)"}, {"pin_number": "B4", "pin_name": "SYNC", "pin_description": "Clock signal for synchronization, should be connected to GND if not used"}, {"pin_number": "A1, A2", "pin_name": "VIN", "pin_description": "Supply voltage for power stage"}, {"pin_number": "A3", "pin_name": "VINA", "pin_description": "Supply voltage for control stage"}, {"pin_number": "B3", "pin_name": "VINA1", "pin_description": "Output of the 100 Ω for designing the VINA filter"}, {"pin_number": "E1,E2", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}, {"pin_number": "D4", "pin_name": "VSEL", "pin_description": "Output voltage select for fixed output voltage options (1 programs higher output voltage option, 0 programs lower output voltage option), must be connected to a defined logic signal at adjustable output voltage option."}]}], "datasheet_cn": "未找到", "datasheet_en": "TPS63010, TPS63011, TPS63012 SLVS653C –FEBRUARY 2016", "family_comparison": "| PART NUMBER | OUTPUT VOLTAGE DC/DC at VSEL = 1 | OUTPUT VOLTAGE DC/DC at VSEL = 0 | PACKAGE MARKING |\n|---|---|---|---|\n| TPS63010 | Adjustable | Adjustable | TPS63010 |\n| TPS63011 | 3.3 V | 2.8 V | TPS63011 |\n| TPS63012 | 3.4 V | 2.9 V | TPS63012 |", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2V", "max_output_voltage": "5.5V", "min_output_voltage": "1.2V", "max_output_current": "1.2A", "max_switch_frequency": "2.6MHz", "quiescent_current": "50μA", "high_side_mosfet_resistance": "100mΩ", "low_side_mosfet_resistance": "100mΩ", "over_current_protection_threshold": "2.2A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±3.6%", "output_reference_voltage": "0.5V", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.400", "height": "0.625", "length": "2.13", "width": "1.92", "pin_count": 20, "type": "OPTION"}]}, {"part_number": "TPS63011", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压-升压(<PERSON><PERSON><PERSON><PERSON>)稳压器", "part_number_title": "TPS6301x Highly Efficient, Single Inductor Buck-Boost Converter With 2-A Switches", "features": ["Up to 96% Efficiency", "1200-mA Output Current at 3.3 V in Step-Down Mode (VIN = 3.6 V to 5.5 V)", "Up to 800-mA Output Current at 3.3 V in Boost Mode (VIN > 2.4 V)", "Automatic Transition Between Step-Down and Boost Mode", "Device Quiescent Current less than 50 μA", "Input Voltage Range: 2 V to 5.5 V", "Fixed and Adjustable Output Voltage Options from 1.2 V to 5.5 V", "Power Save Mode for Improved Efficiency at Low-Output Power", "Forced Fixed Frequency Operation and Synchronization Possible", "Load Disconnect During Shutdown", "Output Overvoltage Protection", "Overtemperature Protection", "Available in Small 20-Pin, 2.126 mm × 1.922 mm, DSBGA Package"], "description": "The TPS6301x devices provide a power supply solution for products powered by either a two-cell or three-cell alkaline, NiCd or NiMH battery, or a one-cell Li-Ion or Li-polymer battery. Output currents can go as high as 1200 mA while using a single-cell Li-Ion or Li-Polymer Battery, and discharge it down to 2.5 V or lower. The buck-boost converter is based on a fixed-frequency, pulse-width-modulation (PWM) controller using synchronous rectification to obtain maximum efficiency. At low load currents, the converter enters power save mode to maintain high efficiency over a wide load current range. The power save mode can be disabled, forcing the converter to operate at a fixed switching frequency. The maximum average current in the switches is limited to a typical value of 2200 mA. The output voltage is programmable using an external resistor divider, or is fixed internally on the chip. The converter can be disabled to minimize battery drain. During shutdown, the load is disconnected from the battery. The device is packaged in a 20-pin DSBGA package measuring 2.126 mm x 1.922 mm (YFF).", "applications": ["All Two-Cell and Three-Cell Alkaline, NiCd or NiMH, or Single-Cell Li Battery-Powered Products", "Portable Audio Players", "PDAs", "Cellular Phones", "Personal Medical Products", "White LEDs"], "ordering_information": [{"part_number": "TPS63011", "order_device": "TPS63011YFFR", "package_type": "DSBGA", "package_drawing_code": "R-XBGA-N20", "output_voltage": "3.3V/2.8V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63011", "order_device": "TPS63011YFFT", "package_type": "DSBGA", "package_drawing_code": "R-XBGA-N20", "output_voltage": "3.3V/2.8V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63010, TPS63011, TPS63012", "package_type": "DSBGA", "pins": [{"pin_number": "A4", "pin_name": "EN", "pin_description": "Enable input. (1 enabled, 0 disabled)"}, {"pin_number": "E3", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT at fixed output voltage versions"}, {"pin_number": "C3, D3, E4", "pin_name": "GND", "pin_description": "Control and logic ground"}, {"pin_number": "B1,B2", "pin_name": "L1", "pin_description": "Connection for Inductor"}, {"pin_number": "D1,D2", "pin_name": "L2", "pin_description": "Connection for Inductor"}, {"pin_number": "C1,C2", "pin_name": "PGND", "pin_description": "Power ground"}, {"pin_number": "C4", "pin_name": "PS", "pin_description": "Enable and disable power save mode (1 disabled, 0 enabled)"}, {"pin_number": "B4", "pin_name": "SYNC", "pin_description": "Clock signal for synchronization, should be connected to GND if not used"}, {"pin_number": "A1, A2", "pin_name": "VIN", "pin_description": "Supply voltage for power stage"}, {"pin_number": "A3", "pin_name": "VINA", "pin_description": "Supply voltage for control stage"}, {"pin_number": "B3", "pin_name": "VINA1", "pin_description": "Output of the 100 Ω for designing the VINA filter"}, {"pin_number": "E1,E2", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}, {"pin_number": "D4", "pin_name": "VSEL", "pin_description": "Output voltage select for fixed output voltage options (1 programs higher output voltage option, 0 programs lower output voltage option), must be connected to a defined logic signal at adjustable output voltage option."}]}], "datasheet_cn": "未找到", "datasheet_en": "TPS63010, TPS63011, TPS63012 SLVS653C –FEBRUARY 2016", "family_comparison": "| PART NUMBER | OUTPUT VOLTAGE DC/DC at VSEL = 1 | OUTPUT VOLTAGE DC/DC at VSEL = 0 | PACKAGE MARKING |\n|---|---|---|---|\n| TPS63010 | Adjustable | Adjustable | TPS63010 |\n| TPS63011 | 3.3 V | 2.8 V | TPS63011 |\n| TPS63012 | 3.4 V | 2.9 V | TPS63012 |", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2V", "max_output_voltage": "3.3V", "min_output_voltage": "2.8V", "max_output_current": "1.2A", "max_switch_frequency": "2.6MHz", "quiescent_current": "50μA", "high_side_mosfet_resistance": "100mΩ", "low_side_mosfet_resistance": "100mΩ", "over_current_protection_threshold": "2.2A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "不适用", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.4", "height": "0.625", "length": "2.126", "width": "1.922", "type": "OPTION", "pin_count": "20"}]}, {"part_number": "TPS63012", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压-升压(<PERSON><PERSON><PERSON><PERSON>)稳压器", "part_number_title": "TPS6301x Highly Efficient, Single Inductor Buck-Boost Converter With 2-A Switches", "features": ["Up to 96% Efficiency", "1200-mA Output Current at 3.3 V in Step-Down Mode (VIN = 3.6 V to 5.5 V)", "Up to 800-mA Output Current at 3.3 V in Boost Mode (VIN > 2.4 V)", "Automatic Transition Between Step-Down and Boost Mode", "Device Quiescent Current less than 50 μA", "Input Voltage Range: 2 V to 5.5 V", "Fixed and Adjustable Output Voltage Options from 1.2 V to 5.5 V", "Power Save Mode for Improved Efficiency at Low-Output Power", "Forced Fixed Frequency Operation and Synchronization Possible", "Load Disconnect During Shutdown", "Output Overvoltage Protection", "Overtemperature Protection", "Available in Small 20-Pin, 2.126 mm × 1.922 mm, DSBGA Package"], "description": "The TPS6301x devices provide a power supply solution for products powered by either a two-cell or three-cell alkaline, NiCd or NiMH battery, or a one-cell Li-Ion or Li-polymer battery. Output currents can go as high as 1200 mA while using a single-cell Li-Ion or Li-Polymer Battery, and discharge it down to 2.5 V or lower. The buck-boost converter is based on a fixed-frequency, pulse-width-modulation (PWM) controller using synchronous rectification to obtain maximum efficiency. At low load currents, the converter enters power save mode to maintain high efficiency over a wide load current range. The power save mode can be disabled, forcing the converter to operate at a fixed switching frequency. The maximum average current in the switches is limited to a typical value of 2200 mA. The output voltage is programmable using an external resistor divider, or is fixed internally on the chip. The converter can be disabled to minimize battery drain. During shutdown, the load is disconnected from the battery. The device is packaged in a 20-pin DSBGA package measuring 2.126 mm x 1.922 mm (YFF).", "applications": ["All Two-Cell and Three-Cell Alkaline, NiCd or NiMH, or Single-Cell Li Battery-Powered Products", "Portable Audio Players", "PDAs", "Cellular Phones", "Personal Medical Products", "White LEDs"], "ordering_information": [{"part_number": "TPS63012", "order_device": "TPS63012YFFR", "package_type": "DSBGA", "package_drawing_code": "R-XBGA-N20", "output_voltage": "3.4V/2.9V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63012", "order_device": "TPS63012YFFT", "package_type": "DSBGA", "package_drawing_code": "R-XBGA-N20", "output_voltage": "3.4V/2.9V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63010, TPS63011, TPS63012", "package_type": "DSBGA", "pins": [{"pin_number": "A4", "pin_name": "EN", "pin_description": "Enable input. (1 enabled, 0 disabled)"}, {"pin_number": "E3", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT at fixed output voltage versions"}, {"pin_number": "C3, D3, E4", "pin_name": "GND", "pin_description": "Control and logic ground"}, {"pin_number": "B1,B2", "pin_name": "L1", "pin_description": "Connection for Inductor"}, {"pin_number": "D1,D2", "pin_name": "L2", "pin_description": "Connection for Inductor"}, {"pin_number": "C1,C2", "pin_name": "PGND", "pin_description": "Power ground"}, {"pin_number": "C4", "pin_name": "PS", "pin_description": "Enable and disable power save mode (1 disabled, 0 enabled)"}, {"pin_number": "B4", "pin_name": "SYNC", "pin_description": "Clock signal for synchronization, should be connected to GND if not used"}, {"pin_number": "A1, A2", "pin_name": "VIN", "pin_description": "Supply voltage for power stage"}, {"pin_number": "A3", "pin_name": "VINA", "pin_description": "Supply voltage for control stage"}, {"pin_number": "B3", "pin_name": "VINA1", "pin_description": "Output of the 100 Ω for designing the VINA filter"}, {"pin_number": "E1,E2", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}, {"pin_number": "D4", "pin_name": "VSEL", "pin_description": "Output voltage select for fixed output voltage options (1 programs higher output voltage option, 0 programs lower output voltage option), must be connected to a defined logic signal at adjustable output voltage option."}]}], "datasheet_cn": "未找到", "datasheet_en": "TPS63010, TPS63011, TPS63012 SLVS653C –FEBRUARY 2016", "family_comparison": "| PART NUMBER | OUTPUT VOLTAGE DC/DC at VSEL = 1 | OUTPUT VOLTAGE DC/DC at VSEL = 0 | PACKAGE MARKING |\n|---|---|---|---|\n| TPS63010 | Adjustable | Adjustable | TPS63010 |\n| TPS63011 | 3.3 V | 2.8 V | TPS63011 |\n| TPS63012 | 3.4 V | 2.9 V | TPS63012 |", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2V", "max_output_voltage": "3.4V", "min_output_voltage": "2.9V", "max_output_current": "1.2A", "max_switch_frequency": "2.6MHz", "quiescent_current": "50μA", "high_side_mosfet_resistance": "100mΩ", "low_side_mosfet_resistance": "100mΩ", "over_current_protection_threshold": "2.2A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "不适用", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.4", "height": "0.625", "length": "2.126", "width": "1.922", "type": "OPTION", "pin_count": "20"}]}]