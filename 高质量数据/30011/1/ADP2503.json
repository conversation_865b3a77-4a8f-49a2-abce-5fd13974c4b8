[{"part_number": "ADP2503", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "600 mA/1000 mA, 2.5 MHz Buck-Boost DC-to-DC Converters", "features": ["1 mm height profile", "Compact PCB footprint", "Seamless transition between modes", "38 µA typical quiescent current", "2.5 MHz operation enables 1.5 µH inductor", "Input voltage: 2.3 V to 5.5 V", "Fixed output voltage: 2.8 V to 5.0 V", "Adjustable model output voltage range: 2.8 V to 5.5 V", "600 mA (ADP2503) and 1000 mA (ADP2504) output options", "Boost converter configuration with load disconnect", "SYNC pin with three different modes", "Power save mode (PSM) for improved light load efficiency", "Forced fixed frequency operation mode", "Synchronization with external clock", "Internal compensation", "Soft start", "Enable/shutdown logic input", "Overtemperature protection", "Short-circuit protection", "Undervoltage lockout protection", "Small 10-lead 3 mm × 3 mm LFCSP (QFN) package", "Supported by ADIsimPower™ design tool"], "description": "The ADP2503/ADP2504 are high efficiency, low quiescent current step-up/step-down dc-to-dc converters that can operate at input voltages greater than, less than, or equal to the regulated output voltage. The power switches and synchronous rectifiers are internal to minimize external device count. At high load currents, the ADP2503/ADP2504 use a current-mode, fixed frequency pulse-width modulation (PWM) control scheme for optimal stability and transient response. To ensure the longest battery life in portable applications, the ADP2503/ADP2504 have an optional power save mode that reduces the switching frequency under light load conditions. For wireless and other low noise applications where variable frequency power save mode may cause interference, the logic control input sync forces fixed frequency PWM operation under all load conditions.\nThe ADP2503/ADP2504 can run from input voltages between 2.3 V and 5.5 V, allowing single lithium or lithium polymer cell, multiple alkaline or NiMH cells, PCMCIA, USB, and other standard power sources. The ADP2503/ADP2504 have fixed output options, or using the adjustable model, the output voltage can be programmed through an external resistor divider. Compensation is internal to minimize the number of external components.\nDuring logic-controlled shutdown, the input is disconnected from the output and draws less than 1 µA from the input source. Operating as boost converters, the ADP2503/ADP2504 feature a true load disconnect function that isolates the load from the power source. Other key features include undervoltage lockout to prevent deep battery discharge, and soft start to prevent input current overshoot at startup.", "applications": ["Wireless handsets", "Digital cameras/portable audio players", "Miniature hard disk power supplies", "USB powered devices"], "ordering_information": [{"part_number": "ADP2503", "order_device": "ADP2503ACPZ-3.3-R7", "package_type": "LFCSP", "package_drawing_code": "CP-10-9", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ADP2503", "order_device": "ADP2503ACPZ-4.2-R7", "package_type": "LFCSP", "package_drawing_code": "CP-10-9", "output_voltage": "4.2V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ADP2503", "order_device": "ADP2503ACPZ-4.5-R7", "package_type": "LFCSP", "package_drawing_code": "CP-10-9", "output_voltage": "4.5V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ADP2503", "order_device": "ADP2503ACPZ-5.0-R7", "package_type": "LFCSP", "package_drawing_code": "CP-10-9", "output_voltage": "5.0V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ADP2503", "order_device": "ADP2503ACPZ-R7", "package_type": "LFCSP", "package_drawing_code": "CP-10-9", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "ADP2503", "package_type": "LFCSP", "pins": [{"pin_number": "1", "pin_name": "VOUT", "pin_description": "Output of the ADP2503/ADP2504. Connect the output capacitor between VOUT and PGND."}, {"pin_number": "2", "pin_name": "SW2", "pin_description": "Power Switch 2 Connection. This is the internal connection to the input PMOS and NMOS switches. Connect SW2 to the inductor with a short, wide track."}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power GND. Connect the input and output capacitors and the PGND pin to a PGND plane."}, {"pin_number": "4", "pin_name": "SW1", "pin_description": "Power Switch 1 Connection. This is the internal connection to the output PMOS and NMOS switches. Connect SW1 to the inductor with a short, wide track."}, {"pin_number": "5", "pin_name": "PVIN", "pin_description": "Power Input. This is the input to the buck-boost power switches. Place a 10 µF capacitor between PVIN and PGND as close as possible to the ADP2503/ADP2504."}, {"pin_number": "6", "pin_name": "EN", "pin_description": "Enable. Drive EN high to turn on the ADP2503/ADP2504. Bring EN low to put the device into shutdown mode."}, {"pin_number": "7", "pin_name": "SYNC", "pin_description": "The SYNC pin permits the ADP2503/ADP2504 to operate in three different modes. Normal operation: with SYNC driven low, the ADP2503/ADP2504 operate at 2.5 MHz PWM mode for heavy and medium loads, and moves to power save mode (PSM) mode for light loads. Forced PWM operation: with SYNC driven high, the ADP2503/ADP2504 operate at fixed 2.5 MHz PWM mode for all load conditions. SYNC mode: to synchronize the ADP2503/ADP2504 switching to an external signal, drive this pin with a clock between 2.2 MHz and 2.8 MHz. The SYNC signal must have on and off times greater than 160 ns."}, {"pin_number": "8", "pin_name": "VIN", "pin_description": "Analog Power Supply. This is the supply for the ADP2503/ADP2504 internal circuitry."}, {"pin_number": "9", "pin_name": "AGND", "pin_description": "Analog Ground."}, {"pin_number": "10", "pin_name": "FB", "pin_description": "Output Feedback. This is an input to the internal error amplifier and must be connected to VOUT on fixed output versions; for the adjustable model, this is the voltage feedback."}, {"pin_number": "EP", "pin_name": "Exposed pad", "pin_description": "Connect the exposed pad to PGND."}]}], "datasheet_cn": "未找到", "datasheet_en": "ADP2503/ADP2504 Rev. F 2024-07", "family_comparison": "ADP2503和ADP2504是同一家族的降压-升压转换器，主要区别在于最大输出电流能力。ADP2503: 600 mA, ADP2504: 1000 mA", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.3V", "max_output_voltage": "5.5V", "min_output_voltage": "2.8V", "max_output_current": "0.6A", "max_switch_frequency": "2.5MHz", "quiescent_current": "38µA", "high_side_mosfet_resistance": "150mΩ", "low_side_mosfet_resistance": "150mΩ", "over_current_protection_threshold": "1A", "operation_mode": "同步", "output_voltage_config_method": "固定/可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PSM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.5V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "LFCSP", "height": "1.0", "length": "3.0", "width": "3.0", "pitch": "2504.", "pin_count": "2504"}]}, {"part_number": "ADP2504", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "600 mA/1000 mA, 2.5 MHz Buck-Boost DC-to-DC Converters", "features": ["1 mm height profile", "Compact PCB footprint", "Seamless transition between modes", "38 µA typical quiescent current", "2.5 MHz operation enables 1.5 µH inductor", "Input voltage: 2.3 V to 5.5 V", "Fixed output voltage: 2.8 V to 5.0 V", "Adjustable model output voltage range: 2.8 V to 5.5 V", "600 mA (ADP2503) and 1000 mA (ADP2504) output options", "Boost converter configuration with load disconnect", "SYNC pin with three different modes", "Power save mode (PSM) for improved light load efficiency", "Forced fixed frequency operation mode", "Synchronization with external clock", "Internal compensation", "Soft start", "Enable/shutdown logic input", "Overtemperature protection", "Short-circuit protection", "Undervoltage lockout protection", "Small 10-lead 3 mm × 3 mm LFCSP (QFN) package", "Supported by ADIsimPower™ design tool"], "description": "The ADP2503/ADP2504 are high efficiency, low quiescent current step-up/step-down dc-to-dc converters that can operate at input voltages greater than, less than, or equal to the regulated output voltage. The power switches and synchronous rectifiers are internal to minimize external device count. At high load currents, the ADP2503/ADP2504 use a current-mode, fixed frequency pulse-width modulation (PWM) control scheme for optimal stability and transient response. To ensure the longest battery life in portable applications, the ADP2503/ADP2504 have an optional power save mode that reduces the switching frequency under light load conditions. For wireless and other low noise applications where variable frequency power save mode may cause interference, the logic control input sync forces fixed frequency PWM operation under all load conditions.\nThe ADP2503/ADP2504 can run from input voltages between 2.3 V and 5.5 V, allowing single lithium or lithium polymer cell, multiple alkaline or NiMH cells, PCMCIA, USB, and other standard power sources. The ADP2503/ADP2504 have fixed output options, or using the adjustable model, the output voltage can be programmed through an external resistor divider. Compensation is internal to minimize the number of external components.\nDuring logic-controlled shutdown, the input is disconnected from the output and draws less than 1 µA from the input source. Operating as boost converters, the ADP2503/ADP2504 feature a true load disconnect function that isolates the load from the power source. Other key features include undervoltage lockout to prevent deep battery discharge, and soft start to prevent input current overshoot at startup.", "applications": ["Wireless handsets", "Digital cameras/portable audio players", "Miniature hard disk power supplies", "USB powered devices"], "ordering_information": [{"part_number": "ADP2504", "order_device": "ADP2504ACPZ-3.3-R7", "package_type": "LFCSP", "package_drawing_code": "CP-10-9", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ADP2504", "order_device": "ADP2504ACPZ-3.5-R7", "package_type": "LFCSP", "package_drawing_code": "CP-10-9", "output_voltage": "3.5V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ADP2504", "order_device": "ADP2504ACPZ-5.0-R7", "package_type": "LFCSP", "package_drawing_code": "CP-10-9", "output_voltage": "5.0V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ADP2504", "order_device": "ADP2504ACPZ-R7", "package_type": "LFCSP", "package_drawing_code": "CP-10-9", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "ADP2504", "package_type": "LFCSP", "pins": [{"pin_number": "1", "pin_name": "VOUT", "pin_description": "Output of the ADP2503/ADP2504. Connect the output capacitor between VOUT and PGND."}, {"pin_number": "2", "pin_name": "SW2", "pin_description": "Power Switch 2 Connection. This is the internal connection to the input PMOS and NMOS switches. Connect SW2 to the inductor with a short, wide track."}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power GND. Connect the input and output capacitors and the PGND pin to a PGND plane."}, {"pin_number": "4", "pin_name": "SW1", "pin_description": "Power Switch 1 Connection. This is the internal connection to the output PMOS and NMOS switches. Connect SW1 to the inductor with a short, wide track."}, {"pin_number": "5", "pin_name": "PVIN", "pin_description": "Power Input. This is the input to the buck-boost power switches. Place a 10 µF capacitor between PVIN and PGND as close as possible to the ADP2503/ADP2504."}, {"pin_number": "6", "pin_name": "EN", "pin_description": "Enable. Drive EN high to turn on the ADP2503/ADP2504. Bring EN low to put the device into shutdown mode."}, {"pin_number": "7", "pin_name": "SYNC", "pin_description": "The SYNC pin permits the ADP2503/ADP2504 to operate in three different modes. Normal operation: with SYNC driven low, the ADP2503/ADP2504 operate at 2.5 MHz PWM mode for heavy and medium loads, and moves to power save mode (PSM) mode for light loads. Forced PWM operation: with SYNC driven high, the ADP2503/ADP2504 operate at fixed 2.5 MHz PWM mode for all load conditions. SYNC mode: to synchronize the ADP2503/ADP2504 switching to an external signal, drive this pin with a clock between 2.2 MHz and 2.8 MHz. The SYNC signal must have on and off times greater than 160 ns."}, {"pin_number": "8", "pin_name": "VIN", "pin_description": "Analog Power Supply. This is the supply for the ADP2503/ADP2504 internal circuitry."}, {"pin_number": "9", "pin_name": "AGND", "pin_description": "Analog Ground."}, {"pin_number": "10", "pin_name": "FB", "pin_description": "Output Feedback. This is an input to the internal error amplifier and must be connected to VOUT on fixed output versions; for the adjustable model, this is the voltage feedback."}, {"pin_number": "EP", "pin_name": "Exposed pad", "pin_description": "Connect the exposed pad to PGND."}]}], "datasheet_cn": "未找到", "datasheet_en": "ADP2503/ADP2504 Rev. F 2024-07", "family_comparison": "ADP2503和ADP2504是同一家族的降压-升压转换器，主要区别在于最大输出电流能力。ADP2503: 600 mA, ADP2504: 1000 mA", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.3V", "max_output_voltage": "5.5V", "min_output_voltage": "2.8V", "max_output_current": "1A", "max_switch_frequency": "2.5MHz", "quiescent_current": "38µA", "high_side_mosfet_resistance": "150mΩ", "low_side_mosfet_resistance": "150mΩ", "over_current_protection_threshold": "1.3A", "operation_mode": "同步", "output_voltage_config_method": "固定/可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PSM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.5V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "LFCSP", "height": "1.0", "length": "3.0", "width": "3.0", "pitch": "2504.", "pin_count": "2504"}]}]