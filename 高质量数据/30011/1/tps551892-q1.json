{"part_number": "TPS551892-Q1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压-升压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "TPS551892-Q1 汽车级 27V、8A 完全集成式降压/升压转换器", "features": ["符合 AEC-Q100 标准: 器件温度等级 1: –40°C 至 +125°C 环境工作温度范围", "宽输入和输出电压范围: 宽输入电压范围: 3.0V 至 27V (绝对最大值为 42V); 可编程输出电压范围: 0.8V 至 22V; ±1% 基准电压精度; 对电缆压降提供可调输出电压补偿; ±5% 精密输出电流监测", "在整个负载范围内具有高效率: VIN = 12V、VOUT = 20V 且 IOUT = 3A 时效率为 96%; 轻负载状态下的可编程 PFM 和 FPWM 模式", "避免频率干扰和串扰: 可选的时钟同步; 可编程开关频率范围为 200kHz 至 2.2MHz", "降低 EMI: 可选可编程扩展频谱; 无引线封装", "丰富的保护特性: 输出过压保护; 利用断续模式实现输出短路保护; 热关断保护; 8A 平均电感器电流限值", "小解决方案尺寸: 开关频率高达 2.2MHz (最大值); 3.0mm x 5.0mm HotRod™ QFN 封装"], "description": "TPS551892-Q1 同步降压/升压转换器经优化，可将电池电压或适配器电压转换为电源轨。TPS551892-Q1 集成了四个 MOSFET 开关，可为各种应用提供紧凑型解决方案。TPS551892-Q1 的输入电压高达 27V。在升压模式下，输入电压为 12V 时，TPS551892-Q1 可提供 60W 的功率；输入电压为 9V 时，可提供 45W 的功率。TPS551892-Q1 采用平均电流模式控制方案。开关频率可通过外部电阻在 200kHz 至 2.2MHz 之间进行编程，并且可与外部时钟同步。TPS551892-Q1 还提供展频选项，从而更大限度地减少峰值 EMI。TPS551892-Q1 提供输出过压保护、平均电感器电流限制、逐周期峰值电流限制和输出短路保护。TPS551892-Q1 还可在持续过载情况下，通过可选输出电流限值和断续模式保护来确保安全运行。TPS551892-Q1 可以使用具有高开关频率的小型电感器和电容器。它采用 3.0mm × 5.0mm QFN 封装。", "applications": ["高级驾驶辅助系统 (ADAS)", "无线充电器", "汽车信息娱乐系统与仪表组"], "ordering_information": [{"part_number": "TPS551892-Q1", "order_device": "TPS551892QWRYQRQ1", "package_type": "VQFN-HR", "package_drawing_code": "RYQ0021B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS551892-Q1", "order_device": "TPS551892QWRYQRQ1.A", "package_type": "VQFN-HR", "package_drawing_code": "RYQ0021B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS551892-Q1", "package_type": "VQFN-HR", "pins": [{"pin_number": "1", "pin_name": "EN/UVLO", "pin_description": "启用逻辑输入和可编程输入电压欠压锁定 (UVLO) 输入。逻辑高电平可启用器件。逻辑低电平可禁用器件并将其转换为关断模式。EN/UVLO 引脚上的电压高于 1.15V 的逻辑高电平电压后，该引脚可充当可编程 UVLO 输入，具有 1.23V 的内部基准电压。"}, {"pin_number": "2", "pin_name": "MODE", "pin_description": "轻负载条件下的模式选择引脚。将它连接至逻辑高电压时，此器件在强制 PWM 模式下运行。将它连接至逻辑低电压时，此器件在自动 PFM 模式下运行。此引脚在应用中不能悬空。"}, {"pin_number": "3", "pin_name": "PG", "pin_description": "指示电源正常的开漏输出。当输出电压高于设定输出电压的 95% 时，此引脚输出高阻抗。当输出电压低于设定输出电压的 90% 时，此引脚输出低电平。"}, {"pin_number": "4", "pin_name": "CC", "pin_description": "指示恒定电流输出的开漏输出。触发输出电流限制时，此引脚输出低电平。"}, {"pin_number": "5", "pin_name": "DITH/SYNC", "pin_description": "抖动频率设置和同步时钟输入。在该引脚和接地端之间，使用电容器来设置抖动频率。该引脚接地短路或拉至 1.2V 以上时，无抖动功能。可以在该引脚上应用外部时钟，来同步开关频率。"}, {"pin_number": "6", "pin_name": "FSW", "pin_description": "开关频率可通过该引脚和 AGND 引脚之间的电阻进行编程。"}, {"pin_number": "7", "pin_name": "VIN", "pin_description": "降压/升压转换器的输入。"}, {"pin_number": "8", "pin_name": "SW1", "pin_description": "降压侧的开关节点引脚。它连接到内部降压低侧功率 MOSFET 的漏极，以及内部降压高侧功率 MOSFET 的源极。"}, {"pin_number": "9", "pin_name": "PGND", "pin_description": "IC 的电源接地。"}, {"pin_number": "10", "pin_name": "SW2", "pin_description": "升压侧的开关节点引脚。它连接到内部升压低侧功率 MOSFET 的漏极，以及内部升压高侧功率 MOSFET 的源极。"}, {"pin_number": "11", "pin_name": "VOUT", "pin_description": "降压/升压转换器的输出。"}, {"pin_number": "12", "pin_name": "ISP", "pin_description": "电流检测放大器的正输入。在 ISP 引脚和 ISN 引脚之间连接的可选电流检测电阻可以限制输出电流。如果检测到的电压达到电流限值，将激活慢速恒定电流控制环路，并开始调节 ISP 引脚和 ISN 引脚之间的电压。将 ISP 引脚和 ISN 引脚与 VOUT 引脚连接到一起，可以禁用输出电流限制功能。"}, {"pin_number": "13", "pin_name": "ISN", "pin_description": "电流检测放大器的负输入。在 ISP 引脚和 ISN 引脚之间连接的可选电流检测电阻可以限制输出电流。如果检测到的电压达到电流限值，将激活慢速恒定电流控制环路，并开始调节 ISP 引脚和 ISN 引脚之间的电压。将 ISP 引脚和 ISN 引脚与 VOUT 引脚连接到一起，可以禁用输出电流限制功能。"}, {"pin_number": "14", "pin_name": "FB", "pin_description": "连接到电阻分压器的中心，可对输出电压进行编程。"}, {"pin_number": "15", "pin_name": "COMP", "pin_description": "内部误差放大器的输出。在该引脚和 AGND 引脚之间连接环路补偿网络。"}, {"pin_number": "16", "pin_name": "CDC", "pin_description": "电压输出与 ISP 引脚和 ISN 引脚之间检测到的电压成正比。在该引脚和 AGND 之间使用一个电阻器来增加输出电压，以补偿电缆上由电缆电阻引起的压降。"}, {"pin_number": "17", "pin_name": "AGND", "pin_description": "IC 的信号接地。"}, {"pin_number": "18", "pin_name": "VCC", "pin_description": "内部稳压器的输出。在此引脚和 AGND 引脚之间需要一个大于 4.7μF 的陶瓷电容器。"}, {"pin_number": "19", "pin_name": "BOOT2", "pin_description": "升压侧高侧 MOSFET 栅极驱动器的电源。必须在此引脚和 SW2 引脚之间连接一个 0.1μF 的陶瓷电容器。"}, {"pin_number": "20", "pin_name": "BOOT1", "pin_description": "降压侧高侧 MOSFET 栅极驱动器的电源。必须在此引脚和 SW1 引脚之间连接一个 0.1μF 的陶瓷电容器。"}, {"pin_number": "21", "pin_name": "EXTVCC", "pin_description": "为 VCC 选择内部 LDO 或外部 5V。将它连接至逻辑高电压或者保持悬空时，可选择内部 LDO。将它连接至逻辑低电压时，可为 VCC 选择外部 5V。"}]}], "datasheet_cn": "TPS551892-Q1_ZHCSUV6.pdf", "datasheet_en": "SLVSHR3", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "27V", "min_input_voltage": "3.0V", "max_output_voltage": "22V", "min_output_voltage": "0.8V", "max_output_current": "8A", "max_switch_frequency": "2.2MHz", "quiescent_current": "760µA", "high_side_mosfet_resistance": "降压侧: 14mΩ, 升压侧: 11mΩ", "low_side_mosfet_resistance": "降压侧: 22mΩ, 升压侧: 11mΩ", "over_current_protection_threshold": "8A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM, FPWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "无", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "无", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "1.2V", "loop_control_mode": "平均电流模式"}, "package": [{"type": "OPTION", "pitch": "0.5", "height": "1.0", "length": "5.0", "width": "3.0", "pin_count": "2"}]}