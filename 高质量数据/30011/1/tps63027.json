{"part_number": "TPS63027", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压-升压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "TPS63027 高电流、高效单电感器降压-升压转换器", "features": ["真正的降压或升压运行，可在降压与升压运行状态之间自动无缝切换", "2.3V 至 5.5V 输入电压范围", "1.0V 至 5.5V 输出电压范围", "2A 持续输出电流: VIN ≥ 2.5V, VOUT = 3.5V", "效率高达 96%", "2.5MHz 典型开关频率", "35µA 静态工作电流", "集成软启动", "节能模式", "真正实现关断", "输出电容器放电功能", "过热保护以及过流保护", "宽泛的电容选择", "小型 2.1mm x 2.1mm, 25 引脚晶圆级芯片 (WCSP) 封装"], "description": "TPS63027 是一款具有低静态电流的高效降压-升压转换器，适用于输入电压可能高于或低于输出电压的应用。在升压模式下，输出电流可高达 2A，而在降压模式下，输出电流可高达 4A。开关的最大平均电流限制为 4.5A（典型值）。TPS63027 能够根据输入电压在降压与升压模式之间自动切换，确保在两种模式之间无缝切换，从而在整个输入电压范围内调节输出电压。此降压-升压转换器基于一个使用同步整流的固定频率、脉宽调制 (PWM) 控制器以获得最高效率。在低负载电流情况下，此转换器进入省电模式，以便在整个负载电流范围内保持高效率。有一个使用户能够在自动 PFM/PWM 模式运行和强制 PWM 运行之间进行选择的 PFM/PWM 引脚。在 PWM 模式下通常使用 2.5MHz 固定频率。使用一个外部电阻器分压器可对输出电压进行编程，或者在芯片上对输出电压进行内部固定。转换器可被禁用以大大减少电池消耗。在关机期间，负载从电池上断开。此器件采用 25 引脚 2.1mm x 2.1 mm WCSP 封装。", "applications": ["手机、智能电话", "平板个人电脑", "个人电脑和智能手机配件", "负载点稳压", "电池供电类 应用"], "ordering_information": [{"part_number": "TPS63027", "order_device": "TPS63027YFFR", "package_type": "DSBGA", "package_drawing_code": "YFF0025", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63027", "order_device": "TPS63027YFFR.A", "package_type": "DSBGA", "package_drawing_code": "YFF0025", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63027", "order_device": "TPS63027YFFT", "package_type": "DSBGA", "package_drawing_code": "YFF0025", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63027", "order_device": "TPS63027YFFT.A", "package_type": "DSBGA", "package_drawing_code": "YFF0025", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63027", "package_type": "DSBGA", "pins": [{"pin_number": "A1, A2, A3, A4", "pin_name": "VIN", "pin_description": "Supply voltage for power stage"}, {"pin_number": "A5", "pin_name": "AVIN", "pin_description": "Supply voltage for control stage"}, {"pin_number": "B1, B2, B3, B4", "pin_name": "L1", "pin_description": "Connection for Inductor"}, {"pin_number": "B5", "pin_name": "EN", "pin_description": "Enable input. Set high to enable and low to disable. It must not be left floating"}, {"pin_number": "C1, C2, C3", "pin_name": "GND", "pin_description": "Power Ground"}, {"pin_number": "C4", "pin_name": "MODE", "pin_description": "PFM/PWM Mode selection. Set HIGH for PFM mode, set LOW for forced PWM mode. It must not be left floating"}, {"pin_number": "C5, D5", "pin_name": "AGND", "pin_description": "Analog Ground"}, {"pin_number": "D1, D2, D3, D4", "pin_name": "L2", "pin_description": "Connection for Inductor"}, {"pin_number": "E1, E2, E3, E4", "pin_name": "VOUT", "pin_description": "Buck-Boost converter output"}, {"pin_number": "E5", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable version, must be connected to VOUT on fixed output voltage versions"}]}], "datasheet_cn": "TPS63027 (版本: ZHCSFX4, 发布日期: 2016-12)", "datasheet_en": "TPS63027 (版本: SLVSDK8)", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.3V", "max_output_voltage": "5.5V", "min_output_voltage": "1V", "max_output_current": "2A", "max_switch_frequency": "2.5MHz", "quiescent_current": "35µA", "high_side_mosfet_resistance": "33mΩ", "low_side_mosfet_resistance": "56mΩ", "over_current_protection_threshold": "4.5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "电流限制", "over_temperature_protection": "自动重启", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.8V", "loop_control_mode": "平均电流模式"}, "package": [{"type": "OPTION", "length": "2.1", "width": "2.1", "pin_count": "7", "pitch": "0.4", "height": "0.625"}]}