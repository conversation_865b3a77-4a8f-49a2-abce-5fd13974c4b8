{"part_number": "MAX77801", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "5.5V Input, 2A, High-Efficiency Buck-Boost Converter", "features": ["VIN Range: 2.30V to 5.5V", "VOUT Range: 2.60V to 4.18V (I2C Programmable in 12.5mV Steps)", "Up to 2A Output Current in Boost Mode (VIN = 3.0V, VOUT = 3.4V)", "Up to 3A Output Current in Buck Mode", "Up to 97% Peak Efficiency", "SKIP Mode for Optimal Light Load Efficiency", "55μA (Typ) Low Quiescent Current", "3.4MHz High-Speed I2C Serial Interface", "Dynamic Voltage Scaling (DVS) Function", "Power-OK Output", "2.5MHz Switching Frequency", "Protection Features: Soft-Start, Thermal Shutdown, Overvoltage Protection, Overcurrent Protection", "2.13mm x 1.83mm, 20-Bump WLP", "4mm x 4mm, 20-Pin TQFN"], "description": "The MAX77801 is a high-efficiency, step-up/step-down (buck-boost) converter targeted for single-cell, Li-ion battery-powered applications. The device maintains a regulated output voltage from 2.6V to 4.18V across an input voltage range of 2.3V to 5.5V. The device supports up to 2A of output current in boost mode and up to 3A in buck mode. The device seamlessly transitions between buck and boost modes. A unique control algorithm allows high-efficiency and outstanding load and line-transient response. Dedicated enable and power-OK pins allow simple hardware control. An I2C serial interface is optionally used for dynamic voltage scaling, system power optimization, and fault read-back. The MAX77801 is available in a 20-bump, 2.13mm x 1.83mm wafer-level package (WLP) and also 20-pin, 4mm x 4mm TQFN.", "applications": ["Single-Cell, Li-Ion Battery-Powered Devices", "Handheld Scanners, Mobile Payment Terminals, Security Cameras", "AR/VR Headsets"], "ordering_information": [{"part_number": "MAX77801", "order_device": "MAX77801EWP+T", "status": "Active", "package_type": "WLP", "package_code": "W201F2+1", "carrier_description": "Tape and reel", "carrier_quantity": "未找到", "package_drawing_code": "21-0771", "marking": "未找到", "pin_count": 20, "length": 2.13, "width": 1.83, "height": "未找到", "pitch": 0.4, "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "3.3V/3.4V", "application_grade": "Automotive"}, {"part_number": "MAX77801", "order_device": "MAX77801ETP+T", "status": "Active", "package_type": "TQFN", "package_code": "T2044-3C", "carrier_description": "Tape and reel", "carrier_quantity": "未找到", "package_drawing_code": "21-0139", "marking": "未找到", "pin_count": 20, "length": 4, "width": 4, "height": "未找到", "pitch": 0.75, "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "3.3V/3.75V", "application_grade": "Automotive"}, {"part_number": "MAX77801", "order_device": "MAX77801HEWP+T", "status": "Active", "package_type": "WLP", "package_code": "W201F2+1", "carrier_description": "Tape and reel", "carrier_quantity": "未找到", "package_drawing_code": "21-0771", "marking": "未找到", "pin_count": 20, "length": 2.13, "width": 1.83, "height": "未找到", "pitch": 0.4, "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "3.3V/3.4V", "application_grade": "Automotive"}], "pin_function": [{"product_part_number": "MAX77801", "package_type": "20-BUMP WLP", "pins": [{"pin_number": "A1", "pin_name": "VSYS", "pin_description": "System (Battery) Voltage Input. Bypass to AGND with a 10V, 1μF capacitor."}, {"pin_number": "A2", "pin_name": "DVS", "pin_description": "Dynamic Voltage Scaling Logic Input. Connect to AGND if not used."}, {"pin_number": "A3, B3", "pin_name": "AGND", "pin_description": "Analog Ground. Connect to PGND on the PCB. See the PCB Layout Guideliness."}, {"pin_number": "A4", "pin_name": "SDA", "pin_description": "I2C Serial Interface Data (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor. Connect to AGND if not used."}, {"pin_number": "A5", "pin_name": "SCL", "pin_description": "I2C Serial Interface Clock (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor. Connect to AGND if not used."}, {"pin_number": "B1", "pin_name": "FB", "pin_description": "Output Voltage Sense."}, {"pin_number": "B2", "pin_name": "POK", "pin_description": "Open-Drain Power-OK Output. Asserts high (high-Z) when buck-boost output reaches 80% of target."}, {"pin_number": "B4", "pin_name": "EN", "pin_description": "Active-High Enable Input. This pin has an 800kΩ internal pulldown to AGND."}, {"pin_number": "B5", "pin_name": "VIO", "pin_description": "I2C Supply Voltage Input. Bypass to AGND with a 0.1μF capacitor. Connect to AGND if not used."}, {"pin_number": "C1, D1", "pin_name": "OUT", "pin_description": "Output. Bypass to PGND with a 10V 47μF ceramic capacitor."}, {"pin_number": "C2, D2", "pin_name": "LX2", "pin_description": "Switching Node 2."}, {"pin_number": "C3, D3", "pin_name": "PGND", "pin_description": "Power Ground. Connect to AGND on the PCB. See the PCB Layout Guideliness."}, {"pin_number": "C4, D4", "pin_name": "LX1", "pin_description": "Switching Node 1."}, {"pin_number": "C5, D5", "pin_name": "IN", "pin_description": "Input. Bypass to PGND with a 10V 10μF ceramic capacitor."}]}, {"product_part_number": "MAX77801", "package_type": "20-<PERSON><PERSON> TQFN", "pins": [{"pin_number": "1, 20", "pin_name": "LX2", "pin_description": "Switching Node 2."}, {"pin_number": "2, 3", "pin_name": "OUT", "pin_description": "Output. Bypass to PGND with a 10V 47μF ceramic capacitor."}, {"pin_number": "4", "pin_name": "FB", "pin_description": "Output Voltage Sense."}, {"pin_number": "5", "pin_name": "VSYS", "pin_description": "System (Battery) Voltage Input. Bypass to AGND with a 10V, 1μF capacitor."}, {"pin_number": "6", "pin_name": "DVS", "pin_description": "Dynamic Voltage Scaling Logic Input. Connect to AGND if not used."}, {"pin_number": "7", "pin_name": "POK", "pin_description": "Open-Drain Power-OK Output. Asserts high (high-Z) when buck-boost output reaches 80% of target."}, {"pin_number": "8, 9", "pin_name": "AGND", "pin_description": "Analog Ground. Connect to PGND on the PCB. See the PCB Layout Guideliness."}, {"pin_number": "10", "pin_name": "SDA", "pin_description": "I2C Serial Interface Data (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor. Connect to AGND if not used."}, {"pin_number": "11", "pin_name": "SCL", "pin_description": "I2C Serial Interface Clock (High-Z in OFF State). Connect to VIO with a 1.5kΩ to 2.2kΩ pullup resistor. Connect to AGND if not used."}, {"pin_number": "12", "pin_name": "EN", "pin_description": "Active-High Enable Input. This pin has an 800kΩ internal pulldown to AGND."}, {"pin_number": "13", "pin_name": "VIO", "pin_description": "I2C Supply Voltage Input. Bypass to AGND with a 0.1μF capacitor. Connect to AGND if not used."}, {"pin_number": "14, 15", "pin_name": "IN", "pin_description": "Input. Bypass to PGND with a 10V 10µF ceramic capacitor."}, {"pin_number": "16, 17", "pin_name": "LX1", "pin_description": "Switching Node 1."}, {"pin_number": "18, 19", "pin_name": "PGND", "pin_description": "Power Ground. Connect to AGND on the PCB. See the PCB Layout Guideliness."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77801.pdf", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.3V", "max_output_voltage": "4.1875V", "min_output_voltage": "2.6V", "max_output_current": "3A", "max_switch_frequency": "2.75MHz", "quiescent_current": "55µA", "high_side_mosfet_resistance": "40mΩ", "low_side_mosfet_resistance": "55mΩ", "over_current_protection_threshold": "4.7A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "SKIP Mode", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "120% VOUT", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "Information", "pitch": "0.4", "length": "2.13", "width": "1.83", "pin_count": "20", "height": "0.4"}]}