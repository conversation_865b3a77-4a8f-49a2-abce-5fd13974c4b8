[{"part_number": "TPS63020", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "具有4A开关的TPS6302x 高效率单电感器降压/升压转换器", "features": ["输入电压范围: 1.8V 至 5.5V", "可调节输出电压: 1.2V 至 5.5V", "VIN > 2.5V、VOUT = 3.3V 时, 输出电流为 2A", "在整个负载范围内具有高效率", "静态工作电流: 25µA", "支持模式选择的省电模式", "平均电流模式降压/升压架构", "模式间自动转换", "2.4MHz 固定频率工作", "可同步", "电源正常状态输出", "安全、可靠运行特性", "过热和过压保护", "关断期间负载断开"], "description": "TPS6302x 器件可以为由两节或三节碱性电池、镍镉电池或镍氢电池或单节锂离子电池或锂聚合物电池、超级电容器或其他电源轨供电的产品提供电源解决方案。支持高达3A 的输出电流。使用电池时,可以放电到2V以下。该降压/升压转换器基于一个使用同步整流的固定频率、脉宽调制(PWM) 控制器来获得最高效率。在负载电流较低的情况下,该转换器会进入节能模式,以在宽负载电流范围内保持高效率。禁用省电模式则会强制转换器以固定开关频率运行。开关的最大平均电流为4A(典型值)。输出电压可通过外部电阻分频器进行编程,或者在内部芯片上固定。转换器可被禁用以最大限度地减少电池消耗。在关断期间,负载从电池上断开。TPS6302x 器件在自然通风环境下运行的温度范围为-40℃ 至 85℃。该器件采用 3mm × 4mm (DSJ) 14引脚 VSON 封装。", "applications": ["电池供电设备前置稳压器: EPOS(便携式数据终端、条形码扫描仪)、电子烟、单板计算机、IP 网络摄像头、可视门铃、陆地移动无线电", "稳压器: 有线通信、无线通信、PLC、光学模块", "超级电容器备用电源: 电表、企业级固态硬盘(SSD)"], "ordering_information": [{"part_number": "TPS63020", "order_device": "TPS63020DSJR", "package_type": "VSON", "package_drawing_code": "DSJ", "output_voltage": "可调节", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63020", "order_device": "TPS63020DSJT", "package_type": "VSON", "package_drawing_code": "DSJ", "output_voltage": "可调节", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63020", "package_type": "VSON", "pins": [{"pin_number": "1", "pin_name": "VINA", "pin_description": "Supply voltage for control stage"}, {"pin_number": "2", "pin_name": "GND", "pin_description": "Control / logic ground"}, {"pin_number": "3", "pin_name": "FB", "pin_description": "Voltage feedback of adjustable versions, must be connected to VOUT on fixed output voltage versions"}, {"pin_number": "4,5", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}, {"pin_number": "6,7", "pin_name": "L2", "pin_description": "Connection for inductor"}, {"pin_number": "8,9", "pin_name": "L1", "pin_description": "Connection for inductor"}, {"pin_number": "10,11", "pin_name": "VIN", "pin_description": "Supply voltage for power stage"}, {"pin_number": "12", "pin_name": "EN", "pin_description": "Enable input (1 enabled, 0 disabled), must not be left open"}, {"pin_number": "13", "pin_name": "PS/SYNC", "pin_description": "Enable / disable power save mode (1 disabled, 0 enabled, clock signal for synchronization), must not be left open"}, {"pin_number": "14", "pin_name": "PG", "pin_description": "Output power good (1 good, 0 failure; open-drain), can be left open"}, {"pin_number": "Exposed Thermal Pad", "pin_name": "PGND", "pin_description": "The exposed thermal pad is connected to PGND."}]}], "datasheet_cn": "ZHCSJG8I", "datasheet_en": "SLVS916", "family_comparison": "器件型号: TPS63020, 输出电压: 可调节, 封装: VSON (14)\n器件型号: TPS63021, 输出电压: 3.3V, 封装: VSON (14)", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.5V", "min_output_voltage": "1.2V", "max_output_current": "3A", "max_switch_frequency": "2.6MHz", "quiescent_current": "25µA", "high_side_mosfet_resistance": "50mΩ", "low_side_mosfet_resistance": "50mΩ", "over_current_protection_threshold": "4A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "无", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "自动重启", "output_under_voltage_protection": "无", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "电流限制", "over_temperature_protection": "自动重启", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.5V", "loop_control_mode": "平均电流模式"}, "package": [{"type": "Diagram", "pitch": "0.5", "height": "0.5", "length": "4.15", "width": "3.15", "pin_count": "14"}]}, {"part_number": "TPS63021", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "具有4A开关的TPS6302x 高效率单电感器降压/升压转换器", "features": ["输入电压范围: 1.8V 至 5.5V", "可调节输出电压: 1.2V 至 5.5V", "VIN > 2.5V、VOUT = 3.3V 时, 输出电流为 2A", "在整个负载范围内具有高效率", "静态工作电流: 25µA", "支持模式选择的省电模式", "平均电流模式降压/升压架构", "模式间自动转换", "2.4MHz 固定频率工作", "可同步", "电源正常状态输出", "安全、可靠运行特性", "过热和过压保护", "关断期间负载断开"], "description": "TPS6302x 器件可以为由两节或三节碱性电池、镍镉电池或镍氢电池或单节锂离子电池或锂聚合物电池、超级电容器或其他电源轨供电的产品提供电源解决方案。支持高达3A 的输出电流。使用电池时,可以放电到2V以下。该降压/升压转换器基于一个使用同步整流的固定频率、脉宽调制(PWM) 控制器来获得最高效率。在负载电流较低的情况下,该转换器会进入节能模式,以在宽负载电流范围内保持高效率。禁用省电模式则会强制转换器以固定开关频率运行。开关的最大平均电流为4A(典型值)。输出电压可通过外部电阻分频器进行编程,或者在内部芯片上固定。转换器可被禁用以最大限度地减少电池消耗。在关断期间,负载从电池上断开。TPS6302x 器件在自然通风环境下运行的温度范围为-40℃ 至 85℃。该器件采用 3mm × 4mm (DSJ) 14引脚 VSON 封装。", "applications": ["电池供电设备前置稳压器: EPOS(便携式数据终端、条形码扫描仪)、电子烟、单板计算机、IP 网络摄像头、可视门铃、陆地移动无线电", "稳压器: 有线通信、无线通信、PLC、光学模块", "超级电容器备用电源: 电表、企业级固态硬盘(SSD)"], "ordering_information": [{"part_number": "TPS63021", "order_device": "TPS63021DSJR", "package_type": "VSON", "package_drawing_code": "DSJ", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63021", "order_device": "TPS63021DSJT", "package_type": "VSON", "package_drawing_code": "DSJ", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63021", "package_type": "VSON", "pins": [{"pin_number": "1", "pin_name": "VINA", "pin_description": "Supply voltage for control stage"}, {"pin_number": "2", "pin_name": "GND", "pin_description": "Control / logic ground"}, {"pin_number": "3", "pin_name": "FB", "pin_description": "must be connected to VOUT on fixed output voltage versions"}, {"pin_number": "4,5", "pin_name": "VOUT", "pin_description": "Buck-boost converter output"}, {"pin_number": "6,7", "pin_name": "L2", "pin_description": "Connection for inductor"}, {"pin_number": "8,9", "pin_name": "L1", "pin_description": "Connection for inductor"}, {"pin_number": "10,11", "pin_name": "VIN", "pin_description": "Supply voltage for power stage"}, {"pin_number": "12", "pin_name": "EN", "pin_description": "Enable input (1 enabled, 0 disabled), must not be left open"}, {"pin_number": "13", "pin_name": "PS/SYNC", "pin_description": "Enable / disable power save mode (1 disabled, 0 enabled, clock signal for synchronization), must not be left open"}, {"pin_number": "14", "pin_name": "PG", "pin_description": "Output power good (1 good, 0 failure; open-drain), can be left open"}, {"pin_number": "Exposed Thermal Pad", "pin_name": "PGND", "pin_description": "The exposed thermal pad is connected to PGND."}]}], "datasheet_cn": "ZHCSJG8I", "datasheet_en": "SLVS916", "family_comparison": "器件型号: TPS63020, 输出电压: 可调节, 封装: VSON (14)\n器件型号: TPS63021, 输出电压: 3.3V, 封装: VSON (14)", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "3.3V", "min_output_voltage": "3.3V", "max_output_current": "3A", "max_switch_frequency": "2.6MHz", "quiescent_current": "25µA", "high_side_mosfet_resistance": "50mΩ", "low_side_mosfet_resistance": "50mΩ", "over_current_protection_threshold": "4A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "无", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "自动重启", "output_under_voltage_protection": "无", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "电流限制", "over_temperature_protection": "自动重启", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "不适用", "loop_control_mode": "平均电流模式"}, "package": [{"type": "Diagram", "pitch": "0.5", "height": "0.5", "length": "4.15", "width": "3.15", "pin_count": "14"}]}]