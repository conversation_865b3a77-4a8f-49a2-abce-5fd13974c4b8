{"part_number": "ADP5080", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PMIC (电源管理集成芯片)", "category_lv3": "多通道电源管理芯片", "part_number_title": "High Efficiency Integrated Power Solution for Multicell Lithium Ion Applications ADP5080", "features": ["Wide input voltage range: 4.0 V to 15 V", "High efficiency architecture", "Up to 2 MHz switching frequency", "6 synchronous rectification dc-to-dc converters", "Channel 1 buck regulator: 3 A maximum", "Channel 2 buck regulator: 1.15 A maximum", "Channel 3 buck regulator: 1.5 A maximum", "Channel 4 buck regulator: 0.8 A maximum", "Channel 5 buck regulator: 2 A maximum", "Channel 6 configurable buck or buck boost regulator", "2 A maximum for buck regulator configuration", "1.5 A maximum for buck boost regulator configuration", "Channel 7 high voltage, high performance LDO regulator: 30 mA maximum", "2 low quiescent current keep-alive LDO regulators", "LDO1 regulator: 400 mA maximum", "LDO2 regulator: 300 mA maximum", "Control circuit", "Charge pump for internal switching driver power supply", "I2C-programmable output levels and power sequencing", "Package: 72-ball, 4.5 mm x 4.0 mm x 0.6 mm WLCSP (0.5 mm pitch)"], "description": "The ADP5080 is a fully integrated, high efficiency power solution for multicell lithium ion battery applications. The device can connect directly to the battery, which eliminates the need for preregulators and, therefore, increases the battery life of the system. The ADP5080 integrates two keep-alive LDO regulators, five synchronous buck regulators, a configurable four-switch buck boost regulator, and a high voltage LDO regulator. The ADP5080 is a highly integrated power solution that incorporates all power MOSFETs, feedback loop compensation, voltage setting resistor dividers, and discharge switches, as well as a charge pump to generate a global bootstrap voltage. All these features help to minimize the number of external components and PCB space required, providing significant advantages for portable applications. The switching frequency is selectable on each channel from 750 kHz to 2 MHz. Key functions for power applications, such as soft start, selectable preset output voltage, and flexible power-up and power-down sequences, are provided on chip and are programmable via the I²C interface with fused factory defaults. The ADP5080 is available in a 72-ball WLCSP 0.5 mm pitch package.", "applications": ["DSLR cameras", "Non-reflex (mirrorless) cameras", "Portable instrumentation"], "ordering_information": [{"part_number": "ADP5080", "order_device": "ADP5080ACBZ-1-RL", "package_type": "WLCSP", "package_drawing_code": "CB-72-2", "output_voltage": "未找到", "min_operation_temp": "-25", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "ADP5080", "package_type": "WLCSP-72", "pins": [{"pin_number": "1A, 2A", "pin_name": "VOUT6", "pin_description": "Output Voltage for Channel 6."}, {"pin_number": "3A", "pin_name": "VISW1", "pin_description": "Input for an External Regulator Output. A 5.0 V to 5.5 V regulator connected to the VISW1 pin can take over from LDO1 to supply the internal circuit of the ADP5080 and the VREG1 load. If this pin is not used, connect it to GND."}, {"pin_number": "4A", "pin_name": "VISW2", "pin_description": "Input for an External Regulator Output. A 3.0 V to 3.3 V regulator connected to the VISW2 pin can take over from LDO2 to supply the internal circuit of the ADP5080 and the VREG2 load. If this pin is not used, connect it to GND."}, {"pin_number": "5A", "pin_name": "PVINCP", "pin_description": "Input Power Supply for the Charge Pump."}, {"pin_number": "6A", "pin_name": "C+", "pin_description": "Flying Capacitor Terminal for the Charge Pump."}, {"pin_number": "7A, 7B", "pin_name": "PGND5", "pin_description": "Power Ground for Channel 5."}, {"pin_number": "8A, 8B", "pin_name": "SW5", "pin_description": "Switching Node for Channel 5."}, {"pin_number": "9A, 9B", "pin_name": "PVIN5", "pin_description": "Input Power Supply for Channel 5."}, {"pin_number": "1B, 2B", "pin_name": "SW6B", "pin_description": "Secondary Side Boost Switching Node for Channel 6."}, {"pin_number": "3B", "pin_name": "VREG1", "pin_description": "Output Voltage for LDO1."}, {"pin_number": "4B", "pin_name": "VREG2", "pin_description": "Output Voltage for LDO2."}, {"pin_number": "5B", "pin_name": "VOLDO7", "pin_description": "Output Voltage for Channel 7. Leave this pin open if not used."}, {"pin_number": "6B", "pin_name": "C-", "pin_description": "Flying Capacitor Terminal for the Charge Pump."}, {"pin_number": "1C, 2C", "pin_name": "PGND6", "pin_description": "Power Ground for Channel 6."}, {"pin_number": "3C", "pin_name": "VBATT", "pin_description": "Power Supply Input for the Internal Circuits. Connect this pin to the battery."}, {"pin_number": "4C", "pin_name": "EN34", "pin_description": "Independent Enable Input for Channel 3 and Channel 4. If this pin is not used, connect it to GND."}, {"pin_number": "5C", "pin_name": "VILDO7", "pin_description": "Input Power Supply for Channel 7. If this pin is not used, connect it to VBATT."}, {"pin_number": "6C", "pin_name": "BSTCP", "pin_description": "Output Voltage for Charge Pump."}, {"pin_number": "7C", "pin_name": "VDR5", "pin_description": "Low-Side FET Driver Power Supply for Channel 5. Connect this pin to VREG1."}, {"pin_number": "8C", "pin_name": "BST45", "pin_description": "High-Side FET Driver Power Supply for Channel 4 and Channel 5."}, {"pin_number": "9C", "pin_name": "PVIN4", "pin_description": "Input Power Supply for Channel 4."}, {"pin_number": "1D, 2D", "pin_name": "SW6A", "pin_description": "Primary Side Switching Node for Channel 6."}, {"pin_number": "3D", "pin_name": "VDR6", "pin_description": "Low-Side FET Driver Power Supply for Channel 6. Connect this pin to VREG1."}, {"pin_number": "4D", "pin_name": "FB6", "pin_description": "<PERSON><PERSON><PERSON> for Channel 6."}, {"pin_number": "5D, 6E, 5G, 7G", "pin_name": "GND", "pin_description": "Ground. All GND pins must be connected."}, {"pin_number": "6D", "pin_name": "SYNC", "pin_description": "External Clock Input (CMOS Input Port). If this pin is not used, connect it to GND."}, {"pin_number": "7D", "pin_name": "FB5", "pin_description": "<PERSON><PERSON><PERSON> for Channel 5."}, {"pin_number": "8D", "pin_name": "FB4", "pin_description": "<PERSON><PERSON><PERSON> for Channel 4."}, {"pin_number": "9D", "pin_name": "SW4", "pin_description": "Switching Node for Channel 4."}, {"pin_number": "1E, 2E", "pin_name": "PVIN6", "pin_description": "Input Power Supply for Channel 6."}, {"pin_number": "3E", "pin_name": "BST16", "pin_description": "High-Side FET Driver Power Supply for Channel 1 and Channel 6."}, {"pin_number": "4E", "pin_name": "SDA", "pin_description": "Data Input/Output for I²C Interface. Open-drain I/O port."}, {"pin_number": "5E", "pin_name": "SCL", "pin_description": "Clock Input for I²C Interface. For start-up requirements, see the I²C Interface section."}, {"pin_number": "7E", "pin_name": "CLKO", "pin_description": "Clock Output (CMOS Output Port). CLKO replicates the Channel 1 switching clock. This output is not available when the SYNC pin is driven by an external clock. If this pin is not used, leave it open."}, {"pin_number": "8E", "pin_name": "VDR34", "pin_description": "Low-Side FET Driver Power Supply for Channel 3 and Channel 4. Connect this pin to VREG1."}, {"pin_number": "9E", "pin_name": "PGND4", "pin_description": "Power Ground for Channel 4."}, {"pin_number": "1F, 2F", "pin_name": "PVIN1", "pin_description": "Input Power Supply for Channel 1."}, {"pin_number": "3F", "pin_name": "FB1", "pin_description": "<PERSON><PERSON><PERSON> for Channel 1."}, {"pin_number": "4F", "pin_name": "EN", "pin_description": "Enable Control Input."}, {"pin_number": "5F", "pin_name": "VDDIO", "pin_description": "Supply Voltage for I²C Interface. Typically, this pin is connected externally to VREG2 or to the host I/O voltage."}, {"pin_number": "6F", "pin_name": "FREQ", "pin_description": "Frequency Pin for the Internal Oscillator. To select the internal clock source oscillator, connect an external 100 kΩ resistor from the FREQ pin to GND."}, {"pin_number": "7F", "pin_name": "FB3", "pin_description": "<PERSON><PERSON><PERSON> for Channel 3."}, {"pin_number": "8F, 9F", "pin_name": "PGND3", "pin_description": "Power Ground for Channel 3."}, {"pin_number": "1G", "pin_name": "SW1A", "pin_description": "Switching Node for Channel 1."}, {"pin_number": "2G", "pin_name": "SW1B", "pin_description": "Switching Node for Channel 1."}, {"pin_number": "3G", "pin_name": "VDR12", "pin_description": "Low-Side FET Driver Power Supply for Channel 1 and Channel 2. Connect this pin to VREG1."}, {"pin_number": "4G", "pin_name": "FB2", "pin_description": "<PERSON><PERSON><PERSON> for Channel 2."}, {"pin_number": "6G", "pin_name": "FAULT", "pin_description": "Fault Status Output Pin. This open-drain output port goes low when a fault occurs. Leave open if not used."}, {"pin_number": "8G, 9G", "pin_name": "SW3", "pin_description": "Switching Node for Channel 3."}, {"pin_number": "1H, 2H", "pin_name": "PGND1", "pin_description": "Power Ground for Channel 1."}, {"pin_number": "3H", "pin_name": "PGND2", "pin_description": "Power Ground for Channel 2."}, {"pin_number": "4H, 5H", "pin_name": "SW2", "pin_description": "Switching Node for Channel 2."}, {"pin_number": "6H", "pin_name": "PVIN2", "pin_description": "Input Power Supply for Channel 2."}, {"pin_number": "7H", "pin_name": "BST23", "pin_description": "High-Side FET Driver Power Supply for Channel 2 and Channel 3."}, {"pin_number": "8H, 9H", "pin_name": "PVIN3", "pin_description": "Input Power Supply for Channel 3."}]}], "datasheet_cn": "未找到", "datasheet_en": "ADP5080", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 9, "max_input_voltage": "15V", "min_input_voltage": "4V", "max_output_voltage": "12V", "min_output_voltage": "1V", "max_output_current": "3A", "max_switch_frequency": "2MHz", "quiescent_current": "8000µA", "high_side_mosfet_resistance": "55mΩ", "low_side_mosfet_resistance": "50mΩ", "over_current_protection_threshold": "4.4A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM/PSM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "True", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±0.8%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.5", "height": "0.66", "length": "4.5", "width": "4.0", "type": "72-ball", "pin_count": "1"}]}