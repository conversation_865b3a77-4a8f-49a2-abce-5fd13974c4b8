{"part_number": "TPS55288", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS55288 具有 I2C 接口的 36V、16A 降压/升压转换器", "features": ["可编程电源 (PPS) 支持 USB 供电 (USB PD)", "宽输入电压范围: 2.7V 至 36V", "可编程输出电压范围: 0.8V 至 22V, 步长为 20mV", "±1% 基准电压精度", "电缆上压降的可调输出电压补偿", "可编程输出电流限值高达 6.35A, 阶跃为 50mA", "±5% 精密输出电流监测", "I2C 接口", "在整个负载范围内具有高效率", "VIN = 12V、VOUT = 20V 且 IOUT = 3A 时效率为 97%", "轻负载状态下的可编程 PFM 和 FPWM 模式", "避免频率干扰和串扰", "可选的时钟同步", "可编程开关频率范围为 200 kHz 至 2.2 MHz", "降低 EMI", "可选可编程扩展频谱", "无引线封装", "丰富的保护特性", "输出过压保护", "利用断续模式实现输出短路保护", "热关断保护", "可编程平均电感器电流限制高达 16A", "小解决方案尺寸", "开关频率高达 2.2 MHz (最大值)", "4.0mm × 3.5mm HotRod™ QFN 封装", "固定 4ms 软启动时间", "使用 TPS55288 并借助 WEBENCH® Power Designer 创建定制设计"], "description": "TPS55288 是一款同步四开关降压/升压转换器，能够将输出电压稳定在等于、高于或低于输入电压的某一电压值上。TPS55288 在 2.7V 至 36V 的宽输入电压范围内工作，可输出 0.8V 至 22V 电压以支持各种不同的应用。TPS55288 集成了两个 16A MOSFET，其中的升压桥臂可实现解决方案尺寸和效率间的平衡。通过 I2C 接口对输出电压和输出电流限制进行编程，TPS55288 完全符合 USB PD 规范。TPS55288 能够通过 12V 输入电压提供 100W 的功率。TPS55288 采用平均电流模式控制方案。开关频率可通过外部电阻在 200 kHz 至 2.2 MHz 之间进行编程，并且可与外部时钟同步。TPS55288 还提供展频选项，从而更大限度地减少峰值 EMI。TPS55288 提供输出过压保护、平均电感器电流限制、逐周期峰值电流限制和输出短路保护。TPS55288 还通过可选输出电流限制和断续模式保护，在持续过载情况下确保安全运行。TPS55288 可在高开关频率下使用小型电感器和小型电容器。此器件采用 4.0mm × 3.5mm QFN 封装。", "applications": ["USB PD", "集线站", "工业 PC", "移动电源", "显示器", "无线充电器"], "ordering_information": [{"part_number": "TPS55288", "order_device": "TPS55288RPMR", "package_type": "VQFN-HR", "package_drawing_code": "RPM0026A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "TPS55288", "order_device": "TPS55288RPMR.A", "package_type": "VQFN-HR", "package_drawing_code": "RPM0026A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS55288", "order_device": "TPS55288RPMR.B", "package_type": "VQFN-HR", "package_drawing_code": "RPM0026A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS55288", "package_type": "VQFN-HR (RPM)", "pins": [{"pin_number": "1", "pin_name": "DR1L", "pin_description": "降压侧低侧 MOSFET 的栅极驱动器输出"}, {"pin_number": "2", "pin_name": "DR1H", "pin_description": "降压侧高侧 MOSFET 的栅极驱动器输出"}, {"pin_number": "3", "pin_name": "VIN", "pin_description": "IC 的输入电源"}, {"pin_number": "4", "pin_name": "EN/UVLO", "pin_description": "使能逻辑输入和可编程输入电压欠压锁定 (UVLO) 输入。逻辑高电平可使能器件。逻辑低电平可禁用该器件并将其置于关断模式。EN/UVLO 引脚上的电压高于 1.15V 的逻辑高电压后，此引脚将用作具有 1.23V 内部基准的可编程 UVLO 输入。"}, {"pin_number": "5", "pin_name": "SCL", "pin_description": "I2C 接口的时钟"}, {"pin_number": "6", "pin_name": "SDA", "pin_description": "I2C 接口的数据"}, {"pin_number": "7", "pin_name": "DITH/SYNC", "pin_description": "抖动频率设置和同步时钟输入。在此引脚和接地之间使用电容器来设置抖动频率。当此引脚短接至接地或上拉至高于 1.2V 时，无抖动功能。可在此引脚上施加外部时钟来同步开关频率。"}, {"pin_number": "8", "pin_name": "FSW", "pin_description": "开关频率通过此引脚和 AGND 引脚之间的电阻器进行编程。"}, {"pin_number": "9, 24", "pin_name": "PGND", "pin_description": "IC 的电源接地。它连接到低侧 MOSFET 的源极。"}, {"pin_number": "10", "pin_name": "AGND", "pin_description": "IC 的信号接地"}, {"pin_number": "11, 26", "pin_name": "VOUT", "pin_description": "降压/升压转换器的输出"}, {"pin_number": "12", "pin_name": "ISP", "pin_description": "电流检测放大器的正输入。ISP 引脚和 ISN 引脚之间连接的可选电流检测电阻器可限制输出电流。如果感测电压达到寄存器中的电流限制设置值，则慢速恒定电流控制环路将变为活动状态，并开始调节 ISP 引脚和 ISN 引脚之间的电压。将 ISP 引脚和 ISN 引脚与 VOUT 引脚连接在一起可以禁用输出电流限制功能。"}, {"pin_number": "13", "pin_name": "ISN", "pin_description": "电流检测放大器的负输入。ISP 引脚和 ISN 引脚之间连接的可选电流检测电阻器可限制输出电流。如果感测电压达到寄存器中的电流限制设置值，则慢速恒定电流控制环路将变为活动状态，并开始调节 ISP 引脚和 ISN 引脚之间的电压。将 ISP 引脚和 ISN 引脚与 VOUT 引脚连接在一起可以禁用输出电流限制功能。"}, {"pin_number": "14", "pin_name": "FB/INT", "pin_description": "当器件设置为使用外部输出电压反馈时，连接到电阻分压器的中心抽头以对输出电压进行编程。当器件设置为使用内部反馈时，此引脚是故障指示器输出。当发生内部故障时，此引脚输出逻辑低电平。"}, {"pin_number": "15", "pin_name": "MODE", "pin_description": "通过此引脚和 AGND 之间的电阻器设置 TPS55288 的运行模式，以选择 PFM 模式或强制 PWM 模式（在轻负载条件下），选择内部 LDO 或外部 5V 用于 VCC，并选择不同的 I2C 地址。"}, {"pin_number": "16", "pin_name": "CDC", "pin_description": "电压输出与 ISP 引脚和 ISN 引脚之间的感测电压成比例。使用此引脚和 AGND 之间的电阻器来增加输出电压，以补偿由电缆电阻引起的电缆压降。"}, {"pin_number": "17", "pin_name": "ILIM", "pin_description": "平均电感器电流限制设置引脚。在此引脚和 AGND 引脚之间连接一个外部电阻器。"}, {"pin_number": "18", "pin_name": "COMP", "pin_description": "内部误差放大器的输出。在此引脚和 AGND 引脚之间连接环路补偿网络。"}, {"pin_number": "19", "pin_name": "VCC", "pin_description": "内部稳压器的输出。此引脚和 AGND 引脚之间需要一个大于 4.7μF 的陶瓷电容器。"}, {"pin_number": "20", "pin_name": "BOOT2", "pin_description": "升压侧高侧 MOSFET 栅极驱动器的电源。此引脚和 SW2 引脚之间必须连接一个 0.1μF 的陶瓷电容器。"}, {"pin_number": "21, 25", "pin_name": "SW2", "pin_description": "升压侧的开关节点引脚。它连接到内部低侧功率 MOSFET 的漏极和内部高侧功率 MOSFET 的源极。"}, {"pin_number": "22", "pin_name": "BOOT1", "pin_description": "降压侧高侧 MOSFET 栅极驱动器的电源。此引脚和 SW1 引脚之间必须连接一个 0.1μF 的陶瓷电容器。"}, {"pin_number": "23", "pin_name": "SW1", "pin_description": "降压侧的开关节点引脚。它连接到外部低侧功率 MOSFET 的漏极和外部高侧功率 MOSFET 的源极。"}]}], "datasheet_cn": "ZHCSKY4B.pdf", "datasheet_en": "SLVSF01", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "2.7V", "max_output_voltage": "22V", "min_output_voltage": "1V", "max_output_current": "16A", "max_switch_frequency": "2.2MHz", "quiescent_current": "760µA", "high_side_mosfet_resistance": "7.6mΩ", "low_side_mosfet_resistance": "7.1mΩ", "over_current_protection_threshold": "16.5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM, FPWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "自动重启", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "1.2V", "loop_control_mode": "平均电流模式"}, "package": [{"type": "OPTION", "pitch": "0.5", "height": "1.0", "length": "4.0", "width": "3.5", "pin_count": "2"}]}