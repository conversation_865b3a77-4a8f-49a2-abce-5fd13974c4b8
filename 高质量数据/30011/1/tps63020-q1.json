{"part_number": "TPS63020-Q1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS63020-Q1 具有 4A 开关电流的高效单电感升压/降压转换器", "features": ["符合汽车应用要求", "具有符合AEC-Q100的下列结果: - 器件温度等级:运行结温范围为-40℃至125°C - 器件人体放电模型(HBM) 静电放电(ESD) 分类等级 H1B - 器件充电器件模型 (CDM) ESD 分类等级 C4B", "输入电压范围:1.8V 至 5.5V", "效率高达96%", "3.3V 降压模式下的输出电流为 3A (VIN > 3.6V)", "3.3V 升压模式下的输出电流高于 2A (VIN > 2.5V)", "在降压和升压模式之间实现自动转换", "动态输入电流限制", "器件的静态电流小于 50µA", "可调节输出电压范围:1.2V 至5.5V", "用于改进低输出功率效率的节能模式", "2.4MHz 强制固定运行频率并可实现同步", "智能电源正常状态输出", "关机期间负载断开", "过温保护", "过压保护", "采用 3mm × 4mm 超薄小外形尺寸无引线 (VSON)-14 封装"], "description": "TPS63020-Q1器件是一款电源解决方案,广泛应用于由2-3 节碱性电池、镍镉 (NiCd) 电池、镍氢 (NiMH)电池以及单节锂离子电池或锂聚合物电池供电的产品。当使用单节锂离子电池或锂聚合物电池供电时,该器件提供高达3A 的输出电流并可对电池进行放电,使其电压降至2.5V 或更低水平。此升压/降压转换器基于一个频率固定的脉宽调制 (PWM) 控制器。该控制器可通过同步整流实现效率最大化。在负载电流较低的情况下,该转换器会进入节能模式,以在宽负载电流范围内保持高效率。禁用省电模式则会强制转换器以固定开关频率运行。开关的最大平均电流为4A(典型值)。输出电压可通过外部电阻分频器进行编程。转换器可被禁用以最大限度地减少电池消耗。在关机期间,负载从电池上断开。该器件采用3mm×4mm 14 引脚VSON PowerPAD™ 封装 (DSJ)。", "applications": ["信息娱乐", "远程信息处理/紧急呼叫 (eCall)"], "ordering_information": [{"part_number": "TPS63020-Q1", "order_device": "TPS63020QDSJRQ1", "package_type": "VSON", "package_drawing_code": "DSJ (R-PVSON-N14)", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63020-Q1", "order_device": "TPS63020QDSJRQ1.A", "package_type": "VSON", "package_drawing_code": "DSJ (R-PVSON-N14)", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63020-Q1", "order_device": "TPS63020QDSJRQ1.B", "package_type": "VSON", "package_drawing_code": "DSJ (R-PVSON-N14)", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63020-Q1", "order_device": "TPS63020QDSJTQ1", "package_type": "VSON", "package_drawing_code": "DSJ (R-PVSON-N14)", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "TPS63020-Q1", "order_device": "TPS63020QDSJTQ1.B", "package_type": "VSON", "package_drawing_code": "DSJ (R-PVSON-N14)", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "TPS63020-Q1", "package_type": "VSON", "pins": [{"pin_number": "12", "pin_name": "EN", "pin_description": "使能输入（1 使能，0 禁用），不得悬空"}, {"pin_number": "3", "pin_name": "FB", "pin_description": "可调版本的电压反馈。"}, {"pin_number": "2", "pin_name": "GND", "pin_description": "控制/逻辑接地"}, {"pin_number": "8, 9", "pin_name": "L1", "pin_description": "电感器连接"}, {"pin_number": "6, 7", "pin_name": "L2", "pin_description": "电感器连接"}, {"pin_number": "14", "pin_name": "PG", "pin_description": "输出电源正常（1 正常，0 故障；开漏）"}, {"pin_number": "未找到", "pin_name": "PGND", "pin_description": "电源接地"}, {"pin_number": "13", "pin_name": "PS/SYNC", "pin_description": "启用/禁用节能模式（1 禁用，0 启用，用于同步的时钟信号），不得悬空"}, {"pin_number": "10, 11", "pin_name": "VIN", "pin_description": "功率级的电源电压"}, {"pin_number": "1", "pin_name": "VINA", "pin_description": "控制级的电源电压"}, {"pin_number": "4, 5", "pin_name": "VOUT", "pin_description": "降压-升压转换器输出"}, {"pin_number": "未找到", "pin_name": "Exposed Thermal Pad", "pin_description": "裸露散热焊盘连接至 PGND。"}]}], "datasheet_cn": "TPS63020-Q1.pdf", "datasheet_en": "SLVSD52", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.5V", "min_output_voltage": "1.2V", "max_output_current": "4A", "max_switch_frequency": "2.4MHz", "quiescent_current": "25µA", "high_side_mosfet_resistance": "50mΩ", "low_side_mosfet_resistance": "50mΩ", "over_current_protection_threshold": "4A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.5V", "loop_control_mode": "平均电流模式"}, "package": [{"type": "VSON", "length": "4.0", "width": "3.0", "pin_count": "14", "pitch": "0.5", "height": "0.5"}]}