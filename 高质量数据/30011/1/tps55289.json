{"part_number": "TPS55289", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "TPS55289 具有 I2C 接口的 30V、8A 降压/升压转换器", "features": ["可编程电源 (PPS) 支持 USB 供电 (USB PD)", "3.0V 至 30V 的宽输入电压范围", "可编程输出电压范围为 0.8V 至 22V，阶跃为 10mV", "±1% 基准电压精度", "电缆上压降的可调输出电压补偿", "可编程输出电流限值高达 6.35A，阶跃为 50mA", "±5% 精密输出电流监测", "I2C 接口", "在整个负载范围内具有高效率", "VIN = 12V、VOUT = 20V 且 IOUT = 3A 时效率为 96%", "轻负载状态下的可编程 PFM 和 FPWM 模式", "避免频率干扰和串扰", "可选的时钟同步", "可编程开关频率范围为 200 kHz 至 2.2 MHz", "降低 EMI", "可选可编程扩展频谱", "无引线封装", "丰富的保护特性", "输出过压保护", "利用断续模式实现输出短路保护", "热关断保护", "8A 平均电感器电流限值", "小解决方案尺寸", "开关频率高达 2.2 MHz (最大值)", "3.0mm × 5.0mm HotRod™ QFN 封装"], "description": "TPS55289 同步降压/升压转换器经优化，可将电池电压或适配器电压转换为电源轨。TPS55289 集成了四个MOSFET 开关，可为 USB 电力输送 (USB PD) 应用提供紧凑型解决方案。\n\nTPS55289 的输入电压高达 30V。通过 I2C 接口，TPS55289 的输出电压可以在 0.8V 至 22V 之间（阶跃为 10mV）进行编程。在升压模式下，输入电压为 12V时，该器件可提供 60W 的输出功率。它能够通过 9V输入电压提供 45W 的功率。\n\nTPS55289 采用平均电流模式控制方案。开关频率可通过外部电阻在 200 kHz 至 2.2 MHz 之间进行编程，并且可与外部时钟同步。TPS55289 还提供展频选项，从而更大限度地减少峰值 EMI。\n\nTPS55289 提供输出过压保护、平均电感器电流限制、逐周期峰值电流限制和输出短路保护功能。TPS55289还具有可选输出电流限制和断续模式保护功能，确保在持续过载情况下安全运行。\n\nTPS55289 可在高开关频率下使用小型电感器和小型电容器。此器件采用 3.0 mm × 5.0 mm QFN 封装。", "applications": ["无线充电器", "USB PD", "集线站", "工业 PC", "移动电源", "显示器"], "ordering_information": [{"part_number": "TPS55289", "order_device": "TPS55289RYQR", "status": "Active", "package_type": "VQFN-HR", "package_code": "RYQ", "carrier_description": "LARGE T&R", "carrier_quantity": "3000", "package_drawing_code": "RYQ0021A", "marking": "S55289", "pin_count": "21", "length": "5.1", "width": "3.1", "height": "1.0", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "Adjustable", "application_grade": "Auto"}, {"part_number": "TPS55289", "order_device": "TPS55289RYQR.A", "status": "Active", "package_type": "VQFN-HR", "package_code": "RYQ", "carrier_description": "LARGE T&R", "carrier_quantity": "3000", "package_drawing_code": "RYQ0021A", "marking": "S55289", "pin_count": "21", "length": "5.1", "width": "3.1", "height": "1.0", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "Adjustable", "application_grade": "Auto"}], "pin_function": [{"product_part_number": "TPS55289", "package_type": "VQFN-HR", "package_code": "RYQ", "pin_count": 21, "pins": [{"pin_number": "1", "pin_name": "EN/UVLO", "pin_description": "使能逻辑输入和可编程输入电压欠压锁定 (UVLO) 输入。逻辑高电平可使能该器件。逻辑低电平可禁用该器件并使其进入关断模式。EN/UVLO 引脚上的电压高于 1.15V 的逻辑高电压后，该引脚将用作具有 1.23V 内部基准的可编程 UVLO 输入。", "pin_type": "I"}, {"pin_number": "2", "pin_name": "MODE", "pin_description": "I2C 目标地址选择。当它连接到逻辑高电压时，I2C 目标地址为 74H。当它连接到逻辑低电压时，I2C 目标地址为 75H。", "pin_type": "I"}, {"pin_number": "3", "pin_name": "SCL", "pin_description": "I2C 接口的时钟", "pin_type": "I"}, {"pin_number": "4", "pin_name": "SDA", "pin_description": "I2C 接口的数据", "pin_type": "I/O"}, {"pin_number": "5", "pin_name": "DITH/SYNC", "pin_description": "抖动频率设置和同步时钟输入。在该引脚和接地之间使用一个电容器来设置抖动频率。当该引脚短接到地或上拉至高于 1.2V 的电压时，没有抖动功能。可在此引脚上施加一个外部时钟来同步开关频率。", "pin_type": "I"}, {"pin_number": "6", "pin_name": "FSW", "pin_description": "开关频率通过该引脚和 AGND 引脚之间的电阻器进行编程。", "pin_type": "I"}, {"pin_number": "7", "pin_name": "VIN", "pin_description": "降压/升压转换器的输入", "pin_type": "PWR"}, {"pin_number": "8", "pin_name": "SW1", "pin_description": "降压侧的开关节点引脚。它连接到内部降压低侧功率 MOSFET 的漏极和内部降压高侧功率 MOSFET 的源极。", "pin_type": "PWR"}, {"pin_number": "9", "pin_name": "PGND", "pin_description": "IC 的功率地", "pin_type": "PWR"}, {"pin_number": "10", "pin_name": "SW2", "pin_description": "升压侧的开关节点引脚。它连接到内部升压低侧功率 MOSFET 的漏极和内部升压高侧功率 MOSFET 的源极。", "pin_type": "PWR"}, {"pin_number": "11", "pin_name": "VOUT", "pin_description": "降压/升压转换器的输出", "pin_type": "PWR"}, {"pin_number": "12", "pin_name": "ISP", "pin_description": "电流检测放大器的正输入。在 ISP 引脚和 ISN 引脚之间连接一个可选的电流检测电阻器可以限制输出电流。如果检测到的电压达到寄存器中的电流限制设置值，则一个慢速恒定电流控制环路将变为活动状态，并开始调节 ISP 引脚和 ISN 引脚之间的电压。将 ISP 引脚和 ISN 引脚与 VOUT 引脚连接在一起可以禁用输出电流限制功能。", "pin_type": "I"}, {"pin_number": "13", "pin_name": "ISN", "pin_description": "电流检测放大器的负输入。在 ISP 引脚和 ISN 引脚之间连接一个可选的电流检测电阻器可以限制输出电流。如果检测到的电压达到寄存器中的电流限制设置值，则一个慢速恒定电流控制环路将变为活动状态，并开始调节 ISP 引脚和 ISN 引脚之间的电压。将 ISP 引脚和 ISN 引脚与 VOUT 引脚连接在一起可以禁用输出电流限制功能。", "pin_type": "I"}, {"pin_number": "14", "pin_name": "FB/INT", "pin_description": "当器件设置为使用外部输出电压反馈时，连接到电阻分压器的中心抽头以对输出电压进行编程。当器件设置为使用内部反馈时，该引脚为故障指示器输出。当发生内部故障时，该引脚输出逻辑低电平。", "pin_type": "I/O"}, {"pin_number": "15", "pin_name": "COMP", "pin_description": "内部误差放大器的输出。在该引脚和 AGND 引脚之间连接环路补偿网络。", "pin_type": "O"}, {"pin_number": "16", "pin_name": "CDC", "pin_description": "电压输出与 ISP 引脚和 ISN 引脚之间检测到的电压成比例。在该引脚和 AGND 之间使用一个电阻器来增加输出电压，以补偿由电缆电阻引起的电缆压降。", "pin_type": "O"}, {"pin_number": "17", "pin_name": "AGND", "pin_description": "IC 的信号地", "pin_type": "GND"}, {"pin_number": "18", "pin_name": "VCC", "pin_description": "内部稳压器的输出。在该引脚和 AGND 引脚之间需要一个大于 4.7 µF 的陶瓷电容器。", "pin_type": "O"}, {"pin_number": "19", "pin_name": "BOOT2", "pin_description": "升压侧高侧 MOSFET 栅极驱动器的电源。必须在该引脚和 SW2 引脚之间连接一个 0.1µF 陶瓷电容器。", "pin_type": "O"}, {"pin_number": "20", "pin_name": "BOOT1", "pin_description": "降压侧高侧 MOSFET 栅极驱动器的电源。必须在该引脚和 SW1 引脚之间连接一个 0.1µF 陶瓷电容器。", "pin_type": "O"}, {"pin_number": "21", "pin_name": "EXTVCC", "pin_description": "为 VCC 选择内部 LDO 或外部 5V。当连接到逻辑高电压时，选择内部 LDO。当连接到逻辑低电压时，选择外部 5V 用于 VCC。", "pin_type": "I"}]}], "datasheet_cn": {"datasheet_name": "TPS55289 具有I2C接口的30V、8A降压/升压转换器", "datasheet_path": "TPS55289.pdf", "release_date": "2022-08", "version": "ZHCSNR7A"}, "datasheet_en": {"datasheet_name": "TPS55289 30-V, 8-A <PERSON><PERSON><PERSON>ost Converter With I2C Interface", "datasheet_path": "未找到", "release_date": "未找到", "version": "SLVSGA9"}, "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "30V", "min_input_voltage": "3.0V", "max_output_voltage": "22V", "min_output_voltage": "1V", "max_output_current": "8A", "max_switch_frequency": "2.2MHz", "quiescent_current": "760µA", "high_side_mosfet_resistance": "14mΩ", "low_side_mosfet_resistance": "22mΩ", "over_current_protection_threshold": "8A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM, FPWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "1.129V", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.5", "height": "1.0", "length": "5.0", "width": "3.0", "type": "VQFN-HR", "pin_count": "2"}]}