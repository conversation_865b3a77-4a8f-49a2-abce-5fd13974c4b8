{"part_number": "MP4248", "manufacturer": "MPS (Monolithic Power Systems)", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "36V, 140W, <PERSON><PERSON><PERSON>ost with Integrated Low-Side MOSFETs, Supports High-Side Current Sense and I²C Interface", "features": ["IEC62368-1 Certified", "140W Buck-Boost Converter with Integrated Low-Side MOSFETs (LS-FETs)", "Integrated Gate Driver for High-Side MOSFETs (HS-FETs)", "Configurable Input UVLO", "3.6V to 36V Start-Up Input Voltage Range", "1V to 36V Output Voltage (VOUT) Range with 1% Accuracy", "Supports 2.8V Falling VIN When VOUT > 3.5V", "Up to 98% Peak Efficiency", "I2C-Configurable VREF Range: 0.1V to 2.147V with 1mV Resolution", "Accurate Output CC Current Limit: ±5%, Supports High-Side (HS) Current Sense", "Accurate Output Current Monitor (IMON)", "Meets USB PD 3.1 EPR AVS and PPS Specification", "Selectable 280kHz, 420kHz, and 580kHz Switching Frequency (fsw)", "Selectable Forced PWM Mode or Automatic PFM/PWM Mode", "Output Bias VCC LDO for Higher Efficiency", "Line Drop Compensation via RSENS", "I2C, Alert, and OTP Memory", "EN Shutdown Passive Discharge", "Output OCP, OVP, and Thermal Shutdown Protection", "Available in a QFN-20 (3mmx5mm) Package"], "description": "The MP4248 is a buck-boost converter with two integrated low-side MOSFETs (LS-FETs). The device can deliver up to 140W of peak output power at a certain input voltage (VIN) range with excellent efficiency. The MP4248 is suitable for USB power delivery (PD) applications. It can work with external USB PD controllers via the I²C interface. The I2C interface and one-time programmable (OTP) memory provide flexibility for configurable features. Fault condition protections include constant current (CC) current limiting with a high-side (HS) current sense, output over-voltage protection (OVP), and thermal shutdown (TSD). The MP4248 requires a minimal number of readily available, standard external components, and it is available in a QFN-20 (3mmx5mm) package.", "applications": ["USB Type-C and USB Power Delivery (PD)", "USB Type-C PD Monitors and Docking Stations", "USB Type-C, USB Type-A Communication Interface, Car Charger", "Wireless Charging"], "ordering_information": [{"part_number": "MP4248", "order_device": "MP4248GQV-0000-Z", "package_type": "QFN-20", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MP4248", "order_device": "MP4248GQV-0011-Z", "package_type": "QFN-20", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MP4248", "order_device": "MP4248GQV-0012-Z", "package_type": "QFN-20", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MP4248", "order_device": "MP4248GQV-xxxx-Z", "package_type": "QFN-20", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MP4248", "package_type": "QFN-20 (3mmx5mm)", "pins": [{"pin_number": "1", "pin_name": "SCL", "pin_description": "I2C clock signal input."}, {"pin_number": "2", "pin_name": "SDA", "pin_description": "I2C data line."}, {"pin_number": "3", "pin_name": "ALT", "pin_description": "I2C alert pin. Open-drain output, active low."}, {"pin_number": "4", "pin_name": "FB", "pin_description": "Feedback pin. Connect the FB pin to the tap of an external resistor divider, connected from the output to AGND, to set the output voltage."}, {"pin_number": "5", "pin_name": "ISEN-", "pin_description": "Negative node of the current-sense signal input. Place a current-sense resistor between ISEN+ and ISEN-."}, {"pin_number": "6", "pin_name": "ISEN+", "pin_description": "Positive node of the current sense-signal input. Place a current-sense resistor between ISEN+ and ISEN-."}, {"pin_number": "7", "pin_name": "VOUT", "pin_description": "Output voltage sense input. The VOUT pin can also be used to provide the VCC supply under certain output voltage (VOUT) conditions."}, {"pin_number": "8", "pin_name": "BST2", "pin_description": "Bootstrap. A 0.22µF capacitor is connected between SW2 and BST2 to form a floating supply across the high-side switch driver."}, {"pin_number": "9", "pin_name": "HG2", "pin_description": "High-side 2 gate drive output for the boost high-side switch (SWD)."}, {"pin_number": "10", "pin_name": "VCC", "pin_description": "Internal 5V LDO regulator output. Decouple the VCC pin with a 0.47µF to 1µF capacitor."}, {"pin_number": "11", "pin_name": "AGND", "pin_description": "Analog ground. Connect AGND to PGND, and connect AGND to the VCC capacitor's ground node."}, {"pin_number": "12, 15", "pin_name": "PGND", "pin_description": "Power ground. PGND requires extra care during PCB layout. Connect PGND to GND with copper traces and vias."}, {"pin_number": "13", "pin_name": "SW2", "pin_description": "Switch 2 node of the buck-boost. Connect SW1 to SW2 with a power inductor. Use a wide PCB trace to make the connection."}, {"pin_number": "14", "pin_name": "SW1", "pin_description": "Switch1 node of the buck-boost. Connect SW1 to SW2 with a power inductor. Use a wide PCB trace to make the connection."}, {"pin_number": "16", "pin_name": "IMON", "pin_description": "Current monitor output. Represent the signal between the ISEN+ and ISEN- pins."}, {"pin_number": "17", "pin_name": "EN", "pin_description": "EN input. Apply a high logic to EN to enable the chip."}, {"pin_number": "18", "pin_name": "HG1", "pin_description": "High-side 1 gate drive output for the buck high-side switch (SWA)."}, {"pin_number": "19", "pin_name": "BST1", "pin_description": "Bootstrap. A 0.22µF capacitor is connected between SW1 and BST1 to form a floating supply across the high-side switch driver."}, {"pin_number": "20", "pin_name": "VIN", "pin_description": "Supply Voltage for internal logic circuitry but not for power MOSFETs. <PERSON><PERSON> connect the VIN pin to the SWA MOSFET's drain with a wide PCB trace, and it cannot be affected by another DC/DC converter."}]}], "datasheet_cn": "未找到", "datasheet_en": "MP4248 Datasheet Rev. 1.0 (2024-05-23)", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "3.6V", "max_output_voltage": "36V", "min_output_voltage": "1V", "max_output_current": "7A", "max_switch_frequency": "0.58MHz", "quiescent_current": "775µA", "high_side_mosfet_resistance": "External", "low_side_mosfet_resistance": "20mΩ", "over_current_protection_threshold": "0.5A-5.4A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM/PWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.5V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "REFERENCE", "pitch": "0.5", "height": "1.0", "width": "3.0", "length": "5.0", "pin_count": "4"}]}