{"part_number": "MP2980", "manufacturer": "Monolithic Power Systems", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "36V, Synchronous Buck-Boost Controller with Digital Interface and Current Monitor", "features": ["6V to 36V Start-Up Input Voltage (VIN) Range", "5V to 36V Operating VIN Range", "Flexible Digital Interface Control: 0.5V to 28V Output Voltage (VOUT) Range, 0.3V to 2.047V Reference Voltage (VREF) Range with 1mV Step", "Selectable VOUT Slew Rate", "Configurable Constant Current Limit", "Output Current (IOUT) Monitor Function (IMON)", "Configurable Soft-Start Time (tss)", "Switching Frequency Spread Spectrum (FSS) for EMI Optimization", "Integrated VOUT Discharge Function", "Selectable 200kHz, 300kHz, 400kHz, and 600kHz Switching Frequency (fsw)", "Forced Continuous Conduction Mode (FCCM)", "Configurable VIN Under-Voltage Lockout (UVLO) Hysteresis", "Minimum VIN Regulation", "Over-Current Protection (OCP), Short-Circuit Protection (SCP), and Over-Voltage Protection (OVP)", "Interrupt Indicator for OCP, OVP, and VOUT Power Not Good (PNG)", "Available in a QFN-32 (4mmx4mm) Package"], "description": "The MP2980 is a synchronous, four-switch, buck-boost controller capable of regulating different output voltages with a wide input voltage (VIN) range and high efficiency. The MP2980 provides a digital interface that supports output voltage (VOUT) configuration, VOUT slew-rate control, and constant output current (IOUT) limit configuration. This makes the MP2980 well-suited for USB power delivery (PD) designs in USB Type-C power supplies. The MP2980 uses valley current control in buck mode and peak current control in boost mode to provide fast load transient response and smooth buck-boost mode transients. The MP2980 provides forced continuous conduction mode (FCCM) and a configurable average current limit, which support flexible designs for different applications. The MP2980 also provides configurable over-current protection (OCP), over-voltage protection (OVP), and VIN under-voltage lockout (UVLO) hysteresis. The MP2980 is available in a QFN-32 (4mmx4mm) package.", "applications": ["USB Power Delivery (PD)", "Industrial PC Power Supplies", "Supercapacitor Charging"], "ordering_information": [{"part_number": "MP2980", "order_device": "MP2980GR", "package_type": "QFN-32", "package_drawing_code": "MO-220", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MP2980", "order_device": "MP2980GR-Z", "package_type": "QFN-32", "package_drawing_code": "MO-220", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MP2980", "package_type": "QFN-32", "pins": [{"pin_number": "1", "pin_name": "SDA", "pin_description": "Digital interface data signal."}, {"pin_number": "2", "pin_name": "SCL", "pin_description": "Digital interface clock signal."}, {"pin_number": "3", "pin_name": "INT", "pin_description": "Interrupt for power not good (PNG), over-current protection (OCP), over-temperature protection (OTP), and over-voltage protection (OVP) events. In the default set-up, the INT pin is masked off in response to a PNG event. INT is an open-drain output and is pulled low if an interrupt event occurs. Once the fault is removed, INT recovers to open drain. INT is also an open drain when the IC is not enabled."}, {"pin_number": "4", "pin_name": "IMON", "pin_description": "Current monitor output. The IMON pin indicates the signal between the IAVGP and IAVGN pins."}, {"pin_number": "5", "pin_name": "CSP", "pin_description": "Positive input of the switching current-sense signal. Connect the CSP pin to the high side of the current-sense resistor."}, {"pin_number": "6", "pin_name": "CSN", "pin_description": "Negative input of the switching current-sense signal. Connect the CSN pin to the low side of the current-sense resistor."}, {"pin_number": "7", "pin_name": "FS", "pin_description": "Register bits FSW[7:6] default value configuration. The FS pin provides two different voltage levels during start-up to set the default FS voltage (VFS): VFS1: 0.51 x AVDD to 0.68 x AVDD, FSW = 00 (200kHz); VFS2: 0.74 x AVDD or above, FSW = 10 (400kHz). FS cannot be floated or connected to ground. Changing the voltage level of FS after start-up does not affect register bits FSW[7:6]. These bits can be changed via the digital interface after start-up."}, {"pin_number": "8", "pin_name": "AVDD", "pin_description": "Internal control circuit bias supply. Decouple the AVDD pin with a ≥2.2µF capacitor."}, {"pin_number": "9", "pin_name": "AGND", "pin_description": "Analog ground."}, {"pin_number": "10, 14", "pin_name": "N/C", "pin_description": "No internal connection."}, {"pin_number": "11", "pin_name": "COMP", "pin_description": "Internal error amplifier output. Connect the COMP pin to a capacitor and a resistor in series with AGND for loop compensation."}, {"pin_number": "12", "pin_name": "FB", "pin_description": "Output voltage (VOUT) feedback. Connect a resistor divider between the FB and VOUT pins."}, {"pin_number": "13", "pin_name": "SS", "pin_description": "Soft-start configuration. Connect the SS pin to an external capacitor. SS also sets the off time in hiccup."}, {"pin_number": "15", "pin_name": "ILIM", "pin_description": "Register bits ILIM[2:0] default value configuration. The ILIM pin provides four different voltage levels during start-up to set the default ILIM voltage (VILIM). Float ILIM to set ILIM = 001 by default. These bits can be changed via the digital interface after start-up."}, {"pin_number": "16", "pin_name": "IAVGN", "pin_description": "Negative terminal of average current limit sense input. The IAVGN pin can only be used to set the output current (IOUT) limit by connecting the pin to the positive terminal of the output rail."}, {"pin_number": "17", "pin_name": "IAVGP", "pin_description": "Positive terminal of average current limit sense input. The IAVGP pin can only be used to set the IOUT limit by connecting the pin to the positive terminal of the output rail."}, {"pin_number": "18", "pin_name": "VOUT", "pin_description": "VOUT sense input. The VOUT pin supplies power to VCC based on the VCC power logic. Connect VOUT to the output capacitor (COUT)."}, {"pin_number": "19", "pin_name": "SW2", "pin_description": "Boost switch node of the controller. Connect the SW2 pin to the SWD source and the SWC drain."}, {"pin_number": "20", "pin_name": "HG2", "pin_description": "Boost high-side MOSFET (HS-FET) gate driver. Connect the HG2 pin directly to the SWD gate."}, {"pin_number": "21", "pin_name": "BST2", "pin_description": "Bootstrap power for boost HS-FET gate driver. Connect a capacitor between the BST2 and SW2 pins. BST2 is supplied by either VCC or BST1."}, {"pin_number": "22", "pin_name": "LG2", "pin_description": "Boost low-side MOSFET (LS-FET) gate driver. Connect the LG2 pin directly to the SWC gate."}, {"pin_number": "23", "pin_name": "PGND", "pin_description": "Power ground. The PGND pin provides the gate-driving current return."}, {"pin_number": "24", "pin_name": "VCC", "pin_description": "Driver circuit and internal bias supply. Place a ≥2.2µF decoupling ceramic capacitor as close to the VCC pin as possible. VCC is powered by the input voltage (VIN) or output voltage (VOUT)."}, {"pin_number": "25", "pin_name": "LG1", "pin_description": "Buck LS-FET gate driver. Connect the LG1 pin directly to the SWB gate."}, {"pin_number": "26", "pin_name": "BST1", "pin_description": "Bootstrap power for buck HS-FET gate driver. Connect a capacitor between the BST1 and SW1 pins. BST1 is supplied by VCC or BST2."}, {"pin_number": "27", "pin_name": "HG1", "pin_description": "<PERSON> HS-FET gate driver. Connect the HG1 pin directly to the SWA gate."}, {"pin_number": "28", "pin_name": "SW1", "pin_description": "<PERSON> switch node of the controller. Connect the SW pin to the SWA source and the SWB drain."}, {"pin_number": "29", "pin_name": "VIN", "pin_description": "Input power supply and voltage sense input."}, {"pin_number": "30", "pin_name": "EN", "pin_description": "Chip enable control. If not used, connect the EN pin to the input source for automatic start-up. EN can also configure VIN under-voltage lockout (UVLO). Do not float this pin."}, {"pin_number": "31", "pin_name": "VINREG", "pin_description": "VIN regulation. The VINREG pin sets the minimum operating VIN during switching. If not used, connect VINREG to AVDD."}, {"pin_number": "32", "pin_name": "ADDR", "pin_description": "Digital interface slave address setting. The ADDR pin sets the default value of the ENPWR bit."}]}], "datasheet_cn": "未找到", "datasheet_en": "MP2980 Datasheet, Rev 1.0, 2023-11-09", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "5V", "max_output_voltage": "28V", "min_output_voltage": "1V", "max_output_current": "5A", "max_switch_frequency": "600kHz", "quiescent_current": "5µA", "high_side_mosfet_resistance": "不适用(外部控制器)", "low_side_mosfet_resistance": "不适用(外部控制器)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "FCCM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.5V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "1.0", "length": "4.0", "width": "4.0", "pin_count": "2", "type": "REFERENCE"}]}