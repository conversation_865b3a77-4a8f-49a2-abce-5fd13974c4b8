{"part_number": "LT8705", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "DC-DC控制器", "part_number_title": "80V VIN 和 VOUT 同步、四开关、降压-升压型 DC/DC 控制器", "features": ["单电感可实现 VIN 高于、低于或等于 VOUT", "VIN 范围: 2.8V (需要 EXTVCC > 6.4V) 至 80V", "VOUT 范围: 1.3V 至 80V", "四路 N 沟道 MOSFET 栅极驱动器", "同步整流: 效率高达 98%", "输入和输出电流监视引脚", "可同步的固定频率: 100kHz 至 400kHz", "集成型输入电流、输入电压、输出电流和输出电压反馈环路", "时钟输出可用于监视芯片温度", "采用 38 引脚 (5mm x 7mm) QFN 封装和 TSSOP 封装 (其中 TSSOP 封装进行了修改以改善高电压操作)"], "description": "LT®8705 是一款高性能降压 - 升压型开关稳压控制器, 其可在输入电压高于、低于或等于输出电压的情况下工作。该器件具有集成型输入电流、输入电压、输出电流和输出电压反馈环路。凭藉很宽的 2.8V 至 80V 输入和 1.3V 至 80V 输出范围, LT8705 可兼容大多数的太阳能、汽车、电信和电池供电式系统。LT8705 的 SRVO 引脚来指明当前哪个反馈环路在工作。在轻负载条件下, MODE 引脚能够在突发模式 (Burst Mode®)、不连续或连续导通模式之间进行选择。其他特点包括一个 3.3V/12mA LDO、一个可同步的固定工作频率、内置栅极驱动器、可调 UVLO 以及具可编程最大值的输入和输出电流监视功能。", "applications": ["高电压降压-升压型转换器", "输入或输出电流限流型转换器", "电信电压稳定器"], "ordering_information": [{"part_number": "LT8705", "order_device": "LT8705EUHF#PBF", "package_type": "QFN", "package_drawing_code": "LTC DWG # 05-08-1701 Rev C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8705", "order_device": "LT8705EUHF#TRPBF", "package_type": "QFN", "package_drawing_code": "LTC DWG # 05-08-1701 Rev C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8705", "order_device": "LT8705IUHF#PBF", "package_type": "QFN", "package_drawing_code": "LTC DWG # 05-08-1701 Rev C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8705", "order_device": "LT8705IUHF#TRPBF", "package_type": "QFN", "package_drawing_code": "LTC DWG # 05-08-1701 Rev C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8705", "order_device": "LT8705EFE#PBF", "package_type": "TSSOP", "package_drawing_code": "LTC DWG # 05-08-1665 Rev B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8705", "order_device": "LT8705EFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "LTC DWG # 05-08-1665 Rev B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8705", "order_device": "LT8705IFE#PBF", "package_type": "TSSOP", "package_drawing_code": "LTC DWG # 05-08-1665 Rev B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8705", "order_device": "LT8705IFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "LTC DWG # 05-08-1665 Rev B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LT8705", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "SHDN", "pin_description": "停机引脚。将该引脚连接至高电平可启用器件。把该引脚接地则使器件停机并将静态电流减至最小。不要将该引脚浮置。"}, {"pin_number": "2", "pin_name": "CSN", "pin_description": "至电感电流检测和反向电流检测放大器的负(−) 输入。"}, {"pin_number": "3", "pin_name": "CSP", "pin_description": "至电感电流检测和反向电流检测放大器的正(+)输入。Vc 引脚电压及 CSP 和 CSN 引脚之间的内置偏移与 RSENSE 电阻阻值一起设定电流跳变门限。"}, {"pin_number": "4", "pin_name": "LDO33", "pin_description": "3.3V 稳压器输出。通过一个最小 0.1μF 的陶瓷电容将该引脚旁路至地。"}, {"pin_number": "5", "pin_name": "FBIN", "pin_description": "输入反馈引脚。该引脚连接至输入误差放大器的输入端。"}, {"pin_number": "6", "pin_name": "FBOUT", "pin_description": "输出反馈引脚。该引脚把误差放大器输入连接至一个从输出端引出的外部分压电阻。"}, {"pin_number": "7", "pin_name": "IMON_OUT", "pin_description": "输出电流监视引脚。该引脚流出的电流与输出电流成比例。"}, {"pin_number": "8", "pin_name": "Vc", "pin_description": "误差放大器输出引脚。将外部补偿网络连接至该引脚。"}, {"pin_number": "9", "pin_name": "SS", "pin_description": "软起动引脚。把至少 100nF 的电容布设于此引脚。在启动时,该引脚将由一个内部电阻充电至 2.5V。"}, {"pin_number": "10", "pin_name": "CLKOUT", "pin_description": "时钟输出引脚。使用该引脚可使一个或多个兼容的开关稳压器 IC 同步至 LT8705。"}, {"pin_number": "11", "pin_name": "SYNC", "pin_description": "如欲使开关频率同步至一个外部时钟,只需采用一个时钟来驱动该引脚。"}, {"pin_number": "12", "pin_name": "RT", "pin_description": "定时电阻引脚。该引脚用于调节开关频率。在该引脚和地之间布设一个电阻可设定自由运行频率。"}, {"pin_number": "13", "pin_name": "GND", "pin_description": "地。直接连接至局部接地平面。"}, {"pin_number": "14", "pin_name": "BG1", "pin_description": "底端栅极驱动。该引脚用于驱动位于地和 GATEVcc 之间的底端 N 沟道 MOSFET 的栅极。"}, {"pin_number": "15", "pin_name": "GATEVCC", "pin_description": "用于栅极驱动器的电源。该引脚必须连接至 INTVcc 引脚。不要采用任何其他电源来给该引脚供电。将该引脚就近旁路至 GND。"}, {"pin_number": "16", "pin_name": "BG2", "pin_description": "底端栅极驱动。该引脚用于驱动位于地和 GATEVcc 之间的底端 N 沟道 MOSFET 的栅极。"}, {"pin_number": "18", "pin_name": "TG2", "pin_description": "顶端栅极驱动。用于驱动顶端 N 沟道 MOSFET,其电压摆幅等于 GATEVCC 与开关节点电压的叠加。"}, {"pin_number": "19", "pin_name": "SW2", "pin_description": "开关节点。自举电容的负(−) 端连接于此。"}, {"pin_number": "21", "pin_name": "SW1", "pin_description": "开关节点。自举电容的负(−) 端连接于此。"}, {"pin_number": "22", "pin_name": "TG1", "pin_description": "顶端栅极驱动。用于驱动顶端 N 沟道 MOSFET,其电压摆幅等于 GATEVCC 与开关节点电压的叠加。"}, {"pin_number": "23", "pin_name": "BOOST1", "pin_description": "升压的浮动驱动器电源。自举电容的正(+)端连接于此。"}, {"pin_number": "25", "pin_name": "SRVO_FBIN", "pin_description": "漏极开路逻辑输出。该引脚在输入电压反馈环路运行时被拉至地。"}, {"pin_number": "26", "pin_name": "SRVO_IIN", "pin_description": "漏极开路逻辑输出。该引脚在输入电流环路运行时被拉至地。"}, {"pin_number": "27", "pin_name": "SRVO_IOUT", "pin_description": "漏极开路逻辑输出。该引脚在输出电流反馈环路运行时被拉至地。"}, {"pin_number": "28", "pin_name": "SRVO_FBOUT", "pin_description": "漏极开路逻辑输出。该引脚在输出电压反馈环路运行时被拉至地。"}, {"pin_number": "29", "pin_name": "EXTVCC", "pin_description": "外部 VCC 输入。当 EXTVCC 超过 6.4V (典型值)时,INTVCC 将从该引脚供电。当 EXTVCC 低于 6.22V (典型值) 时,INTVCC 将从 VIN 供电。"}, {"pin_number": "30", "pin_name": "CSNOUT", "pin_description": "至输出电流监视放大器的负(−)输入。不用时将该引脚连接至 VOUT。"}, {"pin_number": "31", "pin_name": "CSPOUT", "pin_description": "至输出电流监视放大器的正(+)输入。该引脚和 CSNOUT 引脚负责测量检测电阻 RSENSE2 两端的电压以提供输出电流信号。不用时将该引脚连接至 VOUT。"}, {"pin_number": "32", "pin_name": "CSNIN", "pin_description": "至输入电流监视放大器的负(-)输入。该引脚和 CSPIN 引脚负责测量检测电阻 RSENSE1 两端的电压以提供输入电流信号。不用时将该引脚连接至 VIN。"}, {"pin_number": "33", "pin_name": "CSPIN", "pin_description": "至输入电流监视放大器的正(+)输入。不用时将该引脚连接至 VIN。"}, {"pin_number": "34", "pin_name": "VIN", "pin_description": "主输入电源引脚。该引脚必须就近旁路至地。"}, {"pin_number": "35", "pin_name": "INTVCC", "pin_description": "内部 6.35V 稳压器输出。该引脚必须连接至 GATEVCC 引脚。当 EXTVCC 电压高于 6.4V 时,INTVCC 从 EXTVCC 供电,否则 INTVCC 从 VIN 供电。通过一个最小 4.7μF 的陶瓷电容将该引脚旁路至地。"}, {"pin_number": "36", "pin_name": "SWEN", "pin_description": "开关使能引脚。把该引脚连接至高电平将使能开关操作。而把该引脚连接至地则可停用开关操作。不要将该引脚浮置。"}, {"pin_number": "37", "pin_name": "MODE", "pin_description": "模式引脚。施加至该引脚的电压用于设定控制器的工作模式。当施加电压低于 0.4V 时,芯片工作于强制连续电流模式。当允许该引脚浮置时,则芯片工作于突发模式 (Burst Mode)。而当 MODE 引脚电压高于 2.3V 时,则为不连续模式运行。"}, {"pin_number": "38", "pin_name": "IMON_IN", "pin_description": "输入电流监视引脚。该引脚流出的电流与输入电流成比例。"}, {"pin_number": "39", "pin_name": "GND (芯片底部焊盘)", "pin_description": "地。直接连接至局部接地平面。"}]}, {"product_part_number": "LT8705", "package_type": "TSSOP", "pins": [{"pin_number": "1", "pin_name": "INTVCC", "pin_description": "内部 6.35V 稳压器输出。该引脚必须连接至 GATEVCC 引脚。当 EXTVCC 电压高于 6.4V 时,INTVCC 从 EXTVCC 供电,否则 INTVCC 从 VIN 供电。通过一个最小 4.7μF 的陶瓷电容将该引脚旁路至地。"}, {"pin_number": "2", "pin_name": "MODE", "pin_description": "模式引脚。施加至该引脚的电压用于设定控制器的工作模式。当施加电压低于 0.4V 时,芯片工作于强制连续电流模式。当允许该引脚浮置时,则芯片工作于突发模式 (Burst Mode)。而当 MODE 引脚电压高于 2.3V 时,则为不连续模式运行。"}, {"pin_number": "3", "pin_name": "IMON_IN", "pin_description": "输入电流监视引脚。该引脚流出的电流与输入电流成比例。"}, {"pin_number": "4", "pin_name": "SHDN", "pin_description": "停机引脚。将该引脚连接至高电平可启用器件。把该引脚接地则使器件停机并将静态电流减至最小。不要将该引脚浮置。"}, {"pin_number": "5", "pin_name": "CSN", "pin_description": "至电感电流检测和反向电流检测放大器的负(−) 输入。"}, {"pin_number": "6", "pin_name": "CSP", "pin_description": "至电感电流检测和反向电流检测放大器的正(+)输入。Vc 引脚电压及 CSP 和 CSN 引脚之间的内置偏移与 RSENSE 电阻阻值一起设定电流跳变门限。"}, {"pin_number": "7", "pin_name": "LDO33", "pin_description": "3.3V 稳压器输出。通过一个最小 0.1μF 的陶瓷电容将该引脚旁路至地。"}, {"pin_number": "8", "pin_name": "FBIN", "pin_description": "输入反馈引脚。该引脚连接至输入误差放大器的输入端。"}, {"pin_number": "9", "pin_name": "FBOUT", "pin_description": "输出反馈引脚。该引脚把误差放大器输入连接至一个从输出端引出的外部分压电阻。"}, {"pin_number": "10", "pin_name": "IMON_OUT", "pin_description": "输出电流监视引脚。该引脚流出的电流与输出电流成比例。"}, {"pin_number": "11", "pin_name": "Vc", "pin_description": "误差放大器输出引脚。将外部补偿网络连接至该引脚。"}, {"pin_number": "12", "pin_name": "SS", "pin_description": "软起动引脚。把至少 100nF 的电容布设于此引脚。在启动时,该引脚将由一个内部电阻充电至 2.5V。"}, {"pin_number": "13", "pin_name": "CLKOUT", "pin_description": "时钟输出引脚。使用该引脚可使一个或多个兼容的开关稳压器 IC 同步至 LT8705。"}, {"pin_number": "14", "pin_name": "SYNC", "pin_description": "如欲使开关频率同步至一个外部时钟,只需采用一个时钟来驱动该引脚。"}, {"pin_number": "15", "pin_name": "RT", "pin_description": "定时电阻引脚。该引脚用于调节开关频率。在该引脚和地之间布设一个电阻可设定自由运行频率。"}, {"pin_number": "16", "pin_name": "GND", "pin_description": "地。直接连接至局部接地平面。"}, {"pin_number": "17", "pin_name": "BG1", "pin_description": "底端栅极驱动。该引脚用于驱动位于地和 GATEVcc 之间的底端 N 沟道 MOSFET 的栅极。"}, {"pin_number": "18", "pin_name": "GATEVCC", "pin_description": "用于栅极驱动器的电源。该引脚必须连接至 INTVcc 引脚。不要采用任何其他电源来给该引脚供电。将该引脚就近旁路至 GND。"}, {"pin_number": "19", "pin_name": "BG2", "pin_description": "底端栅极驱动。该引脚用于驱动位于地和 GATEVcc 之间的底端 N 沟道 MOSFET 的栅极。"}, {"pin_number": "20", "pin_name": "BOOST2", "pin_description": "升压的浮动驱动器电源。自举电容的正(+)端连接于此。"}, {"pin_number": "21", "pin_name": "TG2", "pin_description": "顶端栅极驱动。用于驱动顶端 N 沟道 MOSFET,其电压摆幅等于 GATEVCC 与开关节点电压的叠加。"}, {"pin_number": "22", "pin_name": "SW2", "pin_description": "开关节点。自举电容的负(−) 端连接于此。"}, {"pin_number": "24", "pin_name": "SW1", "pin_description": "开关节点。自举电容的负(−) 端连接于此。"}, {"pin_number": "26", "pin_name": "TG1", "pin_description": "顶端栅极驱动。用于驱动顶端 N 沟道 MOSFET,其电压摆幅等于 GATEVCC 与开关节点电压的叠加。"}, {"pin_number": "28", "pin_name": "BOOST1", "pin_description": "升压的浮动驱动器电源。自举电容的正(+)端连接于此。"}, {"pin_number": "30", "pin_name": "EXTVCC", "pin_description": "外部 VCC 输入。当 EXTVCC 超过 6.4V (典型值)时,INTVCC 将从该引脚供电。当 EXTVCC 低于 6.22V (典型值) 时,INTVCC 将从 VIN 供电。"}, {"pin_number": "32", "pin_name": "CSNOUT", "pin_description": "至输出电流监视放大器的负(−)输入。不用时将该引脚连接至 VOUT。"}, {"pin_number": "34", "pin_name": "CSPOUT", "pin_description": "至输出电流监视放大器的正(+)输入。该引脚和 CSNOUT 引脚负责测量检测电阻 RSENSE2 两端的电压以提供输出电流信号。不用时将该引脚连接至 VOUT。"}, {"pin_number": "36", "pin_name": "CSNIN", "pin_description": "至输入电流监视放大器的负(-)输入。该引脚和 CSPIN 引脚负责测量检测电阻 RSENSE1 两端的电压以提供输入电流信号。不用时将该引脚连接至 VIN。"}, {"pin_number": "37", "pin_name": "CSPIN", "pin_description": "至输入电流监视放大器的正(+)输入。不用时将该引脚连接至 VIN。"}, {"pin_number": "38", "pin_name": "VIN", "pin_description": "主输入电源引脚。该引脚必须就近旁路至地。"}, {"pin_number": "39", "pin_name": "GND (芯片底部焊盘)", "pin_description": "地。直接连接至局部接地平面。"}]}], "datasheet_cn": "LT8705 Rev A (2013-12)", "datasheet_en": "未找到", "family_comparison": "与LT3791-1, LTC3789, LT3758, LTC3115-1, LTM4609等器件进行了比较。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "80V", "min_input_voltage": "2.8V", "max_output_voltage": "80V", "min_output_voltage": "1.3V", "max_output_current": "未找到", "max_switch_frequency": "0.4MHz", "quiescent_current": "2650µA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "117mV", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode, DCM, CCM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "自动重启", "output_under_voltage_protection": "No", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "电流限制", "over_temperature_protection": "自动重启", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.24%", "output_reference_voltage": "1.207V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "QFN", "pitch": "0.5", "height": "1.2", "length": "7.0", "width": "5.0", "pin_count": "38"}]}