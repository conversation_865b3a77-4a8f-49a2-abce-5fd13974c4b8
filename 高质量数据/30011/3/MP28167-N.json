{"part_number": "MP28167-N", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "MP28167-N 2.8V to 22V VIN, 3A Ιουτ, Integrated Buck-Boost Converter with Four MOSFETS and PG Indication", "features": ["Configurable Output Voltage (VOUT) via the Feedback (FB) Pin", "Wide 2.8V to 22V Operating Input Voltage (VIN) Range", "0.08V to 1.637V Reference Voltage (VREF) Range with 0.8mV Resolution via the Digital Interface (1) (Default 1V VREF)", "Up to 3A Output Current (Ιουτ) or 4A Input Current (IIN)", "Four Low On Resistance (RDS(ON)), Internal Buck Power MOSFETS", "Adjustable Accurate Output Constant Current (CC) Limit with Internal Sensing MOSFET via the Digital Interface", "500kHz, 750kHz, 1MHz, or 1.25MHz Selectable Switching Frequency (fsw)", "Output Over-Voltage Protection (OVP) with Hiccup Mode", "Output Short-Circuit Protection (SCP) with Hiccup Mode", "Over-Temperature Warning and Shutdown", "Power Good (PG) Indication", "One-Time Programmable Non-Volatile Memory", "Configurable via the Digital Interface: Line Drop Compensation, PFM/PWM Mode, Soft Start (SS), OCP, and OVP", "Configurable Enable (EN) Shutdown Discharge", "Available in a QFN-16 (3mmx3mm) Package"], "description": "The MP28167-N is an integrated, synchronous buck-boost converter with four MOSFETS capable of regulating the output voltage (VOUT) across a wide 2.8V to 22V input voltage (VIN) range with high efficiency. The integrated VOUT scaling and adjustable output constant current (CC) limit functions meet USB power delivery (PD) requirements.\nThe MP28167-N uses constant-on-time (COT) control in buck mode and constant-off-time control in boost mode, providing fast load transient response and smooth buck-boost mode transient. The MP28167-N provides automatic pulse-frequency modulation (PFM)/pulse-width modulation (PWM) mode and forced PWM mode. It also provides a configurable output CC limit, which supports flexible design for different applications.\nFull protection features include over-current protection (OCP), over-voltage protection (OVP), under-voltage protection (UVP), configurable soft start (SS), and thermal shutdown.\nThe MP28167-N is available in a QFN-16 (3mmx3mm) package.", "applications": ["Solid-State Drives (SSDs)", "USB Power Delivery (PD) Sourcing Ports", "Buck-Boost Bus Supplies", "Networking and Servers"], "ordering_information": [{"part_number": "MP28167-N", "order_device": "MP28167GQ-N-Z", "package_type": "QFN-16 (3mmx3mm)", "package_drawing_code": "MO-220", "output_voltage": "1V to 20.47V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MP28167-N", "package_type": "QFN-16", "pins": [{"pin_number": "1", "pin_name": "IN", "pin_description": "Input voltage. IN is the drain of the internal power device, and provides power to the entire chip. The MP28167-N operates from a 2.8V to 22V input voltage (VIN) range. A capacitor (CIN) is required to prevent large voltage spikes at the input. Place CIN as close to the IC as possible."}, {"pin_number": "2, 11", "pin_name": "GND", "pin_description": "Power ground. GND is the reference ground of the regulated output voltage (Vουτ). GND requires extra consideration during PCB layout. Connect GND using copper traces and vias."}, {"pin_number": "3", "pin_name": "EN", "pin_description": "On/off control for the entire device. Pull EN high to turn the converter on; pull EN low or float EN to turn it off. EN has an internal 2ΜΩ pull-down resistor connected to ground."}, {"pin_number": "4", "pin_name": "PG", "pin_description": "Power good output. PG indicates the VOUT status."}, {"pin_number": "5", "pin_name": "SCL", "pin_description": "Digital interface clock pin. SCL can support a digital interface clock up to 3.4MHz. If not used, SCL should be pulled up to VCC."}, {"pin_number": "6", "pin_name": "SDA", "pin_description": "Digital interface data pin. If not used, SDA should be pulled up to VCC."}, {"pin_number": "7", "pin_name": "OC", "pin_description": "Output constant current (CC) limit setting."}, {"pin_number": "8", "pin_name": "FB", "pin_description": "Feedback. To set Vουτ, connect FB to the tap of an external resistor divider that is connected between the output and GND."}, {"pin_number": "9", "pin_name": "VCC", "pin_description": "Internal 3.65V low-dropout (LDO) regulator output. Decouple VCC using a 1µF capacitor."}, {"pin_number": "10", "pin_name": "AGND", "pin_description": "Analog ground. Connect AGND to GND using a single point."}, {"pin_number": "12", "pin_name": "OUT", "pin_description": "Output power. Place the output capacitor (COUT) close to OUT and GND."}, {"pin_number": "13", "pin_name": "BST2", "pin_description": "Bootstrap. Connect a 0.1µF capacitor between SW2 and BST2 to form a floating supply across the high-side MOSFET (HS-FET) driver."}, {"pin_number": "14", "pin_name": "SW2", "pin_description": "Second half-bridge switching node. Connect one end of the inductor to SW2 to allow the current to run through the bridge."}, {"pin_number": "15", "pin_name": "SW1", "pin_description": "First half-bridge switching node. Connect one end of the inductor to SW1 to allow the current to run through the bridge."}, {"pin_number": "16", "pin_name": "BST1", "pin_description": "Bootstrap. Connect a 0.1µF capacitor between SW1 and BST1 to form a floating supply across the HS-FET driver."}]}], "datasheet_cn": "未找到", "datasheet_en": "MP28167-<PERSON>, Rev 1.0, 2023-10-24", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "22V", "min_input_voltage": "2.8V", "max_output_voltage": "20.47V", "min_output_voltage": "1V", "max_output_current": "3A", "max_switch_frequency": "1.25MHz", "quiescent_current": "1000µA", "high_side_mosfet_resistance": "25mΩ", "low_side_mosfet_resistance": "21mΩ", "over_current_protection_threshold": "8A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM/PWM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Latch", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "1V", "loop_control_mode": "固定导通时间控制"}, "package": [{"type": "REFERENCE", "pitch": "0.5", "height": "0.9", "length": "3.0", "width": "3.0", "pin_count": "3"}]}