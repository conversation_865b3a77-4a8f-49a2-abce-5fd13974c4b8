{"part_number": "RRB86848", "manufacturer": "Renesas", "country": "Japan", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "RRB86848 Bidirectional Buck-Boost Voltage Regulator with SMBus Interface for General 48V and USB PD", "features": ["Bidirectional buck, boost, and buck-boost operation", "Input voltage range: 3.9V to 55V (no dead zone)", "Output voltage: 2.4V to 55V", "Pass-Through mode in forward direction", "Adapter current and output current monitor (AMON/BMON)", "Battery charging support", "Forward and reverse sleep modes", "4x4 32 Ld TQFN package compatible with the ISL9238, RAA489108, and RAA489118 family of parts"], "description": "The RRB86848 is a bidirectional, buck-boost voltage regulator for power tools, portable vacuums, lawn mowers, and additional system bus regulation for notebooks. This regulator provides protection features and supports any USB-C interface platform including USB PD EPR. The advanced Renesas R3™ technology provides highly efficient light-load operation and fast transient response. RRB86848 takes input power from a wide range of DC power sources (such as conventional AC/DC adapters (ADP), USB PD ports, travel ADP) and safely converts it to a regulated voltage. RRB86848 also operates in the reverse direction, converting a wide-range DC power source connected at its output side to a regulated voltage at its input (ADP side). The bidirectional buck-boost regulation feature provides flexibility in developing applications with the RRB86848. RRB86848 provides programming resistor options for setting the output voltage, the adapter current limit, and the output current limit. Additionally, it provides serial communication that enables programming of many critical parameters to deliver a customized solution.", "applications": ["Voltage regulation for docking stations and other applications using USB-C EPR inputs or outputs", "Battery charging for lawnmowers and other lawn tools, eBikes, power tools, power banks"], "ordering_information": [{"part_number": "RRB86848", "order_device": "RRB86848-AT7", "package_type": "TQFN", "package_drawing_code": "L32.4x4D", "min_operation_temp": "-10", "max_operation_temp": "100"}, {"part_number": "RRB86848", "order_device": "RRB86848-AU7", "package_type": "TQFN", "package_drawing_code": "L32.4x4D", "min_operation_temp": "-10", "max_operation_temp": "100"}, {"part_number": "RRB86848", "order_device": "RRB86848-AT8", "package_type": "TQFN", "package_drawing_code": "L32.4x4D", "min_operation_temp": "-40", "max_operation_temp": "100"}, {"part_number": "RRB86848", "order_device": "RRB86848-AU8", "package_type": "TQFN", "package_drawing_code": "L32.4x4D", "min_operation_temp": "-40", "max_operation_temp": "100"}], "pin_function": [{"product_part_number": "RRB86848", "package_type": "32 Ld TQFN", "pins": [{"pin_number": "Bottom Pad", "pin_name": "GND", "pin_description": "Signal common to the IC. Unless otherwise stated, signals are referenced to the GND pin. GND should also be used as the thermal pad for heat dissipation."}, {"pin_number": "1", "pin_name": "CSON", "pin_description": "Forward mode output current sense negative input. Connect to the forward output current sense resistor negative input. Place a ceramic capacitor between CSOP and CSON to provide differential mode filtering."}, {"pin_number": "2", "pin_name": "CSOP", "pin_description": "Forward mode output current sense positive input. Connect to the forward output current sense resistor positive input. Place a ceramic capacitor between CSOP and CSON to provide differential mode filtering."}, {"pin_number": "3", "pin_name": "VOUTS1", "pin_description": "Provides feedback voltage for OutputVoltage regulation."}, {"pin_number": "4", "pin_name": "BOOT2", "pin_description": "High-side MOSFET Q4 gate driver supply. Connect an MLCC capacitor across the BOOT2 and PHASE2 pins. The boot capacitor is charged through an internal boot diode connected from the VDDP to BOOT2 pins. Connect a 0.47µF bootstrap capacitor, which must have an effective capacitance higher than 0.25µF at 5V and x50 effective high-side MOSFET gate capacitance."}, {"pin_number": "5", "pin_name": "UGATE2", "pin_description": "High-side MOSFET Q4 gate drive."}, {"pin_number": "6", "pin_name": "PHASE2", "pin_description": "Current return path for the high-side MOSFET Q4 gate drive. Connect this pin to the node consisting of the high-side MOSFET Q4 source, the low-side MOSFET Q3 drain, and one terminal of the inductor."}, {"pin_number": "7", "pin_name": "LGATE2", "pin_description": "Low-side MOSFET Q3 gate drive."}, {"pin_number": "8", "pin_name": "VDDP", "pin_description": "Power supply for the gate drivers. Connect to the VDD pin through a 4.7Ω resistor and connect a 2.2µF (10V) MLCC capacitor to GND. The capacitor must have an effective capacitance higher than 0.4µF at 5V and x1.6 effective capacitance at the BOOT pin at 5V."}, {"pin_number": "9", "pin_name": "LGATE1", "pin_description": "Low-side MOSFET Q2 gate drive."}, {"pin_number": "10", "pin_name": "PHASE1", "pin_description": "Current return path for the high-side MOSFET Q1 gate drive. Connect this pin to the node consisting of the high-side MOSFET Q1 source, the low-side MOSFET Q2 drain, and one terminal of the inductor."}, {"pin_number": "11", "pin_name": "UGATE1", "pin_description": "High-side MOSFET Q1 gate drive."}, {"pin_number": "12", "pin_name": "BOOT1", "pin_description": "High-side MOSFET Q1 gate driver supply. Connect an MLCC capacitor across the BOOT1 and PHASE1 pins. The boot capacitor is charged through an internal boot diode connected from the VDDP to BOOT1 pins. Connect a 0.47µF bootstrap capacitor, which must have an effective capacitance higher than 0.25µF at 5V and x50 effective high-side MOSFET gate capacitance."}, {"pin_number": "13", "pin_name": "ASGATE", "pin_description": "Gate drive output to the P-channel adapter FET. The use of ASGATE FETs is optional. If they are not used, leave the ASGATE pin floating."}, {"pin_number": "14", "pin_name": "CSIN", "pin_description": "Forward mode input current sense negative input. Connect to the forward input current sense resistor negative input. Place a ceramic capacitor between CSIP and CSIN to provide differential mode filtering."}, {"pin_number": "15", "pin_name": "CSIP", "pin_description": "Forward mode input current sense positive input. Connect to the forward input current sense resistor positive input. Place a ceramic capacitor between CSIP and CSIN to provide differential mode filtering. The modulator also uses the CSIP pin for sensing input voltage in forward mode and output voltage in reverse mode."}, {"pin_number": "16", "pin_name": "ADP", "pin_description": "Forward mode input used to sense adapter voltage. ASGATE is turned on when the adapter voltage is higher than 3.2V."}, {"pin_number": "17", "pin_name": "DCIN", "pin_description": "Internal LDO input that provides power to the IC. Connect a diode OR from the adapter and output. Bypass DCIN with an MLCC capacitor. Connect a 10Ω DCIN resistor between the DCIN pin and the VADP/VOUT diodes, and connect a 4.7µF DCIN capacitor to GND. The capacitor must have an effective capacitance higher than 0.4µF at 30V."}, {"pin_number": "18", "pin_name": "VDD", "pin_description": "Internal LDO output, which provides the bias power for the internal analog and digital circuit. Connect a 2.2µF (10V) MLCC capacitor to GND. The capacitor must have an effective capacitance higher than 0.4µF at 5V and x1.6 effective capacitance at the BOOT pin at 5V. If VDD is pulled below 2V for more than 1ms, the RRB86848 resets all the SMBus register values to their defaults. An external source can provide VDD power by overdriving the LDO output."}, {"pin_number": "19", "pin_name": "ACIN", "pin_description": "Adapter voltage sense. Use a resistor divider externally to detect adapter voltage. The adapter voltage is valid if the ACIN pin voltage is greater than 0.35V."}, {"pin_number": "20", "pin_name": "OTGEN/CMIN", "pin_description": "Input pin. OTG/reverse mode enable pin, general-purpose comparator input pin. When the reverse mode is enabled and the general-purpose comparator is disabled, pulling this pin high can activate the reverse mode. When the general-purpose comparator is enabled, this pin is the general-purpose comparator input."}, {"pin_number": "21", "pin_name": "SDA", "pin_description": "SMBus data I/O. Connect to the data line from the host controller or smart battery. Connect a 10k pull-up resistor according to the SMBus specification."}, {"pin_number": "22", "pin_name": "SCL", "pin_description": "SMBus clock I/O. Connect to the clock line from the host controller or smart battery. Connect a 10k pull-up resistor according to the SMBus specification."}, {"pin_number": "23", "pin_name": "ALERT#", "pin_description": "Open-drain output. Provides an active-low alert signal if ACOK goes low and/or the general-purpose comparator activates. The ALERT# triggers are enabled using SMBus commands (see Table 7)."}, {"pin_number": "24", "pin_name": "ACOK", "pin_description": "Adapter presence indicator output to indicate the adapter is ready."}, {"pin_number": "25", "pin_name": "RESERVED", "pin_description": "Must be connected to GND."}, {"pin_number": "26", "pin_name": "PGOOD/CMOUT", "pin_description": "Open-drain output. This pin provides either a power-good indication or the general-purpose comparator output."}, {"pin_number": "27", "pin_name": "PROG", "pin_description": "A resistor from the PROG pin to GND sets the following configurations: Default output voltage, Default adapter current limit, Default output current limit. See Table 15 for programming options."}, {"pin_number": "28", "pin_name": "COMP", "pin_description": "Error amplifier output. Connect a compensation network externally from COMP to GND."}, {"pin_number": "29", "pin_name": "AMON/BMON", "pin_description": "Forward input current, reverse output current, forward output current, or reverse input current monitor output."}, {"pin_number": "30", "pin_name": "RESERVED", "pin_description": "This pin should not float. Connect to either VDD or GND."}, {"pin_number": "31", "pin_name": "VOUTS2", "pin_description": "Voltage sense pin for reverse mode. Connect VOUTS1 pin to VOUTS2 pin for Voltage regulator configuration. Connect an optional ceramic capacitor >1µF from VOUTS2 to GND."}, {"pin_number": "32", "pin_name": "NC", "pin_description": "Unused"}]}], "datasheet_en": "RRB86848 Datasheet Rev.1.01", "family_comparison": "RRB86848 is a 4x4 32 Ld TQFN package compatible with the ISL9238, RAA489108, and RAA489118 family of parts.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "55V", "min_input_voltage": "3.9V", "max_output_voltage": "55V", "min_output_voltage": "2.4V", "max_output_current": "12.16A", "max_switch_frequency": "0.948MHz", "quiescent_current": "7µA", "high_side_mosfet_resistance": "不适用(外部控制器)", "low_side_mosfet_resistance": "不适用(外部控制器)", "over_current_protection_threshold": "12.16A (Programmable)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "SMBus", "enable_function": "Yes", "light_load_mode": "Diode Emulation", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "True", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.6%", "output_reference_voltage": "不适用", "loop_control_mode": "Hysteresis Mode Control"}, "package": [{"type": "compatible", "pin_count": "26", "pitch": "0.4", "height": "0.75", "width": "4", "length": "32.4"}]}