[{"part_number": "ISL91110IRNZ", "manufacturer": "Renesas", "country": "日本", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "High Efficiency Buck-Boost Regulator with 5.4A Switches", "features": ["Accepts input voltages above or below regulated output voltage", "Automatic and seamless transitions between buck and boost modes", "Input voltage range: 1.8V to 5.5V", "Output current: up to 2A (PVIN = 3.4V, VOUT = 5V)", "Output current: up to 2A (PVIN = 2.5V, VOUT = 3.3V)", "Burst current: up to 3A (PVIN = 2.9V, VOUT = 3.3V, ton < 600μs, t = 4.6ms)", "High efficiency: up to 95%", "35µA quiescent current maximizes light load efficiency", "2.5MHz switching frequency minimizes external component size", "Fully protected for short-circuit, over-temperature and undervoltage", "Small 4mmx4mm 20 Ld TQFN package"], "description": "The ISL91110IR is a high-current buck-boost switching regulator for systems using new battery chemistries. It uses Intersil's proprietary buck-boost algorithm to maintain voltage regulation while providing excellent efficiency and very low output voltage ripple when the input voltage is close to the output voltage. The ISL91110IR is capable of delivering at least 2A continuous output current (VOUT = 3.3V) over a battery voltage range of 2.5V to 4.35V. This maximizes the energy utilization of advanced single-cell Li-ion battery chemistries that have significant capacity left at voltages below the system voltage. Its fully synchronous low ON-resistance 4-switch architecture and a low quiescent current of only 35µA optimize efficiency under all load conditions. The ISL91110IR supports standalone applications with a fixed 3.3V or 3.5V output voltage or adjustable output voltage with an external resistor divider. Output voltages as low as 1V or as high as 5.2V are supported. The ISL91110IR requires only a single inductor and very few external components. Power supply solution size is minimized by its 2.5MHz switching frequency, allowing small size external components. The ISL91110IR is available in a 4mmx4mm, 20 Ld TQFN package.", "applications": ["Smartphones and tablet PCs", "Wireless communication devices", "Optical modules networking equipment"], "ordering_information": [{"part_number": "ISL91110IRNZ", "order_device": "ISL91110IRNZ-T", "package_type": "TQFN", "package_drawing_code": "L20.4X4C", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL91110IRNZ", "order_device": "ISL91110IRNZ-T7A", "package_type": "TQFN", "package_drawing_code": "L20.4X4C", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "ISL91110IR", "package_type": "TQFN", "pins": [{"pin_number": "6, 7, 8, 9", "pin_name": "PVIN", "pin_description": "Power input; Range: 1.8V to 5.5V. Connect 2x10µF capacitors to PGND."}, {"pin_number": "4, 5", "pin_name": "LX1", "pin_description": "Inductor connection, input side"}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power ground for high switching current"}, {"pin_number": "1, 2", "pin_name": "LX2", "pin_description": "Inductor connection, output side"}, {"pin_number": "17, 18, 19, 20", "pin_name": "VOUT", "pin_description": "Buck-boost regulator output; Connect 2x22µF capacitors to PGND for VOUT = 3.3V and 3.5V applications, and 2x47µF capacitors to PGND for VOUT = 4.5V and 5V applications."}, {"pin_number": "12", "pin_name": "MODE", "pin_description": "Logic input, HIGH for auto PFM mode. LOW for forced PWM operation. Also, this pin can be used with an external clock sync input. Range: 2.75MHz to 3.25MHz. Do not leave floating."}, {"pin_number": "10", "pin_name": "VIN", "pin_description": "Supply input; Range: 1.8V to 5.5V."}, {"pin_number": "11", "pin_name": "EN", "pin_description": "Logic input, drive HIGH to enable device. Do not leave floating."}, {"pin_number": "13, 14", "pin_name": "SGND", "pin_description": "Analog ground pin"}, {"pin_number": "15", "pin_name": "FB", "pin_description": "Voltage feedback pin, connect directly to the VOUT pin for fixed output voltage versions."}, {"pin_number": "16", "pin_name": "NC", "pin_description": "No connect pin"}, {"pin_number": "Epad", "pin_name": "Epad", "pin_description": "Thermal pad, connect to PGND"}]}], "datasheet_cn": "未找到", "datasheet_en": "ISL91110IR", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.2V", "min_output_voltage": "1V", "max_output_current": "2A", "max_switch_frequency": "2.9MHz", "quiescent_current": "35µA", "high_side_mosfet_resistance": "47mΩ", "low_side_mosfet_resistance": "40mΩ", "over_current_protection_threshold": "5.4A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "Internal", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "TQFN", "pitch": "0.5", "height": "0.9", "length": "20.4", "width": "4", "pin_count": "11"}]}, {"part_number": "ISL91110IR2AZ", "manufacturer": "Renesas", "country": "日本", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "High Efficiency Buck-Boost Regulator with 5.4A Switches", "features": ["Accepts input voltages above or below regulated output voltage", "Automatic and seamless transitions between buck and boost modes", "Input voltage range: 1.8V to 5.5V", "Output current: up to 2A (PVIN = 3.4V, VOUT = 5V)", "Output current: up to 2A (PVIN = 2.5V, VOUT = 3.3V)", "Burst current: up to 3A (PVIN = 2.9V, VOUT = 3.3V, ton < 600μs, t = 4.6ms)", "High efficiency: up to 95%", "35µA quiescent current maximizes light load efficiency", "2.5MHz switching frequency minimizes external component size", "Fully protected for short-circuit, over-temperature and undervoltage", "Small 4mmx4mm 20 Ld TQFN package"], "description": "The ISL91110IR is a high-current buck-boost switching regulator for systems using new battery chemistries. It uses Intersil's proprietary buck-boost algorithm to maintain voltage regulation while providing excellent efficiency and very low output voltage ripple when the input voltage is close to the output voltage. The ISL91110IR is capable of delivering at least 2A continuous output current (VOUT = 3.3V) over a battery voltage range of 2.5V to 4.35V. This maximizes the energy utilization of advanced single-cell Li-ion battery chemistries that have significant capacity left at voltages below the system voltage. Its fully synchronous low ON-resistance 4-switch architecture and a low quiescent current of only 35µA optimize efficiency under all load conditions. The ISL91110IR supports standalone applications with a fixed 3.3V or 3.5V output voltage or adjustable output voltage with an external resistor divider. Output voltages as low as 1V or as high as 5.2V are supported. The ISL91110IR requires only a single inductor and very few external components. Power supply solution size is minimized by its 2.5MHz switching frequency, allowing small size external components. The ISL91110IR is available in a 4mmx4mm, 20 Ld TQFN package.", "applications": ["Smartphones and tablet PCs", "Wireless communication devices", "Optical modules networking equipment"], "ordering_information": [{"part_number": "ISL91110IR2AZ", "order_device": "ISL91110IR2AZ-T", "package_type": "TQFN", "package_drawing_code": "L20.4X4C", "output_voltage": "3.5V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL91110IR2AZ", "order_device": "ISL91110IR2AZ-T7A", "package_type": "TQFN", "package_drawing_code": "L20.4X4C", "output_voltage": "3.5V", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "ISL91110IR", "package_type": "TQFN", "pins": [{"pin_number": "6, 7, 8, 9", "pin_name": "PVIN", "pin_description": "Power input; Range: 1.8V to 5.5V. Connect 2x10µF capacitors to PGND."}, {"pin_number": "4, 5", "pin_name": "LX1", "pin_description": "Inductor connection, input side"}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power ground for high switching current"}, {"pin_number": "1, 2", "pin_name": "LX2", "pin_description": "Inductor connection, output side"}, {"pin_number": "17, 18, 19, 20", "pin_name": "VOUT", "pin_description": "Buck-boost regulator output; Connect 2x22µF capacitors to PGND for VOUT = 3.3V and 3.5V applications, and 2x47µF capacitors to PGND for VOUT = 4.5V and 5V applications."}, {"pin_number": "12", "pin_name": "MODE", "pin_description": "Logic input, HIGH for auto PFM mode. LOW for forced PWM operation. Also, this pin can be used with an external clock sync input. Range: 2.75MHz to 3.25MHz. Do not leave floating."}, {"pin_number": "10", "pin_name": "VIN", "pin_description": "Supply input; Range: 1.8V to 5.5V."}, {"pin_number": "11", "pin_name": "EN", "pin_description": "Logic input, drive HIGH to enable device. Do not leave floating."}, {"pin_number": "13, 14", "pin_name": "SGND", "pin_description": "Analog ground pin"}, {"pin_number": "15", "pin_name": "FB", "pin_description": "Voltage feedback pin, connect directly to the VOUT pin for fixed output voltage versions."}, {"pin_number": "16", "pin_name": "NC", "pin_description": "No connect pin"}, {"pin_number": "Epad", "pin_name": "Epad", "pin_description": "Thermal pad, connect to PGND"}]}], "datasheet_cn": "未找到", "datasheet_en": "ISL91110IR", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.2V", "min_output_voltage": "1V", "max_output_current": "2A", "max_switch_frequency": "2.9MHz", "quiescent_current": "35µA", "high_side_mosfet_resistance": "47mΩ", "low_side_mosfet_resistance": "40mΩ", "over_current_protection_threshold": "5.4A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "Internal", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "TQFN", "pitch": "0.5", "height": "0.9", "length": "20.4", "width": "4", "pin_count": "11"}]}, {"part_number": "ISL91110IRAZ", "manufacturer": "Renesas", "country": "日本", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "High Efficiency Buck-Boost Regulator with 5.4A Switches", "features": ["Accepts input voltages above or below regulated output voltage", "Automatic and seamless transitions between buck and boost modes", "Input voltage range: 1.8V to 5.5V", "Output current: up to 2A (PVIN = 3.4V, VOUT = 5V)", "Output current: up to 2A (PVIN = 2.5V, VOUT = 3.3V)", "Burst current: up to 3A (PVIN = 2.9V, VOUT = 3.3V, ton < 600μs, t = 4.6ms)", "High efficiency: up to 95%", "35µA quiescent current maximizes light load efficiency", "2.5MHz switching frequency minimizes external component size", "Fully protected for short-circuit, over-temperature and undervoltage", "Small 4mmx4mm 20 Ld TQFN package"], "description": "The ISL91110IR is a high-current buck-boost switching regulator for systems using new battery chemistries. It uses Intersil's proprietary buck-boost algorithm to maintain voltage regulation while providing excellent efficiency and very low output voltage ripple when the input voltage is close to the output voltage. The ISL91110IR is capable of delivering at least 2A continuous output current (VOUT = 3.3V) over a battery voltage range of 2.5V to 4.35V. This maximizes the energy utilization of advanced single-cell Li-ion battery chemistries that have significant capacity left at voltages below the system voltage. Its fully synchronous low ON-resistance 4-switch architecture and a low quiescent current of only 35µA optimize efficiency under all load conditions. The ISL91110IR supports standalone applications with a fixed 3.3V or 3.5V output voltage or adjustable output voltage with an external resistor divider. Output voltages as low as 1V or as high as 5.2V are supported. The ISL91110IR requires only a single inductor and very few external components. Power supply solution size is minimized by its 2.5MHz switching frequency, allowing small size external components. The ISL91110IR is available in a 4mmx4mm, 20 Ld TQFN package.", "applications": ["Smartphones and tablet PCs", "Wireless communication devices", "Optical modules networking equipment"], "ordering_information": [{"part_number": "ISL91110IRAZ", "order_device": "ISL91110IRAZ-T", "package_type": "TQFN", "package_drawing_code": "L20.4X4C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL91110IRAZ", "order_device": "ISL91110IRAZ-T7A", "package_type": "TQFN", "package_drawing_code": "L20.4X4C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "ISL91110IR", "package_type": "TQFN", "pins": [{"pin_number": "6, 7, 8, 9", "pin_name": "PVIN", "pin_description": "Power input; Range: 1.8V to 5.5V. Connect 2x10µF capacitors to PGND."}, {"pin_number": "4, 5", "pin_name": "LX1", "pin_description": "Inductor connection, input side"}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power ground for high switching current"}, {"pin_number": "1, 2", "pin_name": "LX2", "pin_description": "Inductor connection, output side"}, {"pin_number": "17, 18, 19, 20", "pin_name": "VOUT", "pin_description": "Buck-boost regulator output; Connect 2x22µF capacitors to PGND for VOUT = 3.3V and 3.5V applications, and 2x47µF capacitors to PGND for VOUT = 4.5V and 5V applications."}, {"pin_number": "12", "pin_name": "MODE", "pin_description": "Logic input, HIGH for auto PFM mode. LOW for forced PWM operation. Also, this pin can be used with an external clock sync input. Range: 2.75MHz to 3.25MHz. Do not leave floating."}, {"pin_number": "10", "pin_name": "VIN", "pin_description": "Supply input; Range: 1.8V to 5.5V."}, {"pin_number": "11", "pin_name": "EN", "pin_description": "Logic input, drive HIGH to enable device. Do not leave floating."}, {"pin_number": "13, 14", "pin_name": "SGND", "pin_description": "Analog ground pin"}, {"pin_number": "15", "pin_name": "FB", "pin_description": "Voltage feedback pin, connect directly to the VOUT pin for fixed output voltage versions."}, {"pin_number": "16", "pin_name": "NC", "pin_description": "No connect pin"}, {"pin_number": "Epad", "pin_name": "Epad", "pin_description": "Thermal pad, connect to PGND"}]}], "datasheet_cn": "未找到", "datasheet_en": "ISL91110IR", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.2V", "min_output_voltage": "1V", "max_output_current": "2A", "max_switch_frequency": "2.9MHz", "quiescent_current": "35µA", "high_side_mosfet_resistance": "47mΩ", "low_side_mosfet_resistance": "40mΩ", "over_current_protection_threshold": "5.4A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "Internal", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.875%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "TQFN", "pitch": "0.5", "height": "0.9", "length": "20.4", "width": "4", "pin_count": "11"}]}]