{"part_number": "MP4246", "manufacturer": "Monolithic Power Systems", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "6A Buck-Boost Converter with Four Integrated MOSFETs and I²C Interface", "features": ["6A Buck-Boost Converter with Four Integrated MOSFETs", "4V to 22V Operating Input Voltage (VIN) Range, with a Withstand VIN Up to 36V", "24V Input Over-Voltage (OV) Shutdown Protection", "1V to 22V Output Voltage (VOUT) Range", "I2C-Configurable, 0.1V to 2.147V Reference Voltage (VREF) Range with 1mV Resolution", "Up to 98.82% Peak Efficiency", "Integrated 5V/60mA Low-Dropout (LDO) Regulator to Supply the External Microcontroller Unit (MCU)", "Constant Current (CC) Limit with 5% Accuracy", "Selectable Input or Output CC Limit with 5% Accuracy via Factory Trimming", "Accurate Output Current (Ιουτ) Monitoring", "Selectable 280kHz, 420kHz, 600kHz, or 1MHz Switching Frequency (fsw) with Frequency Spread Spectrum (FSS)", "Selectable Forced Pulse-Width Modulation (PWM) Mode and Automatic Pulse-Frequency Modulation (PFM) or PWM Mode", "Configurable I²C Slave Address", "Line Drop Compensation", "I2C, Alert, and One-Time Programmable (OTP) Memory", "Enable (EN) Shutdown Passive Discharge", "Available in a QFN-19 (4mmx5mm) Package with Wettable Flanks"], "description": "The MP4246 is a synchronous buck-boost converter with four integrated MOSFETs (SWA, SWB, SWC, and SWD). The device can deliver up to 6A of output current (IOUT) at certain input supply ranges with excellent efficiency. The MP4246 is suitable for USB power delivery (PD) and wireless charging applications. The device can work with an external USB PD controller or wireless charging controller via the I2C interface. The I2C interface and one-time programmable (OTP) memory provide flexibility for configurable features. Fault condition protections include constant current (CC) limiting, output over-voltage protection (OVP), input OVP, and thermal shutdown (TSD). The MP4246 requires a minimal number of readily available, standard external components. The MP4246 is available in a small QFN-19 (4mmx5mm) package with wettable flanks.", "applications": ["USB Type-C and USB Power Delivery (PD)", "USB Type-C and USB Type-A Communication Interfaces", "Wireless Charging", "USB Type-C Car Chargers", "<PERSON><PERSON>-<PERSON>ost Converters"], "ordering_information": [{"part_number": "MP4246", "order_device": "MP4246GVE-0000", "package_type": "QFN-19 (4mmx5mm)", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MP4246", "order_device": "MP4246GVE-0000-Z", "package_type": "QFN-19 (4mmx5mm)", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MP4246", "package_type": "QFN-19", "pins": [{"pin_number": "1", "pin_name": "EN", "pin_description": "Enable input. Pull the EN pin logic high to enable the chip."}, {"pin_number": "2", "pin_name": "VIN", "pin_description": "Power supply voltage. The MP4246 operates across a 4V to 22V input voltage (VIN), with a withstand VIN up to 36V. The VIN pin is the drain of the first half-bridge's internal power device. An input capacitor (CIN) is required to prevent large voltage spikes at the input. Place CIN as close to the IC as possible."}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power ground. The PGND pin requires additional care during the PCB layout. Connect the PGND pin to ground using copper traces and vias."}, {"pin_number": "4", "pin_name": "OUT", "pin_description": "Buck-boost output. The OUT pin is the drain of switch D (SWD). The output capacitor (COUT) prevents large voltage spikes at the output. Place COUT as close to the IC as possible."}, {"pin_number": "5", "pin_name": "ISEN+", "pin_description": "Positive node of the current-sense input. The ISEN+ pin can be used for the output current (IOUT) limit."}, {"pin_number": "6", "pin_name": "ISEN-", "pin_description": "Negative node of the current-sense input. The ISEN- pin can be used for the IOUT limit."}, {"pin_number": "7", "pin_name": "IMON", "pin_description": "Output current monitor. The IMON pin outputs a voltage signal that is proportional to IOUT."}, {"pin_number": "8", "pin_name": "ALT", "pin_description": "I2C alert. The ALT pin is an open-drain output. Pull ALT low to indicate the unmasked STATUS register bits."}, {"pin_number": "9", "pin_name": "SDA", "pin_description": "I2C data line."}, {"pin_number": "10", "pin_name": "SCL", "pin_description": "I2C clock signal input."}, {"pin_number": "11", "pin_name": "BST2", "pin_description": "Bootstrap 2. A 0.22µF capacitor is connected between the BST2 and SW2 pins to form a floating supply across the high-side MOSFET (HS-FET) driver."}, {"pin_number": "12", "pin_name": "SW2", "pin_description": "Switch node 2 of the buck-boost converter. Connect the SW2 pin to SW1 using a power inductor. Use a wide PCB trace to make the connection."}, {"pin_number": "13", "pin_name": "SW1", "pin_description": "Switch node 1 of the buck-boost converter. Connect the SW1 pin to SW2 using a power inductor. Use a wide PCB trace to make the connection."}, {"pin_number": "14", "pin_name": "BST1", "pin_description": "Bootstrap 1. A 0.22µF capacitor is connected between the BST1 and SW1 pins to form a floating supply across the HS-FET driver."}, {"pin_number": "15", "pin_name": "VCC", "pin_description": "Internal 3.54V low-dropout (LDO) regulator output. Decouple the VCC pin using a 1µF capacitor."}, {"pin_number": "16", "pin_name": "AGND", "pin_description": "Analog ground. Connect the AGND pin to PGND. In addition, connect AGND to the VCC capacitor's ground node."}, {"pin_number": "17", "pin_name": "FB", "pin_description": "Feedback. Connect the FB pin to an external resistor divider's tap between the output and AGND to set the output voltage (VOUT)."}, {"pin_number": "18", "pin_name": "V5V", "pin_description": "5V LDO output. Bypass the V5V pin using a 1µF capacitor. V5V can supply a 60mA IOUT. The 5V LDO turns on once VIN and the EN pin voltage (VEN) exceed their under-voltage lockout (UVLO) thresholds (VIN_UVLO and VEN_RISING1, respectively); the 5V LDO turns off once the VCC voltage (VCC) drops below its UVLO threshold (VCC_UVLO), meaning EN is pulled low, VIN is below 2.4V (typically), or the device reaches the VIN over-voltage protection (OVP) threshold. The status of OPERATION (01h, bit[7]) has no effect on V5V."}, {"pin_number": "19", "pin_name": "ADDR", "pin_description": "Multi-function. The ADDR pin can set the I2C slave address and default state of the OPERATION bit. The status of ADDR is latched after VIN and VEN exceed VIN_UVLO and VEN_RISING1, respectively. ADDR resets only when VCC is below VCC_UVLO."}]}], "datasheet_cn": "未找到", "datasheet_en": "MP4246 Rev. 1.0, 2024-07-16", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "22V", "min_input_voltage": "4V", "max_output_voltage": "22V", "min_output_voltage": "1V", "max_output_current": "6A", "max_switch_frequency": "1MHz", "quiescent_current": "900µA", "high_side_mosfet_resistance": "10mΩ", "low_side_mosfet_resistance": "14mΩ", "over_current_protection_threshold": "0.5A-6.35A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Internal", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "0.1V-2.147V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "with", "height": "1.0", "length": "5.0", "width": "4.0", "pin_count": "17", "pitch": "2."}]}