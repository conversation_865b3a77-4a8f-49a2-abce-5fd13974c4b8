{"part_number": "ISL91127IR", "manufacturer": "Renesas Electronics Corporation", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "ISL91127IR High Efficiency Buck-Boost Regulator with 4.5A Switches", "features": ["Accepts input voltages above or below regulated output voltage", "Automatic and seamless transitions between buck and boost modes", "Input voltage range: 1.8V to 5.5V", "Output current: up to 2A (PVIN = 2.5V, VOUT = 3.3V)", "High efficiency: up to 96%", "30µA quiescent current maximizes light-load efficiency", "2.5MHz switching frequency minimizes external component size", "Fully protected for short-circuit, over-temperature and undervoltage", "20 Ld 4mmx4mm QFN package"], "description": "The ISL91127IR is a high-current buck-boost switching regulator for systems using new battery chemistries. It uses Renesas' proprietary buck-boost algorithm to maintain voltage regulation while providing excellent efficiency and very low output voltage ripple when the input voltage is close to the output voltage. The ISL91127IR is capable of delivering at least 2A continuous output current (VOUT = 3.3V) over a battery voltage range of 2.5V to 4.35V. This maximizes the energy utilization of advanced single-cell Li-ion battery chemistries that have significant capacity left at voltages below the system voltage. Its fully synchronous low ON-resistance, 4-switch architecture and a low quiescent current of only 30µA optimize efficiency under all load conditions. The ISL91127IR supports stand-alone applications with a fixed 3.3V or 3.5V output voltage or adjustable output voltage with an external resistor divider. Output voltages as low as 1.0V or as high as 5.2V are supported. The ISL91127IR is available in a 20 Ld, 0.5mm pitch QFN (4mmx4mm) package. The 2.5MHz switching frequency further reduces the size of external components.", "applications": ["Handheld and battery powered consumer and medical devices", "Brownout free system voltage for smartphones and tablet PCs", "Wireless communication devices", "2G/3G/4G RF power amplifiers"], "ordering_information": [{"part_number": "ISL91127IR", "order_device": "ISL91127IRNZ-T", "package_type": "QFN", "package_drawing_code": "L20.4x4C", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL91127IR", "order_device": "ISL91127IRNZ-T7A", "package_type": "QFN", "package_drawing_code": "L20.4x4C", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL91127IR", "order_device": "ISL91127IRAZ-T", "package_type": "QFN", "package_drawing_code": "L20.4x4C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL91127IR", "order_device": "ISL91127IRAZ-T7A", "package_type": "QFN", "package_drawing_code": "L20.4x4C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "ISL91127IR", "package_type": "QFN", "pins": [{"pin_number": "6, 7, 8, 9", "pin_name": "PVIN", "pin_description": "Power input. Range: 1.8V to 5.5V. Connect 2x10µF capacitors to PGND."}, {"pin_number": "4, 5", "pin_name": "LX1", "pin_description": "Inductor connection, input side"}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power ground for high switching current"}, {"pin_number": "1, 2", "pin_name": "LX2", "pin_description": "Inductor connection, output side"}, {"pin_number": "17, 18, 19, 20", "pin_name": "VOUT", "pin_description": "Buck-boost regulator output. Connect 2x22µF capacitors to PGND."}, {"pin_number": "12", "pin_name": "MODE", "pin_description": "Logic input, HIGH for auto PFM mode. LOW for forced PWM operation. Also, this pin can be used with an external clock sync input. Range: 2.75MHz to 3.25MHz."}, {"pin_number": "10", "pin_name": "VIN", "pin_description": "Supply input. Range: 1.8V to 5.5V."}, {"pin_number": "11", "pin_name": "EN", "pin_description": "Logic input, drive HIGH to enable device."}, {"pin_number": "13, 14", "pin_name": "SGND", "pin_description": "Analog ground pin"}, {"pin_number": "15", "pin_name": "FB", "pin_description": "Voltage feedback pin"}, {"pin_number": "16", "pin_name": "NC", "pin_description": "No connect pin"}, {"pin_number": "EPAD", "pin_name": "EPAD", "pin_description": "Thermal pad, connect to PGND"}]}], "datasheet_cn": "未找到", "datasheet_en": "FN8859", "family_comparison": "TABLE 1. KEY DIFFERENCES BET<PERSON>EEN FAMILY OF PARTS on page 3 compares ISL91127, ISL91127IR, and ISL91128 across features like BUCK-BOOST REGULATION, BYPASS, VSEL, I2C AND DVS, and PACKAGE.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.2V", "min_output_voltage": "1.0V", "max_output_current": "2A", "max_switch_frequency": "2.9MHz", "quiescent_current": "30µA", "high_side_mosfet_resistance": "32mΩ", "low_side_mosfet_resistance": "37mΩ", "over_current_protection_threshold": "4.5A", "operation_mode": "Synchronous", "output_voltage_config_method": "Fixed/Adjustable", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "Internal", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "QFN", "pitch": "0.5", "height": "0.9", "width": "4", "length": "20.4", "pin_count": "1127"}]}