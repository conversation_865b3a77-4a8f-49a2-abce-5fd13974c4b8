{"part_number": "MP28160", "manufacturer": "Monolithic Power Systems", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "升降压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "5.5V/0.5A High-Efficiency Integrated Buck-Boost Converter with 3.3V Fixed Output in 1.4x1.8mm CSP Package", "features": ["2.5V to 5.5V Input Voltage Range", "1.8MHz Switching Frequency for CCM", "3.3V Fixed Output Voltage", "500mA Continuous Output Current", "1ms Soft-Start Time", "Auto PFM/PWM Mode", "Output Over-Voltage Protection", "Hiccup Over-Current Protection", "1μA Shutdown Current", "Active Low System EN Pin", "Over-Temperature Shutdown", "Available in a Wafer Level Chip Scale Packaging: CSP-12(1.4mmx1.8mm)"], "description": "The MP28160 is an integrated buck-boost converter in a small CSP package. The buck-boost converter can operate from an input voltage above, equal to, or below the output voltage. It uses current mode control with a 1.8MHz fixed PWM frequency to optimize stability and transient response. In a light load condition, it enters auto PFM/PWM mode to get high light load efficiency. Integrated MOSFETs minimize the solution size while maintaining high efficiency. Fault protection includes output hiccup current limiting, OVP, and thermal shutdown. The MP28160 is available in a tiny CSP-12 (1.4mmx1.8mm) package.", "applications": ["USB-C Cable", "Thunderbolt", "Portable Devices"], "ordering_information": [{"part_number": "MP28160", "order_device": "MP28160GC", "package_type": "CSP-12(1.4mmx1.8mm)", "package_drawing_code": "MO-211", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MP28160", "order_device": "MP28160GC-Z", "package_type": "CSP-12(1.4mmx1.8mm)", "package_drawing_code": "MO-211", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MP28160", "package_type": "CSP-12", "pins": [{"pin_number": "A1, B1, A2", "pin_name": "IN", "pin_description": "The buck-boost input pin. The MP28160 operates from a 2.5V to 5.5V VIN voltage. Place a 10μF or larger capacitor for decoupling."}, {"pin_number": "A3", "pin_name": "SW1", "pin_description": "Switch1. The first half-bridge switch node is connected to SW1. Connect an inductor between SW1 and SW2."}, {"pin_number": "B2", "pin_name": "AGND", "pin_description": "Analog ground. Connect it to GND."}, {"pin_number": "B3", "pin_name": "GND", "pin_description": "Power ground. Reference ground of the regulated output voltage. GND requires extra care during PCB layout. Connect to GND with copper traces and vias."}, {"pin_number": "C1", "pin_name": "VCC", "pin_description": "Internal 5V LDO regulator output. Decouple with a 1μF capacitor."}, {"pin_number": "C2", "pin_name": "EN", "pin_description": "On/off control for entire chip. EN is active low. Drive EN high to turn off the chip; drive EN low, or float, to turn on the device. It has internal 600kΩ pull-down resistor to ground."}, {"pin_number": "C3", "pin_name": "SW2", "pin_description": "Switch2. The internal second half-bridge switch node is connected to SW2. Connect an inductor between SW1 and SW2."}, {"pin_number": "D1", "pin_name": "NC", "pin_description": "No connection."}, {"pin_number": "D2", "pin_name": "RSV", "pin_description": "Reserved pin. RSV must be left floating or shorted to GND."}, {"pin_number": "D3", "pin_name": "OUT", "pin_description": "Output pin."}]}], "datasheet_cn": "未找到", "datasheet_en": "MP28160 Rev. 1.0", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.5V", "max_output_voltage": "3.3V", "min_output_voltage": "3.3V", "max_output_current": "0.5A", "max_switch_frequency": "2.15MHz", "quiescent_current": "220µA", "high_side_mosfet_resistance": "90mΩ", "low_side_mosfet_resistance": "80mΩ", "over_current_protection_threshold": "2.5A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Auto PFM/PWM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "未找到", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "Information", "pitch": "0.4", "height": "0.63", "width": "1.8", "length": "1.4", "pin_count": "1"}]}