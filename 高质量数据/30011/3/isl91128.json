{"part_number": "ISL91128", "manufacturer": "Renesas", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "High Efficiency Buck-Boost Regulator with 4.5A Switches and I2C Interface", "features": ["Accepts input voltages above or below regulated output voltage", "Automatic and seamless transitions between Buck and <PERSON><PERSON> modes", "I2C interface", "Input voltage range: 1.8V to 5.5V", "Continuous output current: up to 2.4A (PVIN = 2.5V, VOUT = 3.3V)", "High efficiency: up to 96%", "30µA quiescent current maximizes light-load efficiency", "Selectable bypass power saving mode operation", "2.5MHz switching frequency minimizes external component size", "Fully protected for short-circuit, over-temperature, and undervoltage", "Small 2.15mmx1.74mm WLCSP"], "description": "The ISL91128 is a high-current, buck-boost switching regulator for systems using new battery chemistries. It uses the Renesas proprietary buck-boost algorithm to maintain voltage regulation while providing excellent efficiency and very low output voltage ripple when the input voltage is close to the output voltage. The device also includes a selectable Bypass mode for low power consumption in applications that have a Sleep or Low Power mode. The ISL91128 is capable of delivering at least 2.2A continuous output current (VOUT = 3.3V) across a battery voltage range of 2.5V to 4.35V. This maximizes the energy utilization of advanced, single-cell Li-ion battery chemistries that have significant capacity left at voltages below the system voltage. Its fully synchronous low ON-resistance 4-switch architecture and a low quiescent current of only 30μA optimize efficiency under all load conditions. The ISL91128 supports a broader set of programmable features that can be accessed using an I2C bus interface. With a programmable output voltage range of 1.9V to 5.0V, the ISL91128 is ideal for applications requiring dynamically changing supply voltages. A programmable slew rate can be selected to provide smooth transitions between output voltage settings. The ISL91128 is available in a 20 bump, 0.4mm pitch WLCSP (2.15mmx1.74mm) with a 2.5MHz switching frequency, which further reduces the size of external components.", "applications": ["Brownout-free system voltage for smart phones and tablet PCs", "Wireless communication devices", "2G/3G/4G RF power amplifiers"], "ordering_information": [{"part_number": "ISL91128", "order_device": "ISL91128IINZ-T", "package_type": "WLCSP", "package_drawing_code": "W4x5.20M", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "ISL91128", "order_device": "ISL91128IINZ-T7A", "package_type": "WLCSP", "package_drawing_code": "W4x5.20M", "output_voltage": "3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "ISL91128", "package_type": "WLCSP", "pins": [{"pin_number": "A1, A2, A3", "pin_name": "PVIN", "pin_description": "Power input. Range: 1.8V to 5.5V. Connect 2x10µF capacitors to PGND."}, {"pin_number": "B1, B2, B3", "pin_name": "LX1", "pin_description": "Inductor connection, input side"}, {"pin_number": "C1, C2", "pin_name": "PGND", "pin_description": "Power ground for high switching current"}, {"pin_number": "D1, D2, D3", "pin_name": "LX2", "pin_description": "Inductor connection, output side"}, {"pin_number": "E1, E2, E3", "pin_name": "VOUT", "pin_description": "Buck-boost regulator output. Connect 2x22µF capacitors to PGND."}, {"pin_number": "C4", "pin_name": "SDA", "pin_description": "I2C data input"}, {"pin_number": "C3", "pin_name": "SCL", "pin_description": "I2C clock input"}, {"pin_number": "A4", "pin_name": "VIN", "pin_description": "Supply input. Range: 1.8V to 5.5V."}, {"pin_number": "B4", "pin_name": "EN", "pin_description": "Logic input, drive HIGH to enable device."}, {"pin_number": "D4", "pin_name": "SGND", "pin_description": "Analog ground pin"}, {"pin_number": "E4", "pin_name": "FB", "pin_description": "Voltage feedback pin. Connect to VOUT"}]}], "datasheet_cn": "未找到", "datasheet_en": "ISL91128 <PERSON><PERSON>, Rev.3.00, 2018-05-03", "family_comparison": "Compares ISL91127, ISL91127IR, and ISL91128 based on Buck-Boost Regulation, Bypass, Dynamic Voltage Scaling, I2C, and Package.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5V", "min_output_voltage": "1.95V", "max_output_current": "2.2A", "max_switch_frequency": "2.9MHz", "quiescent_current": "30µA", "high_side_mosfet_resistance": "30mΩ", "low_side_mosfet_resistance": "25mΩ", "over_current_protection_threshold": "4.2A", "operation_mode": "同步", "pass_through_mode": "True", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Latch", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2%", "output_reference_voltage": "未找到", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "Outline", "pitch": "0.4", "height": "0.5", "length": "2.15", "width": "1.74", "pin_count": "20"}]}