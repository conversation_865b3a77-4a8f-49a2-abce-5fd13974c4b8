[{"part_number": "MAX26239", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "4.5V to 36V, 6A Buck-Boost Converters", "features": ["Meets Stringent Quality and Reliability Requirements", "4.5V to 36V Operating Input Voltage Range", "Allows Operation in Wide Input Conditions", "Tolerates Input Transients up to 42V", "EN Pin Compatible up to 42V", "8.2A/10A Typical Input Current Limit", "Fixed and Adjustable Output Voltage Options", "-40°C to +125°C Temperature Range", "High Integration and Thermally Enhanced Package Reduces BOM Cost and Board Space", "Integrated FETs H-Bridge Architecture", "2.1MHz/400kHz Switching Frequency Options", "Phase-Locked Loop (PLL) Frequency Synchronization", "<PERSON><PERSON><PERSON> Enhanced, 22-Pin FC2QFN Package", "Low Quiescent Current Meets Stringent Current Requirements", "95µA Quiescent Current in Standby Mode", "10μA Maximum Shutdown Current", "Reduced EMI Emissions at Switching Frequency", "Spread-Spectrum Function Enabled/Disabled by SPS Pin", "Protection Features Improve System Reliability", "Supply Undervoltage Lockout and Thermal Protection", "Output PGOOD Indicator, Overvoltage, and Short-Circuit Protection"], "description": "The MAX26239/MAX26240 are small, synchronous, buck-boost converters with integrated H-bridge switches. These ICs provide a fixed-output regulation voltage and an externally adjustable output voltage in the 3V to 20V range with an input voltage above, below, or equal to the output regulation voltage. The IC has typical 8.2A and 10A input current-limit options and can support continuous load currents up to 6A depending on the input-to-output voltage ratio and operating frequency. It also has a wide input voltage range of 4.5V to 36V. The MAX26239/MAX26240 have two switching frequency options: 2.1MHz and 400kHz. The 2.1MHz high switching frequency allows small external components and reduced output ripple and guarantees no AM band interference, while the 400kHz switching frequency offers better efficiency and relieves the power consumption concern. The SYNC input allows three operation modes: skip mode with ultra-low quiescent current, forced fixed-frequency PWM operation, and synchronization to an external clock. The IC also includes spread-spectrum frequency modulation to minimize EMI interference. The MAX26239/MAX26240 feature a power-OK (POK) indicator, undervoltage lockout, overvoltage protection, cycle-by-cycle current limit, and thermal shutdown. The ICs are available in a small, 4.25mm x 4.25mm x 0.75mm, 22-pin FC2QFN package.", "applications": ["Point-of-Load Power Supplies", "12V/24V Industrial Applications", "Telecom, Servers, and Networking Equipment"], "ordering_information": [{"part_number": "MAX26239", "order_device": "MAX26239AFFAY+", "package_type": "FC2QFN", "package_drawing_code": "21-100399", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX26239", "order_device": "MAX26239AFFBY+", "package_type": "FC2QFN", "package_drawing_code": "21-100399", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX26239", "order_device": "MAX26239AFFDY+", "package_type": "FC2QFN", "package_drawing_code": "21-100399", "output_voltage": "10.5V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX26239", "order_device": "MAX26239AFFFY+", "package_type": "FC2QFN", "package_drawing_code": "21-100399", "output_voltage": "11.5V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MAX26239/MAX26240", "package_type": "FC2QFN", "pins": [{"pin_number": "1", "pin_name": "BST1", "pin_description": "Bootstrap Capacitor Connection for Switching Node LX1. Connect a 0.1µF ceramic capacitor between LX1 and BST1."}, {"pin_number": "2, 3", "pin_name": "SUP", "pin_description": "Power Supply of the Buck-Boost Converter and Internal VCC LDO Regulator. Bypass SUP to PGND1 with a 4.7uF or larger ceramic capacitor."}, {"pin_number": "4, 11", "pin_name": "NC", "pin_description": "Not Connected"}, {"pin_number": "5, 6", "pin_name": "PGND1", "pin_description": "Power Ground Connection for Buck Low-Side FET LS1. Connect PGND1 and PGND2 together to power ground."}, {"pin_number": "7", "pin_name": "LX1", "pin_description": "Buck-Boost Converter Switching Node 1. Connect LX1 to one side of the power inductor."}, {"pin_number": "8", "pin_name": "LX2", "pin_description": "Buck-Boost Converter Switching Node 2. Connect LX2 to the other side of the power inductor."}, {"pin_number": "9, 10", "pin_name": "PGND2", "pin_description": "Power Ground Connection for Boost Low-Side FET LS2. Connect PGND1 and PGND2 together to power ground."}, {"pin_number": "12, 13", "pin_name": "OUT", "pin_description": "Buck-Boost Converter Output"}, {"pin_number": "14", "pin_name": "BST2", "pin_description": "Bootstrap Capacitor Connection for Switching Node LX2. Connect a 0.1µF ceramic capacitor between LX2 and BST2."}, {"pin_number": "15", "pin_name": "EN", "pin_description": "High-Voltage-Tolerant Enable Input. Drive EN high to enable buck-boost converter."}, {"pin_number": "16", "pin_name": "FB", "pin_description": "Feedback Input. Connect FB to a resistor-divider between OUT and AGND to set the desired output voltage in the range of 3V to 20V. Connect FB to VCC for the fixed output voltage option."}, {"pin_number": "17", "pin_name": "COMP", "pin_description": "Error Amplifier Output. Connect an RC-compensation network between COMP and AGND to stabilize the control loop."}, {"pin_number": "18", "pin_name": "SPS", "pin_description": "Spread-Spectrum (SPS) Function Enable Input. Connect SPS high to enable SPS function and low to disable SPS function."}, {"pin_number": "19", "pin_name": "SYNC", "pin_description": "External Clock Synchronization and Skip/PWM Mode Control Input. Connect SYNC to AGND to enable skip mode. Connect SYNC to VCC to enable PWM mode. Connect SYNC to a valid external clock to synchronize the buck-boost converter switching frequency to an external clock."}, {"pin_number": "20", "pin_name": "PGOOD", "pin_description": "Open-Drain, Power-Good Indicator. Pull up PGOOD with an external resistor to VCC or a positive voltage lower than 5.5V to correctly indicate the OUT voltage status. PGOOD asserts low when the OUT voltage falls below 93% (typ) of its regulation voltage. PGOOD becomes high impedance when the OUT voltage rises above 94% (typ) of its regulation voltage. PGOOD is also in low during soft-start and in shutdown."}, {"pin_number": "21", "pin_name": "VCC", "pin_description": "Internal 1.8V Regulator Output. Bypass VCC to ground with a minimum 4.7µF ceramic capacitor."}, {"pin_number": "22", "pin_name": "AGND", "pin_description": "Analog Ground. Connect AGND, PGND1, and PGND2 together at a single point in a star-ground connection."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX26239/MAX26240 Rev 0 (10/23)", "family_comparison": "MAX26239和MAX26240的主要区别在于输入电流限制，分别为8.2A和10A。此外，每个型号都有不同的订购选项，包括不同的开关频率（400kHz/2.1MHz）和固定的输出电压（5V, 10.5V, 11.5V）。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "4.5V", "max_output_voltage": "20V", "min_output_voltage": "3V", "max_output_current": "6A", "max_switch_frequency": "2.1MHz", "quiescent_current": "95µA", "high_side_mosfet_resistance": "20mΩ", "low_side_mosfet_resistance": "20mΩ", "over_current_protection_threshold": "8.2A", "operation_mode": "同步", "pass_through_mode": "False", "output_voltage_config_method": "Fixed/Adjustable", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Skip Mode", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.75%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}, "package": [{"height": "0.75", "length": "4.25", "width": "4.25", "type": "Reduces", "pin_count": "42", "pitch": "0.75"}]}, {"part_number": "MAX26240", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "4.5V to 36V, 6A Buck-Boost Converters", "features": ["Meets Stringent Quality and Reliability Requirements", "4.5V to 36V Operating Input Voltage Range", "Allows Operation in Wide Input Conditions", "Tolerates Input Transients up to 42V", "EN Pin Compatible up to 42V", "8.2A/10A Typical Input Current Limit", "Fixed and Adjustable Output Voltage Options", "-40°C to +125°C Temperature Range", "High Integration and Thermally Enhanced Package Reduces BOM Cost and Board Space", "Integrated FETs H-Bridge Architecture", "2.1MHz/400kHz Switching Frequency Options", "Phase-Locked Loop (PLL) Frequency Synchronization", "<PERSON><PERSON><PERSON> Enhanced, 22-Pin FC2QFN Package", "Low Quiescent Current Meets Stringent Current Requirements", "95µA Quiescent Current in Standby Mode", "10μA Maximum Shutdown Current", "Reduced EMI Emissions at Switching Frequency", "Spread-Spectrum Function Enabled/Disabled by SPS Pin", "Protection Features Improve System Reliability", "Supply Undervoltage Lockout and Thermal Protection", "Output PGOOD Indicator, Overvoltage, and Short-Circuit Protection"], "description": "The MAX26239/MAX26240 are small, synchronous, buck-boost converters with integrated H-bridge switches. These ICs provide a fixed-output regulation voltage and an externally adjustable output voltage in the 3V to 20V range with an input voltage above, below, or equal to the output regulation voltage. The IC has typical 8.2A and 10A input current-limit options and can support continuous load currents up to 6A depending on the input-to-output voltage ratio and operating frequency. It also has a wide input voltage range of 4.5V to 36V. The MAX26239/MAX26240 have two switching frequency options: 2.1MHz and 400kHz. The 2.1MHz high switching frequency allows small external components and reduced output ripple and guarantees no AM band interference, while the 400kHz switching frequency offers better efficiency and relieves the power consumption concern. The SYNC input allows three operation modes: skip mode with ultra-low quiescent current, forced fixed-frequency PWM operation, and synchronization to an external clock. The IC also includes spread-spectrum frequency modulation to minimize EMI interference. The MAX26239/MAX26240 feature a power-OK (POK) indicator, undervoltage lockout, overvoltage protection, cycle-by-cycle current limit, and thermal shutdown. The ICs are available in a small, 4.25mm x 4.25mm x 0.75mm, 22-pin FC2QFN package.", "applications": ["Point-of-Load Power Supplies", "12V/24V Industrial Applications", "Telecom, Servers, and Networking Equipment"], "ordering_information": [{"part_number": "MAX26240", "order_device": "MAX26240AFFAY+", "package_type": "FC2QFN", "package_drawing_code": "21-100399", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX26240", "order_device": "MAX26240AFFBY+", "package_type": "FC2QFN", "package_drawing_code": "21-100399", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX26240", "order_device": "MAX26240AFFDY+", "package_type": "FC2QFN", "package_drawing_code": "21-100399", "output_voltage": "10.5V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MAX26239/MAX26240", "package_type": "FC2QFN", "pins": [{"pin_number": "1", "pin_name": "BST1", "pin_description": "Bootstrap Capacitor Connection for Switching Node LX1. Connect a 0.1µF ceramic capacitor between LX1 and BST1."}, {"pin_number": "2, 3", "pin_name": "SUP", "pin_description": "Power Supply of the Buck-Boost Converter and Internal VCC LDO Regulator. Bypass SUP to PGND1 with a 4.7uF or larger ceramic capacitor."}, {"pin_number": "4, 11", "pin_name": "NC", "pin_description": "Not Connected"}, {"pin_number": "5, 6", "pin_name": "PGND1", "pin_description": "Power Ground Connection for Buck Low-Side FET LS1. Connect PGND1 and PGND2 together to power ground."}, {"pin_number": "7", "pin_name": "LX1", "pin_description": "Buck-Boost Converter Switching Node 1. Connect LX1 to one side of the power inductor."}, {"pin_number": "8", "pin_name": "LX2", "pin_description": "Buck-Boost Converter Switching Node 2. Connect LX2 to the other side of the power inductor."}, {"pin_number": "9, 10", "pin_name": "PGND2", "pin_description": "Power Ground Connection for Boost Low-Side FET LS2. Connect PGND1 and PGND2 together to power ground."}, {"pin_number": "12, 13", "pin_name": "OUT", "pin_description": "Buck-Boost Converter Output"}, {"pin_number": "14", "pin_name": "BST2", "pin_description": "Bootstrap Capacitor Connection for Switching Node LX2. Connect a 0.1µF ceramic capacitor between LX2 and BST2."}, {"pin_number": "15", "pin_name": "EN", "pin_description": "High-Voltage-Tolerant Enable Input. Drive EN high to enable buck-boost converter."}, {"pin_number": "16", "pin_name": "FB", "pin_description": "Feedback Input. Connect FB to a resistor-divider between OUT and AGND to set the desired output voltage in the range of 3V to 20V. Connect FB to VCC for the fixed output voltage option."}, {"pin_number": "17", "pin_name": "COMP", "pin_description": "Error Amplifier Output. Connect an RC-compensation network between COMP and AGND to stabilize the control loop."}, {"pin_number": "18", "pin_name": "SPS", "pin_description": "Spread-Spectrum (SPS) Function Enable Input. Connect SPS high to enable SPS function and low to disable SPS function."}, {"pin_number": "19", "pin_name": "SYNC", "pin_description": "External Clock Synchronization and Skip/PWM Mode Control Input. Connect SYNC to AGND to enable skip mode. Connect SYNC to VCC to enable PWM mode. Connect SYNC to a valid external clock to synchronize the buck-boost converter switching frequency to an external clock."}, {"pin_number": "20", "pin_name": "PGOOD", "pin_description": "Open-Drain, Power-Good Indicator. Pull up PGOOD with an external resistor to VCC or a positive voltage lower than 5.5V to correctly indicate the OUT voltage status. PGOOD asserts low when the OUT voltage falls below 93% (typ) of its regulation voltage. PGOOD becomes high impedance when the OUT voltage rises above 94% (typ) of its regulation voltage. PGOOD is also in low during soft-start and in shutdown."}, {"pin_number": "21", "pin_name": "VCC", "pin_description": "Internal 1.8V Regulator Output. Bypass VCC to ground with a minimum 4.7µF ceramic capacitor."}, {"pin_number": "22", "pin_name": "AGND", "pin_description": "Analog Ground. Connect AGND, PGND1, and PGND2 together at a single point in a star-ground connection."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX26239/MAX26240 Rev 0 (10/23)", "family_comparison": "MAX26239和MAX26240的主要区别在于输入电流限制，分别为8.2A和10A。此外，每个型号都有不同的订购选项，包括不同的开关频率（400kHz/2.1MHz）和固定的输出电压（5V, 10.5V, 11.5V）。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "4.5V", "max_output_voltage": "20V", "min_output_voltage": "3V", "max_output_current": "6A", "max_switch_frequency": "2.1MHz", "quiescent_current": "95µA", "high_side_mosfet_resistance": "20mΩ", "low_side_mosfet_resistance": "20mΩ", "over_current_protection_threshold": "10A", "operation_mode": "同步", "pass_through_mode": "False", "output_voltage_config_method": "Fixed/Adjustable", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Skip Mode", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.75%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}, "package": [{"height": "0.75", "length": "4.25", "width": "4.25", "type": "Reduces", "pin_count": "42", "pitch": "0.75"}]}]