{"part_number": "MP5031", "manufacturer": "Monolithic Power Systems (MPS)", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "USB Power Delivery Controller", "category_lv3": "USB PD 控制器", "part_number_title": "USB PD Port Controller with Five Configurable PDOs and Load-Shedding", "features": ["4.6V to 5.5V VCC Supply Voltage Range", "3.3V to 21V Bus Voltage (VBUS) Range", "Integrated Physical Layer for Biphase Mark Code (BMC)", "Integrated Protocol Layer", "Integrated Policy Engine", "Supports One Type-C Downward-Facing Port (DFP) with USB PD 3.0 and a Programmable Power Supply (PPS)", "Supports Dedicated Charging Port (DCP) Schemes for BC1.2, Apple Divider 3 Mode, and 1.2V/1.2V Mode", "Supports Quick Charge 3.0 (QC3.0) and Huawei Fast Charge Protocol (FCP)", "Low 100µA Standby Quiescent Current (IQ)", "VBUS Isolation N-Channel MOSFET Driver", "EN Off Timer Up to 120 Minutes", "I2C Master/Slave Interface and Interrupt Function", "Load-Shedding with Thermal Sense and Low Battery Detection", "High-Voltage Pins: CC1, CC2, DP, and DM", "Integrated High-Voltage VCONN Power MOSFET", "60W/100W USB-IF PPS Certified", "Available in a QFN-20 (4mmx4mm) Package with Wettable Flanks"], "description": "The MP5031 is a USB power delivery (PD) controller that is compatible with USB Type-C 2.0 and USB PD 3.0 specifications. The MP5031 is designed for downward-facing port (DFP) applications, such as USB PD charging ports. The MP5031's backward compatibility supports dedicated charging port (DCP) schemes for Quick Charge 3.0 (QC3.0), Huawei Fast Charge Protocol (FCP), BC1.2, Apple divider 3 mode, and 1.2V/1.2V mode without outside user interaction. It also supports BC1.2 charging data port (CDP) handshaking. The I²C interface and GPIO pins provide communication with an external power converter. The MP5031 supports up to 100W of PD power. It can also support a programmable power supply (PPS). The power data object (PDO) list and charging protocols can be flexibly configured via the I2C. The I2C also selects the slave devices and protection mode. The two NTC pins monitor abnormal temperature rises, such as a temperature rise on the Type-C receptacle or the PCB board. The power-sharing function supports smart power budget management between two USB PD ports. If a car battery voltage is low, then the PDO capacity is reduced. The high-voltage I/O pins support short-circuit protection (SCP) for the DC/DC converter (i.e. battery short protection and VBUS short protection). The MP5031 is available in a QFN-20 (4mmx4mm) package with wettable flanks.", "applications": ["USB Power Delivery (PD) Charging Ports", "Car Chargers", "Multi-Port Wall Chargers"], "ordering_information": [{"part_number": "MP5031", "order_device": "MP5031GRE-00A3", "package_type": "QFN-20 (4mmx4mm)", "package_drawing_code": "MO-220", "output_voltage": "不适用", "min_operation_temp": -40, "max_operation_temp": 125}, {"part_number": "MP5031", "order_device": "MP5031GRE-xxxx-Z", "package_type": "QFN-20 (4mmx4mm)", "package_drawing_code": "MO-220", "output_voltage": "不适用", "min_operation_temp": -40, "max_operation_temp": 125}], "pin_function": [{"product_part_number": "MP5031", "package_type": "QFN-20", "pins": [{"pin_number": "1", "pin_name": "SDA", "pin_description": "I2C data line."}, {"pin_number": "2", "pin_name": "SCL", "pin_description": "I2C clock signal input. If the MP5031 is selected as the I2C master, then the SCL pin is an output pin."}, {"pin_number": "3", "pin_name": "GPIO1", "pin_description": "General-purpose I/O 1. The GPIO1 pin is a low-voltage pin that supports 5.5V operation. For more information on GPIO1's configurable functions, see the CTL3 register description on page 34."}, {"pin_number": "4", "pin_name": "GPIO5", "pin_description": "General-purpose I/O 5. The GPIO5 pin can be configured via the I2C. For more information on GPIO5's configurable functions, see the CTL4 register description on page 35."}, {"pin_number": "5", "pin_name": "I2C_MODE", "pin_description": "I2C operation mode setting. Float or pull the I2C_MODE pin low to set the MP5031 as the master. Pull I2C_MODE high to set the MP5031 as the slave. If the MP5031 is in slave mode, then the GPIO5 and GPIO6 slave functions are disabled. The SDA and SCL pins are the I2C slave entrance. I2C_MODE has a 1MΩ internal pull-down resistor."}, {"pin_number": "6", "pin_name": "VDD", "pin_description": "1.8V internal LDO output. Decouple the VDD pin with a 0.47µF capacitor."}, {"pin_number": "7", "pin_name": "VCC", "pin_description": "5V power supply for the internal circuitry. The MP5031 operates from a 4.5V to 5.5V input voltage (VIN). A 4.7µF ceramic capacitor (CIN) is required to supply power to the internal circuitry (including VCONN)."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Ground."}, {"pin_number": "9", "pin_name": "GPIO3", "pin_description": "General-purpose I/O 3. The GPIO3 pin is a low-voltage pin that supports 5.5V operation and has an internal ESD Zener diode. GPIO3 can be configured via the I2C. For more information on GPIO3's configurable functions, see the CTL3 register description on page 33."}, {"pin_number": "10", "pin_name": "GPIO4", "pin_description": "General-purpose I/O 4. The GPIO4 pin can be configured via the I²C. For more information on GPIO4's configurable functions, see the CTL3 register description on page 33."}, {"pin_number": "11", "pin_name": "GPIO2", "pin_description": "General-purpose I/O 2. The GPIO2 pin is a low-voltage pin that can be configured via the I2C. For more information on GPIO2's configurable functions, see the CTL3 register description on page 33."}, {"pin_number": "12", "pin_name": "GPIO6", "pin_description": "General-purpose I/O 6. The GPIO6 pin can be configured via the I²C. For more information on GPIO6's configurable functions, see the CTL4 register description on page 35."}, {"pin_number": "13", "pin_name": "NTC", "pin_description": "External temperature-sense pin. The NTC pin sets the negative temperature coefficient (NTC) behavior. For more information on the NTC settings, see the CTL4 register description on page 34."}, {"pin_number": "14", "pin_name": "CC2", "pin_description": "Configuration channel. The CC2 pin detects, configures, and manages the connections across a USB Type-C cable."}, {"pin_number": "15", "pin_name": "CC1", "pin_description": "Configuration channel. The CC1 pin detects, configures, and manages the connections across a USB Type-C cable."}, {"pin_number": "16", "pin_name": "DP", "pin_description": "D+ data line to USB connector. The DP pin is an input and output used for handshaking with portable devices."}, {"pin_number": "17", "pin_name": "DM", "pin_description": "D- data line to USB connector. The DM pin is an input and output used for handshaking with portable devices."}, {"pin_number": "18", "pin_name": "GPIO7", "pin_description": "General purpose I/O 7. The GPIO7 pin is a low-voltage pin that can be configured via the I2C. For more information on GPIO7's configurable functions, see the CTL4 register description on page 34."}, {"pin_number": "19", "pin_name": "VDRV", "pin_description": "External N-channel MOSFET gate driver signal. If the sink is attached, then the VDRV pin turns the external N-channel MOSFET on, and power flows from the DC/DC output to the sink. If the sink is detached, VDRV turns the external N-channel MOSFET off to isolate the power path."}, {"pin_number": "20", "pin_name": "VBUS_P", "pin_description": "Bus voltage (VBUS) sensing and discharge pin."}, {"pin_number": "Exposed pad", "pin_name": "Exposed pad", "pin_description": "Exposed pad. Connect the exposed pad to GND."}]}], "datasheet_cn": "未找到", "datasheet_en": "MP5031 Rev. 1.0", "family_comparison": "未找到", "attributes": {"power_device_type": "External MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "4.6V", "max_output_voltage": "21V", "min_output_voltage": "3.3V", "max_output_current": "5A", "max_switch_frequency": "0.45MHz", "quiescent_current": "100µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "未找到", "operation_mode": "未找到", "output_voltage_config_method": "I2C", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "UVLO", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "未找到", "output_reference_voltage": "不适用", "loop_control_mode": "不适用"}, "package": [{"type": "with", "pitch": "0.5", "height": "1.0", "width": "4.0", "length": "4.0", "pin_count": "1"}]}