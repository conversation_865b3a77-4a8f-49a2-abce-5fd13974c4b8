{"part_number": "MP2984", "manufacturer": "Monolithic Power Systems", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "36V, Synchronous Buck-Boost Controller with Current Monitoring, I2C Interface, and Adjustable OCP via IPWM", "features": ["Wide 5V to 36V Operating VIN Range", "Wide 6V to 36V Start-Up Input Voltage (VIN) Range", "Flexible Control via the I2C Interface: 0.5V to 28V Output Voltage (VOUT) Range, 0.3V to 2.047V Reference Voltage (VREF) Range with 1mV Step, Selectable VOUT Slew Rate, Configurable Constant-Current Limit", "Adjustable Current Limit with <50mA Steps via IPWM", "Output Current Monitoring (IMON)", "Configurable Soft-Start Time (tss)", "Switching Frequency (fsw) with Frequency Spread Spectrum for Reduced EMI", "Integrated VOUT Discharge", "200kHz, 300kHz, 400kHz, or 600kHz Selectable fsw", "Forced Continuous Conduction Mode (FCCM)", "Configurable VIN Under-Voltage Lockout (UVLO) Hysteresis", "Minimum VIN Regulation", "Over-Current Protection (OCP), Short-Circuit Protection (SCP), and Over-Voltage Protection (OVP)", "OCP, OVP, OTP, and PNG Interrupt Indication", "Available in a QFN-32 (4mmx4mm) Package"], "description": "The MP2984 is a high-efficiency, synchronous, quad-switch, buck-boost controller that can regulate different output voltages across a wide input voltage (VIN) range. The output voltage (VOUT), VOUT slew-rate, and output constant-current limit can be configured via the I²C interface. The MP2984 is suitable for USB power delivery (PD) applications in USB Type-C power supplies. The MP2984 uses valley current control in buck mode and peak current control in boost mode to provide fast load transient response and smooth buck-boost mode transient. Forced continuous conduction mode (FCCM) and a configurable current limit support flexible design for various applications. Full protection features include configurable over-current protection (OCP), over-voltage protection (OVP), and VIN under-voltage lockout (UVLO) hysteresis. The MP2984 is available in a QFN-32 (4mmx4mm) package.", "applications": ["USB Power Delivery (PD)", "Industrial PC Power Supplies"], "ordering_information": [{"part_number": "MP2984", "order_device": "MP2984GR-Z", "package_type": "QFN-32", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MP2984", "package_type": "QFN-32", "pins": [{"pin_number": "1", "pin_name": "SDA", "pin_description": "I2C data signal."}, {"pin_number": "2", "pin_name": "SCL", "pin_description": "I2C clock signal."}, {"pin_number": "3", "pin_name": "INT", "pin_description": "Interrupt for over-current (OC), over-temperature (OT), over-voltage (OV), and PNG fault events. If a PNG event occurs in a default set-up, the INT pin is masked off. INT is an open-drain output. If an interrupt event occurs, INT is pulled low. INT recovers to its initial status once the fault is removed. If the IC is disabled, then INT is an open drain."}, {"pin_number": "4", "pin_name": "IPWM", "pin_description": "Current dimming pulse-width modulation (PWM) signal input. If the output current limit (IOUT_LIMIT) is configured via the I2C register, then the IPWM pin input signal duty cycle controls the average IOUT_LIMIT. Pull IPWM up to the AVDD pin internally via a 1MΩ resistor to set a 100% duty high-level voltage. It is recommended to connect an additional 100kΩ external pull-up resistor to IPWM."}, {"pin_number": "5", "pin_name": "CSP", "pin_description": "Positive switching current-sense input. Connect the CSP pin to the high side (HS) of the current-sense resistor."}, {"pin_number": "6", "pin_name": "CSN", "pin_description": "Negative switching current-sense input. Connect the CSN pin to the low side (LS) of the current-sense resistor."}, {"pin_number": "7", "pin_name": "FS", "pin_description": "Switching frequency (fsw) bits default value setting. During start-up, the two voltage levels on the FS pin set the default fsw values. The first FS voltage level is 0.51 x VAVDD to 0.68 x VAVDD, and the FSW bits are set to 00 (200kHz default fsw). The second FS voltage level is 0.74 x VAVDD or greater, and the FSW bits are set to 10 (400kHz default fsw). Do not float FS or connect FS to GND. For different voltage level settings, see the Electrical Characteristics section on page 9. Changing the FS voltage level after start-up does not change the FSW bit settings. After start-up, the FSW bits can be set via the I²C interface."}, {"pin_number": "8", "pin_name": "AVDD", "pin_description": "Internal control circuit bias supply. Use a ≥2.2µF decoupling capacitor to decouple the AVDD pin."}, {"pin_number": "9", "pin_name": "AGND", "pin_description": "Analog ground."}, {"pin_number": "10", "pin_name": "IMON", "pin_description": "Current monitoring output. The IMON pin outputs the voltage signal between the IAVGP and IAVGN pins. The MP2984 senses the average load current via a current-sense resistor to output this signal."}, {"pin_number": "11", "pin_name": "COMP", "pin_description": "Internal error amplifier (EA) output. For loop compensation, connect a capacitor and a resistor in series between the COMP and AGND pins."}, {"pin_number": "12", "pin_name": "FB", "pin_description": "Output voltage (VOUT) feedback. Connect a resistor divider between the VOUT and FB pins."}, {"pin_number": "13", "pin_name": "SS", "pin_description": "Soft start (SS) setting. Connect an external capacitor to the SS pin. SS also sets the hiccup mode off time."}, {"pin_number": "14", "pin_name": "NC", "pin_description": "No connection."}, {"pin_number": "15", "pin_name": "ILIM", "pin_description": "ILIM bits default value setting. During start-up, the two voltage levels on the ILIM pin set the default current limit (ILIMIT) values. At the first ILIM voltage level, the ILIM bits are set to 001 (32mV default ILIMIT). At the second ILIM voltage level, the ILIM bits are set to 011 (45mV default ILIMIT). At the third ILIM voltage level, the ILIM bits are set to 101 (56mV default ILIMIT). At the fourth ILIM voltage level, the ILIM bits are set to 111 (68mV default ILIMIT). In default set-up, float the ILIM pin to set the ILIMT bits to 111. For different voltage level settings, see the Electrical Characteristics section on page 9. Changing the ILIM voltage level after start-up does not change the ILIM bit settings. After start-up, the ILIM bits can be set via the I2C interface."}, {"pin_number": "16", "pin_name": "IAVGN", "pin_description": "Negative average current limit sense input. Connect the IAVGN and IAVGP pins to the output rail's positive terminal to use IAVGN and IAVGP to set IOUT_LIMIT."}, {"pin_number": "17", "pin_name": "IAVGP", "pin_description": "Positive average current limit sense input. Connect the IAVGN and IAVGP pins to the output rail's positive terminal to use IAVGN and IAVGP to set IOUT_LIMIT."}, {"pin_number": "18", "pin_name": "VOUT", "pin_description": "VOUT voltage sense input. The VOUT pin supplies power to the VCC pin based on the VCC power logic. Connect VOUT to the output capacitor (COUT)."}, {"pin_number": "19", "pin_name": "SW2", "pin_description": "Converter boost switch node. Connect the SW2 pin to SWD's source and SWC's drain."}, {"pin_number": "20", "pin_name": "HG2", "pin_description": "Boost high-side MOSFET (HS-FET) gate driver. Connect the HG2 pin to SWD's gate."}, {"pin_number": "21", "pin_name": "BST2", "pin_description": "Boost HS-FET gate driver bootstrap power. Connect a capacitor between the BST2 and SW2 pins. VCC or BST1 can supply BST2."}, {"pin_number": "22", "pin_name": "LG2", "pin_description": "Boost low-side MOSFET (LS-FET) gate driver. Connect the LG2 pin to SWC's gate."}, {"pin_number": "23", "pin_name": "PGND", "pin_description": "Power ground. Gate driver current return pin."}, {"pin_number": "24", "pin_name": "VCC", "pin_description": "Driver circuit and internal bias supply. Use a ≥2.2µF ceramic decoupling capacitor to decouple VCC. Place this capacitor as close to VCC as possible. VOUT or VIN can supply VCC."}, {"pin_number": "25", "pin_name": "LG1", "pin_description": "Buck LS-FET gate driver pin. Connect LG1 to SWB's gate."}, {"pin_number": "26", "pin_name": "BST1", "pin_description": "Buck HS-FET gate driver bootstrap power. Connect a capacitor between the BST1 and SW1 pins. VCC or BST2 can supply BST1."}, {"pin_number": "27", "pin_name": "HG1", "pin_description": "<PERSON>S-FET gate driver. Connect directly to the gate of SWA."}, {"pin_number": "28", "pin_name": "SW1", "pin_description": "Converter buck switch node. Connect to the source of SWA and the drain of SWB."}, {"pin_number": "29", "pin_name": "VIN", "pin_description": "Power supply and voltage sense input."}, {"pin_number": "30", "pin_name": "EN", "pin_description": "Enable. If not used, connect the EN pin to the input source for automatic start-up. EN can also configure VIN under-voltage lockout (UVLO) protection. Do not float EN."}, {"pin_number": "31", "pin_name": "VINREG", "pin_description": "VIN regulation. The VINREG pin sets the minimum operating VIN during switching. Connect VINREG to AVDD if not used."}, {"pin_number": "32", "pin_name": "ADDR", "pin_description": "I2C slave address setting. The ADDR pin sets the ENPWR bit's default value."}]}], "datasheet_cn": "未找到", "datasheet_en": "MP2984 Rev. 1.0 (2021-12-09)", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "5V", "max_output_voltage": "28V", "min_output_voltage": "1V", "max_output_current": "未找到", "max_switch_frequency": "0.6MHz", "quiescent_current": "5uA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "Adjustable", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.5V", "loop_control_mode": "Valley Current Mode (Buck) / Peak Current Mode (Boost)"}, "package": [{"pitch": "0.40", "height": "1.00", "length": "4", "width": "4", "type": "INFORMATION", "pin_count": "2"}]}