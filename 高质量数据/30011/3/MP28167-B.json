{"part_number": "MP28167-B", "manufacturer": "Monolithic Power Systems", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "2.8V to 22V VIN, 3A IOUT, Four-Switch, High-Frequency, Integrated Buck-Boost Converter with PG Indication", "features": ["Configurable Vουτ via the FB Pin", "Wide 2.8V to 22V Operating VIN Range", "0.08V to 1.637V Reference Voltage (VREF) Range with 0.8mV Resolution via the I2C (Default 0.54V VREF)", "3A Output Current (Ιουτ) and 4A Input Current (IIN)", "Four Low On Resistance (RDS(ON)) Internal Buck Power MOSFETS", "Adjustable, Accurate CC ΙοUT_LIMIT with Internal Sensing MOSFET via the I²C", "500kHz, 750kHz, 1MHz, or 1.25MHz Selectable Switching Frequency (fsw) (Default 1.25MHz fsw)", "Output OVP with Hiccup Mode", "Output Short-Circuit Protection (SCP) with Hiccup Mode", "Over-Temperature Warning and Shutdown", "Power Good (PG) Indication", "One-Time Programmable (OTP) Non-Volatile Memory (NVM)", "I2C-Configurable Line Drop Compensation, PFM/PWM Mode, SS, OCP, and OVP", "Configurable Enable (EN) Shutdown Discharge", "Available in a QFN-16 (3mmx3mm) Package"], "description": "The MP28167-B is a synchronous, four-switch, integrated buck-boost converter regulates the output voltage (Vout) across a wide 2.8V to 22V input voltage (VIN) range with high efficiency. Integrated VOUT scaling and the adjustable output current limit (IOUT_LIMIT) meet USB power delivery (PD) requirements. The MP28167-B uses constant-on-time (COT) control in buck mode and constant-off-time control in boost mode to provide fast load transient response and smooth buck-boost mode transient. The MP28167-B provides automatic pulse-frequency modulation (PFM)/pulse-width modulation (PWM) mode and forced PWM modes. It also provides a configurable output constant current (CC) limit, which supports flexible design for different applications. Full protection features include over-current protection (OCP), over-voltage protection (OVP), under-voltage protection (UVP), configurable soft start (SS), and thermal shutdown. The MP28167-B is available in a QFN-16 (3mmx3mm) package.", "applications": ["USB Power Delivery (PD) Sourcing Ports", "Wireless Charging Transmitter", "Buck-Boost Bus Supplies"], "ordering_information": [{"part_number": "MP28167-B", "order_device": "MP28167GQ-B-Z", "package_type": "QFN-16 (3mmx3mm)", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MP28167-B", "package_type": "QFN-16 (3mmx3mm)", "pins": [{"pin_number": "1", "pin_name": "IN", "pin_description": "Input voltage. The IN pin is the drain of the internal power device, and provides power to the entire chip. The MP28167-B operates from a 2.8V to 22V input voltage (VIN). A capacitor (CIN) is required to prevent large voltage spikes from appearing at the input. Place CIN as close to the IC as possible."}, {"pin_number": "2, 11", "pin_name": "GND", "pin_description": "Power ground. GND is the reference ground of the regulated output voltage (VOUT). GND requires extra consideration during PCB layout. Connect GND with copper traces and vias."}, {"pin_number": "3", "pin_name": "EN", "pin_description": "On/off control for entire chip. Pull EN high to turn the device on; pull EN low or float EN to turn it off. EN has an internal, 2MΩ pull-down resistor connected to ground."}, {"pin_number": "4", "pin_name": "PG", "pin_description": "Power good output. The PG pin indicates the VOUT status."}, {"pin_number": "5", "pin_name": "SCL", "pin_description": "I2C interface clock pin. The SCL pin can support an I²C clock up to 3.4MHz. If not used, SCL should be pulled up to VCC."}, {"pin_number": "6", "pin_name": "SDA", "pin_description": "Data pin of the I2C interface. If not used, SDA should be pulled up to VCC."}, {"pin_number": "7", "pin_name": "OC", "pin_description": "Output constant current (CC) limit setting."}, {"pin_number": "8", "pin_name": "FB", "pin_description": "Feedback. The FB pin sets VOUT when connected to the tap of an external resistor divider that is connected between the output and GND."}, {"pin_number": "9", "pin_name": "VCC", "pin_description": "Internal 3.65V LDO regulator output. Decouple VCC using a 1µF capacitor."}, {"pin_number": "10", "pin_name": "AGND", "pin_description": "Analog ground. Connect AGND to GND using a single point."}, {"pin_number": "12", "pin_name": "OUT", "pin_description": "Output power pin. Place the output capacitor (COUT) close to the OUT and GND pins."}, {"pin_number": "13", "pin_name": "BST2", "pin_description": "Bootstrap. Connect a 0.1µF capacitor between the SW2 and BST2 pins to form a floating supply across the high-side MOSFET (HS-FET) driver."}, {"pin_number": "14", "pin_name": "SW2", "pin_description": "Second half-bridge switching node. Connect one end of the inductor to SW2 for the current to run through the bridge."}, {"pin_number": "15", "pin_name": "SW1", "pin_description": "First half-bridge switching node. Connect one end of the inductor to SW1 for the current to run through the bridge."}, {"pin_number": "16", "pin_name": "BST1", "pin_description": "Bootstrap. Connect a 0.1µF capacitor between the SW1 and BST1 pins to form a floating supply across the HS-FET driver."}]}], "datasheet_cn": "未找到", "datasheet_en": "MP28167-B Rev. 1.0", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "22V", "min_input_voltage": "2.8V", "max_output_voltage": "20.47V", "min_output_voltage": "1V", "max_output_current": "3A", "max_switch_frequency": "1.25MHz", "quiescent_current": "1000μA", "high_side_mosfet_resistance": "25mΩ", "low_side_mosfet_resistance": "21mΩ", "over_current_protection_threshold": "0-6.35A(可调)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM, PWM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Hiccup", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "0.54V", "loop_control_mode": "固定导通时间控制"}, "package": [{"type": "INFORMATION", "pitch": "0.5", "height": "1.0", "width": "3.0", "length": "3.0", "pin_count": "2"}]}