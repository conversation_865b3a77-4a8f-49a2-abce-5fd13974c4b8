[{"part_number": "LT8253", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "LT8253/LT8253A 40V USB Type-C Power Delivery Buck-Boost Controller", "features": ["Proprietary Low-EMI Buck-Boost Architecture", "Wide Input Range: 4V to 40V", "Synchronous Switching: Up to 98% Efficiency", "±1.5% Output Voltage Regulation", "Single Output supports 1 Type-C Port up to 100W", "Output Channel Enable Function", "Programmable Switching Frequency with External Synchronization and Spread Spectrum", "Over-Current, Over-Voltage, Short-Circuit Protection", "Available in 28-Lead Side Solderable QFN Package", "AEC-Q100 Qualified for Automotive Applications"], "description": "The LT®8253/LT8253A are synchronous 4-switch buck-boost controllers optimized for automotive USB-C power delivery. The LT8253/53A are fully compliant to the USB Power Delivery (PD) specification when used in conjunction with a USB Type-C or PD port controller. The output voltage slew rate can be controlled through the FB pin. The LT8253 can deliver up to 100W output power with 98% peak efficiency when running below the AM band. The LT8253A can deliver up to 60W output power with 95% peak efficiency when running above the AM band. The LT8253/8253A support single buck-boost output for 1 Type-C port with power good flag. Over-current, over-voltage, and short-circuit protections are also available.", "applications": ["Automotive USB-C Power Delivery", "General Purpose Voltage Regulator"], "ordering_information": [{"part_number": "LT8253", "order_device": "LT8253EUFDM#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8253", "order_device": "LT8253EUFDM#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8253", "order_device": "LT8253JUFDM#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8253", "order_device": "LT8253JUFDM#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8253", "order_device": "LT8253HUFDM#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8253", "order_device": "LT8253HUFDM#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8253", "order_device": "LT8253JUFDM#WPBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8253", "order_device": "LT8253JUFDM#WTRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8253", "order_device": "LT8253HUFDM#WPBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8253", "order_device": "LT8253HUFDM#WTRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}], "pin_function": [{"product_part_number": "LT8253/LT8253A", "package_type": "UFDM", "pins": [{"pin_number": "1", "pin_name": "TG1", "pin_description": "Buck Side Top Gate Drive. Drives the gate of buck side top N-Channel MOSFET with a voltage swing from SW1 to BST1."}, {"pin_number": "2", "pin_name": "LSP", "pin_description": "Positive Terminal of the Buck Side Inductor Current Sense Resistor. Ensure accurate current sense with <PERSON><PERSON> connection."}, {"pin_number": "3", "pin_name": "LSN", "pin_description": "Negative Terminal of the Buck Side Inductor Current Sense Resistor. Ensure accurate current sense with <PERSON><PERSON> connection."}, {"pin_number": "4", "pin_name": "VIN", "pin_description": "Input Supply. The VIN pin must be tied to the power input to determine its operation regions. Locally bypass this pin to ground with a minimum 0.1μF ceramic capacitor."}, {"pin_number": "5", "pin_name": "INTVCC", "pin_description": "Internal 5V Linear Regulator Output. The INTVCC linear regulator is supplied from the VIN pin and powers the internal control circuitry and gate drivers. Locally bypass this pin to ground with a minimum 4.7μF ceramic capacitor."}, {"pin_number": "6", "pin_name": "EN/UVLO", "pin_description": "Enable and Undervoltage Lockout. Force the pin below 0.3V to shut down the part and force the pin above 1.23V for normal operation. The 1.22V falling threshold and 2.5μA pull-down current can be used to program VIN UVLO with hysteresis. If neither function is used, tie this pin directly to VIN."}, {"pin_number": "7", "pin_name": "TEST", "pin_description": "Factory Test. This pin is for factory testing purpose only and must be directly connected to ground for proper operation."}, {"pin_number": "8", "pin_name": "VOUTEN", "pin_description": "Output Enable. The VOUTEN pin is used to enable buck-boost switching and deliver output power."}, {"pin_number": "9", "pin_name": "PGOOD", "pin_description": "Power Good Open Drain Output. The PGOOD pin is pulled low when the FB pin is within ±10% of its regulation voltage. To function, the pin requires an external pull-up resistor."}, {"pin_number": "10", "pin_name": "VREF", "pin_description": "Voltage Reference Output. The VREF pin provides an accurate 2V reference capable of supplying 1mA current. Locally bypass this pin to ground with a 0.47μF ceramic capacitor."}, {"pin_number": "11", "pin_name": "VOUT", "pin_description": "Output Pin. The VOUT pin must be tied to the power output to determine its operation regions. Locally bypass this pin to ground with a minimum 0.1μF ceramic capacitor."}, {"pin_number": "12", "pin_name": "BG2", "pin_description": "Boost Side Bottom Gate Drive. Drives the gate of boost side bottom N-Channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "13", "pin_name": "BG1", "pin_description": "Buck Side Bottom Gate Drive. Drives the gate of buck side bottom N-Channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "14", "pin_name": "SS", "pin_description": "Soft-Start Timer Setting. The SS pin is used to set soft-start timer by connecting a capacitor to ground. An internal 12.5μA pull-up current charging the external SS capacitor gradually ramps up FB regulation voltage."}, {"pin_number": "15", "pin_name": "FB", "pin_description": "Voltage Loop Feedback Input. The FB pin is used for output voltage regulation and output fault protection."}, {"pin_number": "16", "pin_name": "VC", "pin_description": "Error Amplifier Output. The VC pin is used to compensate the control loop with an external RC network."}, {"pin_number": "17", "pin_name": "RT", "pin_description": "Switching Frequency Setting. Connect a resistor from this pin to ground to set the internal oscillator frequency."}, {"pin_number": "18", "pin_name": "SYNC/SPRD", "pin_description": "External Clock Frequency Synchronization or Spread Spectrum. Ground this pin for switching at internal oscillator frequency. Apply a clock signal for external frequency synchronization. Tie to INTVCC for spread spectrum frequency modulation."}, {"pin_number": "19", "pin_name": "NC", "pin_description": "未找到"}, {"pin_number": "20", "pin_name": "TG2", "pin_description": "Boost Side Top Gate Drive. Drives the gate of boost side top N-Channel MOSFET with a voltage swing from SW2 to BST2."}, {"pin_number": "21", "pin_name": "SW2", "pin_description": "Boost Side Switch Node."}, {"pin_number": "22", "pin_name": "BST2", "pin_description": "Boost Side Bootstrap Floating Driver Supply. The BST2 pin has an integrated bootstrap diode from the INTVCC pin and requires an external bootstrap capacitor to the SW2 pin."}, {"pin_number": "23", "pin_name": "SW1", "pin_description": "Buck Side Switch Node."}, {"pin_number": "24", "pin_name": "BST1", "pin_description": "Buck Side Bootstrap Floating Driver Supply. The BST1 pin has an integrated bootstrap diode from the INTVCC pin and requires an external bootstrap capacitor to the SW1 pin."}, {"pin_number": "25", "pin_name": "PGND", "pin_description": "未找到"}, {"pin_number": "26", "pin_name": "PGND", "pin_description": "未找到"}, {"pin_number": "27", "pin_name": "PGND", "pin_description": "未找到"}, {"pin_number": "28", "pin_name": "PGND", "pin_description": "未找到"}, {"pin_number": "29", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Solder the exposed pad directly to the ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT8253/LT8253A Rev. A", "family_comparison": "LT8253: Can deliver up to 100W output power with 98% peak efficiency, operating below the AM band (150kHz-650kHz). LT8253A: Can deliver up to 60W output power with 95% peak efficiency, operating above the AM band (600kHz-2MHz).", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "40V", "min_input_voltage": "4V", "max_output_voltage": "25V", "min_output_voltage": "1V", "max_output_current": "5A", "max_switch_frequency": "0.65MHz", "quiescent_current": "1µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "50mV", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Discontinuous Conduction Mode", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "1V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "DESCRIPTION", "pitch": "0.5", "height": "0.75", "length": "12.5", "width": "0.3", "pin_count": "2"}]}, {"part_number": "LT8253A", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "LT8253/LT8253A 40V USB Type-C Power Delivery Buck-Boost Controller", "features": ["Proprietary Low-EMI Buck-Boost Architecture", "Wide Input Range: 4V to 40V", "Synchronous Switching: Up to 98% Efficiency", "±1.5% Output Voltage Regulation", "Single Output supports 1 Type-C Port up to 100W", "Output Channel Enable Function", "Programmable Switching Frequency with External Synchronization and Spread Spectrum", "Over-Current, Over-Voltage, Short-Circuit Protection", "Available in 28-Lead Side Solderable QFN Package", "AEC-Q100 Qualified for Automotive Applications"], "description": "The LT®8253/LT8253A are synchronous 4-switch buck-boost controllers optimized for automotive USB-C power delivery. The LT8253/53A are fully compliant to the USB Power Delivery (PD) specification when used in conjunction with a USB Type-C or PD port controller. The output voltage slew rate can be controlled through the FB pin. The LT8253 can deliver up to 100W output power with 98% peak efficiency when running below the AM band. The LT8253A can deliver up to 60W output power with 95% peak efficiency when running above the AM band. The LT8253/8253A support single buck-boost output for 1 Type-C port with power good flag. Over-current, over-voltage, and short-circuit protections are also available.", "applications": ["Automotive USB-C Power Delivery", "General Purpose Voltage Regulator"], "ordering_information": [{"part_number": "LT8253A", "order_device": "LT8253AEUFDM#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8253A", "order_device": "LT8253AEUFDM#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8253A", "order_device": "LT8253AJUFDM#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8253A", "order_device": "LT8253AJUFDM#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8253A", "order_device": "LT8253AHUFDM#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8253A", "order_device": "LT8253AHUFDM#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8253A", "order_device": "LT8253AJUFDM#WPBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8253A", "order_device": "LT8253AJUFDM#WTRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8253A", "order_device": "LT8253AHUFDM#WPBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8253A", "order_device": "LT8253AHUFDM#WTRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1682", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}], "pin_function": [{"product_part_number": "LT8253/LT8253A", "package_type": "UFDM", "pins": [{"pin_number": "1", "pin_name": "TG1", "pin_description": "Buck Side Top Gate Drive. Drives the gate of buck side top N-Channel MOSFET with a voltage swing from SW1 to BST1."}, {"pin_number": "2", "pin_name": "LSP", "pin_description": "Positive Terminal of the Buck Side Inductor Current Sense Resistor. Ensure accurate current sense with <PERSON><PERSON> connection."}, {"pin_number": "3", "pin_name": "LSN", "pin_description": "Negative Terminal of the Buck Side Inductor Current Sense Resistor. Ensure accurate current sense with <PERSON><PERSON> connection."}, {"pin_number": "4", "pin_name": "VIN", "pin_description": "Input Supply. The VIN pin must be tied to the power input to determine its operation regions. Locally bypass this pin to ground with a minimum 0.1μF ceramic capacitor."}, {"pin_number": "5", "pin_name": "INTVCC", "pin_description": "Internal 5V Linear Regulator Output. The INTVCC linear regulator is supplied from the VIN pin and powers the internal control circuitry and gate drivers. Locally bypass this pin to ground with a minimum 4.7μF ceramic capacitor."}, {"pin_number": "6", "pin_name": "EN/UVLO", "pin_description": "Enable and Undervoltage Lockout. Force the pin below 0.3V to shut down the part and force the pin above 1.23V for normal operation. The 1.22V falling threshold and 2.5μA pull-down current can be used to program VIN UVLO with hysteresis. If neither function is used, tie this pin directly to VIN."}, {"pin_number": "7", "pin_name": "TEST", "pin_description": "Factory Test. This pin is for factory testing purpose only and must be directly connected to ground for proper operation."}, {"pin_number": "8", "pin_name": "VOUTEN", "pin_description": "Output Enable. The VOUTEN pin is used to enable buck-boost switching and deliver output power."}, {"pin_number": "9", "pin_name": "PGOOD", "pin_description": "Power Good Open Drain Output. The PGOOD pin is pulled low when the FB pin is within ±10% of its regulation voltage. To function, the pin requires an external pull-up resistor."}, {"pin_number": "10", "pin_name": "VREF", "pin_description": "Voltage Reference Output. The VREF pin provides an accurate 2V reference capable of supplying 1mA current. Locally bypass this pin to ground with a 0.47μF ceramic capacitor."}, {"pin_number": "11", "pin_name": "VOUT", "pin_description": "Output Pin. The VOUT pin must be tied to the power output to determine its operation regions. Locally bypass this pin to ground with a minimum 0.1μF ceramic capacitor."}, {"pin_number": "12", "pin_name": "BG2", "pin_description": "Boost Side Bottom Gate Drive. Drives the gate of boost side bottom N-Channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "13", "pin_name": "BG1", "pin_description": "Buck Side Bottom Gate Drive. Drives the gate of buck side bottom N-Channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "14", "pin_name": "SS", "pin_description": "Soft-Start Timer Setting. The SS pin is used to set soft-start timer by connecting a capacitor to ground. An internal 12.5μA pull-up current charging the external SS capacitor gradually ramps up FB regulation voltage."}, {"pin_number": "15", "pin_name": "FB", "pin_description": "Voltage Loop Feedback Input. The FB pin is used for output voltage regulation and output fault protection."}, {"pin_number": "16", "pin_name": "VC", "pin_description": "Error Amplifier Output. The VC pin is used to compensate the control loop with an external RC network."}, {"pin_number": "17", "pin_name": "RT", "pin_description": "Switching Frequency Setting. Connect a resistor from this pin to ground to set the internal oscillator frequency."}, {"pin_number": "18", "pin_name": "SYNC/SPRD", "pin_description": "External Clock Frequency Synchronization or Spread Spectrum. Ground this pin for switching at internal oscillator frequency. Apply a clock signal for external frequency synchronization. Tie to INTVCC for spread spectrum frequency modulation."}, {"pin_number": "19", "pin_name": "NC", "pin_description": "未找到"}, {"pin_number": "20", "pin_name": "TG2", "pin_description": "Boost Side Top Gate Drive. Drives the gate of boost side top N-Channel MOSFET with a voltage swing from SW2 to BST2."}, {"pin_number": "21", "pin_name": "SW2", "pin_description": "Boost Side Switch Node."}, {"pin_number": "22", "pin_name": "BST2", "pin_description": "Boost Side Bootstrap Floating Driver Supply. The BST2 pin has an integrated bootstrap diode from the INTVCC pin and requires an external bootstrap capacitor to the SW2 pin."}, {"pin_number": "23", "pin_name": "SW1", "pin_description": "Buck Side Switch Node."}, {"pin_number": "24", "pin_name": "BST1", "pin_description": "Buck Side Bootstrap Floating Driver Supply. The BST1 pin has an integrated bootstrap diode from the INTVCC pin and requires an external bootstrap capacitor to the SW1 pin."}, {"pin_number": "25", "pin_name": "PGND", "pin_description": "未找到"}, {"pin_number": "26", "pin_name": "PGND", "pin_description": "未找到"}, {"pin_number": "27", "pin_name": "PGND", "pin_description": "未找到"}, {"pin_number": "28", "pin_name": "PGND", "pin_description": "未找到"}, {"pin_number": "29", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Solder the exposed pad directly to the ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT8253/LT8253A Rev. A", "family_comparison": "LT8253: Can deliver up to 100W output power with 98% peak efficiency, operating below the AM band (150kHz-650kHz). LT8253A: Can deliver up to 60W output power with 95% peak efficiency, operating above the AM band (600kHz-2MHz).", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "40V", "min_input_voltage": "4V", "max_output_voltage": "25V", "min_output_voltage": "1V", "max_output_current": "3A", "max_switch_frequency": "2MHz", "quiescent_current": "1µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "50mV", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Discontinuous Conduction Mode", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "1V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "DESCRIPTION", "pitch": "0.5", "height": "0.75", "length": "12.5", "width": "0.3", "pin_count": "2"}]}]