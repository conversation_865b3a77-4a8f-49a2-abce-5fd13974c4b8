{"part_number": "MP28167-A", "manufacturer": "Monolithic Power Systems", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "2.8V to 22V VIN, 3A Ιουτ, 4-Switch, Integrated Buck-Boost Converter with FB Pin", "features": ["Configurable Output Voltage via FB Pin", "Wide 2.8V to 22V Operating Input Voltage Range", "0.08V to 1.637V Reference Voltage Range with 0.8mV Resolution through I2C (Default 1V Reference Voltage)", "3A Output Current or 4A Input Current", "Four Low RDS(ON) Internal Buck Power MOSFETS", "Adjustable Accurate CC Output Current Limit with Internal Sensing MOSFET via I2C", "500kHz/750kHz Selectable Switching Frequency", "Output Over-Voltage Protection (OVP) with Hiccup", "Output Short-Circuit Protection (SCP) with Hiccup", "Over-Temperature Warning and Shutdown", "I2C Interface with ALT Pin", "One-Time Programmable (OTP) Non-Volatile Memory", "I2C-Configurable Line Drop Compensation, PFM/PWM Mode, Soft Start, OCP, and OVP", "Configurable EN Shutdown Discharge", "Available in a QFN-16 (3mmx3mm) Package", "The MPL-AL Inductor Series Matches the Best Performance"], "description": "The MP28167-A is a synchronous, four-switch, integrated buck-boost converter capable of regulating the output voltage across a wide 2.8V to 22V input voltage range with high efficiency. The integrated output voltage scaling and adjustable output current limit functions meet USB power delivery (PD) requirements. The MP28167-A uses constant-on-time (COT) control in buck mode and constant-off-time control in boost mode, providing fast load transient response and smooth buck-boost mode transient. The MP28167-A provides auto-PFM/PWM or forced PWM switching modes. It also provides configurable output constant current (CC) current limit, which supports flexible design for different applications. Full protection features include over-current protection (OCP), over-voltage protection (OVP), under-voltage protection (UVP), configurable soft start, and thermal shutdown. The MP28167-A is available in a QFN-16 (3mmx3mm) package.", "applications": ["USB PD Sourcing Ports", "Buck-Boost Bus Supplies"], "ordering_information": [{"part_number": "MP28167-A", "order_device": "MP28167GQ-A-Z", "package_type": "QFN-16 (3mmx3mm)", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MP28167-A", "package_type": "QFN-16", "pins": [{"pin_number": "1", "pin_name": "IN", "pin_description": "Supply voltage. IN is the drain of the internal power device, and provides power to the entire chip. The MP28167-A operates from a 2.8V to 22V input voltage. A capacitor (CIN) is required to prevent large voltage spikes from appearing at the input. Place CIN as close to the IC as possible."}, {"pin_number": "2, 11", "pin_name": "GND", "pin_description": "Power ground. GND is the reference ground of the regulated output voltage. GND requires extra consideration during PCB layout. Connect GND with copper traces and vias."}, {"pin_number": "3", "pin_name": "EN", "pin_description": "On/off control for entire chip. Drive EN high to turn the device on. Drive EN low or float EN to turn it off. EN has an internal 2MΩ pull-down resistor to ground."}, {"pin_number": "4", "pin_name": "ALT", "pin_description": "Alert output. If ALT pulls to logic low, a fault or warning has occurred."}, {"pin_number": "5", "pin_name": "SCL", "pin_description": "Clock pin of the I2C interface. SCL can support an I2C clock up to 3.4MHz. If not used, SCL should be pulled up to VCC."}, {"pin_number": "6", "pin_name": "SDA", "pin_description": "Data pin of the I2C interface. If not used, SDA should be pulled up to VCC."}, {"pin_number": "7", "pin_name": "OC", "pin_description": "Output constant current limit set pin."}, {"pin_number": "8", "pin_name": "FB", "pin_description": "Feedback. Sets the output voltage when connected to the tap of an external resistor divider that is connected between output and GND."}, {"pin_number": "9", "pin_name": "VCC", "pin_description": "Internal 3.65V LDO regulator output. Decouple VCC with a 1μF capacitor."}, {"pin_number": "10", "pin_name": "AGND", "pin_description": "Analog ground. Connect AGND to GND."}, {"pin_number": "12", "pin_name": "OUT", "pin_description": "Output power pin. Place the output capacitor close to OUT and GND."}, {"pin_number": "13", "pin_name": "BST2", "pin_description": "Bootstrap. Connect a 0.1μF capacitor between SW2 and BST2 to form a floating supply across the high-side switch driver."}, {"pin_number": "14", "pin_name": "SW2", "pin_description": "Switching node of the second half bridge. Connect one end of the inductor to SW2 for the current to run through the bridge."}, {"pin_number": "15", "pin_name": "SW1", "pin_description": "Switching node of the first half bridge. Connect one end of the inductor to SW1 for the current to run through the bridge."}, {"pin_number": "16", "pin_name": "BST1", "pin_description": "Bootstrap. Connect a 0.1μF capacitor between SW1 and BST1 to form a floating supply across the high-side switch driver."}]}], "datasheet_cn": "未找到", "datasheet_en": "MP28167-A Rev. 1.0 (2020-04-15)", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "22V", "min_input_voltage": "2.8V", "max_output_voltage": "20.47V", "min_output_voltage": "1V", "max_output_current": "3A", "max_switch_frequency": "0.75MHz", "quiescent_current": "1000μA", "high_side_mosfet_resistance": "25mΩ", "low_side_mosfet_resistance": "21mΩ", "over_current_protection_threshold": "可调 (0A - 6.35A)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM/PWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Hiccup", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "1V", "loop_control_mode": "Constant On Time (COT)"}, "package": [{"type": "INFORMATION", "pitch": "0.5", "height": "0.9", "width": "3.0", "length": "3.0", "pin_count": "8"}]}