{"part_number": "MP28162", "manufacturer": "MPS (Monolithic Power Systems)", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "MP28162 High-Efficiency, Synchronous Buck-Boost Converter with 1.5A MOSFETS in a Small WLCSP Package", "features": ["1.8V Minimum Start-Up Input Voltage (VIN)", "1.2V to 5.5V Operating VIN Range", "1.5V to 5.5V Output Voltage (VOUT) Range", "1.5A Switching Current Limit", "2MHz Fixed Switching Frequency (fREQ) or External Synchronous fREQ", "Selectable Pulse-Skip Mode or Pulse-Width Modulation (PWM) Mode", "25μA Typical Quiescent Current (IQ)", "Load Disconnect during Shutdown", "Internal Soft Start (SS) and Compensation", "Short-Circuit Protection (SCP) with Hiccup Mode", "Over-Temperature Protection (OTP)", "Available in a Small WLCSP-15 (1.3mmx2.1mm) Package"], "description": "The MP28162 is a high-efficiency, low quiescent current (IQ), buck-boost converter that operates from an input voltage (VIN) exceeding, below, or equal to the output voltage (VOUT). The device provides a compact solution for products powered by single-cell Li-ion or multi-cell alkaline batteries, where VOUT is within the battery voltage (VBATT) range. Current-mode control and the fixed pulse-width modulation (PWM) frequency provide optimal stability and transient response. The fixed 2MHz switching frequency (fREQ) and integrated, low on resistance (RDS(ON)) MOSFETs reduce the solution size and maintain high efficiency. To optimize battery life, the MP28162 employs selectable pulse-skip mode to reduce fREQ under light-load conditions. For low-noise applications where pulse-skip mode may cause interference, a high-logic input on the MODE/SYNC pin ensures that the part operates in fixed-frequency PWM mode under all load conditions. The MP28162 is available in a small WLCSP-15 (1.3mmx2.1mm) package.", "applications": ["Optical Modules", "Portable Instruments", "Battery-Powered Devices"], "ordering_information": [{"part_number": "MP28162", "order_device": "MP28162GC-Z", "package_type": "WLCSP-15", "package_drawing_code": "MO-211", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MP28162", "package_type": "WLCSP-15", "pins": [{"pin_number": "A1", "pin_name": "EN", "pin_description": "On/off control. Pull the EN pin high to enable the MP28162; pull EN low or float the pin to disable all internal circuits. EN is pulled down to AGND with an internal, 1.5MΩ resistor."}, {"pin_number": "A2, A3", "pin_name": "VIN", "pin_description": "Supply voltage for power stage."}, {"pin_number": "B1", "pin_name": "MODE/SYNC", "pin_description": "Operation mode selection. If the MODE/SYNC pin is low, the MP28162 switches between pulse-skip mode and fixed-frequency pulse-width modulation (PWM) mode automatically, based on the load level. If MODE/SYNC is high, the MP28162 works in fixed-frquency PWM mode continuously. An external clock can be applied to MODE/SYNC for switching frequency (fREQ) synchronization. MODE/SYNC is pulled down to AGND with an internal, 1MΩ resistor. Pull MODE/SYNC high or low via a resistor below 10kΩ."}, {"pin_number": "B2, B3", "pin_name": "SW1", "pin_description": "Switch 1. Internal switches are connected to the SW1 pin. Connect an inductor between the SW1 and SW2 pins."}, {"pin_number": "C1", "pin_name": "VCC", "pin_description": "Supply voltage for control stage. The VCC pin is powered by the higher value between the input voltage (VIN) and output voltage (VOUT). Decouple VCC using a 4.7μF capacitor."}, {"pin_number": "C2, C3", "pin_name": "PGND", "pin_description": "Power ground."}, {"pin_number": "D1", "pin_name": "AGND", "pin_description": "Signal ground."}, {"pin_number": "D2, D3", "pin_name": "SW2", "pin_description": "Switch 2. Internal switches are connected to the SW2 pin. Connect an inductor between the SW1 and SW2 pins."}, {"pin_number": "E1", "pin_name": "FB", "pin_description": "Output voltage feedback. Keep the FB pin and its associated traces far from noise sources such as SW1 and SW2."}, {"pin_number": "E2, E3", "pin_name": "VOUT", "pin_description": "Buck-boost converter output. Place an output capacitor (COUT) close to the VOUT and PGND pins."}]}], "datasheet_cn": "未找到", "datasheet_en": "MP28162 Rev. 1.0", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.2V", "max_output_voltage": "5.5V", "min_output_voltage": "1.5V", "max_output_current": "1.5A", "max_switch_frequency": "2.3MHz", "quiescent_current": "25µA", "high_side_mosfet_resistance": "45mΩ", "low_side_mosfet_resistance": "35mΩ", "over_current_protection_threshold": "1.5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PSM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.5V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "REFERENCE", "length": "2.1", "width": "1.3", "pin_count": "1", "pitch": "0.4", "height": "0.63"}]}