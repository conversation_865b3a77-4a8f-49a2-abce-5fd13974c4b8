[{"part_number": "MP4247GQV-0000", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "MP4247 36V, 100W, Buck-Boost Converter with Integrated Low-Side MOSFETs and I²C Interface", "features": ["UL and IEC62368-1 Certified, Efile #322138", "100W Buck-Boost Converter with Integrated Low-Side MOSFETs (LS-FETs)", "Integrated Gate Driver for High-Side Power MOSFETs (HS-FETs)", "3.6V to 36V Start-Up Input Voltage (VIN) Range", "Supports 2.8V Falling VIN when the Output Voltage (VOUT) > 3.5V", "1V to 24V VOUT Range with 1% Accuracy", "Up to 5A Output Current (IOUT)", "Up to 98% Peak Efficiency", "Configurable Reference Voltage (VREF) Range (0.1V to 2.147V) with 1mV Resolution via I2C Interface", "Accurate Output CC Current Limit: ±5%", "Meets USB PD 3.0 with PPS Specifications", "Selectable 280kHz, 420kHz, or 600kHz Switching Frequency (fsw)", "Selectable Forced PWM or Auto-PFM/PWM", "Output Biased VCC LDO for Higher Efficiency", "Output Current Monitor (IMON) Function", "Line Drop Compensation via RSENS", "I2C, Alert, and One-Time Programmable (OTP) Memory", "EN Shutdown Passive Discharge", "Output OCP, OVP, and Thermal Shutdown", "Available in a QFN-20 (3mmx5mm) Package"], "description": "The MP4247 is a buck-boost converter with two integrated low-side power switches. The device can deliver up to 100W of peak output power at certain input supplies with excellent efficiency. The MP4247 is well-suited for USB power delivery (USB PD) applications. The device can work with an external USB PD controller via the I2C interface. The I2C interface and one-time programmable (OTP) memory provide flexible and configurable parameters. Fault condition protection includes constant current (CC) current limiting, output over-voltage protection (OVP), and thermal shutdown (TSD). The MP4247 requires a minimal number of readily available, standard external components, and is available in a QFN-20 (3mmx5mm) package.", "applications": ["USB Type-C and USB Power Delivery", "USB Type-C PD Monitors and Docking Stations", "USB Type-C Car Chargers", "Wireless Charging"], "ordering_information": [{"part_number": "MP4247GQV-0000", "order_device": "MP4247GQV-0000-Z", "package_type": "QFN-20", "package_drawing_code": "MO-220", "output_voltage": "5V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MP4247GQV-0000", "package_type": "QFN-20", "pins": [{"pin_number": "1", "pin_name": "SCL", "pin_description": "I2C clock signal input."}, {"pin_number": "2", "pin_name": "SDA", "pin_description": "I2C data line."}, {"pin_number": "3", "pin_name": "ALT", "pin_description": "I2C alert pin. The ALT pin is an open-drain, active low output."}, {"pin_number": "4", "pin_name": "FB", "pin_description": "Feedback pin. To set the output voltage (VOUT), connect FB to the tap of an external resistor divider from the output to AGND."}, {"pin_number": "5", "pin_name": "ISEN-", "pin_description": "Negative node of current-sense signal input. Place a current-sense resistor between PGND and the GND capacitor (COUT). Then connect the ISEN- pin to the PGND side of the capacitor."}, {"pin_number": "6", "pin_name": "ISEN+", "pin_description": "Positive node of current-sense signal input. Place a current-sense resistor between PGND and the GND capacitor (COUT). Then connect the ISEN+ pin to the GND side of the capacitor."}, {"pin_number": "7", "pin_name": "VOUT", "pin_description": "Output voltage-sense input. VOUT provides the VCC supply under certain VOUT conditions."}, {"pin_number": "8", "pin_name": "BST2", "pin_description": "Bootstrap. A 0.22µF capacitor should be kelvin connected to SW2 pin from BST2 to form a floating supply across the high-side switch driver."}, {"pin_number": "9", "pin_name": "HG2", "pin_description": "High-side 2 gate drive output for boost mode's high-side MOSFET (SWD)."}, {"pin_number": "10", "pin_name": "VCC", "pin_description": "Internal 5V LDO regulator output. Decouple VCC with a 0.47 µF to 1µF capacitor. If the VCC is used to supply other controller, suggest the total VCC capacitor is <1.5µF."}, {"pin_number": "11", "pin_name": "AGND", "pin_description": "Analog ground. Connect AGND to PGND. Connect AGND to the VCC capacitor's ground node."}, {"pin_number": "12, 15", "pin_name": "PGND", "pin_description": "Power ground. PGND requires extra consideration during PCB layout. Connect PGND to GND with copper traces and vias."}, {"pin_number": "13", "pin_name": "SW2", "pin_description": "Switch 2 node for buck-boost mode. Connect SW1 to SW2 using a power inductor. Use a wide PCB trace to make the inductor connection."}, {"pin_number": "14", "pin_name": "SW1", "pin_description": "Switch 1 node for buck-boost mode. Connect SW1 to SW2 using a power inductor. Use a wide PCB trace to make the inductor connection."}, {"pin_number": "16", "pin_name": "IMON", "pin_description": "Current monitor output. IMON indicates the signal between ISEN+ and ISEN-."}, {"pin_number": "17", "pin_name": "EN", "pin_description": "EN input. Apply a high logic to EN to enable the chip."}, {"pin_number": "18", "pin_name": "HG1", "pin_description": "High-side 1 gate drive output for buck mode's high-side MOSFET (SWA)."}, {"pin_number": "19", "pin_name": "BST1", "pin_description": "Bootstrap. Connect a 0.22µF capacitor between SW1 and BST1 to form a floating supply across the high-side MOSFET driver."}, {"pin_number": "20", "pin_name": "VIN", "pin_description": "Supply voltage for internal logic circuitry (but not for power MOSFETs). <PERSON><PERSON> connect Vin pin to SWA MOSFET drain with wide PCB trace, and it can't be shared by other DC-DC."}]}], "datasheet_cn": "未找到", "datasheet_en": "MP4247 Rev. 1.0", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "3.6V", "max_output_voltage": "24V", "min_output_voltage": "1V", "max_output_current": "5A", "max_switch_frequency": "420kHz", "quiescent_current": "775µA", "high_side_mosfet_resistance": "不适用(外部器件)", "low_side_mosfet_resistance": "14mΩ", "over_current_protection_threshold": "5.4A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM/PWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "No", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.5V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "REFERENCE", "pitch": "1.0", "height": "1.0", "length": "5.0", "width": "3.0", "pin_count": "2"}]}, {"part_number": "MP4247GQV-0002", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "MP4247 36V, 100W, Buck-Boost Converter with Integrated Low-Side MOSFETs and I²C Interface", "features": ["UL and IEC62368-1 Certified, Efile #322138", "100W Buck-Boost Converter with Integrated Low-Side MOSFETs (LS-FETs)", "Integrated Gate Driver for High-Side Power MOSFETs (HS-FETs)", "3.6V to 36V Start-Up Input Voltage (VIN) Range", "Supports 2.8V Falling VIN when the Output Voltage (VOUT) > 3.5V", "1V to 24V VOUT Range with 1% Accuracy", "Up to 5A Output Current (IOUT)", "Up to 98% Peak Efficiency", "Configurable Reference Voltage (VREF) Range (0.1V to 2.147V) with 1mV Resolution via I2C Interface", "Accurate Output CC Current Limit: ±5%", "Meets USB PD 3.0 with PPS Specifications", "Selectable 280kHz, 420kHz, or 600kHz Switching Frequency (fsw)", "Selectable Forced PWM or Auto-PFM/PWM", "Output Biased VCC LDO for Higher Efficiency", "Output Current Monitor (IMON) Function", "Line Drop Compensation via RSENS", "I2C, Alert, and One-Time Programmable (OTP) Memory", "EN Shutdown Passive Discharge", "Output OCP, OVP, and Thermal Shutdown", "Available in a QFN-20 (3mmx5mm) Package"], "description": "The MP4247 is a buck-boost converter with two integrated low-side power switches. The device can deliver up to 100W of peak output power at certain input supplies with excellent efficiency. The MP4247 is well-suited for USB power delivery (USB PD) applications. The device can work with an external USB PD controller via the I2C interface. The I2C interface and one-time programmable (OTP) memory provide flexible and configurable parameters. Fault condition protection includes constant current (CC) current limiting, output over-voltage protection (OVP), and thermal shutdown (TSD). The MP4247 requires a minimal number of readily available, standard external components, and is available in a QFN-20 (3mmx5mm) package.", "applications": ["USB Type-C and USB Power Delivery", "USB Type-C PD Monitors and Docking Stations", "USB Type-C Car Chargers", "Wireless Charging"], "ordering_information": [{"part_number": "MP4247GQV-0002", "order_device": "MP4247GQV-0002-Z", "package_type": "QFN-20", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MP4247GQV-0002", "package_type": "QFN-20", "pins": [{"pin_number": "1", "pin_name": "SCL", "pin_description": "I2C clock signal input."}, {"pin_number": "2", "pin_name": "SDA", "pin_description": "I2C data line."}, {"pin_number": "3", "pin_name": "ALT", "pin_description": "I2C alert pin. The ALT pin is an open-drain, active low output."}, {"pin_number": "4", "pin_name": "FB", "pin_description": "Feedback pin. To set the output voltage (VOUT), connect FB to the tap of an external resistor divider from the output to AGND."}, {"pin_number": "5", "pin_name": "ISEN-", "pin_description": "Negative node of current-sense signal input. Place a current-sense resistor between PGND and the GND capacitor (COUT). Then connect the ISEN- pin to the PGND side of the capacitor."}, {"pin_number": "6", "pin_name": "ISEN+", "pin_description": "Positive node of current-sense signal input. Place a current-sense resistor between PGND and the GND capacitor (COUT). Then connect the ISEN+ pin to the GND side of the capacitor."}, {"pin_number": "7", "pin_name": "VOUT", "pin_description": "Output voltage-sense input. VOUT provides the VCC supply under certain VOUT conditions."}, {"pin_number": "8", "pin_name": "BST2", "pin_description": "Bootstrap. A 0.22µF capacitor should be kelvin connected to SW2 pin from BST2 to form a floating supply across the high-side switch driver."}, {"pin_number": "9", "pin_name": "HG2", "pin_description": "High-side 2 gate drive output for boost mode's high-side MOSFET (SWD)."}, {"pin_number": "10", "pin_name": "VCC", "pin_description": "Internal 5V LDO regulator output. Decouple VCC with a 0.47 µF to 1µF capacitor. If the VCC is used to supply other controller, suggest the total VCC capacitor is <1.5µF."}, {"pin_number": "11", "pin_name": "AGND", "pin_description": "Analog ground. Connect AGND to PGND. Connect AGND to the VCC capacitor's ground node."}, {"pin_number": "12, 15", "pin_name": "PGND", "pin_description": "Power ground. PGND requires extra consideration during PCB layout. Connect PGND to GND with copper traces and vias."}, {"pin_number": "13", "pin_name": "SW2", "pin_description": "Switch 2 node for buck-boost mode. Connect SW1 to SW2 using a power inductor. Use a wide PCB trace to make the inductor connection."}, {"pin_number": "14", "pin_name": "SW1", "pin_description": "Switch 1 node for buck-boost mode. Connect SW1 to SW2 using a power inductor. Use a wide PCB trace to make the inductor connection."}, {"pin_number": "16", "pin_name": "IMON", "pin_description": "Current monitor output. IMON indicates the signal between ISEN+ and ISEN-."}, {"pin_number": "17", "pin_name": "EN", "pin_description": "EN input. Apply a high logic to EN to enable the chip."}, {"pin_number": "18", "pin_name": "HG1", "pin_description": "High-side 1 gate drive output for buck mode's high-side MOSFET (SWA)."}, {"pin_number": "19", "pin_name": "BST1", "pin_description": "Bootstrap. Connect a 0.22µF capacitor between SW1 and BST1 to form a floating supply across the high-side MOSFET driver."}, {"pin_number": "20", "pin_name": "VIN", "pin_description": "Supply voltage for internal logic circuitry (but not for power MOSFETs). <PERSON><PERSON> connect Vin pin to SWA MOSFET drain with wide PCB trace, and it can't be shared by other DC-DC."}]}], "datasheet_cn": "未找到", "datasheet_en": "MP4247 Rev. 1.0", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "3.6V", "max_output_voltage": "24V", "min_output_voltage": "1V", "max_output_current": "5A", "max_switch_frequency": "600kHz", "quiescent_current": "775µA", "high_side_mosfet_resistance": "不适用(外部器件)", "low_side_mosfet_resistance": "14mΩ", "over_current_protection_threshold": "5.4A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM/PWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "No", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.5V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "REFERENCE", "pitch": "1.0", "height": "1.0", "length": "5.0", "width": "3.0", "pin_count": "2"}]}, {"part_number": "MP4247GQV-0011", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "MP4247 36V, 100W, Buck-Boost Converter with Integrated Low-Side MOSFETs and I²C Interface", "features": ["UL and IEC62368-1 Certified, Efile #322138", "100W Buck-Boost Converter with Integrated Low-Side MOSFETs (LS-FETs)", "Integrated Gate Driver for High-Side Power MOSFETs (HS-FETs)", "3.6V to 36V Start-Up Input Voltage (VIN) Range", "Supports 2.8V Falling VIN when the Output Voltage (VOUT) > 3.5V", "1V to 24V VOUT Range with 1% Accuracy", "Up to 5A Output Current (IOUT)", "Up to 98% Peak Efficiency", "Configurable Reference Voltage (VREF) Range (0.1V to 2.147V) with 1mV Resolution via I2C Interface", "Accurate Output CC Current Limit: ±5%", "Meets USB PD 3.0 with PPS Specifications", "Selectable 280kHz, 420kHz, or 600kHz Switching Frequency (fsw)", "Selectable Forced PWM or Auto-PFM/PWM", "Output Biased VCC LDO for Higher Efficiency", "Output Current Monitor (IMON) Function", "Line Drop Compensation via RSENS", "I2C, Alert, and One-Time Programmable (OTP) Memory", "EN Shutdown Passive Discharge", "Output OCP, OVP, and Thermal Shutdown", "Available in a QFN-20 (3mmx5mm) Package"], "description": "The MP4247 is a buck-boost converter with two integrated low-side power switches. The device can deliver up to 100W of peak output power at certain input supplies with excellent efficiency. The MP4247 is well-suited for USB power delivery (USB PD) applications. The device can work with an external USB PD controller via the I2C interface. The I2C interface and one-time programmable (OTP) memory provide flexible and configurable parameters. Fault condition protection includes constant current (CC) current limiting, output over-voltage protection (OVP), and thermal shutdown (TSD). The MP4247 requires a minimal number of readily available, standard external components, and is available in a QFN-20 (3mmx5mm) package.", "applications": ["USB Type-C and USB Power Delivery", "USB Type-C PD Monitors and Docking Stations", "USB Type-C Car Chargers", "Wireless Charging"], "ordering_information": [{"part_number": "MP4247GQV-0011", "order_device": "MP4247GQV-0011-Z", "package_type": "QFN-20", "package_drawing_code": "MO-220", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MP4247GQV-0011", "package_type": "QFN-20", "pins": [{"pin_number": "1", "pin_name": "SCL", "pin_description": "I2C clock signal input."}, {"pin_number": "2", "pin_name": "SDA", "pin_description": "I2C data line."}, {"pin_number": "3", "pin_name": "ALT", "pin_description": "I2C alert pin. The ALT pin is an open-drain, active low output."}, {"pin_number": "4", "pin_name": "FB", "pin_description": "Feedback pin. To set the output voltage (VOUT), connect FB to the tap of an external resistor divider from the output to AGND."}, {"pin_number": "5", "pin_name": "ISEN-", "pin_description": "Negative node of current-sense signal input. Place a current-sense resistor between PGND and the GND capacitor (COUT). Then connect the ISEN- pin to the PGND side of the capacitor."}, {"pin_number": "6", "pin_name": "ISEN+", "pin_description": "Positive node of current-sense signal input. Place a current-sense resistor between PGND and the GND capacitor (COUT). Then connect the ISEN+ pin to the GND side of the capacitor."}, {"pin_number": "7", "pin_name": "VOUT", "pin_description": "Output voltage-sense input. VOUT provides the VCC supply under certain VOUT conditions."}, {"pin_number": "8", "pin_name": "BST2", "pin_description": "Bootstrap. A 0.22µF capacitor should be kelvin connected to SW2 pin from BST2 to form a floating supply across the high-side switch driver."}, {"pin_number": "9", "pin_name": "HG2", "pin_description": "High-side 2 gate drive output for boost mode's high-side MOSFET (SWD)."}, {"pin_number": "10", "pin_name": "VCC", "pin_description": "Internal 5V LDO regulator output. Decouple VCC with a 0.47 µF to 1µF capacitor. If the VCC is used to supply other controller, suggest the total VCC capacitor is <1.5µF."}, {"pin_number": "11", "pin_name": "AGND", "pin_description": "Analog ground. Connect AGND to PGND. Connect AGND to the VCC capacitor's ground node."}, {"pin_number": "12, 15", "pin_name": "PGND", "pin_description": "Power ground. PGND requires extra consideration during PCB layout. Connect PGND to GND with copper traces and vias."}, {"pin_number": "13", "pin_name": "SW2", "pin_description": "Switch 2 node for buck-boost mode. Connect SW1 to SW2 using a power inductor. Use a wide PCB trace to make the inductor connection."}, {"pin_number": "14", "pin_name": "SW1", "pin_description": "Switch 1 node for buck-boost mode. Connect SW1 to SW2 using a power inductor. Use a wide PCB trace to make the inductor connection."}, {"pin_number": "16", "pin_name": "IMON", "pin_description": "Current monitor output. IMON indicates the signal between ISEN+ and ISEN-."}, {"pin_number": "17", "pin_name": "EN", "pin_description": "EN input. Apply a high logic to EN to enable the chip."}, {"pin_number": "18", "pin_name": "HG1", "pin_description": "High-side 1 gate drive output for buck mode's high-side MOSFET (SWA)."}, {"pin_number": "19", "pin_name": "BST1", "pin_description": "Bootstrap. Connect a 0.22µF capacitor between SW1 and BST1 to form a floating supply across the high-side MOSFET driver."}, {"pin_number": "20", "pin_name": "VIN", "pin_description": "Supply voltage for internal logic circuitry (but not for power MOSFETs). <PERSON><PERSON> connect Vin pin to SWA MOSFET drain with wide PCB trace, and it can't be shared by other DC-DC."}]}], "datasheet_cn": "未找到", "datasheet_en": "MP4247 Rev. 1.0", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "3.6V", "max_output_voltage": "24V", "min_output_voltage": "1V", "max_output_current": "5A", "max_switch_frequency": "600kHz", "quiescent_current": "775µA", "high_side_mosfet_resistance": "不适用(外部器件)", "low_side_mosfet_resistance": "14mΩ", "over_current_protection_threshold": "5.4A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM/PWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "No", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.5V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "REFERENCE", "pitch": "1.0", "height": "1.0", "length": "5.0", "width": "3.0", "pin_count": "2"}]}]