{"part_number": "LTC3777", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "150V VIN and VOUT Synchronous 4-Switch Buck-Boost Controller + Switching Bias Supply", "features": ["4-Switch Current Mode Single Inductor Architecture Allows VIN Above, Below or Equal to VOUT", "Wide VIN Range: 4.5V to 150V", "Wide Output Voltage Range: 1.2V ≤ VOUT ≤ 150V", "Synchronous Rectification: Up to 99% Efficiency", "±1% 1.2V Voltage Reference", "Input or Output Average Current Limit", "Integrated 12μA IQ, 150V, 85mA, Switching Bias for Optimal Thermal Performance", "Programmable 6V to 10V DRVCC Optimizes Efficiency", "No Top FET Refresh Noise in Boost or Buck Mode", "VOUT Disconnected from VIN During Shutdown", "Phase-Lockable Fixed Frequency (50kHz to 600kHz)", "No Reverse Current During Start-Up", "150V Rated RUN Pin with Accurate Turn-On Threshold", "Thermally Enhanced 48-Lead e-LQFP Package"], "description": "The LTC®3777 is a high performance buck-boost switching regulator controller that operates from input voltages above, below or equal to the output voltage. The constant frequency current mode architecture allows a phase-lockable frequency of up to 600kHz, while an input/output constant current loop provides support for battery charging. The 150V integrated switching bias supply is a high efficiency step-down regulator that draws only 12μA typical DC supply current with a regulated output voltage at no load. With a wide 4.5V to 150V input and output range and seamless transfers between operating regions, the LTC3777 is ideal for industrial, telecom and battery-powered systems. The LTC3777 features a power good output indicator and a MODE pin to select between pulse-skipping mode or forced continuous mode of operation. The PLLIN pin allows the IC to be synchronized to an external clock. The SS pin ramps the output voltage during start-up. Current foldback limits MOSFET heat dissipation during short-circuit conditions.", "applications": ["Industrial", "Transportation", "Medical", "Military", "Avionics"], "ordering_information": [{"part_number": "LTC3777", "order_device": "LTC3777ELXE#PBF", "package_type": "e-LQFP", "package_drawing_code": "05-08-1832 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3777", "order_device": "LTC3777ELXE#TRMPBF", "package_type": "e-LQFP", "package_drawing_code": "05-08-1832 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3777", "order_device": "LTC3777ILXE#PBF", "package_type": "e-LQFP", "package_drawing_code": "05-08-1832 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3777", "order_device": "LTC3777ILXE#TRMPBF", "package_type": "e-LQFP", "package_drawing_code": "05-08-1832 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LTC3777", "package_type": "e-LQFP", "pins": [{"pin_number": "1", "pin_name": "BRUN", "pin_description": "Bias Supply Run Control Input. A voltage on this pin above 1.21V enables normal operation. Forcing this pin below 0.7V shuts down the switching bias supply, reducing quiescent current to approximately 1.4μA. Optionally, connect to the input supply through a resistor divider to set the undervoltage lockout."}, {"pin_number": "3", "pin_name": "BSW", "pin_description": "Bias Supply Switch Node Connection to Inductor. This pin connects to the drains of the internal power MOSFET switches."}, {"pin_number": "5", "pin_name": "BVIN", "pin_description": "Bias Supply Main Supply Pin. A ceramic bypass capacitor should be tied between this pin and GND."}, {"pin_number": "7", "pin_name": "BOV", "pin_description": "Bias Supply Overvoltage Lockout Input. Connect to the input supply through a resistor divider to set the overvoltage lockout level. A voltage on this pin above 1.21V disables the internal MOSFET switches. Normal operation resumes when the voltage on this pin decreases below 1.10V. Tie this pin to ground if the overvoltage is not used."}, {"pin_number": "8", "pin_name": "BFBO", "pin_description": "Bias Supply Feedback Comparator Output. The typical pull-up current is 20μA. The typical pull-down impedance is 70Ω. This output signal can be used to synchronize other ICs."}, {"pin_number": "10", "pin_name": "BGND", "pin_description": "Ground."}, {"pin_number": "11", "pin_name": "BISET", "pin_description": "Bias Supply Peak Current Set Input. Leave floating for the maximum peak current (230mA typical) or short to ground for minimum peak current (25mA typical). The maximum output current is one-half the peak current."}, {"pin_number": "12", "pin_name": "BVFB", "pin_description": "Bias Supply Output Voltage Feedback. Connect to an external resistive divider to divide the output voltage down for comparison to the 0.8V reference."}, {"pin_number": "13", "pin_name": "SENSEP", "pin_description": "The positive input to the differential current comparator. This pin is normally connected to a sense resistor at the source of the power MOSFET."}, {"pin_number": "14", "pin_name": "SENSEN", "pin_description": "The negative input to the differential current sense comparator. This pin is normally connected to the ground side of the sense resistor."}, {"pin_number": "15", "pin_name": "ITH", "pin_description": "Error Amplifier Output. The current comparator trip threshold increases with the ITH control voltage. The ITH pin is also used for compensating the control loop of the converter."}, {"pin_number": "16", "pin_name": "SGND", "pin_description": "Signal ground. All feedback and soft-start connections should return to SGND."}, {"pin_number": "17", "pin_name": "MODE", "pin_description": "Mode Selection pin. Tying this pin to SGND or below 0.8V enables forced continuous mode. Tying it to V5 enables pulse-skipping mode."}, {"pin_number": "18", "pin_name": "PLLIN", "pin_description": "External Synchronization Input to Phase Detector. For external sync, apply a clock signal to this pin and the internal PLL will synchronize the internal oscillator to the clock."}, {"pin_number": "19", "pin_name": "FREQ", "pin_description": "The frequency control pin for the internal VCO. Frequencies between 40kHz and 500kHz can be programmed by using a resistor between FREQ and SGND."}, {"pin_number": "20", "pin_name": "PGOOD", "pin_description": "Fault indicator Output. Open-drain output that pulls to ground when the voltage on the VFB pin is not within ±10% of its set point."}, {"pin_number": "21", "pin_name": "BG2", "pin_description": "Bottom Gate Driver Outputs. This pin drives the gate(s) of the bottom N-Channel MOSFET between PGND to DRVCC."}, {"pin_number": "23", "pin_name": "SW2", "pin_description": "Switch Node Connections to the Inductors."}, {"pin_number": "24", "pin_name": "TG2", "pin_description": "High Current Gate Drives for Top N-Channel MOSFETs. These are the outputs of floating high side drivers with a voltage swing equal to DRVCC superimposed on the switch node voltage SW."}, {"pin_number": "25", "pin_name": "BOOST2", "pin_description": "Boosted Floating Driver Supplies. The (+) terminal of the bootstrap capacitor connects to this pin. This pin swings from a diode drop below DRVCC up to VIN + DRVCC."}, {"pin_number": "27", "pin_name": "RUN", "pin_description": "Enable Control Input. A voltage above 1.2V turns on the IC. There is a 2.5μA pull-up current on this pin. Forcing this pin below 1.1V shuts down the controller. This pin can be tied to VIN for always-on operation."}, {"pin_number": "29", "pin_name": "IAVGSNSP", "pin_description": "The positive input to the Input / Output Average Current Sense Amplifier."}, {"pin_number": "30", "pin_name": "IAVGSNSN", "pin_description": "The negative input to the Input / Output Average Current Sense Amplifier. Short IAVGSNSP and IAVGSNSN pins together, and tie them to V5, if this average current loop function is not used."}, {"pin_number": "32", "pin_name": "VOUTSNS", "pin_description": "VOUT Sense Input to the Buck-Boost Transition comparator. Connect this pin to the drain of the top N-channel MOSFET on the output side through a 1kΩ resistor."}, {"pin_number": "34", "pin_name": "VINSNS", "pin_description": "VIN Sense Input to the Buck-Boost Transition comparator. Connect this pin to the drain of the top N-channel MOSFET on the input side."}, {"pin_number": "36", "pin_name": "VIN", "pin_description": "Main Supply Pin. A bypass capacitor should be tied between this pin and the PGND pin."}, {"pin_number": "37", "pin_name": "BOOST1", "pin_description": "Boosted Floating Driver Supplies. The (+) terminal of the bootstrap capacitor connects to this pin. This pin swings from a diode drop below DRVCC up to VIN + DRVCC."}, {"pin_number": "38", "pin_name": "TG1", "pin_description": "High Current Gate Drives for Top N-Channel MOSFETs. These are the outputs of floating high side drivers with a voltage swing equal to DRVCC superimposed on the switch node voltage SW."}, {"pin_number": "39", "pin_name": "SW1", "pin_description": "Switch Node Connections to the Inductors."}, {"pin_number": "41", "pin_name": "BG1", "pin_description": "Bottom Gate Driver Outputs. This pin drives the gate(s) of the bottom N-Channel MOSFET between PGND to DRVCC."}, {"pin_number": "42", "pin_name": "VINOV", "pin_description": "Connect to the input supply through a resistor divider to set the over-voltage lockout level. A voltage on this pin above 1.28V disables all switching. Tie this pin to ground if the overvoltage function is not used."}, {"pin_number": "43", "pin_name": "DRVSET", "pin_description": "Sets the regulated output voltage of the DRVCC linear regulator from 6V to 10V in 1V increments."}, {"pin_number": "44", "pin_name": "EXTVCC", "pin_description": "External Power Input to an Internal LDO Connected to DRVCC. When the voltage on this pin is greater than the DRVCC LDO setting minus 500mV, this LDO bypasses the internal LDO powered from VIN. Tie this pin to ground if the EXTVCC is not used."}, {"pin_number": "45", "pin_name": "DRVCC", "pin_description": "Output of the Internal or External Low Dropout Regulator. The gate drivers are powered from this voltage source. The DRVCC voltage is set by the DRVSET pin. A low ESR 4.7μF ceramic bypass capacitor should be connected between DRVCC and PGND."}, {"pin_number": "46", "pin_name": "V5", "pin_description": "Output of the Internal 5.5V Low Dropout Regulator. The control circuits are powered from this voltage. Bypass this pin to SGND with a minimum of 2.2μF low ESR capacitor."}, {"pin_number": "47", "pin_name": "SS", "pin_description": "Soft-Start Input. The voltage ramp rate at this pin sets the voltage ramp rate of the regulated voltage. This pin has a 5μA pull-up current. A capacitor to ground at this pin sets the ramp time to final regulated output voltage."}, {"pin_number": "48", "pin_name": "VFB", "pin_description": "Error Amplifier Input. The FB pin should be connected through a resistive divider network to VOUT to set the output voltage."}, {"pin_number": "49", "pin_name": "PGND", "pin_description": "Driver Power Ground. Connects to the (–) terminal of CIN, COUT and RSENSE. The exposed pad must be soldered to PCB ground for electrical contact and rated thermal performance."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3777 Datasheet Rev A", "family_comparison": "Related parts include LTC3779, LT8705A, LTC7813, LTC3899, LTM8056, LTC3895/LTC7801, LTC3871, LTC3638, LTC7103.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "150V", "min_input_voltage": "4.5V", "max_output_voltage": "150V", "min_output_voltage": "1.2V", "max_output_current": "10A", "max_switch_frequency": "0.6MHz", "quiescent_current": "40µA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "90mV (Buck) / 140mV (Boost)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Pulse Skipping", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Fold Back", "output_short_circuit_protection": "Fold Back", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "1.2V", "loop_control_mode": "峰值电流模式"}, "package": [{"height": "1.6", "length": "5.5", "width": "0.7", "type": "e-LQFP", "pin_count": "48", "pitch": "1.6"}]}