{"part_number": "LM5175-Q1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压-升压(<PERSON><PERSON><PERSON><PERSON>)控制器", "part_number_title": "LM5175-Q1 42V宽VIN 同步4开关降压-升压控制器", "features": ["适用于汽车电子应用", "具有符合AEC-Q100 标准的下列结果: - 器件温度 1 级: -40℃ 至 +125℃ 的环境运行温度范围; - 器件人体模型 (HBM) 静电放电 (ESD) 分类等级 2; - 器件组件充电模式 (CDM) ESD 分类等级 C4B", "单电感降压-升压控制器，用于升压/降压 DC/DC 转换", "宽VIN范围: 3.5V 至 42V，最大值为 60V", "灵活的VOUT 范围: 0.8V 至 55V", "VOUT 短路保护", "高效降压-升压转换", "可调开关频率", "可选频率同步和抖动", "集成 2A 金属氧化物半导体场效应晶体管 (MOSFET) 栅极驱动器", "逐周期电流限制和可选断续模式", "可选输入或输出平均电流限制", "可编程的输入欠压闭锁 (UVLO) 和软启动", "电源正常和输出过压保护", "可利用脉冲跳跃来选择连续导通模式 (CCM) 或断续导通模式 (DCM)", "薄型小外形尺寸 (HTSSOP)-28 封装"], "description": "LM5175-Q1 是一款同步四开关降压-升压 DC/DC 控制器，能够将输出电压稳定在输入电压、高于输入电压或者低于输入电压的某一电压值上。LM5175-Q1 可在 3.5V 至 42V 的宽输入电压范围内运行（最大值为 60V），支持各类应用。LM5175-Q1 在降压和升压工作模式下均采用电流模式控制，以提供出色的负载和线路调节性能。开关频率可通过外部电阻进行编程，并且可与外部时钟信号同步。该器件还具有可编程软启动功能，并且提供诸如逐周期电流限制、输入欠压锁定 (UVLO)、输出过压保护 (OVP) 和热关断等各类保护特性。此外，LM5175-Q1 特有可选择的连续导通模式 (CCM) 或断续导通模式 (DCM)、可选平均输入或输出电流限制、可降低峰值电磁干扰 (EMI) 的可选扩展频谱以及应对持续过载情况的可选断续模式保护。", "applications": ["汽车起停系统", "备用电池和超级电容充电", "工业 PC 用电源", "USB 供电", "LED 照明"], "ordering_information": [{"part_number": "LM5175-Q1", "order_device": "LM5175QPWPRQ1", "package_type": "HTSSOP", "package_drawing_code": "PWP0028V", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5175-Q1", "order_device": "LM5175QPWPTQ1", "package_type": "HTSSOP", "package_drawing_code": "PWP0028V", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LM5175-Q1", "package_type": "HTSSOP-28", "pins": [{"pin_number": "1", "pin_name": "EN/UVLO", "pin_description": "使能引脚。对于 EN/UVLO < 0.4V，LM5175-Q1 处于低电流关断模式。对于 0.7V < EN/UVLO < 1.23V，控制器在待机模式下运行，其中 VCC 稳压器已启用，但 PWM 控制器未切换。对于 EN/UVLO > 1.23V，PWM 功能已启用，前提是 VCC 超过 VCC UV 阈值。"}, {"pin_number": "2", "pin_name": "VIN", "pin_description": "IC 的输入电源引脚。将 VIN 连接到 3.5V 和 42V 之间的电源电压。"}, {"pin_number": "3", "pin_name": "VISNS", "pin_description": "VIN 检测输入。连接到输入电容器。"}, {"pin_number": "4", "pin_name": "MODE", "pin_description": "模式选择引脚。Mode = GND, DCM, Hiccup Disabled; Mode = 1.00 V, DCM, Hiccup Enabled; Mode = 1.85 V, CCM, Hiccup Enabled; Mode = VCC, CCM, Hiccup Disabled。"}, {"pin_number": "5", "pin_name": "DITH", "pin_description": "连接在 DITH 引脚和 AGND 之间的电容器由 10uA 电流源充电和放电。当 DITH 引脚上的电压上下变化时，振荡器频率在由 RT 电阻器设置的标称频率的 -5% 和 +5% 之间进行调制。将 DITH 引脚接地将禁用抖动功能。在外部同步模式下，DITH 引脚电压被忽略。"}, {"pin_number": "6", "pin_name": "RT/SYNC", "pin_description": "开关频率编程引脚。一个外部电阻器连接到 RT/SYNC 引脚和 AGND 以设置开关频率。此引脚也可用于将 PWM 控制器同步到外部时钟。"}, {"pin_number": "7", "pin_name": "SLOPE", "pin_description": "连接在 SLOPE 引脚和 AGND 之间的电容器为降压和升压模式下的稳定电流模式操作提供斜坡补偿。"}, {"pin_number": "8", "pin_name": "SS", "pin_description": "软启动编程引脚。SS 引脚和 AGND 引脚之间的电容器编程软启动时间。"}, {"pin_number": "9", "pin_name": "COMP", "pin_description": "误差放大器的输出。连接在 COMP 和 AGND 之间的外部 RC 网络补偿稳压器反馈环路。"}, {"pin_number": "10", "pin_name": "AGND", "pin_description": "IC 的模拟地。"}, {"pin_number": "11", "pin_name": "FB", "pin_description": "用于输出电压调节的反馈引脚。将转换器输出端的电阻分压器网络连接到 FB 引脚。"}, {"pin_number": "12", "pin_name": "VOSNS", "pin_description": "VOUT 检测输入。连接到输出电容器。"}, {"pin_number": "13", "pin_name": "ISNS(-)", "pin_description": "输入或输出电流检测放大器输入。一个可选的电流检测电阻器连接在 ISNS(+) 和 ISNS(-) 之间，可以位于转换器的输入侧或输出侧。如果 ISNS(+) 和 ISNS(-) 引脚之间的检测电压达到 50mV，一个慢速恒流 (CC) 控制环路将变为活动状态，并开始对软启动电容器放电，以将 ISNS(+) 和 ISNS(-) 之间的压降调节到 50mV。将 ISNS(+) 和 ISNS(-) 短接在一起以禁用此功能。"}, {"pin_number": "14", "pin_name": "ISNS(+)", "pin_description": "输入或输出电流检测放大器输入。一个可选的电流检测电阻器连接在 ISNS(+) 和 ISNS(-) 之间，可以位于转换器的输入侧或输出侧。如果 ISNS(+) 和 ISNS(-) 引脚之间的检测电压达到 50mV，一个慢速恒流 (CC) 控制环路将变为活动状态，并开始对软启动电容器放电，以将 ISNS(+) 和 ISNS(-) 之间的压降调节到 50mV。将 ISNS(+) 和 ISNS(-) 短接在一起以禁用此功能。"}, {"pin_number": "15", "pin_name": "CSG", "pin_description": "PWM 电流检测放大器的负或接地输入。直接连接到电流检测电阻器的低侧（接地）。"}, {"pin_number": "16", "pin_name": "CS", "pin_description": "PWM 电流检测放大器的正输入。"}, {"pin_number": "17", "pin_name": "PGOOD", "pin_description": "电源良好开漏输出。当 FB 在 0.8V ±10% 调节窗口之外时，PGOOD 被拉低。"}, {"pin_number": "18", "pin_name": "SW2", "pin_description": "升压侧开关节点。"}, {"pin_number": "19", "pin_name": "HDRV2", "pin_description": "高侧栅极驱动器的输出。直接连接到高侧 MOSFET 的栅极。"}, {"pin_number": "20", "pin_name": "BOOT2", "pin_description": "在 BOOT2 引脚和 SW2 引脚之间需要一个外部电容器，为高侧 MOSFET 栅极驱动器提供偏置。"}, {"pin_number": "21", "pin_name": "LDRV2", "pin_description": "低侧栅极驱动器的输出。直接连接到低侧 MOSFET 的栅极。"}, {"pin_number": "22", "pin_name": "PGND", "pin_description": "IC 的电源地。低侧栅极驱动器的高电流接地连接。"}, {"pin_number": "23", "pin_name": "VCC", "pin_description": "VCC 偏置稳压器的输出。将电容器连接到地。"}, {"pin_number": "24", "pin_name": "BIAS", "pin_description": "VCC 偏置稳压器的可选输入。从外部电源而不是 VIN 为 VCC 供电可以减少高 VIN 时的功率损耗。对于 VBIAS > 8V，VCC 稳压器从 BIAS 引脚获取功率。BIAS 引脚电压不得超过 40V。"}, {"pin_number": "25", "pin_name": "LDRV1", "pin_description": "低侧栅极驱动器的输出。直接连接到低侧 MOSFET 的栅极。"}, {"pin_number": "26", "pin_name": "BOOT1", "pin_description": "在 BOOT1 引脚和 SW1 引脚之间需要一个外部电容器，为高侧 MOSFET 栅极驱动器提供偏置。"}, {"pin_number": "27", "pin_name": "HDRV1", "pin_description": "高侧栅极驱动器的输出。直接连接到高侧 MOSFET 的栅极。"}, {"pin_number": "28", "pin_name": "SW1", "pin_description": "降压侧开关节点。"}, {"pin_number": "PowerPAD", "pin_name": "PowerPAD", "pin_description": "PowerPAD 应焊接到模拟地。如果可能，使用散热过孔连接到 PCB 接地层以改善功耗。"}]}], "datasheet_cn": "ZHCSEX5–APRIL 2016", "datasheet_en": "SNVSAD9", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "42V", "min_input_voltage": "3.5V", "max_output_voltage": "55V", "min_output_voltage": "1V", "max_output_current": "依赖外部元件", "max_switch_frequency": "0.6MHz", "quiescent_current": "1650µA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "Buck: 76mV (Valley), Boost: 160mV (Peak)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "CCM, DCM", "power_good_indicator": "Yes", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式, 谷值电流模式"}, "package": [{"type": "OPTION", "length": "9.7", "width": "4.4", "pin_count": "2", "pitch": "0.65", "height": "1.2"}]}