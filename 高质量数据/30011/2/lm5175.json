{"part_number": "LM5175", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压-升压控制器", "part_number_title": "LM5175 42V 宽VIN 同步4开关降压- 升压控制器", "features": ["单电感降压-升压控制器, 用于升压/降压 DC/DC 转换", "宽 VIN 范围: 3.5V 至 42V, 最大值为 60V", "灵活的 VOUT 范围: 0.8V 至 55V", "VOUT 短路保护", "高效降压-升压转换", "可调开关频率", "可选频率同步和抖动", "集成 2A 金属氧化物半导体场效应晶体管 (MOSFET) 栅极驱动器", "逐周期电流限制和可选断续模式", "可选输入或输出平均电流限制", "可编程的输入欠压闭锁 (UVLO) 和软启动", "电源正常和输出过压保护", "可利用脉冲跳跃来选择连续导通模式 (CCM) 或断续导通模式 (DCM)", "采用 HTSSOP-28 和 QFN-28 封装"], "description": "LM5175 是一款同步四开关降压-升压 DC/DC 控制器, 能够将输出电压稳定在输入电压、高于输入电压或者低于输入电压的某一电压值上。LM5175 具有 3.5V 至 42V 的宽输入电压范围（最大值为 60V），支持各类应用。LM5175 在降压和升压工作模式下均采用电流模式控制, 以提供出色的负载和线路调节性能。开关频率可通过外部电阻进行编程, 并且可与外部时钟信号同步。该器件还 具有可编程的软启动功能, 并且提供 诸如逐周期电流限制、输入欠压锁定 (UVLO)、输出过压保护 (OVP) 和热关断等各类保护特性。此外, LM5175 特有可选择的连续导通模式 (CCM) 或断续导通模式 (DCM)、可选平均输入或输出电流限制、可降低峰值电磁干扰 (EMI) 的可选扩展频谱、以及应对持续过载情况的可选断续模式保护。", "applications": ["汽车起停系统", "备用电池和超级电容充电", "工业 PC 用电源", "USB 供电", "LED 照明"], "ordering_information": [{"part_number": "LM5175", "order_device": "LM5175PWPR", "package_type": "HTSSOP", "package_drawing_code": "PWP0028V", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5175", "order_device": "LM5175PWPT", "package_type": "HTSSOP", "package_drawing_code": "PWP0028V", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5175", "order_device": "LM5175RHFR", "package_type": "VQFN", "package_drawing_code": "RHF0028A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5175", "order_device": "LM5175RHFT", "package_type": "VQFN", "package_drawing_code": "RHF0028A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LM5175", "package_type": "HTSSOP-28", "pins": [{"pin_number": "1", "pin_name": "EN/UVLO", "pin_description": "使能引脚。当EN/UVLO < 0.4V时，LM5175处于低电流关断模式。当0.7V < EN/UVLO < 1.23V时，控制器处于待机模式，VCC稳压器使能但PWM控制器不工作。当EN/UVLO > 1.23V时，PWM功能使能。"}, {"pin_number": "2", "pin_name": "VIN", "pin_description": "IC的输入电源引脚。连接VIN到3.5V至42V的电源电压。"}, {"pin_number": "3", "pin_name": "VISNS", "pin_description": "VIN检测输入。连接到输入电容。"}, {"pin_number": "4", "pin_name": "MODE", "pin_description": "模式选择引脚，用于选择DCM/CCM和Hiccup模式。"}, {"pin_number": "5", "pin_name": "DITH", "pin_description": "频率抖动编程引脚。连接电容到AGND以启用抖动功能。"}, {"pin_number": "6", "pin_name": "RT/SYNC", "pin_description": "开关频率编程引脚。连接外部电阻到AGND设置开关频率。也可用于同步到外部时钟。"}, {"pin_number": "7", "pin_name": "SLOPE", "pin_description": "连接电容到AGND提供斜坡补偿。"}, {"pin_number": "8", "pin_name": "SS", "pin_description": "软启动编程引脚。连接电容到AGND编程软启动时间。"}, {"pin_number": "9", "pin_name": "COMP", "pin_description": "误差放大器输出。连接外部RC网络进行环路补偿。"}, {"pin_number": "10", "pin_name": "AGND", "pin_description": "IC的模拟地。"}, {"pin_number": "11", "pin_name": "FB", "pin_description": "输出电压调节反馈引脚。连接电阻分压网络从转换器输出到FB引脚。"}, {"pin_number": "12", "pin_name": "VOSNS", "pin_description": "VOUT检测输入。连接到输出电容。"}, {"pin_number": "13", "pin_name": "ISNS(-)", "pin_description": "输入或输出电流检测放大器负输入。"}, {"pin_number": "14", "pin_name": "ISNS(+)", "pin_description": "输入或输出电流检测放大器正输入。"}, {"pin_number": "15", "pin_name": "CSG", "pin_description": "PWM电流检测放大器的负或地输入。直接连接到电流检测电阻的低侧（地）。"}, {"pin_number": "16", "pin_name": "CS", "pin_description": "PWM电流检测放大器的正输入。"}, {"pin_number": "17", "pin_name": "PGOOD", "pin_description": "电源正常开漏输出。当FB在0.8V ±10%调节窗口之外时，PGOOD被拉低。"}, {"pin_number": "18", "pin_name": "SW2", "pin_description": "升压侧开关节点。"}, {"pin_number": "19", "pin_name": "HDRV2", "pin_description": "高侧栅极驱动器输出。直接连接到高侧MOSFET的栅极。"}, {"pin_number": "20", "pin_name": "BOOT2", "pin_description": "升压侧自举电容引脚。"}, {"pin_number": "21", "pin_name": "LDRV2", "pin_description": "低侧栅极驱动器输出。直接连接到低侧MOSFET的栅极。"}, {"pin_number": "22", "pin_name": "PGND", "pin_description": "IC的功率地。低侧栅极驱动器的大电流地连接。"}, {"pin_number": "23", "pin_name": "VCC", "pin_description": "VCC偏置稳压器输出。连接电容到地。"}, {"pin_number": "24", "pin_name": "BIAS", "pin_description": "VCC偏置稳压器的可选输入。从外部电源为VCC供电可以降低高VIN时的功耗。"}, {"pin_number": "25", "pin_name": "LDRV1", "pin_description": "低侧栅极驱动器输出。直接连接到低侧MOSFET的栅极。"}, {"pin_number": "26", "pin_name": "BOOT1", "pin_description": "降压侧自举电容引脚。"}, {"pin_number": "27", "pin_name": "HDRV1", "pin_description": "高侧栅极驱动器输出。直接连接到高侧MOSFET的栅极。"}, {"pin_number": "28", "pin_name": "SW1", "pin_description": "降压侧开关节点。"}, {"pin_number": "PowerPAD", "pin_name": "PowerPAD", "pin_description": "PowerPAD应焊接到模拟地。如果可能，使用热过孔连接到PCB接地层以改善散热。"}]}, {"product_part_number": "LM5175", "package_type": "QFN-28", "pins": [{"pin_number": "1", "pin_name": "MODE", "pin_description": "模式选择引脚，用于选择DCM/CCM和Hiccup模式。"}, {"pin_number": "2", "pin_name": "DITH", "pin_description": "频率抖动编程引脚。连接电容到AGND以启用抖动功能。"}, {"pin_number": "3", "pin_name": "RT/SYNC", "pin_description": "开关频率编程引脚。连接外部电阻到AGND设置开关频率。也可用于同步到外部时钟。"}, {"pin_number": "4", "pin_name": "SLOPE", "pin_description": "连接电容到AGND提供斜坡补偿。"}, {"pin_number": "5", "pin_name": "SS", "pin_description": "软启动编程引脚。连接电容到AGND编程软启动时间。"}, {"pin_number": "6", "pin_name": "COMP", "pin_description": "误差放大器输出。连接外部RC网络进行环路补偿。"}, {"pin_number": "7", "pin_name": "AGND", "pin_description": "IC的模拟地。"}, {"pin_number": "8", "pin_name": "FB", "pin_description": "输出电压调节反馈引脚。连接电阻分压网络从转换器输出到FB引脚。"}, {"pin_number": "9", "pin_name": "VOSNS", "pin_description": "VOUT检测输入。连接到输出电容。"}, {"pin_number": "10", "pin_name": "ISNS(-)", "pin_description": "输入或输出电流检测放大器负输入。"}, {"pin_number": "11", "pin_name": "ISNS(+)", "pin_description": "输入或输出电流检测放大器正输入。"}, {"pin_number": "12", "pin_name": "CSG", "pin_description": "PWM电流检测放大器的负或地输入。直接连接到电流检测电阻的低侧（地）。"}, {"pin_number": "13", "pin_name": "CS", "pin_description": "PWM电流检测放大器的正输入。"}, {"pin_number": "14", "pin_name": "PGOOD", "pin_description": "电源正常开漏输出。当FB在0.8V ±10%调节窗口之外时，PGOOD被拉低。"}, {"pin_number": "15", "pin_name": "SW2", "pin_description": "升压侧开关节点。"}, {"pin_number": "16", "pin_name": "HDRV2", "pin_description": "高侧栅极驱动器输出。直接连接到高侧MOSFET的栅极。"}, {"pin_number": "17", "pin_name": "BOOT2", "pin_description": "升压侧自举电容引脚。"}, {"pin_number": "18", "pin_name": "LDRV2", "pin_description": "低侧栅极驱动器输出。直接连接到低侧MOSFET的栅极。"}, {"pin_number": "19", "pin_name": "PGND", "pin_description": "IC的功率地。低侧栅极驱动器的大电流地连接。"}, {"pin_number": "20", "pin_name": "VCC", "pin_description": "VCC偏置稳压器输出。连接电容到地。"}, {"pin_number": "21", "pin_name": "BIAS", "pin_description": "VCC偏置稳压器的可选输入。从外部电源为VCC供电可以降低高VIN时的功耗。"}, {"pin_number": "22", "pin_name": "LDRV1", "pin_description": "低侧栅极驱动器输出。直接连接到低侧MOSFET的栅极。"}, {"pin_number": "23", "pin_name": "BOOT1", "pin_description": "降压侧自举电容引脚。"}, {"pin_number": "24", "pin_name": "HDRV1", "pin_description": "高侧栅极驱动器输出。直接连接到高侧MOSFET的栅极。"}, {"pin_number": "25", "pin_name": "SW1", "pin_description": "降压侧开关节点。"}, {"pin_number": "26", "pin_name": "EN/UVLO", "pin_description": "使能引脚。当EN/UVLO < 0.4V时，LM5175处于低电流关断模式。当0.7V < EN/UVLO < 1.23V时，控制器处于待机模式，VCC稳压器使能但PWM控制器不工作。当EN/UVLO > 1.23V时，PWM功能使能。"}, {"pin_number": "27", "pin_name": "VIN", "pin_description": "IC的输入电源引脚。连接VIN到3.5V至42V的电源电压。"}, {"pin_number": "28", "pin_name": "VISNS", "pin_description": "VIN检测输入。连接到输入电容。"}, {"pin_number": "PowerPAD", "pin_name": "PowerPAD", "pin_description": "PowerPAD应焊接到模拟地。如果可能，使用热过孔连接到PCB接地层以改善散热。"}]}], "datasheet_cn": "ZHCSDH1A", "datasheet_en": "SNVSA37", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "42V", "min_input_voltage": "3.5V", "max_output_voltage": "55V", "min_output_voltage": "1V", "max_output_current": "6A", "max_switch_frequency": "0.6MHz", "quiescent_current": "1650μA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "Buck: 76mV (Valley), Boost: 170mV (Peak)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "DCM", "power_good_indicator": "Yes", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "OPTION", "pitch": "0.65", "height": "1.2", "length": "9.7", "width": "4.4", "pin_count": "2"}]}