[{"part_number": "LT8705E", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "80V VIN and VOUT Synchronous 4-Switch Buck-Boost DC/DC Controller", "features": ["Single Inductor Allows VIN Above, Below, or Equal to Regulated VOUT", "VIN Range 2.8V (Need EXTVCC > 6.4V) to 80V", "VOUT Range: 1.3V to 80V", "Quad N-Channel MOSFET Gate Drivers", "Synchronous Rectification: Up to 98% Efficiency", "Input and Output Current Monitor Pins", "Synchronizable Fixed Frequency: 100kHz to 400kHz", "Integrated Input Current, Input Voltage, Output Current and Output Voltage Feedback Loops", "Clock Output Usable To Monitor Die Temperature", "Available in 38-Lead (5mm × 7mm) QFN and TSSOP Packages with the TSSOP Modified for Improved High Voltage Operation"], "description": "The LT®8705 is a high performance buck-boost switching regulator controller that operates from input voltages above, below or equal to the output voltage. The part has integrated input current, input voltage, output current and output voltage feedback loops. With a wide 2.8V to 80V input and 1.3V to 80V output range, the LT8705 is compatible with most solar, automotive, telecom and battery-powered systems.\nThe LT8705 includes servo pins to indicate which feedback loops are active. The MODE pin selects among Burst Mode® operation, discontinuous or continuous conduction mode at light loads. Additional features include a 3.3V/12mA LDO, a synchronizable fixed operating frequency, onboard gate drivers, adjustable UVLO, along with input and output current monitoring with programmable maximum levels.", "applications": ["High Voltage Buck-Boost Converters", "Input or Output Current Limited Converters"], "ordering_information": [{"part_number": "LT8705E", "order_device": "LT8705EUHF#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1701 Rev C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8705E", "order_device": "LT8705EUHF#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1701 Rev C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8705E", "order_device": "LT8705EFE#PBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1865 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8705E", "order_device": "LT8705EFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1865 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LT8705", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "SHDN", "pin_description": "Shutdown Pin. Tie high to enable device. Ground to shut down and reduce quiescent current to a minimum. Do not float this pin."}, {"pin_number": "2", "pin_name": "CSN", "pin_description": "The (–) Input to the Inductor Current Sense and Reverse-Current Detect Amplifier."}, {"pin_number": "3", "pin_name": "CSP", "pin_description": "The (+) Input to the Inductor Current Sense and Reverse-Current Detect Amplifier. The Vc pin voltage and built-in offsets between CSP and CSN pins, in conjunction with the RSENSE resistor value, set the current trip threshold."}, {"pin_number": "4", "pin_name": "LD033", "pin_description": "3.3V Regulator Output. Bypass this pin to ground with a minimum 0.1μF ceramic capacitor."}, {"pin_number": "5", "pin_name": "FBIN", "pin_description": "Input Feedback Pin. This pin is connected to the input error amplifier input."}, {"pin_number": "6", "pin_name": "FBOUT", "pin_description": "Output Feedback Pin. This pin connects the error amplifier input to an external resistor divider from the output."}, {"pin_number": "7", "pin_name": "IMON_OUT", "pin_description": "Output Current Monitor Pin. The current out of this pin is proportional to the output current."}, {"pin_number": "8", "pin_name": "Vc", "pin_description": "Error Amplifier Output Pin. Tie external compensation network to this pin."}, {"pin_number": "9", "pin_name": "SS", "pin_description": "Soft-Start Pin. Place at least 100nF of capacitance here. Upon start-up, this pin will be charged by an internal resistor to 2.5V."}, {"pin_number": "10", "pin_name": "CLKOUT", "pin_description": "Clock Output Pin. Use this pin to synchronize one or more compatible switching regulator ICs to the LT8705. CLKOUT toggles at the same frequency as the internal oscillator or as the SYNC pin, but is approximately 180° out of phase. CLKOUT may also be used as a temperature monitor since the CLKOUT duty cycle varies linearly with the part’s junction temperature. The CLKOUT pin can drive capacitive loads up to 200pF."}, {"pin_number": "11", "pin_name": "SYNC", "pin_description": "To synchronize the switching frequency to an outside clock, simply drive this pin with a clock. The high voltage level of the clock needs to exceed 1.3V, and the low level should be less than 0.5V. Drive this pin to less than 0.5V to revert to the internal free-running clock."}, {"pin_number": "12", "pin_name": "RT", "pin_description": "Timing Resistor Pi<PERSON>. Adjusts the switching frequency. Place a resistor from this pin to ground to set the free-running frequency. Do not float this pin."}, {"pin_number": "13", "pin_name": "GND", "pin_description": "Ground. Tie directly to local ground plane."}, {"pin_number": "14", "pin_name": "BG1", "pin_description": "Bottom Gate Drive. Drives the gates of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "15", "pin_name": "GATEVCC", "pin_description": "Power Supply for Gate Drivers. Must be connected to the INTVCC pin. Do not power from any other supply. Locally bypass to GND."}, {"pin_number": "16", "pin_name": "BG2", "pin_description": "Bottom Gate Drive. Drives the gates of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "17", "pin_name": "BOOST2", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST2 pin swings from a diode voltage below GATEVCC up to VOUT + GATEVCC."}, {"pin_number": "18", "pin_name": "TG2", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "19", "pin_name": "SW2", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "21", "pin_name": "SW1", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "22", "pin_name": "TG1", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "23", "pin_name": "BOOST1", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST1 pin swings from a diode voltage below GATEVCC up to VIN + GATEVCC."}, {"pin_number": "25", "pin_name": "SRVO_FBIN", "pin_description": "Open-Drain Logic Output. This pin is pulled to ground when the input voltage feedback loop is active."}, {"pin_number": "26", "pin_name": "SRVO_IIN", "pin_description": "Open-Drain Logic Output. The pin is pulled to ground when the input current loop is active."}, {"pin_number": "27", "pin_name": "SRVO_IOUT", "pin_description": "Open-Drain Logic Output. The pin is pulled to ground when the output current feedback loop is active."}, {"pin_number": "28", "pin_name": "SRVO_FBOUT", "pin_description": "Open-Drain Logic Output. This pin is pulled to ground when the output voltage feedback loop is active."}, {"pin_number": "29", "pin_name": "EXTVCC", "pin_description": "External VCC Input. When EXTVCC exceeds 6.4V (typical), INTVCC will be powered from this pin. When EXTVCC is lower than 6.22V (typical), INTVCC will be powered from VIN."}, {"pin_number": "30", "pin_name": "CSNOUT", "pin_description": "The (–) Input to the Output Current Monitor Amplifier. Connect this pin to VOUT when not in use."}, {"pin_number": "31", "pin_name": "CSPOUT", "pin_description": "The (+) Input to the Output Current Monitor Amplifier. This pin and the CSNOUT pin measure the voltage across the sense resistor, RSENSE2, to provide the output current signals. Connect this pin to VOUT when not in use."}, {"pin_number": "32", "pin_name": "CSNIN", "pin_description": "The (–) Input to the Input Current Monitor Amplifier. This pin and the CSPIN pin measure the voltage across the sense resistor, RSENSE1, to provide the input current signals. Connect this pin to VIN when not in use."}, {"pin_number": "33", "pin_name": "CSPIN", "pin_description": "The (+) Input to the Input Current Monitor Amplifier. Connect this pin to VIN when not in use."}, {"pin_number": "34", "pin_name": "VIN", "pin_description": "Main Input Supply Pin. It must be locally bypassed to ground."}, {"pin_number": "35", "pin_name": "INTVCC", "pin_description": "Internal 6.35V Regulator Output. Must be connected to the GATEVCC pin. INTVCC is powered from EXTVCC when the EXTVCC voltage is higher than 6.4V, otherwise INTVCC is powered from VIN. Bypass this pin to ground with a minimum 4.7μF ceramic capacitor."}, {"pin_number": "36", "pin_name": "SWEN", "pin_description": "Switch Enable Pin. Tie high to enable switching. Ground to disable switching. Don’t float this pin."}, {"pin_number": "37", "pin_name": "MODE", "pin_description": "Mode Pin. The voltage applied to this pin sets the operating mode of the controller. When the applied voltage is less than 0.4V, the forced continuous current mode is active. When this pin is allowed to float, Burst Mode operation is active. When the MODE pin voltage is higher than 2.3V, discontinuous mode is active."}, {"pin_number": "38", "pin_name": "IMON_IN", "pin_description": "Input Current Monitor Pin. The current out of this pin is proportional to the input current."}, {"pin_number": "39", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Tie directly to local ground plane."}]}, {"product_part_number": "LT8705", "package_type": "TSSOP", "pins": [{"pin_number": "1", "pin_name": "INTVCC", "pin_description": "Internal 6.35V Regulator Output. Must be connected to the GATEVCC pin. INTVCC is powered from EXTVCC when the EXTVCC voltage is higher than 6.4V, otherwise INTVCC is powered from VIN. Bypass this pin to ground with a minimum 4.7μF ceramic capacitor."}, {"pin_number": "2", "pin_name": "MODE", "pin_description": "Mode Pin. The voltage applied to this pin sets the operating mode of the controller. When the applied voltage is less than 0.4V, the forced continuous current mode is active. When this pin is allowed to float, Burst Mode operation is active. When the MODE pin voltage is higher than 2.3V, discontinuous mode is active."}, {"pin_number": "3", "pin_name": "IMON_IN", "pin_description": "Input Current Monitor Pin. The current out of this pin is proportional to the input current."}, {"pin_number": "4", "pin_name": "SHDN", "pin_description": "Shutdown Pin. Tie high to enable device. Ground to shut down and reduce quiescent current to a minimum. Do not float this pin."}, {"pin_number": "5", "pin_name": "CSN", "pin_description": "The (–) Input to the Inductor Current Sense and Reverse-Current Detect Amplifier."}, {"pin_number": "6", "pin_name": "CSP", "pin_description": "The (+) Input to the Inductor Current Sense and Reverse-Current Detect Amplifier. The Vc pin voltage and built-in offsets between CSP and CSN pins, in conjunction with the RSENSE resistor value, set the current trip threshold."}, {"pin_number": "7", "pin_name": "LD033", "pin_description": "3.3V Regulator Output. Bypass this pin to ground with a minimum 0.1μF ceramic capacitor."}, {"pin_number": "8", "pin_name": "FBIN", "pin_description": "Input Feedback Pin. This pin is connected to the input error amplifier input."}, {"pin_number": "9", "pin_name": "FBOUT", "pin_description": "Output Feedback Pin. This pin connects the error amplifier input to an external resistor divider from the output."}, {"pin_number": "10", "pin_name": "IMON_OUT", "pin_description": "Output Current Monitor Pin. The current out of this pin is proportional to the output current."}, {"pin_number": "11", "pin_name": "Vc", "pin_description": "Error Amplifier Output Pin. Tie external compensation network to this pin."}, {"pin_number": "12", "pin_name": "SS", "pin_description": "Soft-Start Pin. Place at least 100nF of capacitance here. Upon start-up, this pin will be charged by an internal resistor to 2.5V."}, {"pin_number": "13", "pin_name": "CLKOUT", "pin_description": "Clock Output Pin. Use this pin to synchronize one or more compatible switching regulator ICs to the LT8705. CLKOUT toggles at the same frequency as the internal oscillator or as the SYNC pin, but is approximately 180° out of phase. CLKOUT may also be used as a temperature monitor since the CLKOUT duty cycle varies linearly with the part’s junction temperature. The CLKOUT pin can drive capacitive loads up to 200pF."}, {"pin_number": "14", "pin_name": "SYNC", "pin_description": "To synchronize the switching frequency to an outside clock, simply drive this pin with a clock. The high voltage level of the clock needs to exceed 1.3V, and the low level should be less than 0.5V. Drive this pin to less than 0.5V to revert to the internal free-running clock."}, {"pin_number": "15", "pin_name": "RT", "pin_description": "Timing Resistor Pi<PERSON>. Adjusts the switching frequency. Place a resistor from this pin to ground to set the free-running frequency. Do not float this pin."}, {"pin_number": "16", "pin_name": "GND", "pin_description": "Ground. Tie directly to local ground plane."}, {"pin_number": "17", "pin_name": "BG1", "pin_description": "Bottom Gate Drive. Drives the gates of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "18", "pin_name": "GATEVCC", "pin_description": "Power Supply for Gate Drivers. Must be connected to the INTVCC pin. Do not power from any other supply. Locally bypass to GND."}, {"pin_number": "19", "pin_name": "BG2", "pin_description": "Bottom Gate Drive. Drives the gates of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "20", "pin_name": "BOOST2", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST2 pin swings from a diode voltage below GATEVCC up to VOUT + GATEVCC."}, {"pin_number": "21", "pin_name": "TG2", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "22", "pin_name": "SW2", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "24", "pin_name": "SW1", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "26", "pin_name": "TG1", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "28", "pin_name": "BOOST1", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST1 pin swings from a diode voltage below GATEVCC up to VIN + GATEVCC."}, {"pin_number": "30", "pin_name": "EXTVCC", "pin_description": "External VCC Input. When EXTVCC exceeds 6.4V (typical), INTVCC will be powered from this pin. When EXTVCC is lower than 6.22V (typical), INTVCC will be powered from VIN."}, {"pin_number": "32", "pin_name": "CSNOUT", "pin_description": "The (–) Input to the Output Current Monitor Amplifier. Connect this pin to VOUT when not in use."}, {"pin_number": "34", "pin_name": "CSPOUT", "pin_description": "The (+) Input to the Output Current Monitor Amplifier. This pin and the CSNOUT pin measure the voltage across the sense resistor, RSENSE2, to provide the output current signals. Connect this pin to VOUT when not in use."}, {"pin_number": "36", "pin_name": "CSNIN", "pin_description": "The (–) Input to the Input Current Monitor Amplifier. This pin and the CSPIN pin measure the voltage across the sense resistor, RSENSE1, to provide the input current signals. Connect this pin to VIN when not in use."}, {"pin_number": "37", "pin_name": "CSPIN", "pin_description": "The (+) Input to the Input Current Monitor Amplifier. Connect this pin to VIN when not in use."}, {"pin_number": "38", "pin_name": "VIN", "pin_description": "Main Input Supply Pin. It must be locally bypassed to ground."}, {"pin_number": "39", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Tie directly to local ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT8705.pdf", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "80V", "min_input_voltage": "2.8V", "max_output_voltage": "80V", "min_output_voltage": "1.3V", "max_output_current": "外部器件决定", "max_switch_frequency": "400kHz", "quiescent_current": "2.65mA", "high_side_mosfet_resistance": "不适用(外部MOSFET)", "low_side_mosfet_resistance": "不适用(外部MOSFET)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode, DCM, CCM", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.3%", "output_reference_voltage": "1.207V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.5", "length": "9.8", "width": "4.4", "type": "DESCRIPTION", "pin_count": "2", "height": "1.2"}]}, {"part_number": "LT8705I", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "80V VIN and VOUT Synchronous 4-Switch Buck-Boost DC/DC Controller", "features": ["Single Inductor Allows VIN Above, Below, or Equal to Regulated VOUT", "VIN Range 2.8V (Need EXTVCC > 6.4V) to 80V", "VOUT Range: 1.3V to 80V", "Quad N-Channel MOSFET Gate Drivers", "Synchronous Rectification: Up to 98% Efficiency", "Input and Output Current Monitor Pins", "Synchronizable Fixed Frequency: 100kHz to 400kHz", "Integrated Input Current, Input Voltage, Output Current and Output Voltage Feedback Loops", "Clock Output Usable To Monitor Die Temperature", "Available in 38-Lead (5mm × 7mm) QFN and TSSOP Packages with the TSSOP Modified for Improved High Voltage Operation"], "description": "The LT®8705 is a high performance buck-boost switching regulator controller that operates from input voltages above, below or equal to the output voltage. The part has integrated input current, input voltage, output current and output voltage feedback loops. With a wide 2.8V to 80V input and 1.3V to 80V output range, the LT8705 is compatible with most solar, automotive, telecom and battery-powered systems.\nThe LT8705 includes servo pins to indicate which feedback loops are active. The MODE pin selects among Burst Mode® operation, discontinuous or continuous conduction mode at light loads. Additional features include a 3.3V/12mA LDO, a synchronizable fixed operating frequency, onboard gate drivers, adjustable UVLO, along with input and output current monitoring with programmable maximum levels.", "applications": ["High Voltage Buck-Boost Converters", "Input or Output Current Limited Converters"], "ordering_information": [{"part_number": "LT8705I", "order_device": "LT8705IUHF#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1701 Rev C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8705I", "order_device": "LT8705IUHF#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1701 Rev C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8705I", "order_device": "LT8705IFE#PBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1865 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8705I", "order_device": "LT8705IFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1865 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LT8705", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "SHDN", "pin_description": "Shutdown Pin. Tie high to enable device. Ground to shut down and reduce quiescent current to a minimum. Do not float this pin."}, {"pin_number": "2", "pin_name": "CSN", "pin_description": "The (–) Input to the Inductor Current Sense and Reverse-Current Detect Amplifier."}, {"pin_number": "3", "pin_name": "CSP", "pin_description": "The (+) Input to the Inductor Current Sense and Reverse-Current Detect Amplifier. The Vc pin voltage and built-in offsets between CSP and CSN pins, in conjunction with the RSENSE resistor value, set the current trip threshold."}, {"pin_number": "4", "pin_name": "LD033", "pin_description": "3.3V Regulator Output. Bypass this pin to ground with a minimum 0.1μF ceramic capacitor."}, {"pin_number": "5", "pin_name": "FBIN", "pin_description": "Input Feedback Pin. This pin is connected to the input error amplifier input."}, {"pin_number": "6", "pin_name": "FBOUT", "pin_description": "Output Feedback Pin. This pin connects the error amplifier input to an external resistor divider from the output."}, {"pin_number": "7", "pin_name": "IMON_OUT", "pin_description": "Output Current Monitor Pin. The current out of this pin is proportional to the output current."}, {"pin_number": "8", "pin_name": "Vc", "pin_description": "Error Amplifier Output Pin. Tie external compensation network to this pin."}, {"pin_number": "9", "pin_name": "SS", "pin_description": "Soft-Start Pin. Place at least 100nF of capacitance here. Upon start-up, this pin will be charged by an internal resistor to 2.5V."}, {"pin_number": "10", "pin_name": "CLKOUT", "pin_description": "Clock Output Pin. Use this pin to synchronize one or more compatible switching regulator ICs to the LT8705. CLKOUT toggles at the same frequency as the internal oscillator or as the SYNC pin, but is approximately 180° out of phase. CLKOUT may also be used as a temperature monitor since the CLKOUT duty cycle varies linearly with the part’s junction temperature. The CLKOUT pin can drive capacitive loads up to 200pF."}, {"pin_number": "11", "pin_name": "SYNC", "pin_description": "To synchronize the switching frequency to an outside clock, simply drive this pin with a clock. The high voltage level of the clock needs to exceed 1.3V, and the low level should be less than 0.5V. Drive this pin to less than 0.5V to revert to the internal free-running clock."}, {"pin_number": "12", "pin_name": "RT", "pin_description": "Timing Resistor Pi<PERSON>. Adjusts the switching frequency. Place a resistor from this pin to ground to set the free-running frequency. Do not float this pin."}, {"pin_number": "13", "pin_name": "GND", "pin_description": "Ground. Tie directly to local ground plane."}, {"pin_number": "14", "pin_name": "BG1", "pin_description": "Bottom Gate Drive. Drives the gates of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "15", "pin_name": "GATEVCC", "pin_description": "Power Supply for Gate Drivers. Must be connected to the INTVCC pin. Do not power from any other supply. Locally bypass to GND."}, {"pin_number": "16", "pin_name": "BG2", "pin_description": "Bottom Gate Drive. Drives the gates of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "17", "pin_name": "BOOST2", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST2 pin swings from a diode voltage below GATEVCC up to VOUT + GATEVCC."}, {"pin_number": "18", "pin_name": "TG2", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "19", "pin_name": "SW2", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "21", "pin_name": "SW1", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "22", "pin_name": "TG1", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "23", "pin_name": "BOOST1", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST1 pin swings from a diode voltage below GATEVCC up to VIN + GATEVCC."}, {"pin_number": "25", "pin_name": "SRVO_FBIN", "pin_description": "Open-Drain Logic Output. This pin is pulled to ground when the input voltage feedback loop is active."}, {"pin_number": "26", "pin_name": "SRVO_IIN", "pin_description": "Open-Drain Logic Output. The pin is pulled to ground when the input current loop is active."}, {"pin_number": "27", "pin_name": "SRVO_IOUT", "pin_description": "Open-Drain Logic Output. The pin is pulled to ground when the output current feedback loop is active."}, {"pin_number": "28", "pin_name": "SRVO_FBOUT", "pin_description": "Open-Drain Logic Output. This pin is pulled to ground when the output voltage feedback loop is active."}, {"pin_number": "29", "pin_name": "EXTVCC", "pin_description": "External VCC Input. When EXTVCC exceeds 6.4V (typical), INTVCC will be powered from this pin. When EXTVCC is lower than 6.22V (typical), INTVCC will be powered from VIN."}, {"pin_number": "30", "pin_name": "CSNOUT", "pin_description": "The (–) Input to the Output Current Monitor Amplifier. Connect this pin to VOUT when not in use."}, {"pin_number": "31", "pin_name": "CSPOUT", "pin_description": "The (+) Input to the Output Current Monitor Amplifier. This pin and the CSNOUT pin measure the voltage across the sense resistor, RSENSE2, to provide the output current signals. Connect this pin to VOUT when not in use."}, {"pin_number": "32", "pin_name": "CSNIN", "pin_description": "The (–) Input to the Input Current Monitor Amplifier. This pin and the CSPIN pin measure the voltage across the sense resistor, RSENSE1, to provide the input current signals. Connect this pin to VIN when not in use."}, {"pin_number": "33", "pin_name": "CSPIN", "pin_description": "The (+) Input to the Input Current Monitor Amplifier. Connect this pin to VIN when not in use."}, {"pin_number": "34", "pin_name": "VIN", "pin_description": "Main Input Supply Pin. It must be locally bypassed to ground."}, {"pin_number": "35", "pin_name": "INTVCC", "pin_description": "Internal 6.35V Regulator Output. Must be connected to the GATEVCC pin. INTVCC is powered from EXTVCC when the EXTVCC voltage is higher than 6.4V, otherwise INTVCC is powered from VIN. Bypass this pin to ground with a minimum 4.7μF ceramic capacitor."}, {"pin_number": "36", "pin_name": "SWEN", "pin_description": "Switch Enable Pin. Tie high to enable switching. Ground to disable switching. Don’t float this pin."}, {"pin_number": "37", "pin_name": "MODE", "pin_description": "Mode Pin. The voltage applied to this pin sets the operating mode of the controller. When the applied voltage is less than 0.4V, the forced continuous current mode is active. When this pin is allowed to float, Burst Mode operation is active. When the MODE pin voltage is higher than 2.3V, discontinuous mode is active."}, {"pin_number": "38", "pin_name": "IMON_IN", "pin_description": "Input Current Monitor Pin. The current out of this pin is proportional to the input current."}, {"pin_number": "39", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Tie directly to local ground plane."}]}, {"product_part_number": "LT8705", "package_type": "TSSOP", "pins": [{"pin_number": "1", "pin_name": "INTVCC", "pin_description": "Internal 6.35V Regulator Output. Must be connected to the GATEVCC pin. INTVCC is powered from EXTVCC when the EXTVCC voltage is higher than 6.4V, otherwise INTVCC is powered from VIN. Bypass this pin to ground with a minimum 4.7μF ceramic capacitor."}, {"pin_number": "2", "pin_name": "MODE", "pin_description": "Mode Pin. The voltage applied to this pin sets the operating mode of the controller. When the applied voltage is less than 0.4V, the forced continuous current mode is active. When this pin is allowed to float, Burst Mode operation is active. When the MODE pin voltage is higher than 2.3V, discontinuous mode is active."}, {"pin_number": "3", "pin_name": "IMON_IN", "pin_description": "Input Current Monitor Pin. The current out of this pin is proportional to the input current."}, {"pin_number": "4", "pin_name": "SHDN", "pin_description": "Shutdown Pin. Tie high to enable device. Ground to shut down and reduce quiescent current to a minimum. Do not float this pin."}, {"pin_number": "5", "pin_name": "CSN", "pin_description": "The (–) Input to the Inductor Current Sense and Reverse-Current Detect Amplifier."}, {"pin_number": "6", "pin_name": "CSP", "pin_description": "The (+) Input to the Inductor Current Sense and Reverse-Current Detect Amplifier. The Vc pin voltage and built-in offsets between CSP and CSN pins, in conjunction with the RSENSE resistor value, set the current trip threshold."}, {"pin_number": "7", "pin_name": "LD033", "pin_description": "3.3V Regulator Output. Bypass this pin to ground with a minimum 0.1μF ceramic capacitor."}, {"pin_number": "8", "pin_name": "FBIN", "pin_description": "Input Feedback Pin. This pin is connected to the input error amplifier input."}, {"pin_number": "9", "pin_name": "FBOUT", "pin_description": "Output Feedback Pin. This pin connects the error amplifier input to an external resistor divider from the output."}, {"pin_number": "10", "pin_name": "IMON_OUT", "pin_description": "Output Current Monitor Pin. The current out of this pin is proportional to the output current."}, {"pin_number": "11", "pin_name": "Vc", "pin_description": "Error Amplifier Output Pin. Tie external compensation network to this pin."}, {"pin_number": "12", "pin_name": "SS", "pin_description": "Soft-Start Pin. Place at least 100nF of capacitance here. Upon start-up, this pin will be charged by an internal resistor to 2.5V."}, {"pin_number": "13", "pin_name": "CLKOUT", "pin_description": "Clock Output Pin. Use this pin to synchronize one or more compatible switching regulator ICs to the LT8705. CLKOUT toggles at the same frequency as the internal oscillator or as the SYNC pin, but is approximately 180° out of phase. CLKOUT may also be used as a temperature monitor since the CLKOUT duty cycle varies linearly with the part’s junction temperature. The CLKOUT pin can drive capacitive loads up to 200pF."}, {"pin_number": "14", "pin_name": "SYNC", "pin_description": "To synchronize the switching frequency to an outside clock, simply drive this pin with a clock. The high voltage level of the clock needs to exceed 1.3V, and the low level should be less than 0.5V. Drive this pin to less than 0.5V to revert to the internal free-running clock."}, {"pin_number": "15", "pin_name": "RT", "pin_description": "Timing Resistor Pi<PERSON>. Adjusts the switching frequency. Place a resistor from this pin to ground to set the free-running frequency. Do not float this pin."}, {"pin_number": "16", "pin_name": "GND", "pin_description": "Ground. Tie directly to local ground plane."}, {"pin_number": "17", "pin_name": "BG1", "pin_description": "Bottom Gate Drive. Drives the gates of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "18", "pin_name": "GATEVCC", "pin_description": "Power Supply for Gate Drivers. Must be connected to the INTVCC pin. Do not power from any other supply. Locally bypass to GND."}, {"pin_number": "19", "pin_name": "BG2", "pin_description": "Bottom Gate Drive. Drives the gates of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "20", "pin_name": "BOOST2", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST2 pin swings from a diode voltage below GATEVCC up to VOUT + GATEVCC."}, {"pin_number": "21", "pin_name": "TG2", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "22", "pin_name": "SW2", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "24", "pin_name": "SW1", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "26", "pin_name": "TG1", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "28", "pin_name": "BOOST1", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST1 pin swings from a diode voltage below GATEVCC up to VIN + GATEVCC."}, {"pin_number": "30", "pin_name": "EXTVCC", "pin_description": "External VCC Input. When EXTVCC exceeds 6.4V (typical), INTVCC will be powered from this pin. When EXTVCC is lower than 6.22V (typical), INTVCC will be powered from VIN."}, {"pin_number": "32", "pin_name": "CSNOUT", "pin_description": "The (–) Input to the Output Current Monitor Amplifier. Connect this pin to VOUT when not in use."}, {"pin_number": "34", "pin_name": "CSPOUT", "pin_description": "The (+) Input to the Output Current Monitor Amplifier. This pin and the CSNOUT pin measure the voltage across the sense resistor, RSENSE2, to provide the output current signals. Connect this pin to VOUT when not in use."}, {"pin_number": "36", "pin_name": "CSNIN", "pin_description": "The (–) Input to the Input Current Monitor Amplifier. This pin and the CSPIN pin measure the voltage across the sense resistor, RSENSE1, to provide the input current signals. Connect this pin to VIN when not in use."}, {"pin_number": "37", "pin_name": "CSPIN", "pin_description": "The (+) Input to the Input Current Monitor Amplifier. Connect this pin to VIN when not in use."}, {"pin_number": "38", "pin_name": "VIN", "pin_description": "Main Input Supply Pin. It must be locally bypassed to ground."}, {"pin_number": "39", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Tie directly to local ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT8705.pdf", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "80V", "min_input_voltage": "2.8V", "max_output_voltage": "80V", "min_output_voltage": "1.3V", "max_output_current": "外部器件决定", "max_switch_frequency": "400kHz", "quiescent_current": "2.65mA", "high_side_mosfet_resistance": "不适用(外部MOSFET)", "low_side_mosfet_resistance": "不适用(外部MOSFET)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode, DCM, CCM", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.3%", "output_reference_voltage": "1.207V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.5", "length": "9.8", "width": "4.4", "type": "DESCRIPTION", "pin_count": "2", "height": "1.2"}]}, {"part_number": "LT8705H", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "80V VIN and VOUT Synchronous 4-Switch Buck-Boost DC/DC Controller", "features": ["Single Inductor Allows VIN Above, Below, or Equal to Regulated VOUT", "VIN Range 2.8V (Need EXTVCC > 6.4V) to 80V", "VOUT Range: 1.3V to 80V", "Quad N-Channel MOSFET Gate Drivers", "Synchronous Rectification: Up to 98% Efficiency", "Input and Output Current Monitor Pins", "Synchronizable Fixed Frequency: 100kHz to 400kHz", "Integrated Input Current, Input Voltage, Output Current and Output Voltage Feedback Loops", "Clock Output Usable To Monitor Die Temperature", "Available in 38-Lead (5mm × 7mm) QFN and TSSOP Packages with the TSSOP Modified for Improved High Voltage Operation"], "description": "The LT®8705 is a high performance buck-boost switching regulator controller that operates from input voltages above, below or equal to the output voltage. The part has integrated input current, input voltage, output current and output voltage feedback loops. With a wide 2.8V to 80V input and 1.3V to 80V output range, the LT8705 is compatible with most solar, automotive, telecom and battery-powered systems.\nThe LT8705 includes servo pins to indicate which feedback loops are active. The MODE pin selects among Burst Mode® operation, discontinuous or continuous conduction mode at light loads. Additional features include a 3.3V/12mA LDO, a synchronizable fixed operating frequency, onboard gate drivers, adjustable UVLO, along with input and output current monitoring with programmable maximum levels.", "applications": ["High Voltage Buck-Boost Converters", "Input or Output Current Limited Converters"], "ordering_information": [{"part_number": "LT8705H", "order_device": "LT8705HFE#PBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1865 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8705H", "order_device": "LT8705HFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1865 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}], "pin_function": [{"product_part_number": "LT8705", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "SHDN", "pin_description": "Shutdown Pin. Tie high to enable device. Ground to shut down and reduce quiescent current to a minimum. Do not float this pin."}, {"pin_number": "2", "pin_name": "CSN", "pin_description": "The (–) Input to the Inductor Current Sense and Reverse-Current Detect Amplifier."}, {"pin_number": "3", "pin_name": "CSP", "pin_description": "The (+) Input to the Inductor Current Sense and Reverse-Current Detect Amplifier. The Vc pin voltage and built-in offsets between CSP and CSN pins, in conjunction with the RSENSE resistor value, set the current trip threshold."}, {"pin_number": "4", "pin_name": "LD033", "pin_description": "3.3V Regulator Output. Bypass this pin to ground with a minimum 0.1μF ceramic capacitor."}, {"pin_number": "5", "pin_name": "FBIN", "pin_description": "Input Feedback Pin. This pin is connected to the input error amplifier input."}, {"pin_number": "6", "pin_name": "FBOUT", "pin_description": "Output Feedback Pin. This pin connects the error amplifier input to an external resistor divider from the output."}, {"pin_number": "7", "pin_name": "IMON_OUT", "pin_description": "Output Current Monitor Pin. The current out of this pin is proportional to the output current."}, {"pin_number": "8", "pin_name": "Vc", "pin_description": "Error Amplifier Output Pin. Tie external compensation network to this pin."}, {"pin_number": "9", "pin_name": "SS", "pin_description": "Soft-Start Pin. Place at least 100nF of capacitance here. Upon start-up, this pin will be charged by an internal resistor to 2.5V."}, {"pin_number": "10", "pin_name": "CLKOUT", "pin_description": "Clock Output Pin. Use this pin to synchronize one or more compatible switching regulator ICs to the LT8705. CLKOUT toggles at the same frequency as the internal oscillator or as the SYNC pin, but is approximately 180° out of phase. CLKOUT may also be used as a temperature monitor since the CLKOUT duty cycle varies linearly with the part’s junction temperature. The CLKOUT pin can drive capacitive loads up to 200pF."}, {"pin_number": "11", "pin_name": "SYNC", "pin_description": "To synchronize the switching frequency to an outside clock, simply drive this pin with a clock. The high voltage level of the clock needs to exceed 1.3V, and the low level should be less than 0.5V. Drive this pin to less than 0.5V to revert to the internal free-running clock."}, {"pin_number": "12", "pin_name": "RT", "pin_description": "Timing Resistor Pi<PERSON>. Adjusts the switching frequency. Place a resistor from this pin to ground to set the free-running frequency. Do not float this pin."}, {"pin_number": "13", "pin_name": "GND", "pin_description": "Ground. Tie directly to local ground plane."}, {"pin_number": "14", "pin_name": "BG1", "pin_description": "Bottom Gate Drive. Drives the gates of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "15", "pin_name": "GATEVCC", "pin_description": "Power Supply for Gate Drivers. Must be connected to the INTVCC pin. Do not power from any other supply. Locally bypass to GND."}, {"pin_number": "16", "pin_name": "BG2", "pin_description": "Bottom Gate Drive. Drives the gates of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "17", "pin_name": "BOOST2", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST2 pin swings from a diode voltage below GATEVCC up to VOUT + GATEVCC."}, {"pin_number": "18", "pin_name": "TG2", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "19", "pin_name": "SW2", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "21", "pin_name": "SW1", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "22", "pin_name": "TG1", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "23", "pin_name": "BOOST1", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST1 pin swings from a diode voltage below GATEVCC up to VIN + GATEVCC."}, {"pin_number": "25", "pin_name": "SRVO_FBIN", "pin_description": "Open-Drain Logic Output. This pin is pulled to ground when the input voltage feedback loop is active."}, {"pin_number": "26", "pin_name": "SRVO_IIN", "pin_description": "Open-Drain Logic Output. The pin is pulled to ground when the input current loop is active."}, {"pin_number": "27", "pin_name": "SRVO_IOUT", "pin_description": "Open-Drain Logic Output. The pin is pulled to ground when the output current feedback loop is active."}, {"pin_number": "28", "pin_name": "SRVO_FBOUT", "pin_description": "Open-Drain Logic Output. This pin is pulled to ground when the output voltage feedback loop is active."}, {"pin_number": "29", "pin_name": "EXTVCC", "pin_description": "External VCC Input. When EXTVCC exceeds 6.4V (typical), INTVCC will be powered from this pin. When EXTVCC is lower than 6.22V (typical), INTVCC will be powered from VIN."}, {"pin_number": "30", "pin_name": "CSNOUT", "pin_description": "The (–) Input to the Output Current Monitor Amplifier. Connect this pin to VOUT when not in use."}, {"pin_number": "31", "pin_name": "CSPOUT", "pin_description": "The (+) Input to the Output Current Monitor Amplifier. This pin and the CSNOUT pin measure the voltage across the sense resistor, RSENSE2, to provide the output current signals. Connect this pin to VOUT when not in use."}, {"pin_number": "32", "pin_name": "CSNIN", "pin_description": "The (–) Input to the Input Current Monitor Amplifier. This pin and the CSPIN pin measure the voltage across the sense resistor, RSENSE1, to provide the input current signals. Connect this pin to VIN when not in use."}, {"pin_number": "33", "pin_name": "CSPIN", "pin_description": "The (+) Input to the Input Current Monitor Amplifier. Connect this pin to VIN when not in use."}, {"pin_number": "34", "pin_name": "VIN", "pin_description": "Main Input Supply Pin. It must be locally bypassed to ground."}, {"pin_number": "35", "pin_name": "INTVCC", "pin_description": "Internal 6.35V Regulator Output. Must be connected to the GATEVCC pin. INTVCC is powered from EXTVCC when the EXTVCC voltage is higher than 6.4V, otherwise INTVCC is powered from VIN. Bypass this pin to ground with a minimum 4.7μF ceramic capacitor."}, {"pin_number": "36", "pin_name": "SWEN", "pin_description": "Switch Enable Pin. Tie high to enable switching. Ground to disable switching. Don’t float this pin."}, {"pin_number": "37", "pin_name": "MODE", "pin_description": "Mode Pin. The voltage applied to this pin sets the operating mode of the controller. When the applied voltage is less than 0.4V, the forced continuous current mode is active. When this pin is allowed to float, Burst Mode operation is active. When the MODE pin voltage is higher than 2.3V, discontinuous mode is active."}, {"pin_number": "38", "pin_name": "IMON_IN", "pin_description": "Input Current Monitor Pin. The current out of this pin is proportional to the input current."}, {"pin_number": "39", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Tie directly to local ground plane."}]}, {"product_part_number": "LT8705", "package_type": "TSSOP", "pins": [{"pin_number": "1", "pin_name": "INTVCC", "pin_description": "Internal 6.35V Regulator Output. Must be connected to the GATEVCC pin. INTVCC is powered from EXTVCC when the EXTVCC voltage is higher than 6.4V, otherwise INTVCC is powered from VIN. Bypass this pin to ground with a minimum 4.7μF ceramic capacitor."}, {"pin_number": "2", "pin_name": "MODE", "pin_description": "Mode Pin. The voltage applied to this pin sets the operating mode of the controller. When the applied voltage is less than 0.4V, the forced continuous current mode is active. When this pin is allowed to float, Burst Mode operation is active. When the MODE pin voltage is higher than 2.3V, discontinuous mode is active."}, {"pin_number": "3", "pin_name": "IMON_IN", "pin_description": "Input Current Monitor Pin. The current out of this pin is proportional to the input current."}, {"pin_number": "4", "pin_name": "SHDN", "pin_description": "Shutdown Pin. Tie high to enable device. Ground to shut down and reduce quiescent current to a minimum. Do not float this pin."}, {"pin_number": "5", "pin_name": "CSN", "pin_description": "The (–) Input to the Inductor Current Sense and Reverse-Current Detect Amplifier."}, {"pin_number": "6", "pin_name": "CSP", "pin_description": "The (+) Input to the Inductor Current Sense and Reverse-Current Detect Amplifier. The Vc pin voltage and built-in offsets between CSP and CSN pins, in conjunction with the RSENSE resistor value, set the current trip threshold."}, {"pin_number": "7", "pin_name": "LD033", "pin_description": "3.3V Regulator Output. Bypass this pin to ground with a minimum 0.1μF ceramic capacitor."}, {"pin_number": "8", "pin_name": "FBIN", "pin_description": "Input Feedback Pin. This pin is connected to the input error amplifier input."}, {"pin_number": "9", "pin_name": "FBOUT", "pin_description": "Output Feedback Pin. This pin connects the error amplifier input to an external resistor divider from the output."}, {"pin_number": "10", "pin_name": "IMON_OUT", "pin_description": "Output Current Monitor Pin. The current out of this pin is proportional to the output current."}, {"pin_number": "11", "pin_name": "Vc", "pin_description": "Error Amplifier Output Pin. Tie external compensation network to this pin."}, {"pin_number": "12", "pin_name": "SS", "pin_description": "Soft-Start Pin. Place at least 100nF of capacitance here. Upon start-up, this pin will be charged by an internal resistor to 2.5V."}, {"pin_number": "13", "pin_name": "CLKOUT", "pin_description": "Clock Output Pin. Use this pin to synchronize one or more compatible switching regulator ICs to the LT8705. CLKOUT toggles at the same frequency as the internal oscillator or as the SYNC pin, but is approximately 180° out of phase. CLKOUT may also be used as a temperature monitor since the CLKOUT duty cycle varies linearly with the part’s junction temperature. The CLKOUT pin can drive capacitive loads up to 200pF."}, {"pin_number": "14", "pin_name": "SYNC", "pin_description": "To synchronize the switching frequency to an outside clock, simply drive this pin with a clock. The high voltage level of the clock needs to exceed 1.3V, and the low level should be less than 0.5V. Drive this pin to less than 0.5V to revert to the internal free-running clock."}, {"pin_number": "15", "pin_name": "RT", "pin_description": "Timing Resistor Pi<PERSON>. Adjusts the switching frequency. Place a resistor from this pin to ground to set the free-running frequency. Do not float this pin."}, {"pin_number": "16", "pin_name": "GND", "pin_description": "Ground. Tie directly to local ground plane."}, {"pin_number": "17", "pin_name": "BG1", "pin_description": "Bottom Gate Drive. Drives the gates of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "18", "pin_name": "GATEVCC", "pin_description": "Power Supply for Gate Drivers. Must be connected to the INTVCC pin. Do not power from any other supply. Locally bypass to GND."}, {"pin_number": "19", "pin_name": "BG2", "pin_description": "Bottom Gate Drive. Drives the gates of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "20", "pin_name": "BOOST2", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST2 pin swings from a diode voltage below GATEVCC up to VOUT + GATEVCC."}, {"pin_number": "21", "pin_name": "TG2", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "22", "pin_name": "SW2", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "24", "pin_name": "SW1", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "26", "pin_name": "TG1", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "28", "pin_name": "BOOST1", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST1 pin swings from a diode voltage below GATEVCC up to VIN + GATEVCC."}, {"pin_number": "30", "pin_name": "EXTVCC", "pin_description": "External VCC Input. When EXTVCC exceeds 6.4V (typical), INTVCC will be powered from this pin. When EXTVCC is lower than 6.22V (typical), INTVCC will be powered from VIN."}, {"pin_number": "32", "pin_name": "CSNOUT", "pin_description": "The (–) Input to the Output Current Monitor Amplifier. Connect this pin to VOUT when not in use."}, {"pin_number": "34", "pin_name": "CSPOUT", "pin_description": "The (+) Input to the Output Current Monitor Amplifier. This pin and the CSNOUT pin measure the voltage across the sense resistor, RSENSE2, to provide the output current signals. Connect this pin to VOUT when not in use."}, {"pin_number": "36", "pin_name": "CSNIN", "pin_description": "The (–) Input to the Input Current Monitor Amplifier. This pin and the CSPIN pin measure the voltage across the sense resistor, RSENSE1, to provide the input current signals. Connect this pin to VIN when not in use."}, {"pin_number": "37", "pin_name": "CSPIN", "pin_description": "The (+) Input to the Input Current Monitor Amplifier. Connect this pin to VIN when not in use."}, {"pin_number": "38", "pin_name": "VIN", "pin_description": "Main Input Supply Pin. It must be locally bypassed to ground."}, {"pin_number": "39", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Tie directly to local ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT8705.pdf", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "80V", "min_input_voltage": "2.8V", "max_output_voltage": "80V", "min_output_voltage": "1.3V", "max_output_current": "外部器件决定", "max_switch_frequency": "400kHz", "quiescent_current": "2.65mA", "high_side_mosfet_resistance": "不适用(外部MOSFET)", "low_side_mosfet_resistance": "不适用(外部MOSFET)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode, DCM, CCM", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.3%", "output_reference_voltage": "1.207V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.5", "length": "9.8", "width": "4.4", "type": "DESCRIPTION", "pin_count": "2", "height": "1.2"}]}, {"part_number": "LT8705MP", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "80V VIN and VOUT Synchronous 4-Switch Buck-Boost DC/DC Controller", "features": ["Single Inductor Allows VIN Above, Below, or Equal to Regulated VOUT", "VIN Range 2.8V (Need EXTVCC > 6.4V) to 80V", "VOUT Range: 1.3V to 80V", "Quad N-Channel MOSFET Gate Drivers", "Synchronous Rectification: Up to 98% Efficiency", "Input and Output Current Monitor Pins", "Synchronizable Fixed Frequency: 100kHz to 400kHz", "Integrated Input Current, Input Voltage, Output Current and Output Voltage Feedback Loops", "Clock Output Usable To Monitor Die Temperature", "Available in 38-Lead (5mm × 7mm) QFN and TSSOP Packages with the TSSOP Modified for Improved High Voltage Operation"], "description": "The LT®8705 is a high performance buck-boost switching regulator controller that operates from input voltages above, below or equal to the output voltage. The part has integrated input current, input voltage, output current and output voltage feedback loops. With a wide 2.8V to 80V input and 1.3V to 80V output range, the LT8705 is compatible with most solar, automotive, telecom and battery-powered systems.\nThe LT8705 includes servo pins to indicate which feedback loops are active. The MODE pin selects among Burst Mode® operation, discontinuous or continuous conduction mode at light loads. Additional features include a 3.3V/12mA LDO, a synchronizable fixed operating frequency, onboard gate drivers, adjustable UVLO, along with input and output current monitoring with programmable maximum levels.", "applications": ["High Voltage Buck-Boost Converters", "Input or Output Current Limited Converters"], "ordering_information": [{"part_number": "LT8705MP", "order_device": "LT8705MPFE#PBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1865 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}, {"part_number": "LT8705MP", "order_device": "LT8705MPFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1865 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}], "pin_function": [{"product_part_number": "LT8705", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "SHDN", "pin_description": "Shutdown Pin. Tie high to enable device. Ground to shut down and reduce quiescent current to a minimum. Do not float this pin."}, {"pin_number": "2", "pin_name": "CSN", "pin_description": "The (–) Input to the Inductor Current Sense and Reverse-Current Detect Amplifier."}, {"pin_number": "3", "pin_name": "CSP", "pin_description": "The (+) Input to the Inductor Current Sense and Reverse-Current Detect Amplifier. The Vc pin voltage and built-in offsets between CSP and CSN pins, in conjunction with the RSENSE resistor value, set the current trip threshold."}, {"pin_number": "4", "pin_name": "LD033", "pin_description": "3.3V Regulator Output. Bypass this pin to ground with a minimum 0.1μF ceramic capacitor."}, {"pin_number": "5", "pin_name": "FBIN", "pin_description": "Input Feedback Pin. This pin is connected to the input error amplifier input."}, {"pin_number": "6", "pin_name": "FBOUT", "pin_description": "Output Feedback Pin. This pin connects the error amplifier input to an external resistor divider from the output."}, {"pin_number": "7", "pin_name": "IMON_OUT", "pin_description": "Output Current Monitor Pin. The current out of this pin is proportional to the output current."}, {"pin_number": "8", "pin_name": "Vc", "pin_description": "Error Amplifier Output Pin. Tie external compensation network to this pin."}, {"pin_number": "9", "pin_name": "SS", "pin_description": "Soft-Start Pin. Place at least 100nF of capacitance here. Upon start-up, this pin will be charged by an internal resistor to 2.5V."}, {"pin_number": "10", "pin_name": "CLKOUT", "pin_description": "Clock Output Pin. Use this pin to synchronize one or more compatible switching regulator ICs to the LT8705. CLKOUT toggles at the same frequency as the internal oscillator or as the SYNC pin, but is approximately 180° out of phase. CLKOUT may also be used as a temperature monitor since the CLKOUT duty cycle varies linearly with the part’s junction temperature. The CLKOUT pin can drive capacitive loads up to 200pF."}, {"pin_number": "11", "pin_name": "SYNC", "pin_description": "To synchronize the switching frequency to an outside clock, simply drive this pin with a clock. The high voltage level of the clock needs to exceed 1.3V, and the low level should be less than 0.5V. Drive this pin to less than 0.5V to revert to the internal free-running clock."}, {"pin_number": "12", "pin_name": "RT", "pin_description": "Timing Resistor Pi<PERSON>. Adjusts the switching frequency. Place a resistor from this pin to ground to set the free-running frequency. Do not float this pin."}, {"pin_number": "13", "pin_name": "GND", "pin_description": "Ground. Tie directly to local ground plane."}, {"pin_number": "14", "pin_name": "BG1", "pin_description": "Bottom Gate Drive. Drives the gates of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "15", "pin_name": "GATEVCC", "pin_description": "Power Supply for Gate Drivers. Must be connected to the INTVCC pin. Do not power from any other supply. Locally bypass to GND."}, {"pin_number": "16", "pin_name": "BG2", "pin_description": "Bottom Gate Drive. Drives the gates of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "17", "pin_name": "BOOST2", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST2 pin swings from a diode voltage below GATEVCC up to VOUT + GATEVCC."}, {"pin_number": "18", "pin_name": "TG2", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "19", "pin_name": "SW2", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "21", "pin_name": "SW1", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "22", "pin_name": "TG1", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "23", "pin_name": "BOOST1", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST1 pin swings from a diode voltage below GATEVCC up to VIN + GATEVCC."}, {"pin_number": "25", "pin_name": "SRVO_FBIN", "pin_description": "Open-Drain Logic Output. This pin is pulled to ground when the input voltage feedback loop is active."}, {"pin_number": "26", "pin_name": "SRVO_IIN", "pin_description": "Open-Drain Logic Output. The pin is pulled to ground when the input current loop is active."}, {"pin_number": "27", "pin_name": "SRVO_IOUT", "pin_description": "Open-Drain Logic Output. The pin is pulled to ground when the output current feedback loop is active."}, {"pin_number": "28", "pin_name": "SRVO_FBOUT", "pin_description": "Open-Drain Logic Output. This pin is pulled to ground when the output voltage feedback loop is active."}, {"pin_number": "29", "pin_name": "EXTVCC", "pin_description": "External VCC Input. When EXTVCC exceeds 6.4V (typical), INTVCC will be powered from this pin. When EXTVCC is lower than 6.22V (typical), INTVCC will be powered from VIN."}, {"pin_number": "30", "pin_name": "CSNOUT", "pin_description": "The (–) Input to the Output Current Monitor Amplifier. Connect this pin to VOUT when not in use."}, {"pin_number": "31", "pin_name": "CSPOUT", "pin_description": "The (+) Input to the Output Current Monitor Amplifier. This pin and the CSNOUT pin measure the voltage across the sense resistor, RSENSE2, to provide the output current signals. Connect this pin to VOUT when not in use."}, {"pin_number": "32", "pin_name": "CSNIN", "pin_description": "The (–) Input to the Input Current Monitor Amplifier. This pin and the CSPIN pin measure the voltage across the sense resistor, RSENSE1, to provide the input current signals. Connect this pin to VIN when not in use."}, {"pin_number": "33", "pin_name": "CSPIN", "pin_description": "The (+) Input to the Input Current Monitor Amplifier. Connect this pin to VIN when not in use."}, {"pin_number": "34", "pin_name": "VIN", "pin_description": "Main Input Supply Pin. It must be locally bypassed to ground."}, {"pin_number": "35", "pin_name": "INTVCC", "pin_description": "Internal 6.35V Regulator Output. Must be connected to the GATEVCC pin. INTVCC is powered from EXTVCC when the EXTVCC voltage is higher than 6.4V, otherwise INTVCC is powered from VIN. Bypass this pin to ground with a minimum 4.7μF ceramic capacitor."}, {"pin_number": "36", "pin_name": "SWEN", "pin_description": "Switch Enable Pin. Tie high to enable switching. Ground to disable switching. Don’t float this pin."}, {"pin_number": "37", "pin_name": "MODE", "pin_description": "Mode Pin. The voltage applied to this pin sets the operating mode of the controller. When the applied voltage is less than 0.4V, the forced continuous current mode is active. When this pin is allowed to float, Burst Mode operation is active. When the MODE pin voltage is higher than 2.3V, discontinuous mode is active."}, {"pin_number": "38", "pin_name": "IMON_IN", "pin_description": "Input Current Monitor Pin. The current out of this pin is proportional to the input current."}, {"pin_number": "39", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Tie directly to local ground plane."}]}, {"product_part_number": "LT8705", "package_type": "TSSOP", "pins": [{"pin_number": "1", "pin_name": "INTVCC", "pin_description": "Internal 6.35V Regulator Output. Must be connected to the GATEVCC pin. INTVCC is powered from EXTVCC when the EXTVCC voltage is higher than 6.4V, otherwise INTVCC is powered from VIN. Bypass this pin to ground with a minimum 4.7μF ceramic capacitor."}, {"pin_number": "2", "pin_name": "MODE", "pin_description": "Mode Pin. The voltage applied to this pin sets the operating mode of the controller. When the applied voltage is less than 0.4V, the forced continuous current mode is active. When this pin is allowed to float, Burst Mode operation is active. When the MODE pin voltage is higher than 2.3V, discontinuous mode is active."}, {"pin_number": "3", "pin_name": "IMON_IN", "pin_description": "Input Current Monitor Pin. The current out of this pin is proportional to the input current."}, {"pin_number": "4", "pin_name": "SHDN", "pin_description": "Shutdown Pin. Tie high to enable device. Ground to shut down and reduce quiescent current to a minimum. Do not float this pin."}, {"pin_number": "5", "pin_name": "CSN", "pin_description": "The (–) Input to the Inductor Current Sense and Reverse-Current Detect Amplifier."}, {"pin_number": "6", "pin_name": "CSP", "pin_description": "The (+) Input to the Inductor Current Sense and Reverse-Current Detect Amplifier. The Vc pin voltage and built-in offsets between CSP and CSN pins, in conjunction with the RSENSE resistor value, set the current trip threshold."}, {"pin_number": "7", "pin_name": "LD033", "pin_description": "3.3V Regulator Output. Bypass this pin to ground with a minimum 0.1μF ceramic capacitor."}, {"pin_number": "8", "pin_name": "FBIN", "pin_description": "Input Feedback Pin. This pin is connected to the input error amplifier input."}, {"pin_number": "9", "pin_name": "FBOUT", "pin_description": "Output Feedback Pin. This pin connects the error amplifier input to an external resistor divider from the output."}, {"pin_number": "10", "pin_name": "IMON_OUT", "pin_description": "Output Current Monitor Pin. The current out of this pin is proportional to the output current."}, {"pin_number": "11", "pin_name": "Vc", "pin_description": "Error Amplifier Output Pin. Tie external compensation network to this pin."}, {"pin_number": "12", "pin_name": "SS", "pin_description": "Soft-Start Pin. Place at least 100nF of capacitance here. Upon start-up, this pin will be charged by an internal resistor to 2.5V."}, {"pin_number": "13", "pin_name": "CLKOUT", "pin_description": "Clock Output Pin. Use this pin to synchronize one or more compatible switching regulator ICs to the LT8705. CLKOUT toggles at the same frequency as the internal oscillator or as the SYNC pin, but is approximately 180° out of phase. CLKOUT may also be used as a temperature monitor since the CLKOUT duty cycle varies linearly with the part’s junction temperature. The CLKOUT pin can drive capacitive loads up to 200pF."}, {"pin_number": "14", "pin_name": "SYNC", "pin_description": "To synchronize the switching frequency to an outside clock, simply drive this pin with a clock. The high voltage level of the clock needs to exceed 1.3V, and the low level should be less than 0.5V. Drive this pin to less than 0.5V to revert to the internal free-running clock."}, {"pin_number": "15", "pin_name": "RT", "pin_description": "Timing Resistor Pi<PERSON>. Adjusts the switching frequency. Place a resistor from this pin to ground to set the free-running frequency. Do not float this pin."}, {"pin_number": "16", "pin_name": "GND", "pin_description": "Ground. Tie directly to local ground plane."}, {"pin_number": "17", "pin_name": "BG1", "pin_description": "Bottom Gate Drive. Drives the gates of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "18", "pin_name": "GATEVCC", "pin_description": "Power Supply for Gate Drivers. Must be connected to the INTVCC pin. Do not power from any other supply. Locally bypass to GND."}, {"pin_number": "19", "pin_name": "BG2", "pin_description": "Bottom Gate Drive. Drives the gates of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "20", "pin_name": "BOOST2", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST2 pin swings from a diode voltage below GATEVCC up to VOUT + GATEVCC."}, {"pin_number": "21", "pin_name": "TG2", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "22", "pin_name": "SW2", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "24", "pin_name": "SW1", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "26", "pin_name": "TG1", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "28", "pin_name": "BOOST1", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST1 pin swings from a diode voltage below GATEVCC up to VIN + GATEVCC."}, {"pin_number": "30", "pin_name": "EXTVCC", "pin_description": "External VCC Input. When EXTVCC exceeds 6.4V (typical), INTVCC will be powered from this pin. When EXTVCC is lower than 6.22V (typical), INTVCC will be powered from VIN."}, {"pin_number": "32", "pin_name": "CSNOUT", "pin_description": "The (–) Input to the Output Current Monitor Amplifier. Connect this pin to VOUT when not in use."}, {"pin_number": "34", "pin_name": "CSPOUT", "pin_description": "The (+) Input to the Output Current Monitor Amplifier. This pin and the CSNOUT pin measure the voltage across the sense resistor, RSENSE2, to provide the output current signals. Connect this pin to VOUT when not in use."}, {"pin_number": "36", "pin_name": "CSNIN", "pin_description": "The (–) Input to the Input Current Monitor Amplifier. This pin and the CSPIN pin measure the voltage across the sense resistor, RSENSE1, to provide the input current signals. Connect this pin to VIN when not in use."}, {"pin_number": "37", "pin_name": "CSPIN", "pin_description": "The (+) Input to the Input Current Monitor Amplifier. Connect this pin to VIN when not in use."}, {"pin_number": "38", "pin_name": "VIN", "pin_description": "Main Input Supply Pin. It must be locally bypassed to ground."}, {"pin_number": "39", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Tie directly to local ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT8705.pdf", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "80V", "min_input_voltage": "2.8V", "max_output_voltage": "80V", "min_output_voltage": "1.3V", "max_output_current": "外部器件决定", "max_switch_frequency": "400kHz", "quiescent_current": "2.65mA", "high_side_mosfet_resistance": "不适用(外部MOSFET)", "low_side_mosfet_resistance": "不适用(外部MOSFET)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode, DCM, CCM", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.3%", "output_reference_voltage": "1.207V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.5", "length": "9.8", "width": "4.4", "type": "DESCRIPTION", "pin_count": "2", "height": "1.2"}]}]