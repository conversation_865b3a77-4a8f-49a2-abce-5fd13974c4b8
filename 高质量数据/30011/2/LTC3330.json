{"part_number": "LTC3330", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "Nanopower Buck-Boost DC/DC with Energy Harvesting Battery Life Extender", "features": ["Dual Input, Single Output DC/DCs with Input Prioritizer", "Energy Harvesting Input: 3.0V to 19V Buck DC/DC", "Primary Cell Input: 1.8V to 5.5V Buck-Boost DC/DC", "Zero Battery IQ When Energy Harvesting Source is Available", "Ultralow Quiescent Current: 750nA at No-Load", "Low Noise LDO Post Regulator", "Integrated Supercapacitor Balancer", "Up to 50mA of Output Current", "Programmable DC/DC and LDO Output Voltages, Buck UVLO, and Buck-Boost Peak Input Current", "Integrated Low Loss Full-Wave Bridge Rectifier", "Input Protective Shunt: Up to 25mA at VIN ≥ 20V", "5mm × 5mm QFN-32 Package"], "description": "The LTC®3330 integrates a high voltage energy harvesting power supply plus a DC/DC converter powered by a primary cell battery to create a single output supply for alternative energy applications. The energy harvesting power supply, consisting of an integrated full-wave bridge rectifier and a high voltage buck converter, harvests energy from piezoelectric, solar, or magnetic sources. The primary cell input powers a buck-boost converter capable of operation down to 1.8V at its input. Either DC/DC converter can deliver energy to a single output. The buck operates when harvested energy is available, reducing the quiescent current draw on the battery to essentially zero, thereby extending the life of the battery. The buck-boost powers VOUT only when harvested energy goes away. A low noise LDO post regulator and a supercapacitor balancer are also integrated, accommodating a wide range of output storage configurations. Voltage and current settings for both inputs and outputs are programmable via pin-strapped logic inputs. The LTC3330 is available in a 5mm × 5mm QFN-32 package.", "applications": ["Energy Harvesting", "Solar Powered Systems with Primary Cell Backup", "Wireless HVAC Sensors and Security Devices", "Mobile Asset Tracking"], "ordering_information": [{"part_number": "LTC3330", "order_device": "LTC3330EUH#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3330", "order_device": "LTC3330EUH#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3330", "order_device": "LTC3330IUH#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3330", "order_device": "LTC3330IUH#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LTC3330", "package_type": "QFN-32", "pins": [{"pin_number": "1", "pin_name": "BAL", "pin_description": "Supercapacitor Balance Point. The common node of a stack of two supercapacitors is connected to BAL. A source/sink balancing current of up to 10mA is available. Tie BAL along with SCAP to GND to disable the balancer and its associated quiescent current."}, {"pin_number": "2", "pin_name": "SCAP", "pin_description": "Supply and Input for Supercapacitor Balancer. Tie the top of a 2-capacitor stack to SCAP and the middle of the stack to BAL to activate balancing. Tie SCAP along with BAL to GND to disable the balancer and its associated quiescent current."}, {"pin_number": "3", "pin_name": "VIN2", "pin_description": "Internal Low Voltage Rail to Serve as Gate Drive for Buck NMOS Switch. Connect a 4.7µF (or larger) capacitor from VIN2 to GND. This pin is not intended for use as an external system rail."}, {"pin_number": "4, 5, 6, 7", "pin_name": "UV3, UV2, UV1, UV0", "pin_description": "UVLO Select Bits for the Buck Switching Regulator. Tie high to VIN2 or low to GND to select the desired UVLO rising and falling thresholds (see Table 4). The UVLO falling threshold must be greater than the selected VOUT regulation level. Do not float."}, {"pin_number": "8", "pin_name": "AC1", "pin_description": "Input Connection for Piezoelectric Element, Other AC Source, or current limited DC source (used in conjunction with AC2 for differential AC inputs)."}, {"pin_number": "9", "pin_name": "AC2", "pin_description": "Input Connection for Piezoelectric Element, Other AC Source, or current limited DC source (used in conjunction with AC1 for differential AC inputs)."}, {"pin_number": "10", "pin_name": "VIN", "pin_description": "Rectified Input Voltage. A capacitor on this pin serves as an energy reservoir and input supply for the buck regulator. The VIN voltage is internally clamped to a maximum of 20V (typical)."}, {"pin_number": "11", "pin_name": "CAP", "pin_description": "Internal Rail Referenced to VIN to Serve as Gate Drive for Buck PMOS Switch. Connect a 1µF (or larger) capacitor between CAP and VIN. This pin is not intended for use as an external system rail."}, {"pin_number": "12", "pin_name": "SW", "pin_description": "Switch Node for the Buck Switching Regulator. Connect a 22µH or greater external inductor between this node and VOUT."}, {"pin_number": "13", "pin_name": "VOUT", "pin_description": "Regulated Output Voltage Derived from the Buck or Buck-Boost Switching Regulator."}, {"pin_number": "14", "pin_name": "SWB", "pin_description": "Switch Node for the Buck-Boost Switching Regulator. Connect an external inductor (value in Table 3) between this node and SWA."}, {"pin_number": "15", "pin_name": "SWA", "pin_description": "Switch Node for the Buck-Boost Switching Regulator. Connect an external inductor (value in Table 3) between this node and SWB."}, {"pin_number": "16", "pin_name": "BAT", "pin_description": "Battery Input. BAT serves as the input to the buck-boost switching regulator."}, {"pin_number": "17, 18, 19", "pin_name": "IPK0, IPK1, IPK2", "pin_description": "IPEAK_BB Select Bits for the Buck-Boost Switching Regulator. Tie high to VIN3 or low to GND to select the desired IPEAK_BB (see Table 3). Do not float."}, {"pin_number": "20", "pin_name": "LDO_OUT", "pin_description": "Regulated LDO Output. This output can be used as a quiet supply. One of the eight settings provides for a current limited switched output where LDO_OUT = LDO_IN."}, {"pin_number": "21", "pin_name": "LDO_IN", "pin_description": "Input Voltage for the LDO regulator."}, {"pin_number": "22, 23, 24", "pin_name": "LDO2, LDO1, LDO0", "pin_description": "LDO Voltage Select Bits. Tie high to LDO_IN or low to GND to select the desired LDO_OUT voltage (see Table 2). Do not float."}, {"pin_number": "25", "pin_name": "LDO_EN", "pin_description": "LDO Enable Input. Active high input with logic levels referenced to LDO_IN. Do not float."}, {"pin_number": "26", "pin_name": "VIN3", "pin_description": "Internal Low Voltage Rail Used by the Prioritizer. Logic high reference for IPK[2:0] and OUT[2:0]. Connect a 1µF (or larger) capacitor from VIN3 to GND. This pin is not intended for use as an external system rail."}, {"pin_number": "27", "pin_name": "PGLDO", "pin_description": "Power Good Output for LDO_OUT. Logic level output referenced to an internal maximum rail (see Operation). PGLDO transitioning high indicates 92% (typical) regulation has been reached on LDO_OUT. PGLDO remains high until LDO_OUT falls to 90% (typical) of the programmed regulation point."}, {"pin_number": "28", "pin_name": "PGVOUT", "pin_description": "Power Good Output for VOUT. Logic level output referenced to an internal maximum rail (see Operation). PGVOUT transitioning high indicates regulation has been reached on VOUT (VOUT = Sleep Rising). PGVOUT remains high until VOUT falls to 92% (typical) of the programmed regulation point."}, {"pin_number": "29", "pin_name": "EH_ON", "pin_description": "Switcher Status. Logic level output referenced to VIN3. EH_ON is high when the buck switching regulator is in use. It is pulled low when the buck-boost switching regulator is in use."}, {"pin_number": "30, 31, 32", "pin_name": "OUT0, OUT1, OUT2", "pin_description": "VOUT Voltage Select Bits. Tie high to VIN3 or low to GND to select the desired VOUT (see Table 1). Do not float."}, {"pin_number": "33", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. The exposed pad must be connected to a continuous ground plane on the second layer of the printed circuit board by several vias directly under the LTC3330."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3330 Datasheet Rev. C", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "19V", "min_input_voltage": "1.8V", "max_output_voltage": "5V", "min_output_voltage": "1.8V", "max_output_current": "50mA", "max_switch_frequency": "未找到", "quiescent_current": "750nA", "high_side_mosfet_resistance": "1400mΩ", "low_side_mosfet_resistance": "1200mΩ", "over_current_protection_threshold": "250mA", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "20V", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "pass_through_mode": "True", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2%", "output_reference_voltage": "未找到", "loop_control_mode": "迟滞模式控制"}, "package": [{"pitch": "0.5", "height": "0.75", "length": "5", "width": "1.8", "type": "DESCRIPTION", "pin_count": "8"}]}