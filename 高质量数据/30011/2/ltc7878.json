{"part_number": "LTC7878A", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "70V Parallelable 4-Switch Buck-Boost Controller with Inductor DCR Current Sensing", "features": ["Single Inductor Architecture Allows Vın Above, Below or Equal to the Regulated Vout", "Synchronous Rectification: Up to 98% Efficiency", "Wide VIN Voltage Range: 5V to 70V", "±1% Output Voltage Accuracy: 1V ≤ VOUT ≤ 70V", "DCR or RSENSE Current Sensing", "Peak Current Mode Control in Buck/Boost/Buck-Boost Mode", "Programmable Input or Output Current Regulation", "7V NMOS Gate Drivers", "Phase-Lockable Frequency (100kHz to 600kHz)", "Multiphase/Multi-ICs Parallel Operation", "Selectable Continuous or Pulse-Skipping Mode Operation and Inductor Peak Current Limits", "Small 32-Lead 5mm × 5mm QFN Package", "48-Lead 7mm x 7mm x 0.75mm LFCSP Package"], "description": "The LTC7878 is a high performance buck-boost switching regulator controller that operates from an input voltage above, below or equal to the output voltage. The constant-frequency, peak current mode architecture allows a phase-lockable switching frequency of up to 600kHz, while a programmable input or output current loop regulates the average input or output current accurately supporting battery charging applications. With a wide 5V to 70V input and output range and seamless, low noise transitions between operating regions, the LTC7878 is ideal for industrial and telecom systems.\nThe LTC7878 features forced continuous mode (FCM)/pulse-skipping mode operation. It supports multiphase/multi-ICs parallel operation for high power applications. Additional features include 7V N-Channel gate drivers, smart EXTVCC auxiliary supply and programmable inductor peak current limit. A PGOOD pin indicates when the output is within 10% of its designed set point.\nThe LTC7878 is available in both a low-profile, 32-lead QFN and a 48-lead LFCSP packages.", "applications": ["Industrial and Telecom Systems", "Distributed DC Power or Battery Systems", "Battery Backup Units (BBU)"], "ordering_information": [{"part_number": "LTC7878A", "order_device": "LTC7878AUH#PBF", "package_type": "QFN", "package_drawing_code": "UH32", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC7878A", "order_device": "LTC7878AUH#TRPBF", "package_type": "QFN", "package_drawing_code": "UH32", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC7878A", "order_device": "LTC7878AUK#PBF", "package_type": "LFCSP", "package_drawing_code": "UK48", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC7878A", "order_device": "LTC7878AUK#TRPBF", "package_type": "LFCSP", "package_drawing_code": "UK48", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LTC7878A", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "BG1", "pin_description": "Bottom Gate Driver Output. Drives the gate of the bottom N-Channel MOSFET between PGND and DRVCC."}, {"pin_number": "2", "pin_name": "VIN", "pin_description": "Main IC Supply. Power input to the internal LDO connect to DRVCC. Bypass this pin to PGND with a capacitor (0.1μF to 1μF) close to the pin."}, {"pin_number": "3", "pin_name": "DRVCC", "pin_description": "Gate Driver Current Supply LDO Output. The voltage on this pin is regulated to 7V from either VIN or EXTVCC during normal operation. Bypass this pin to PGND with a minimum of 4.7μF low ESR tantalum or ceramic capacitor."}, {"pin_number": "4", "pin_name": "EXTVCC", "pin_description": "External Power Input to an Internal LDO Connected to DRVCC. When the voltage on this pin is greater 8V and lower than the VIN pin voltage, this LDO bypasses the internal LDO powered from VIN. Bypass this pin to PGND with a capacitor (0.1μF to 1μF) close to the pin."}, {"pin_number": "5", "pin_name": "INTVCC", "pin_description": "Internal 5V Regulator Output. The control circuits are powered from this voltage. Bypass this pin to SGND with a minimum of 4.7μF low ESR tantalum or ceramic capacitor."}, {"pin_number": "6", "pin_name": "RUN", "pin_description": "Enable Control Input. A voltage above 1.22V turns on the IC. There is a 2μA pull-up current on this pin. Once the RUN pin rises above the 1.22V threshold, the pull-up increases to 6μA."}, {"pin_number": "7", "pin_name": "PGND", "pin_description": "Power Ground Pin. Connect this pin closely to the sources of bottom N-channel MOSFETs and negative terminal of VIN, DRVCC, EXTVCC bypass capacitors."}, {"pin_number": "8", "pin_name": "BG2", "pin_description": "Bottom Gate Driver Output. Drives the gate of the bottom N-Channel MOSFET between PGND and DRVCC."}, {"pin_number": "9", "pin_name": "TG2", "pin_description": "Top Gate Driver Output. This is the output of a floating driver with a voltage swing equal to DRVCC superimposed on the switching node voltage."}, {"pin_number": "10", "pin_name": "BOOST2", "pin_description": "Bootstrapped Supply to the Top Side Floating Driver. A capacitor is connected between the BOOST2 and SW2 pins and a <PERSON><PERSON><PERSON><PERSON> diode is tied between the BOOST2 and DRVCC pins. Voltage swing at the BOOST2 pin is from DRVCC to (VOUT + DRVCC)."}, {"pin_number": "11", "pin_name": "SW2", "pin_description": "Switch Node Connection. Normally connects to the inductor terminal. SW2 swings from a <PERSON><PERSON>tky diode voltage drop below ground up to VOUT."}, {"pin_number": "12", "pin_name": "PGOOD", "pin_description": "Power Good Inductor Output for the Regulated Output Voltage. Open-drain logic out that is pulled down to ground when the regulated output voltage exceeds ±10% regulation window."}, {"pin_number": "13", "pin_name": "SS", "pin_description": "Soft-Start Input. The voltage ramp rate at this pin sets the output voltage ramp rate. A capacitor to ground accomplishes soft-start. This pin has a 2.5μA pull-up current."}, {"pin_number": "14", "pin_name": "ITHB", "pin_description": "Fast Transient Current Control Threshold. Optional transient response improvement configure pin. If no use, short ITHB pin and ITH pin together."}, {"pin_number": "15", "pin_name": "ITH", "pin_description": "Current Control Threshold and Error Amplifier Compensation Point. The current comparator's threshold varies with the ITH control voltage."}, {"pin_number": "16", "pin_name": "VFB", "pin_description": "Output Voltage Sensing Error Amplifier Noninverting Input. This pin receives the remotely sensed feedback voltage from external resistor divider across the output voltage."}, {"pin_number": "17", "pin_name": "MODE/ILIM", "pin_description": "FCM/Pulse-Skipping Mode and Inductor Current Limit Program Pin. Sets operation mode and current limit."}, {"pin_number": "18", "pin_name": "PHASMD", "pin_description": "Phase Mode Program Pin. Sets the phase relationship between the internal oscillator clock and the output clock on the CLKOUT pin (180, 120, or 90 degrees)."}, {"pin_number": "19", "pin_name": "FREQ", "pin_description": "Frequency Set Pin. A resistor between this pin and SGND sets the switching frequency. This pin sources 10μA current."}, {"pin_number": "20", "pin_name": "CLKOUT", "pin_description": "Clock Output Pin. Use this pin to synchronize the switching frequency of multiple LTC7878 ICs for parallel operations. Signal swings is from INTVCC to ground."}, {"pin_number": "21", "pin_name": "SYNC", "pin_description": "Switching Frequency Synchronization Pin. Applying an external clock between 100kHz to 600kHz will cause the switching frequency to synchronize to the external clock."}, {"pin_number": "22", "pin_name": "SETCUR", "pin_description": "Average Current Regulation Pin. A resistor from this pin to SGND sets the maximum average input or output current sensed by the CSP and CSN pins. This pin sources 15μA current."}, {"pin_number": "23", "pin_name": "CSP", "pin_description": "Average Current Sensing Pin. The positive input of the internal rail-to-rail average current sense amplifier."}, {"pin_number": "24", "pin_name": "CSN", "pin_description": "Average Current Sensing Pin. The negative input of the internal rail-to-rail average current sense amplifier."}, {"pin_number": "25", "pin_name": "VOUT", "pin_description": "Floating Driver <PERSON><PERSON> in Boost Mode. Supply the bias current for the BOOST1 to SW1 driver circuitry when VOUT is much higher than VIN."}, {"pin_number": "26", "pin_name": "NC", "pin_description": "No Connect Pin."}, {"pin_number": "27", "pin_name": "ISNSN", "pin_description": "Negative Current Sense Comparator Input. The negative input of the current comparator is normally connected to the DCR sensing network."}, {"pin_number": "28", "pin_name": "ISNSP", "pin_description": "Positive Current Sense Comparator Input. This pin must be <PERSON><PERSON> connected to the inductor on the switching node SW1."}, {"pin_number": "29", "pin_name": "ISNSD", "pin_description": "DC Current Sense Comparator Input. These inputs amplify the DC portion of the sensed current signal to the IC's current comparator."}, {"pin_number": "30", "pin_name": "SW1", "pin_description": "Switch Node Connection. Normally connect to the inductor terminal. SW1 swings from a <PERSON><PERSON><PERSON>ky diode voltage drop below ground up to VIN."}, {"pin_number": "31", "pin_name": "BOOST1", "pin_description": "Bootstrapped Supply to the Top Side Floating Driver. A capacitor is connected between the BOOST1 and SW1 pins and a <PERSON><PERSON><PERSON><PERSON> diode is tied between the BOOST1 and DRVCC pins. Voltage swing at the BOOST1 pin is from DRVCC to (VIN + DRVCC)."}, {"pin_number": "32", "pin_name": "TG1", "pin_description": "Top Gate Driver Output. This is the output of a floating driver with a voltage swing equal to DRVCC superimposed on the switching node voltage."}, {"pin_number": "33", "pin_name": "SGND", "pin_description": "Signal Ground Pin. Must be soldered to PCB ground for rated thermal performance. All small signal components should be connected here."}]}, {"product_part_number": "LTC7878A", "package_type": "LFCSP", "pins": [{"pin_number": "1", "pin_name": "BG1", "pin_description": "Bottom Gate Driver Output. Drives the gate of the bottom N-Channel MOSFET between PGND and DRVCC."}, {"pin_number": "4", "pin_name": "VIN", "pin_description": "Main IC Supply. Power input to the internal LDO connect to DRVCC. Bypass this pin to PGND with a capacitor (0.1μF to 1μF) close to the pin."}, {"pin_number": "7", "pin_name": "DRVCC", "pin_description": "Gate Driver Current Supply LDO Output. The voltage on this pin is regulated to 7V from either VIN or EXTVCC during normal operation. Bypass this pin to PGND with a minimum of 4.7μF low ESR tantalum or ceramic capacitor."}, {"pin_number": "8", "pin_name": "EXTVCC", "pin_description": "External Power Input to an Internal LDO Connected to DRVCC. When the voltage on this pin is greater 8V and lower than the VIN pin voltage, this LDO bypasses the internal LDO powered from VIN. Bypass this pin to PGND with a capacitor (0.1μF to 1μF) close to the pin."}, {"pin_number": "9", "pin_name": "INTVCC", "pin_description": "Internal 5V Regulator Output. The control circuits are powered from this voltage. Bypass this pin to SGND with a minimum of 4.7μF low ESR tantalum or ceramic capacitor."}, {"pin_number": "10", "pin_name": "RUN", "pin_description": "Enable Control Input. A voltage above 1.22V turns on the IC. There is a 2μA pull-up current on this pin. Once the RUN pin rises above the 1.22V threshold, the pull-up increases to 6μA."}, {"pin_number": "11", "pin_name": "PGND", "pin_description": "Power Ground Pin. Connect this pin closely to the sources of bottom N-channel MOSFETs and negative terminal of VIN, DRVCC, EXTVCC bypass capacitors."}, {"pin_number": "12", "pin_name": "BG2", "pin_description": "Bottom Gate Driver Output. Drives the gate of the bottom N-Channel MOSFET between PGND and DRVCC."}, {"pin_number": "14", "pin_name": "TG2", "pin_description": "Top Gate Driver Output. This is the output of a floating driver with a voltage swing equal to DRVCC superimposed on the switching node voltage."}, {"pin_number": "15", "pin_name": "BOOST2", "pin_description": "Bootstrapped Supply to the Top Side Floating Driver. A capacitor is connected between the BOOST2 and SW2 pins and a <PERSON><PERSON><PERSON><PERSON> diode is tied between the BOOST2 and DRVCC pins. Voltage swing at the BOOST2 pin is from DRVCC to (VOUT + DRVCC)."}, {"pin_number": "16", "pin_name": "SW2", "pin_description": "Switch Node Connection. Normally connect to the inductor terminal. SW2 swings from a <PERSON><PERSON>tky diode voltage drop below ground up to VOUT."}, {"pin_number": "19", "pin_name": "PGOOD", "pin_description": "Power Good Inductor Output for the Regulated Output Voltage. Open-drain logic out that is pulled down to ground when the regulated output voltage exceeds ±10% regulation window."}, {"pin_number": "20", "pin_name": "SS", "pin_description": "Soft-Start Input. The voltage ramp rate at this pin sets the output voltage ramp rate. A capacitor to ground accomplishes soft-start. This pin has a 2.5μA pull-up current."}, {"pin_number": "21", "pin_name": "ITHB", "pin_description": "Fast Transient Current Control Threshold. Optional transient response improvement configure pin. If no use, short ITHB pin and ITH pin together."}, {"pin_number": "22", "pin_name": "ITH", "pin_description": "Current Control Threshold and Error Amplifier Compensation Point. The current comparator's threshold varies with the ITH control voltage."}, {"pin_number": "25", "pin_name": "VFB", "pin_description": "Output Voltage Sensing Error Amplifier Noninverting Input. This pin receives the remotely sensed feedback voltage from external resistor divider across the output voltage."}, {"pin_number": "26", "pin_name": "MODE/ILIM", "pin_description": "FCM/Pulse-Skipping Mode and Inductor Current Limit Program Pin. Sets operation mode and current limit."}, {"pin_number": "27", "pin_name": "PHASMD", "pin_description": "Phase Mode Program Pin. Sets the phase relationship between the internal oscillator clock and the output clock on the CLKOUT pin (180, 120, or 90 degrees)."}, {"pin_number": "28", "pin_name": "FREQ", "pin_description": "Frequency Set Pin. A resistor between this pin and SGND sets the switching frequency. This pin sources 10μA current."}, {"pin_number": "29", "pin_name": "CLKOUT", "pin_description": "Clock Output Pin. Use this pin to synchronize the switching frequency of multiple LTC7878 ICs for parallel operations. Signal swings is from INTVCC to ground."}, {"pin_number": "30", "pin_name": "SYNC", "pin_description": "Switching Frequency Synchronization Pin. Applying an external clock between 100kHz to 600kHz will cause the switching frequency to synchronize to the external clock."}, {"pin_number": "31", "pin_name": "SETCUR", "pin_description": "Average Current Regulation Pin. A resistor from this pin to SGND sets the maximum average input or output current sensed by the CSP and CSN pins. This pin sources 15μA current."}, {"pin_number": "34", "pin_name": "CSP", "pin_description": "Average Current Sensing Pin. The positive input of the internal rail-to-rail average current sense amplifier."}, {"pin_number": "35", "pin_name": "CSN", "pin_description": "Average Current Sensing Pin. The negative input of the internal rail-to-rail average current sense amplifier."}, {"pin_number": "38", "pin_name": "VOUT", "pin_description": "Floating Driver <PERSON><PERSON> in Boost Mode. Supply the bias current for the BOOST1 to SW1 driver circuitry when VOUT is much higher than VIN."}, {"pin_number": "41", "pin_name": "ISNSN", "pin_description": "Negative Current Sense Comparator Input. The negative input of the current comparator is normally connected to the DCR sensing network."}, {"pin_number": "42", "pin_name": "ISNSP", "pin_description": "Positive Current Sense Comparator Input. This pin must be <PERSON><PERSON> connected to the inductor on the switching node SW1."}, {"pin_number": "43", "pin_name": "ISNSD", "pin_description": "DC Current Sense Comparator Input. These inputs amplify the DC portion of the sensed current signal to the IC's current comparator."}, {"pin_number": "45", "pin_name": "SW1", "pin_description": "Switch Node Connection. Normally connect to the inductor terminal. SW1 swings from a <PERSON><PERSON><PERSON>ky diode voltage drop below ground up to VIN."}, {"pin_number": "46", "pin_name": "BOOST1", "pin_description": "Bootstrapped Supply to the Top Side Floating Driver. A capacitor is connected between the BOOST1 and SW1 pins and a <PERSON><PERSON><PERSON><PERSON> diode is tied between the BOOST1 and DRVCC pins. Voltage swing at the BOOST1 pin is from DRVCC to (VIN + DRVCC)."}, {"pin_number": "47", "pin_name": "TG1", "pin_description": "Top Gate Driver Output. This is the output of a floating driver with a voltage swing equal to DRVCC superimposed on the switching node voltage."}, {"pin_number": "49", "pin_name": "SGND", "pin_description": "Signal Ground Pin. Must be soldered to PCB ground for rated thermal performance. All small signal components should be connected here."}, {"pin_number": "2, 3, 5, 6, 13, 17, 18, 23, 24, 32, 33, 36, 37, 39, 40, 44, 48", "pin_name": "NC", "pin_description": "No Connect Pin."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC7878 <PERSON><PERSON><PERSON>, Rev. B", "family_comparison": "Comparison with related parts like LTC3789, LTC3779, LT8705A available, differing in voltage ranges and features.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "70V", "min_input_voltage": "5V", "max_output_voltage": "70V", "min_output_voltage": "1V", "max_output_current": "10A", "max_switch_frequency": "0.6MHz", "quiescent_current": "70μA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "Adjustable", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Pulse-Skipping, CCM", "power_good_indicator": "Yes", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "1V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.5", "height": "0.75", "length": "5", "width": "1.", "type": "DESCRIPTION", "pin_count": "2"}]}