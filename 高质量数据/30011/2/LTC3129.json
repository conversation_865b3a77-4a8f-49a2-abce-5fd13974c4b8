{"part_number": "LTC3129", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "15V, 200mA Synchronous Buck-Boost DC/DC Converter with 1.3µA Quiescent Current", "features": ["Regulates Vout Above, Below or Equal to Vin", "Wide VIN Range: 2.42V to 15V, 1.92V to 15V After Start-Up (Bootstrapped)", "Wide Vout Range: 1.4V to 15.75V", "200mA Output Current in Buck Mode", "Single Inductor", "1.3µA Quiescent Current", "Programmable Maximum Power Point Control", "1.2MHz Ultralow Noise PWM", "Current Mode Control", "Pin Selectable Burst Mode® Operation", "Up to 95% Efficiency", "Accurate RUN Pin Threshold", "Power Good Indicator", "10nA Shutdown Current", "Thermally Enhanced 3mm × 3mm QFN and 16-Lead MSOP Packages"], "description": "The LTC3129 is a high efficiency, 200mA buck-boost DC/DC converter with a wide Vın and Vout range. It includes an accurate RUN pin threshold to allow predictable regulator turn-on and a maximum power point control (MPPC) capability that ensures maximum power extraction from non-ideal power sources such as photovoltaic panels.\nThe LTC3129 employs an ultralow noise, 1.2MHz PWM switching architecture that minimizes solution footprint by allowing the use of tiny, low profile inductors and ceramic capacitors. Built-in loop compensation and soft-start simplify the design. For high efficiency operation at light loads, automatic Burst Mode operation can be selected, reducing the quiescent current to just 1.3µA.\nAdditional features include a power good output, less than 10nA of shutdown current and thermal shutdown.\nThe LTC3129 is available in thermally enhanced 3mm × 3mm QFN and 16-lead MSOP packages. For fixed output voltage options, see the functionally equivalent LTC3129-1, which eliminates the need for an external feedback divider.", "applications": ["Industrial Wireless Sensor Nodes", "Post-Regulator for Harvested Energy", "Solar Panel Post-Regulator/Charger", "Intrinsically Safe Power Supplies", "Wireless Microphones", "Avionics-Grade Wireless Headsets"], "ordering_information": [{"part_number": "LTC3129", "order_device": "LTC3129EUD#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1700 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3129", "order_device": "LTC3129EUD#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1700 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3129", "order_device": "LTC3129IUD#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1700 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3129", "order_device": "LTC3129IUD#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1700 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3129", "order_device": "LTC3129EMSE#PBF", "package_type": "MSOP", "package_drawing_code": "05-08-1667 Rev F", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3129", "order_device": "LTC3129EMSE#TRPBF", "package_type": "MSOP", "package_drawing_code": "05-08-1667 Rev F", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3129", "order_device": "LTC3129IMSE#PBF", "package_type": "MSOP", "package_drawing_code": "05-08-1667 Rev F", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3129", "order_device": "LTC3129IMSE#TRPBF", "package_type": "MSOP", "package_drawing_code": "05-08-1667 Rev F", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LTC3129", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "BST1", "pin_description": "Bootstrapped Floating Supply for High Side NMOS Gate Drive. Connect to SW1 through a 22nF capacitor, as close to the part as possible. The value is not critical. Any value from 4.7nF to 47nF may be used."}, {"pin_number": "2", "pin_name": "VIN", "pin_description": "Input Voltage for the Converter. Connect a minimum of 4.7µF ceramic decoupling capacitor from this pin to the ground plane, as close to the pin as possible."}, {"pin_number": "3", "pin_name": "VCC", "pin_description": "Output voltage of the internal voltage regulator. This is the supply pin for the internal circuitry. Bypass this output with a minimum of 2.2µF ceramic capacitor close to the pin. This pin may be back-driven by an external supply, up to a maximum of 5.5V."}, {"pin_number": "4", "pin_name": "RUN", "pin_description": "Input to the Run Comparator. Pull this pin above 1.1V to enable the VCC regulator and above 1.28V to enable the converter. Connecting this pin to a resistor divider from VIN to ground allows programming a VIN start threshold higher than the 1.8V (typical) VIN UVLO threshold."}, {"pin_number": "5", "pin_name": "MPPC", "pin_description": "Maximum Power Point Control Programming Pin. Connect this pin to a resistor divider from VIN to ground to enable the MPPC functionality. If this function is not needed, tie the pin to VCC."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Signal Ground. Provide a short direct PCB path between GND and the ground plane where the exposed pad is soldered."}, {"pin_number": "7", "pin_name": "FB", "pin_description": "Feedback Input to the Error Amplifier. Connect to a resistor divider from VOUT to ground. The output voltage can be adjusted from 1.4V to 15.75V."}, {"pin_number": "8, 9", "pin_name": "NC", "pin_description": "Unused. These pins should be grounded."}, {"pin_number": "10", "pin_name": "PWM", "pin_description": "Mode Select Pin. PWM = Low (ground): Enables automatic Burst Mode operation. PWM = High (tie to VCC): Fixed frequency PWM operation. This pin should not be allowed to float. It has an internal 5M pull-down resistor."}, {"pin_number": "11", "pin_name": "PGOOD", "pin_description": "Open drain output that pulls to ground when FB drops too far below its regulated voltage. Connect a pull-up resistor from this pin to a positive supply. This pin can sink up to the absolute maximum rating of 15mA when low."}, {"pin_number": "12", "pin_name": "VOUT", "pin_description": "Output voltage of the converter. Connect a minimum value of 4.7µF ceramic capacitor from this pin to the ground plane, as close to the pin as possible."}, {"pin_number": "13", "pin_name": "BST2", "pin_description": "Bootstrapped floating supply for high side NMOS gate drive. Connect to SW2 through a 22nF capacitor, as close to the part as possible. The value is not critical. Any value from 4.7nF to 47nF may be used."}, {"pin_number": "14", "pin_name": "SW2", "pin_description": "Switch Pin. Connect to one side of the inductor. Keep PCB trace lengths as short and wide as possible to reduce EMI."}, {"pin_number": "15, Exposed Pad 17", "pin_name": "PGND", "pin_description": "Power Ground. Provide a short direct PCB path between PGND and the ground plane. The exposed pad must also be soldered to the PCB ground plane."}, {"pin_number": "16", "pin_name": "SW1", "pin_description": "Switch Pin. Connect to one side of the inductor. Keep PCB trace lengths as short and wide as possible to reduce EMI."}]}, {"product_part_number": "LTC3129", "package_type": "MSOP", "pins": [{"pin_number": "1", "pin_name": "VCC", "pin_description": "Output voltage of the internal voltage regulator. This is the supply pin for the internal circuitry. Bypass this output with a minimum of 2.2µF ceramic capacitor close to the pin. This pin may be back-driven by an external supply, up to a maximum of 5.5V."}, {"pin_number": "2", "pin_name": "RUN", "pin_description": "Input to the Run Comparator. Pull this pin above 1.1V to enable the VCC regulator and above 1.28V to enable the converter. Connecting this pin to a resistor divider from VIN to ground allows programming a VIN start threshold higher than the 1.8V (typical) VIN UVLO threshold."}, {"pin_number": "3", "pin_name": "MPPC", "pin_description": "Maximum Power Point Control Programming Pin. Connect this pin to a resistor divider from VIN to ground to enable the MPPC functionality. If this function is not needed, tie the pin to VCC."}, {"pin_number": "4", "pin_name": "GND", "pin_description": "Signal Ground. Provide a short direct PCB path between GND and the ground plane where the exposed pad is soldered."}, {"pin_number": "5", "pin_name": "FB", "pin_description": "Feedback Input to the Error Amplifier. Connect to a resistor divider from VOUT to ground. The output voltage can be adjusted from 1.4V to 15.75V."}, {"pin_number": "6, 7", "pin_name": "NC", "pin_description": "Unused. These pins should be grounded."}, {"pin_number": "8", "pin_name": "PWM", "pin_description": "Mode Select Pin. PWM = Low (ground): Enables automatic Burst Mode operation. PWM = High (tie to VCC): Fixed frequency PWM operation. This pin should not be allowed to float. It has an internal 5M pull-down resistor."}, {"pin_number": "9", "pin_name": "PGOOD", "pin_description": "Open drain output that pulls to ground when FB drops too far below its regulated voltage. Connect a pull-up resistor from this pin to a positive supply. This pin can sink up to the absolute maximum rating of 15mA when low."}, {"pin_number": "10", "pin_name": "VOUT", "pin_description": "Output voltage of the converter. Connect a minimum value of 4.7µF ceramic capacitor from this pin to the ground plane, as close to the pin as possible."}, {"pin_number": "11", "pin_name": "BST2", "pin_description": "Bootstrapped floating supply for high side NMOS gate drive. Connect to SW2 through a 22nF capacitor, as close to the part as possible. The value is not critical. Any value from 4.7nF to 47nF may be used."}, {"pin_number": "12", "pin_name": "SW2", "pin_description": "Switch Pin. Connect to one side of the inductor. Keep PCB trace lengths as short and wide as possible to reduce EMI."}, {"pin_number": "13, Exposed Pad 17", "pin_name": "PGND", "pin_description": "Power Ground. Provide a short direct PCB path between PGND and the ground plane. The exposed pad must also be soldered to the PCB ground plane."}, {"pin_number": "14", "pin_name": "SW1", "pin_description": "Switch Pin. Connect to one side of the inductor. Keep PCB trace lengths as short and wide as possible to reduce EMI."}, {"pin_number": "15", "pin_name": "BST1", "pin_description": "Bootstrapped Floating Supply for High Side NMOS Gate Drive. Connect to SW1 through a 22nF capacitor, as close to the part as possible. The value is not critical. Any value from 4.7nF to 47nF may be used."}, {"pin_number": "16", "pin_name": "VIN", "pin_description": "Input Voltage for the Converter. Connect a minimum of 4.7µF ceramic decoupling capacitor from this pin to the ground plane, as close to the pin as possible."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3129 Datasheet, Rev C", "family_comparison": "The 'RELATED PARTS' table on page 30 lists other power management ICs from Linear Technology with their key specifications, serving as a selection guide for similar applications.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "15V", "min_input_voltage": "1.92V", "max_output_voltage": "15.75V", "min_output_voltage": "1.4V", "max_output_current": "0.2A", "max_switch_frequency": "1.4MHz", "quiescent_current": "1.3µA", "high_side_mosfet_resistance": "750mΩ", "low_side_mosfet_resistance": "750mΩ", "over_current_protection_threshold": "0.5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "Fold Back", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.1%", "output_reference_voltage": "1.175V", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.5", "length": "4.9", "width": "3.0", "type": "DESCRIPTION", "pin_count": "2", "height": "1.1"}]}