{"part_number": "LT3154", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "5.5V 6A Low-IQ Monolithic Buck-Boost DC/DC Converter", "features": ["Single Inductor Buck-Boost Architecture", "Low Noise Buck-Boost Architecture", "Wide VIN Range 1.8V to 5.5V", "Adjustable Vout Range 1.8V to 5.5V", "5A of Continuous Output Current with VIN > VOUT", "Up to 97% Efficiency", "Selectable Internal 2.2MHz Fixed Frequency Adjustable and Synchronizable: Up to 4MHz", "Burst Mode® IQ 17µA for High Efficiency at Light Loads", "Programmable Soft-Start and VIN UVLO", "Simple Solution with Minimal External Components", "Small 3mm x 3mm × 0.74mm LQFN package"], "description": "The LT®3154 is a highly efficient, high current, buck-boost DC/DC converter that operates from input voltages above, below or equal to the regulated output voltage. The LT3154's advanced topology provides a continuous transfer through all operating modes. Vın operation from 1.8V to 5.5V provides flexibility for a wide variety of power sources. The output voltage is adjustable between 1.8V to 5.5V. To minimize external components the LT3154 can be configured to operate from a 2.2MHz internal oscillator. To optimize applications for highest efficiency or smallest footprint the oscillator can be programmed between 500kHz to 4MHz, or synchronized to an external clock for noise sensitive circuits. Selectable Burst Mode operation reduces quiescent current to 17µA, ensuring high efficiency across the entire load range. The Vın start-up threshold (UVLO) can be adjusted for various input sources. An internal or externally programmable soft-start limits inrush current during start-up. Other features include <1µA shutdown current, short circuit, and thermal overload protection. The LT3154 is housed in the thermally enhanced 16-lead (3mm x 3mm × 0.74mm) LQFN package.", "applications": ["Portable Inventory Terminals", "Handheld Computers", "Medical and Industrial Instruments", "Wireless RF Transmitter", "Backup Power Applications", "Battery Powered Systems"], "ordering_information": [{"part_number": "LT3154", "order_device": "LT3154AV#PBF", "package_type": "LQFN", "package_drawing_code": "LGA 16 0817 REV Ø", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT3154", "order_device": "LT3154AV#TRPBF", "package_type": "LQFN", "package_drawing_code": "LGA 16 0817 REV Ø", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LT3154", "package_type": "LQFN", "pins": [{"pin_number": "1, 2", "pin_name": "PVIN", "pin_description": "Power Input for Buck-Boost Converter. Connect a minimum 22µF low ESR capacitor to PGND as close to the device as possible. This pin must be connected to VIN in application."}, {"pin_number": "3", "pin_name": "VIN", "pin_description": "Signal Input Voltage. Decouple with minimum 1µF capacitor. Low noise input for control circuitry."}, {"pin_number": "4", "pin_name": "EN/UVLO", "pin_description": "Input to Enable the IC. Connect EN/UVLO to VIN to enable the LT3154 at the 1.8V minimum operating voltage. Connect to an external divider from VIN to provide a programmable accurate VIN undervoltage threshold."}, {"pin_number": "5", "pin_name": "SYNC/MODE", "pin_description": "Burst Mode operation Select and Oscillator Synchronization. Do not leave this pin floating. SYNC/MODE = High (VIN) disables Burst Mode. SYNC/MODE = Low (GND) enables Automatic Burst Mode. SYNC/MODE = External CLK synchronizes the internal oscillator."}, {"pin_number": "6", "pin_name": "RT", "pin_description": "Oscillator Frequency Programming Input. Connect to Vin for 2.2MHz fixed frequency operation. Connect an external resistor from RT to GND to program the switching frequency."}, {"pin_number": "7", "pin_name": "GND", "pin_description": "Signal Ground. Low noise ground for control circuits."}, {"pin_number": "8", "pin_name": "SS", "pin_description": "External Soft Start. Connect to VIN for 2.2ms default soft-start period. Connect an external capacitor to set soft start period."}, {"pin_number": "9", "pin_name": "FB", "pin_description": "Feedback Input to Error Amplifier. The resistor divider connected to this pin sets the converter output voltage."}, {"pin_number": "10", "pin_name": "VC", "pin_description": "Voltage error amplifier output VC is used to program average inductor current. An R-C from this pin to ground sets the voltage loop compensation."}, {"pin_number": "11", "pin_name": "VOUT", "pin_description": "Signal Output Voltage. Decouple with minimum 1µF capacitor. Low noise input for Vout control circuitry. Vout must be connected to PVout at the IC."}, {"pin_number": "12", "pin_name": "PVOUT", "pin_description": "Power Output for Buck-Boost Converter. Connect a minimum 68µF low ESR capacitor to PGND as close to the device as possible. This pin must be connected to Vout in application."}, {"pin_number": "13", "pin_name": "SW2", "pin_description": "Buck-Boost Converter Switch Pin. Connect inductor between SW1 and SW2 pins."}, {"pin_number": "14, 15, 17-Expo<PERSON> Pad", "pin_name": "PGND", "pin_description": "Power Ground Connection. These pins and exposed thermal pad must make full connection to PCB ground plane to meet specified thermal requirements. PGND must be connected to the GND pin in the application."}, {"pin_number": "16", "pin_name": "SW1", "pin_description": "Buck-Boost Converter Switch Pin. Connect inductor between SW1 and SW2 pins."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT3154 Datasheet", "family_comparison": "Related parts include LTC3533, <PERSON>TC3113, LTC3112, LT3120, ADP2503/ADP2504 with varying input/output voltages, currents, and quiescent currents.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.5V", "min_output_voltage": "1.8V", "max_output_current": "6A", "max_switch_frequency": "4MHz", "quiescent_current": "17µA", "high_side_mosfet_resistance": "25mΩ", "low_side_mosfet_resistance": "18mΩ", "over_current_protection_threshold": "9.7A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.02%", "output_reference_voltage": "0.99V", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.5", "height": "0.74", "length": "2.2", "width": "5.5", "type": "DESCRIPTION", "pin_count": "2"}]}