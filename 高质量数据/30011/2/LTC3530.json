{"part_number": "LTC3530", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "Wide Input Voltage Synchronous Buck-Boost DC/DC Converter", "features": ["Regulated Output with Input Voltages Above, Below or Equal to the Output", "1.8V to 5.5V Input and 1.8V to 5.25V Output Range", "250mA Continuous Output Current from 1.8V VIN", "600mA Continuous/1A Peak Output Current from Li-Ion", "Single Inductor", "Synchronous Rectification: Up to 96% Efficiency", "Programmable Automatic Burst Mode® Operation", "Output Disconnect in Shutdown", "Pin Compatible with the LTC3440", "Programmable Frequency from 300kHz to 2MHz", "<1μA Shutdown Current", "Small Thermally Enhanced 10-Lead (3mm × 3mm) DFN and 10-Lead MS Packages"], "description": "The LTC®3530 is a wide VIN range, highly efficient, fixed frequency, buck-boost DC/DC converter that operates from input voltages above, below or equal to the output voltage. The topology incorporated in the IC provides a continuous transfer function through all operating modes, making the product ideal for single lithium-ion, two-cell alkaline or NiMH applications where the output voltage is within the battery voltage range.\nThe LTC3530 is pin compatible with the LTC3440 buck-boost DC/DC converter but adds programmable automatic Burst Mode operation and extends the VIN/VOUT range to 1.8V. Switching frequencies up to 2MHz are programmed with an external resistor. Automatic Burst Mode operation allows the user to program the load current threshold for Burst Mode operation using a single resistor from the BURST pin to GND.\nOther features include 1μA shutdown, short circuit protection, programmable soft-start control, current limit and thermal shutdown. The LTC3530 is available in a thermally enhanced 10-lead (3mm × 3mm) DFN or MSOP package.", "applications": ["MP3 Players", "Handheld Instruments", "Digital Cameras", "Smart Phones", "Portable GPS Units", "Miniature Hard Disk Drive Power"], "ordering_information": [{"part_number": "LTC3530", "order_device": "LTC3530EDD#PBF", "package_type": "DFN", "package_drawing_code": "05-08-1695", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3530", "order_device": "LTC3530EDD#TRPBF", "package_type": "DFN", "package_drawing_code": "05-08-1695", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3530", "order_device": "LTC3530EMS#PBF", "package_type": "MSOP", "package_drawing_code": "05-08-1661", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3530", "order_device": "LTC3530EMS#TRPBF", "package_type": "MSOP", "package_drawing_code": "05-08-1661", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "LTC3530", "package_type": "DFN/MSOP", "pins": [{"pin_number": "1", "pin_name": "RT", "pin_description": "Programs the Frequency of the Internal Oscillator. Connect a resistor from RT to ground. f(kHz) = 33,170/RT (kΩ)"}, {"pin_number": "2", "pin_name": "BURST", "pin_description": "Used to Set the Automatic Burst Mode Threshold. Connect a resistor and capacitor in parallel from this pin to ground. See the Applications Information section for component value selection. For manual control, ground the pin to force Burst Mode operation, connect to VIN to force fixed frequency PWM mode."}, {"pin_number": "3", "pin_name": "SW1", "pin_description": "Switch Pin Where the Internal Switches A and B are Connected. Connect inductor from SW1 to SW2. An optional Schottky diode can be connected from SW1 to ground for a moderate efficiency improvement. Minimize trace length to keep EMI down."}, {"pin_number": "4", "pin_name": "SW2", "pin_description": "Switch Pin Where the Internal Switches C and D are Connected. For applications with output voltages over 4.3V, a Schottky diode is required from SW2 to VOUT to ensure the SW pin does not exhibit excessive voltage."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Ground for the IC."}, {"pin_number": "6", "pin_name": "VOUT", "pin_description": "Output of the Synchronous Rectifier. A filter capacitor is placed from VOUT to GND. A ceramic bypass capacitor is recommended as close to the VOUT and GND pins as possible."}, {"pin_number": "7", "pin_name": "VIN", "pin_description": "Input Supply Voltage. Internal VCC for the IC. A 10μF ceramic capacitor is recommended as close to the VIN and GND pins as possible."}, {"pin_number": "8", "pin_name": "SHDN/SS", "pin_description": "Combined Soft-Start and Shutdown. Applied voltage <0.4V shuts down the IC. Tie to >1.4V to enable the IC and >1.6V to ensure the error amp is not clamped from soft-start. An R-C from the shutdown command signal to this pin will provide a soft-start function by limiting the rise time of VC."}, {"pin_number": "9", "pin_name": "FB", "pin_description": "Feedback Pin. Connect resistor divider tap here. The output voltage can be adjusted from 1.8V to 5.25V. The feedback reference is typically 1.215V."}, {"pin_number": "10", "pin_name": "VC", "pin_description": "Error Amp Output. An R-C network is connected from this pin to FB for loop compensation. Refer to “Closing the Feedback Loop” section for component selection guidelines. During Burst Mode operation, VC is internally clamped."}, {"pin_number": "11", "pin_name": "Exposed Pad", "pin_description": "Ground. This pin must be soldered to the PCB and electrically connected to ground."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3530 Datasheet, Rev B (2006)", "family_comparison": "有", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.25V", "min_output_voltage": "1.8V", "max_output_current": "0.6A", "max_switch_frequency": "2MHz", "quiescent_current": "40µA", "high_side_mosfet_resistance": "240mΩ", "low_side_mosfet_resistance": "210mΩ", "over_current_protection_threshold": "2A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "1.215V", "loop_control_mode": "电压模式"}, "package": [{"pitch": "0.5", "height": "0.75", "length": "3", "width": "2.", "type": "DESCRIPTION", "pin_count": "9"}]}