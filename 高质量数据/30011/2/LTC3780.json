{"part_number": "LTC3780", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "High Efficiency, Synchronous, 4-<PERSON><PERSON> <PERSON>-Boost Controller", "features": ["Single Inductor Architecture Allows VIN Above, Below or Equal to VOUT", "Wide VIN Range: 4V to 36V Operation", "Synchronous Rectification: Up to 98% Efficiency", "Current Mode Control", "±1% Output Voltage Accuracy: 0.8V < VOUT < 30V", "Phase-Lockable Fixed Frequency: 200kHz to 400kHz", "Power Good Output Voltage Monitor", "Internal LDO for MOSFET Supply", "Quad N-Channel MOSFET Synchronous Drive", "VOUT Disconnected from VIN During Shutdown", "Adjustable Soft-Start Current Ramping", "Foldback Output Current Limiting", "Selectable Low Current Modes", "Output Overvoltage Protection", "Available in 24-Lead SSOP and Exposed Pad (5mm x 5mm) 32-Lead QFN Packages"], "description": "The LTC®3780 is a high performance buck-boost switching regulator controller that operates from input voltages above, below or equal to the output voltage. The constant frequency current mode architecture allows a phase-lockable frequency of up to 400kHz. With a wide 4V to 30V (36V maximum) input and output range and seamless transfers between operating modes, the LTC3780 is ideal for automotive, telecom and battery-powered systems. The operating mode of the controller is determined through the FCB pin. For boost operation, the FCB mode pin can select among Burst Mode® operation, discontinuous mode and forced continuous mode. During buck operation, the FCB mode pin can select among skip-cycle mode, discontinuous mode and forced continuous mode. Burst Mode operation and skip-cycle mode provide high efficiency operation at light loads while forced continuous mode and discontinuous mode operate at a constant frequency. Fault protection is provided by an output overvoltage comparator and internal foldback current limiting. A power good output pin indicates when the output is within 7.5% of its designed set point.", "applications": ["Automotive Systems", "Telecom Systems", "DC Power Distribution Systems", "High Power Battery-Operated Devices", "Industrial Control"], "ordering_information": [{"part_number": "LTC3780", "order_device": "LTC3780EG#PBF", "package_type": "SSOP", "package_drawing_code": "G24 SSOP 0204", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3780", "order_device": "LTC3780EG#TRPBF", "package_type": "SSOP", "package_drawing_code": "G24 SSOP 0204", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3780", "order_device": "LTC3780IG#PBF", "package_type": "SSOP", "package_drawing_code": "G24 SSOP 0204", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3780", "order_device": "LTC3780IG#TRPBF", "package_type": "SSOP", "package_drawing_code": "G24 SSOP 0204", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3780", "order_device": "LTC3780MPG#PBF", "package_type": "SSOP", "package_drawing_code": "G24 SSOP 0204", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "125"}, {"part_number": "LTC3780", "order_device": "LTC3780MPG#TRPBF", "package_type": "SSOP", "package_drawing_code": "G24 SSOP 0204", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "125"}, {"part_number": "LTC3780", "order_device": "LTC3780EUH#PBF", "package_type": "QFN", "package_drawing_code": "(UH32) QFN 0406 REV D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3780", "order_device": "LTC3780EUH#TRPBF", "package_type": "QFN", "package_drawing_code": "(UH32) QFN 0406 REV D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3780", "order_device": "LTC3780IUH#PBF", "package_type": "QFN", "package_drawing_code": "(UH32) QFN 0406 REV D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3780", "order_device": "LTC3780IUH#TRPBF", "package_type": "QFN", "package_drawing_code": "(UH32) QFN 0406 REV D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LTC3780", "package_type": "SSOP", "pins": [{"pin_number": "1", "pin_name": "PGOOD", "pin_description": "Open-Drain Logic Output. PGOOD is pulled to ground when the output voltage is not within ±7.5% of the regulation point."}, {"pin_number": "2", "pin_name": "SS", "pin_description": "Soft-start reduces the input power sources’ surge currents by gradually increasing the controller’s current limit. A minimum value of 6.8nF is recommended on this pin."}, {"pin_number": "3", "pin_name": "SENSE+", "pin_description": "The (+) Input to the Current Sense and Reverse Current Detect Comparators. The ITH pin voltage and built-in offsets between SENSE– and SENSE+ pins, in conjunction with RSENSE, set the current trip threshold."}, {"pin_number": "4", "pin_name": "SENSE-", "pin_description": "The (–) Input to the Current Sense and Reverse Current Detect Comparators."}, {"pin_number": "5", "pin_name": "ITH", "pin_description": "Current Control Threshold and Error Amplifier Compensation Point. The current comparator threshold increases with this control voltage. The voltage ranges from 0V to 2.4V."}, {"pin_number": "6", "pin_name": "VOSENSE", "pin_description": "Error Amplifier Feedback Input. This pin connects the error amplifier input to an external resistor divider from VOUT."}, {"pin_number": "7", "pin_name": "SGND", "pin_description": "Signal Ground. All small-signal components and compensation components should connect to this ground, which should be connected to PGND at a single point."}, {"pin_number": "8", "pin_name": "RUN", "pin_description": "Run Control Input. Forcing the RUN pin below 1.5V causes the IC to shut down the switching regulator circuitry. There is a 100k resistor between the RUN pin and SGND in the IC. Do not apply >6V to this pin."}, {"pin_number": "9", "pin_name": "FCB", "pin_description": "Forced Continuous Control Input. The voltage applied to this pin sets the operating mode of the controller."}, {"pin_number": "10", "pin_name": "PLLFLTR", "pin_description": "The phase-locked loop’s lowpass filter is tied to this pin. Alternatively, this pin can be driven with an AC or DC voltage source to vary the frequency of the internal oscillator."}, {"pin_number": "11", "pin_name": "PLLIN", "pin_description": "External Synchronization Input to Phase Detector. This pin is internally terminated to SGND with 50kΩ."}, {"pin_number": "12", "pin_name": "STBYMD", "pin_description": "LDO Control Pin. Determines whether the internal LDO remains active when the controller is shut down."}, {"pin_number": "13", "pin_name": "BOOST2", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor CB connects here. The BOOST2 pin swings from a diode voltage below INTVCC up to VIN + INTVCC."}, {"pin_number": "14", "pin_name": "TG2", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFET with a voltage swing equal to INTVCC superimposed on the switch node voltage SW."}, {"pin_number": "15", "pin_name": "SW2", "pin_description": "Switch Node. The (–) terminal of the bootstrap capacitor CB connects here. The SW2 pin swings from a Schottky diode (external) voltage drop below ground up to VIN."}, {"pin_number": "16", "pin_name": "BG2", "pin_description": "Bottom Gate Drive. Drives the gate of the bottom N-channel MOSFET between ground and INTVCC."}, {"pin_number": "17", "pin_name": "PGND", "pin_description": "Power Ground. Connect this pin closely to the source of the bottom N-channel MOSFET, the (–) terminal of CVCC and the (–) terminal of CIN."}, {"pin_number": "18", "pin_name": "BG1", "pin_description": "Bottom Gate Drive. Drives the gate of the bottom N-channel MOSFET between ground and INTVCC."}, {"pin_number": "19", "pin_name": "INTVCC", "pin_description": "Internal 6V Regulator Output. The driver and control circuits are powered from this voltage. Bypass this pin to ground with a minimum of 4.7μF low ESR tantalum or ceramic capacitor."}, {"pin_number": "20", "pin_name": "EXTVCC", "pin_description": "External VCC Input. When EXTVCC exceeds 5.7V, an internal switch connects this pin to INTVCC and shuts down the internal regulator."}, {"pin_number": "21", "pin_name": "VIN", "pin_description": "Main Input Supply. Bypass this pin to SGND with an RC filter (1Ω, 0.1μF)."}, {"pin_number": "22", "pin_name": "SW1", "pin_description": "Switch Node. The (–) terminal of the bootstrap capacitor CA connects here. The SW1 pin swings from a Schottky diode (external) voltage drop below ground up to VOUT."}, {"pin_number": "23", "pin_name": "TG1", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFET with a voltage swing equal to INTVCC superimposed on the switch node voltage SW."}, {"pin_number": "24", "pin_name": "BOOST1", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor CA connects here. The BOOST1 pin swings from a diode voltage below INTVCC up to VOUT + INTVCC."}]}, {"product_part_number": "LTC3780", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "SENSE+", "pin_description": "The (+) Input to the Current Sense and Reverse Current Detect Comparators."}, {"pin_number": "2", "pin_name": "SENSE-", "pin_description": "The (–) Input to the Current Sense and Reverse Current Detect Comparators."}, {"pin_number": "3", "pin_name": "ITH", "pin_description": "Current Control Threshold and Error Amplifier Compensation Point."}, {"pin_number": "4", "pin_name": "VOSENSE", "pin_description": "Error Amplifier Feedback Input."}, {"pin_number": "5", "pin_name": "SGND", "pin_description": "Signal Ground."}, {"pin_number": "6", "pin_name": "RUN", "pin_description": "Run Control Input."}, {"pin_number": "7", "pin_name": "FCB", "pin_description": "Forced Continuous Control Input."}, {"pin_number": "8", "pin_name": "PLLFLTR", "pin_description": "The phase-locked loop’s lowpass filter is tied to this pin."}, {"pin_number": "10", "pin_name": "PLLIN", "pin_description": "External Synchronization Input to Phase Detector."}, {"pin_number": "11", "pin_name": "STBYMD", "pin_description": "LDO Control Pin."}, {"pin_number": "14", "pin_name": "BOOST2", "pin_description": "Boosted Floating Driver Supply."}, {"pin_number": "15", "pin_name": "TG2", "pin_description": "Top Gate Drive."}, {"pin_number": "17", "pin_name": "SW2", "pin_description": "Switch Node."}, {"pin_number": "18", "pin_name": "BG2", "pin_description": "Bottom Gate Drive."}, {"pin_number": "19", "pin_name": "PGND", "pin_description": "Power Ground."}, {"pin_number": "20", "pin_name": "BG1", "pin_description": "Bottom Gate Drive."}, {"pin_number": "21", "pin_name": "INTVCC", "pin_description": "Internal 6V Regulator Output."}, {"pin_number": "22", "pin_name": "EXTVCC", "pin_description": "External VCC Input."}, {"pin_number": "23", "pin_name": "VIN", "pin_description": "Main Input Supply."}, {"pin_number": "24", "pin_name": "SW1", "pin_description": "Switch Node."}, {"pin_number": "26", "pin_name": "TG1", "pin_description": "Top Gate Drive."}, {"pin_number": "27", "pin_name": "BOOST1", "pin_description": "Boosted Floating Driver Supply."}, {"pin_number": "30", "pin_name": "PGOOD", "pin_description": "Open-Drain Logic Output."}, {"pin_number": "31", "pin_name": "SS", "pin_description": "Soft-start reduces the input power sources’ surge currents."}, {"pin_number": "33", "pin_name": "Exposed Pad", "pin_description": "Signal Ground. The QFN exposed pad must be soldered to PCB ground for electrical connection and rated thermal performance."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3780", "family_comparison": "The datasheet includes a 'Related Parts' section comparing the LTC3780 with other controllers like LTC3789, LT3791-1, and LT8705.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "4V", "max_output_voltage": "30V", "min_output_voltage": "1V", "max_output_current": "5A", "max_switch_frequency": "0.4MHz", "quiescent_current": "2400µA", "high_side_mosfet_resistance": "不适用(外部器件)", "low_side_mosfet_resistance": "不适用(外部器件)", "over_current_protection_threshold": "可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode, Skip-cycle Mode, DCM", "power_good_indicator": "Yes", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "No", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "Description", "pitch": "0.65", "height": "2.0", "length": "5.0", "width": "5.3", "pin_count": "2"}]}