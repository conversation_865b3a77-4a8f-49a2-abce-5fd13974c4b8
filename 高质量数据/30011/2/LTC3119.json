{"part_number": "LTC3119", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "18V, 5A Synchronous Buck-Boost DC/DC Converter", "features": ["Input Voltage Range: 2.5V to 18V", "Runs Down to VIN = 250mV After Start-Up", "Output Voltage Range: 0.8V to 18V", "5A Output Current in Buck Mode, VIN > 6V", "3A Output Current for VIN = 3.6V, VOUT = 5V", "Programmable Switching Frequency: 400kHz to 2MHz", "Synchronizable with an External Clock Up to 2MHz", "Accurate Run Comparator Thr<PERSON><PERSON>", "Burst Mode® Operation, No-Load IQ = 35µA", "Ultralow Noise Buck-Boost PWM", "Current Mode Control", "Maximum Power Point Control", "Power Good Indicator", "Internal Soft-Start", "28-Lead 4mm × 5mm QFN and TSSOP Packages"], "description": "The LTC®3119 is a high efficiency 18V monolithic buck-boost converter that can deliver up to 5A of continuous output current. Extensive feature integration and very low resistance internal power switches minimize the total solution footprint for even the most demanding applications. A proprietary 4-switch PWM architecture provides seamless low noise operation from input voltages above, equal to, or below the output voltage. External frequency programming as well as synchronization using an internal PLL enable operation over a wide switching frequency range of 400kHz to 2MHz. The wide 2.5V to 18V input range is well suited for operation from unregulated power sources including battery stacks and backup capacitors. After start-up, operation is possible with input voltages as low as 250mV. Other features include: output short-circuit protection, thermal overload protection, less than 3µA shutdown current, power good indicator, Burst Mode operation, and maximum power point control. The LTC3119 is offered in thermally enhanced 28-lead 4mm x 5mm QFN and TSSOP packages.", "applications": ["Wide Input Range Power Supplies", "1- to 4-Cell Lithium Battery Powered Products", "RF Power Supplies", "Solar Battery Chargers", "System Backup Power Supplies", "Lead Acid to 12V Regulator"], "ordering_information": [{"part_number": "LTC3119", "order_device": "LTC3119EUFD#PBF", "package_type": "QFN", "package_drawing_code": "UFD", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3119", "order_device": "LTC3119EUFD#TRPBF", "package_type": "QFN", "package_drawing_code": "UFD", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3119", "order_device": "LTC3119IUFD#PBF", "package_type": "QFN", "package_drawing_code": "UFD", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3119", "order_device": "LTC3119IUFD#TRPBF", "package_type": "QFN", "package_drawing_code": "UFD", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3119", "order_device": "LTC3119EFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3119", "order_device": "LTC3119EFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3119", "order_device": "LTC3119IFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3119", "order_device": "LTC3119IFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3119", "order_device": "LTC3119HFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3119", "order_device": "LTC3119HFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3119", "order_device": "LTC3119MPFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}, {"part_number": "LTC3119", "order_device": "LTC3119MPFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}], "pin_function": [{"product_part_number": "LTC3119", "package_type": "QFN", "pins": [{"pin_number": "1, 6, 17, 22, Exposed Pad 29", "pin_name": "PGND", "pin_description": "Power Ground Connection. These pins should be connected to the power ground using the shortest and widest connections possible. The exposed pad must be soldered to the PCB and electrically connected to ground through the shortest and lowest impedance path possible and to the PCB ground plane for rated thermal performance."}, {"pin_number": "2, 5", "pin_name": "SW2", "pin_description": "Buck-Boost Converter Power Switch Pin. These pins should be connected to one side of the Buck-<PERSON><PERSON> inductor."}, {"pin_number": "3, 4", "pin_name": "PVOUT", "pin_description": "Output Voltage Power Connection. These pins is connected to switch D of the buck-boost converter. Connect a low ESR 10µF or larger capacitor between this pin and ground using the lowest impedance path possible."}, {"pin_number": "7", "pin_name": "PGOOD", "pin_description": "Open drain output that pulls to ground when FB drops too far below its regulated voltage. Connect a pull-up resistor from this pin to a positive supply."}, {"pin_number": "8", "pin_name": "SVCC", "pin_description": "Supplies voltage to internal circuits used for production test. This pin must be tied to VCC."}, {"pin_number": "9", "pin_name": "FB", "pin_description": "Feedback Voltage Input. A resistor divider connected to this pin sets the output voltage for the buck-boost converter."}, {"pin_number": "10", "pin_name": "VC", "pin_description": "Error Amplifier Output. A frequency compensation network must be connected between this pin and SGND to stabilize the voltage control loop."}, {"pin_number": "11", "pin_name": "SGND", "pin_description": "Signal Ground. This pin is the ground connection for the control circuitry of the IC and must be tied to ground in the application."}, {"pin_number": "12", "pin_name": "MPPC", "pin_description": "Maximum Power Point Control Setpoint. Connect this pin to a resistive divider from VIN to GND to set the input regulation voltage. The MPPC pin should be tied to VCC to disable MPPC operation."}, {"pin_number": "13", "pin_name": "VCC", "pin_description": "Internal Regulator Output Voltage and Supply Rail for Control Circuits. This pin is the output of the internal low voltage linear regulator used to supply the control circuitry. A 4.7μF capacitor should be connected between this pin and GND."}, {"pin_number": "14", "pin_name": "RT", "pin_description": "Oscillator Frequency Programming Input. Connect a resistor between this pin and GND to set the buck-boost converter switching frequency."}, {"pin_number": "15", "pin_name": "RUN", "pin_description": "Input to Enable and Disable the IC and Set Custom Input UVLO Threshold. The IC is enabled if the RUN pin voltage exceeds 1.205V nominally."}, {"pin_number": "16", "pin_name": "VIN", "pin_description": "Input Voltage Pin for Internal VCC Regulator."}, {"pin_number": "19, 20", "pin_name": "PVIN", "pin_description": "Input Voltage Power Connection. These pins is connected to switch A of the buck-boost converter. Connect a 10µF or larger capacitor between this pin and GND."}, {"pin_number": "18, 21", "pin_name": "SW1", "pin_description": "Buck-Boost Converter Power Switch Pin. These pins should be connected to one side of the Buck-<PERSON><PERSON> inductor."}, {"pin_number": "23", "pin_name": "BST1", "pin_description": "Power Rail for SWA Driver. This pin must be connected to SW1 through a 0.1µF capacitor."}, {"pin_number": "24, 26, 27", "pin_name": "N/C", "pin_description": "No Connection. Can be connected to ground or left open."}, {"pin_number": "25", "pin_name": "PWM/SYNC", "pin_description": "Automatic Burst Mode Operation/PWM Mode Control Pin and Synchronization Input."}, {"pin_number": "28", "pin_name": "BST2", "pin_description": "Power Rail for SWD Driver. This pin must be connected to SW2 through a 0.1µF capacitor."}]}, {"product_part_number": "LTC3119", "package_type": "TSSOP", "pins": [{"pin_number": "1, 2, 27", "pin_name": "N/C", "pin_description": "No Connection. Can be connected to ground or left open."}, {"pin_number": "3", "pin_name": "BST2", "pin_description": "Power Rail for SWD Driver. This pin must be connected to SW2 through a 0.1µF capacitor."}, {"pin_number": "4, 9, 20, 25, Exposed Pad 29", "pin_name": "PGND", "pin_description": "Power Ground Connection. These pins should be connected to the power ground using the shortest and widest connections possible. The exposed pad must be soldered to the PCB and electrically connected to ground."}, {"pin_number": "5, 8", "pin_name": "SW2", "pin_description": "Buck-Boost Converter Power Switch Pin. These pins should be connected to one side of the Buck-<PERSON><PERSON> inductor."}, {"pin_number": "6, 7", "pin_name": "PVOUT", "pin_description": "Output Voltage Power Connection. These pins is connected to switch D of the buck-boost converter. Connect a low ESR 10µF or larger capacitor between this pin and ground."}, {"pin_number": "10", "pin_name": "PGOOD", "pin_description": "Open drain output that pulls to ground when FB drops too far below its regulated voltage. Connect a pull-up resistor from this pin to a positive supply."}, {"pin_number": "11", "pin_name": "SVCC", "pin_description": "Supplies voltage to internal circuits used for production test. This pin must be tied to VCC."}, {"pin_number": "12", "pin_name": "FB", "pin_description": "Feedback Voltage Input. A resistor divider connected to this pin sets the output voltage for the buck-boost converter."}, {"pin_number": "13", "pin_name": "VC", "pin_description": "Error Amplifier Output. A frequency compensation network must be connected between this pin and SGND to stabilize the voltage control loop."}, {"pin_number": "14", "pin_name": "SGND", "pin_description": "Signal Ground. This pin is the ground connection for the control circuitry of the IC and must be tied to ground in the application."}, {"pin_number": "15", "pin_name": "MPPC", "pin_description": "Maximum Power Point Control Setpoint. Connect this pin to a resistive divider from VIN to GND to set the input regulation voltage. The MPPC pin should be tied to VCC to disable MPPC operation."}, {"pin_number": "16", "pin_name": "VCC", "pin_description": "Internal Regulator Output Voltage and Supply Rail for Control Circuits. This pin is the output of the internal low voltage linear regulator used to supply the control circuitry. A 4.7μF capacitor should be connected between this pin and GND."}, {"pin_number": "17", "pin_name": "RT", "pin_description": "Oscillator Frequency Programming Input. Connect a resistor between this pin and GND to set the buck-boost converter switching frequency."}, {"pin_number": "18", "pin_name": "RUN", "pin_description": "Input to Enable and Disable the IC and Set Custom Input UVLO Threshold. The IC is enabled if the RUN pin voltage exceeds 1.205V nominally."}, {"pin_number": "19", "pin_name": "VIN", "pin_description": "Input Voltage Pin for Internal VCC Regulator."}, {"pin_number": "22, 23", "pin_name": "PVIN", "pin_description": "Input Voltage Power Connection. These pins is connected to switch A of the buck-boost converter. Connect a 10µF or larger capacitor between this pin and GND."}, {"pin_number": "21, 24", "pin_name": "SW1", "pin_description": "Buck-Boost Converter Power Switch Pin. These pins should be connected to one side of the Buck-<PERSON><PERSON> inductor."}, {"pin_number": "26", "pin_name": "BST1", "pin_description": "Power Rail for SWA Driver. This pin must be connected to SW1 through a 0.1µF capacitor."}, {"pin_number": "28", "pin_name": "PWM/SYNC", "pin_description": "Automatic Burst Mode Operation/PWM Mode Control Pin and Synchronization Input."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3119 Datasheet", "family_comparison": "RELATED PARTS section available on page 36", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "2.5V", "max_output_voltage": "18V", "min_output_voltage": "1V", "max_output_current": "5A", "max_switch_frequency": "2MHz", "quiescent_current": "31µA", "high_side_mosfet_resistance": "30mΩ", "low_side_mosfet_resistance": "30mΩ", "over_current_protection_threshold": "8A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "电流限制", "over_temperature_protection": "自动重启", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.795V", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.5", "length": "9.8", "width": "4.4", "type": "QFN", "pin_count": "28", "height": "1.2"}]}