{"part_number": "LTC3521", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "多通道DC-DC", "part_number_title": "1A Buck-Boost DC/DC and Dual 600mA Buck DC/DC Converters", "features": ["Three High Efficiency DC/DC Converters: Buck-Boost (VOUT: 1.8V to 5.25V, IOUT: 1A), Dual Buck (VOUT: 0.6V to VIN, IOUT: 600mA)", "1.8V to 5.5V Input Voltage Range", "Pin-Selectable Burst Mode® Operation", "30µA Total Quiescent Current in Burst Mode Operation", "Independent Power Good Indicator Outputs", "Integrated Soft-Start", "Thermal and Overcurrent Protection", "<2µA Current in Shutdown", "Small 4mm × 4mm QFN and Thermally Enhanced TSSOP Packages"], "description": "The LTC3521 combines a 1A buck-boost DC/DC converter and dual 600mA synchronous buck DC/DC converters. The 1.1MHz switching frequency minimizes the solution footprint while maintaining high efficiency. All three converters feature soft-start and internal compensation to minimize the solution footprint and simplify the design process. The buck converters are current mode controlled and utilize an internal synchronous rectifier to improve efficiency. The buck converters support 100% duty cycle operation to extend battery life. If the PWM pin is held low, the buck converters automatically transition from Burst Mode operation to PWM mode at high loads. With the PWM pin held high, the buck converters remain in low noise, 1.1MHz PWM mode. The buck-boost converter features continuous conduction operation to maximize efficiency and minimize noise. At light loads, the buck-boost converter can be operated in Burst Mode operation to improve efficiency and reduce no-load standby current. The LTC3521 provides a <2µA shutdown mode, over-temperature shutdown and current limit protection on all converters. The LTC3521 is available in a 24-pin 0.75mm × 4mm × 4mm QFN package, and a 20-pin thermally enhanced TSSOP package.", "applications": ["Bar Code Readers", "Medical Instruments", "Handy Terminals", "PDAs, Handheld PCs", "GPS Receivers"], "ordering_information": [{"part_number": "LTC3521", "order_device": "LTC3521EFE#PBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 <PERSON> J", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "marking": "LTC3521FE", "carrier_description": "LEAD FREE FINISH"}, {"part_number": "LTC3521", "order_device": "LTC3521EFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 <PERSON> J", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "marking": "LTC3521FE", "carrier_description": "TAPE AND REEL"}, {"part_number": "LTC3521", "order_device": "LTC3521IFE#PBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 <PERSON> J", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "marking": "LTC3521FE", "carrier_description": "LEAD FREE FINISH"}, {"part_number": "LTC3521", "order_device": "LTC3521IFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 <PERSON> J", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "marking": "LTC3521FE", "carrier_description": "TAPE AND REEL"}, {"part_number": "LTC3521", "order_device": "LTC3521EUF#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1697 Rev B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "marking": "3521", "carrier_description": "LEAD FREE FINISH"}, {"part_number": "LTC3521", "order_device": "LTC3521EUF#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1697 Rev B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "marking": "3521", "carrier_description": "TAPE AND REEL"}, {"part_number": "LTC3521", "order_device": "LTC3521IUF#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1697 Rev B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "marking": "3521", "carrier_description": "LEAD FREE FINISH"}, {"part_number": "LTC3521", "order_device": "LTC3521IUF#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1697 Rev B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "marking": "3521", "carrier_description": "TAPE AND REEL"}], "pin_function": [{"product_part_number": "LTC3521", "package_type": "TSSOP-20 (FE)", "pins": [{"pin_number": "1", "pin_name": "FB3", "pin_description": "Feedback Voltage for the Buck Converter Derived from a Resistor Divider Connected to the Buck VOUT3 Output Voltage."}, {"pin_number": "2", "pin_name": "FB2", "pin_description": "Feedback Voltage for the Buck Converter Derived from a Resistor Divider Connected to the Buck VOUT2 Output Voltage."}, {"pin_number": "3", "pin_name": "SHDN2", "pin_description": "Forcing this pin above 1.4V enables the buck converter output at SW2. Forcing this pin below 0.4V disables the buck converter."}, {"pin_number": "4", "pin_name": "PGOOD3", "pin_description": "Open-drain output which pulls low when VOUT3 is out of regulation, in overtemperature shutdown, undervoltage lockout, or SHDN3 is low."}, {"pin_number": "5", "pin_name": "PGOOD2", "pin_description": "Open-drain output which pulls low when VOUT2 is out of regulation, in overtemperature shutdown, undervoltage lockout, or SHDN2 is low."}, {"pin_number": "6", "pin_name": "PGOOD1", "pin_description": "Open-drain output which pulls low when VOUT1 is out of regulation, in overtemperature shutdown, undervoltage lockout, in current limit, or SHDN1 is low."}, {"pin_number": "7", "pin_name": "VIN", "pin_description": "Low Current Power Supply Connection Used to Power the Internal Circuitry of the LTC3521."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Small Signal Ground."}, {"pin_number": "9", "pin_name": "PWM", "pin_description": "Logic Input Used to Choose Between Burst Mode Operation and PWM Mode for All Three Converters."}, {"pin_number": "10", "pin_name": "FB1", "pin_description": "Feedback Voltage for the Buck-Boost Converter Derived from a Resistor Divider on the Buck-Boost Output Voltage."}, {"pin_number": "11", "pin_name": "SHDN3", "pin_description": "Forcing this pin above 1.4V enables the buck converter output at SW3. Forcing this pin below 0.4V disables the buck converter."}, {"pin_number": "12", "pin_name": "SHDN1", "pin_description": "Forcing this pin above 1.4V enables the buck-boost converter. Forcing this pin below 0.4V disables the buck-boost converter."}, {"pin_number": "13", "pin_name": "PVIN1", "pin_description": "High current power supply connection used to supply switch A of the buck-boost converter."}, {"pin_number": "14", "pin_name": "SW1B", "pin_description": "Buck-Boost Switch Node. This pin must be connected to one side of the buck-boost inductor."}, {"pin_number": "15", "pin_name": "SW1A", "pin_description": "Buck-Boost Switch Node. This pin must be connected to one side of the buck-boost inductor."}, {"pin_number": "16", "pin_name": "VOUT1", "pin_description": "Buck-Boost Output Voltage Node."}, {"pin_number": "17", "pin_name": "SW3", "pin_description": "Buck converter Switch Node. Connected to the opposite side of the inductor connected to VOUT3."}, {"pin_number": "18", "pin_name": "PGND2", "pin_description": "High Current Ground Connection for Both Buck Converters."}, {"pin_number": "19", "pin_name": "SW2", "pin_description": "Buck Converter Switch Node. Connected to the opposite side of the inductor connected to VOUT2."}, {"pin_number": "20", "pin_name": "PVIN2", "pin_description": "High Current Power Supply Connection Used to Supply the Buck Converter Power Switches."}, {"pin_number": "21 (Exposed Pad)", "pin_name": "PGND1A", "pin_description": "High Current Ground Connection for the Buck-Boost Switch B. Must be soldered to PCB ground."}]}, {"product_part_number": "LTC3521", "package_type": "QFN-24 (UF)", "pins": [{"pin_number": "1", "pin_name": "SHDN2", "pin_description": "Forcing this pin above 1.4V enables the buck converter output at SW2. Forcing this pin below 0.4V disables the buck converter."}, {"pin_number": "2", "pin_name": "PGOOD3", "pin_description": "Open-drain output which pulls low when VOUT3 is out of regulation, in overtemperature shutdown, undervoltage lockout, or SHDN3 is low."}, {"pin_number": "3", "pin_name": "PGOOD2", "pin_description": "Open-drain output which pulls low when VOUT2 is out of regulation, in overtemperature shutdown, undervoltage lockout, or SHDN2 is low."}, {"pin_number": "4", "pin_name": "PGOOD1", "pin_description": "Open-drain output which pulls low when VOUT1 is out of regulation, in overtemperature shutdown, undervoltage lockout, in current limit, or SHDN1 is low."}, {"pin_number": "5", "pin_name": "VIN", "pin_description": "Low Current Power Supply Connection Used to Power the Internal Circuitry of the LTC3521."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Small Signal Ground."}, {"pin_number": "7", "pin_name": "PWM", "pin_description": "Logic Input Used to Choose Between Burst Mode Operation and PWM Mode for All Three Converters."}, {"pin_number": "8", "pin_name": "FB1", "pin_description": "Feedback Voltage for the Buck-Boost Converter Derived from a Resistor Divider on the Buck-Boost Output Voltage."}, {"pin_number": "9", "pin_name": "SHDN3", "pin_description": "Forcing this pin above 1.4V enables the buck converter output at SW3. Forcing this pin below 0.4V disables the buck converter."}, {"pin_number": "10", "pin_name": "SHDN1", "pin_description": "Forcing this pin above 1.4V enables the buck-boost converter. Forcing this pin below 0.4V disables the buck-boost converter."}, {"pin_number": "11", "pin_name": "PVIN1", "pin_description": "High current power supply connection used to supply switch A of the buck-boost converter."}, {"pin_number": "12", "pin_name": "PGND1B", "pin_description": "High Current Ground Connection for the Buck-Boost Switch C."}, {"pin_number": "13", "pin_name": "NC", "pin_description": "No Internal Connection."}, {"pin_number": "14", "pin_name": "SW1B", "pin_description": "Buck-Boost Switch Node. This pin must be connected to one side of the buck-boost inductor."}, {"pin_number": "15", "pin_name": "SW1A", "pin_description": "Buck-Boost Switch Node. This pin must be connected to one side of the buck-boost inductor."}, {"pin_number": "16", "pin_name": "VOUT1", "pin_description": "Buck-Boost Output Voltage Node."}, {"pin_number": "17", "pin_name": "SW3", "pin_description": "Buck converter Switch Node. Connected to the opposite side of the inductor connected to VOUT3."}, {"pin_number": "18", "pin_name": "PGND2", "pin_description": "High Current Ground Connection for Both Buck Converters."}, {"pin_number": "19", "pin_name": "NC", "pin_description": "No Internal Connection."}, {"pin_number": "20", "pin_name": "SW2", "pin_description": "Buck Converter Switch Node. Connected to the opposite side of the inductor connected to VOUT2."}, {"pin_number": "21", "pin_name": "PGND1A", "pin_description": "High Current Ground Connection for the Buck-Boost Switch B."}, {"pin_number": "22", "pin_name": "PVIN2", "pin_description": "High Current Power Supply Connection Used to Supply the Buck Converter Power Switches."}, {"pin_number": "23", "pin_name": "FB3", "pin_description": "Feedback Voltage for the Buck Converter Derived from a Resistor Divider Connected to the Buck VOUT3 Output Voltage."}, {"pin_number": "24", "pin_name": "FB2", "pin_description": "Feedback Voltage for the Buck Converter Derived from a Resistor Divider Connected to the Buck VOUT2 Output Voltage."}, {"pin_number": "25 (Exposed Pad)", "pin_name": "PGND1A", "pin_description": "High Current Ground Connection for the Buck-Boost Switch B. Must be soldered to PCB ground."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3521 Datasheet Rev. B", "family_comparison": "Yes", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 3, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.25V", "min_output_voltage": "1V", "max_output_current": "1A", "max_switch_frequency": "1.35MHz", "quiescent_current": "30µA", "high_side_mosfet_resistance": "110mΩ", "low_side_mosfet_resistance": "85mΩ", "over_current_protection_threshold": "2.1A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "True", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.5%", "output_reference_voltage": "0.6V", "loop_control_mode": "峰值电流模式, 电压模式"}, "package": [{"pitch": "0.65", "height": "1.2", "length": "6.6", "width": "4.4", "type": "DESCRIPTION", "pin_count": "3"}]}