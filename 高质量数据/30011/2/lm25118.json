{"part_number": "LM25118", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "DC/DC 开关稳压器", "category_lv3": "降压升压型稳压器", "part_number_title": "LM25118 宽电压范围降压/升压控制器", "features": ["输入电压工作范围为3V 至 42V", "仿真峰值电流模式控制", "在降压和升压模式之间平滑转换", "开关频率最高可通过编程设定为 500kHz", "振荡器同步功能", "内部高电压偏置稳压器", "集成了高侧和低侧栅极驱动器", "可编程软启动时间", "超低关断电流", "使能输入", "宽带宽误差放大器", "1.5% 反馈基准精度", "热关断", "封装: 20 引脚 HTSSOP (裸露焊盘)"], "description": "LM25118 宽电压范围降压/升压开关稳压器控制器 具有使用最少外部组件实现高性能且具成本效益的降压/升压稳压器所需的所有功能。当输入电压低于或高于输出电压时，降压/升压拓扑可使输出电压保持稳定，因此，这款器件非常适合汽车应用。当输入电压比调节后的输出电压足够大时，LM25118将作为降压稳压器运行，然后随着输入电压接近输出电压逐渐过渡到相应的降压/升压模式。这种双模式方法可在宽输入电压范围内保持稳压，并且在降压模式下提供最佳的转换效率，同时在模式转换期间提供无干扰的输出。该控制器易于使用，其中包含适用于高侧降压 MOSFET 和低侧升压 MOSFET的驱动器。该稳压器的控制方法基于采用仿真电流斜坡的电流模式控制。仿真电流模式控制可降低脉宽调制电路的噪声敏感度，以便可靠地控制高输入电压应用中所需的极小占空比。其他保护功能包括电流限制、热关断和使能输入。该器件采用功耗增强型 20 引脚 HTSSOP 封装，并且配有利于散热的裸露芯片连接焊盘。", "applications": ["工业降压/升压电源"], "ordering_information": [{"part_number": "LM25118", "order_device": "LM25118MH/NOPB", "package_type": "HTSSOP", "package_drawing_code": "PWP0020A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM25118", "order_device": "LM25118MHE/NOPB", "package_type": "HTSSOP", "package_drawing_code": "PWP0020A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM25118", "order_device": "LM25118MHX/NOPB", "package_type": "HTSSOP", "package_drawing_code": "PWP0020A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LM25118", "package_type": "HTSSOP-20", "pins": [{"pin_number": "1", "pin_name": "VIN", "pin_description": "输入电源电压。"}, {"pin_number": "2", "pin_name": "UVLO", "pin_description": "如果UVLO引脚低于1.23V，稳压器将处于待机模式（VCC稳压器运行，开关稳压器禁用）。当UVLO引脚超过1.23V时，稳压器进入正常工作模式。可使用外部电压分压器设置欠压关断阈值。一个固定的5µA电流从UVLO引脚源出。如果限流条件连续存在256个开关周期，内部开关会将UVLO引脚拉至地然后释放。"}, {"pin_number": "3", "pin_name": "RT", "pin_description": "内部振荡器频率由该引脚与AGND引脚之间的单个电阻器设置。推荐的频率范围是50kHz至500kHz。"}, {"pin_number": "4", "pin_name": "EN", "pin_description": "如果EN引脚低于0.5V，稳压器将进入低功耗状态，从VIN吸收的电流小于10µA。EN必须升至3V以上才能正常工作。"}, {"pin_number": "5", "pin_name": "RAMP", "pin_description": "斜坡控制信号。连接在该引脚和AGND引脚之间的外部电容器设置用于仿真电流模式控制的斜坡斜率。"}, {"pin_number": "6", "pin_name": "AGND", "pin_description": "模拟地。"}, {"pin_number": "7", "pin_name": "SS", "pin_description": "软启动。外部电容器和内部10µA电流源设置误差放大器基准的上升时间。当VCC低于VCC欠压阈值（<3.7V）、UVLO引脚低（<1.23V）、EN引脚低（<0.5V）或热关断激活时，SS引脚被拉低。"}, {"pin_number": "8", "pin_name": "FB", "pin_description": "来自稳压输出的反馈信号。连接到内部误差放大器的反相输入端。"}, {"pin_number": "9", "pin_name": "COMP", "pin_description": "内部误差放大器的输出。环路补偿网络应连接在COMP和FB引脚之间。"}, {"pin_number": "10", "pin_name": "VOUT", "pin_description": "用于仿真电流模式控制的输出电压监视器。将此引脚直接连接到稳压输出。"}, {"pin_number": "11", "pin_name": "SYNC", "pin_description": "用于将开关稳压器同步到外部时钟的同步输入。"}, {"pin_number": "12", "pin_name": "CS", "pin_description": "电流检测输入。连接到电流检测电阻的二极管侧。"}, {"pin_number": "13", "pin_name": "CSG", "pin_description": "电流检测接地输入。连接到电流检测电阻的接地侧。"}, {"pin_number": "14", "pin_name": "PGND", "pin_description": "功率地。"}, {"pin_number": "15", "pin_name": "LO", "pin_description": "升压MOSFET栅极驱动输出。连接到外部升压MOSFET的栅极。"}, {"pin_number": "16", "pin_name": "VCC", "pin_description": "偏置稳压器的输出。使用靠近控制器的低ESR/ESL电容器局部去耦到PGND。"}, {"pin_number": "17", "pin_name": "VCCX", "pin_description": "用于外部供电的偏置电源的可选输入。如果VCCX引脚的电压大于3.9V，内部VCC稳压器被禁用，VCC引脚内部连接到VCCX引脚电源。如果不使用VCCX，请连接到AGND。"}, {"pin_number": "18", "pin_name": "HB", "pin_description": "用于自举操作的高侧栅极驱动器电源。自举电容器为高侧MOSFET栅极充电提供电流。该电容器应尽可能靠近控制器放置，并连接在HB和HS之间。"}, {"pin_number": "19", "pin_name": "HO", "pin_description": "降压MOSFET栅极驱动输出。通过短的、低电感的路径连接到高侧降压MOSFET的栅极。"}, {"pin_number": "20", "pin_name": "HS", "pin_description": "降压MOSFET源极引脚。连接到高侧降压MOSFET的源极端子和自举电容器。"}, {"pin_number": "EP", "pin_name": "EP", "pin_description": "裸露散热焊盘。焊接到IC下方的接地平面以帮助散热。"}]}], "datasheet_cn": "ZHCSGG8F", "datasheet_en": "SNVS726", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "42V", "min_input_voltage": "3V", "max_output_voltage": "可调", "min_output_voltage": "1.23V", "max_output_current": "可调", "max_switch_frequency": "500kHz", "quiescent_current": "4.5mA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "pass_through_mode": "False", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "热关断", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "1.23V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "OPTION", "pitch": "0.65", "height": "1.2", "length": "6.6", "width": "4.4", "pin_count": "20"}]}