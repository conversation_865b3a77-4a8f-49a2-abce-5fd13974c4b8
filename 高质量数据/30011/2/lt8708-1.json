{"part_number": "LT8708-1", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "DC/DC 开关控制器", "category_lv3": "降压-升压(<PERSON><PERSON><PERSON><PERSON>)控制器", "part_number_title": "80V 同步4开关降压-升压型DC/DC从控制器，用于LT8708多相系统 (80V Synchronous 4-Switch Buck-Boost DC/DC Slave Controller for LT8708 Multiphase System)", "features": ["LT8708的从芯片，提供额外功率", "通过电流调节实现与LT8708平均输出电流的良好匹配", "通过四个引脚轻松与LT8708并联", "与LT8708同步启动", "与LT8708具有相同的传导模式", "同步整流：效率高达98%", "频率范围：100kHz至400kHz", "提供40引脚 (5mm × 8mm) QFN封装，具有高压引脚间距和64引脚 (10mm × 10mm) eLQFP封装", "AEC-Q100认证，适用于汽车应用"], "description": "LT8708-1是一款高性能的降压-升压型开关稳压控制器，与LT8708并联，为LT8708系统增加功率和相位。LT8708-1始终作为主LT8708的从设备运行，并能够提供与主设备一样多的电流或功率。一个或多个从设备可以连接到单个主设备，按比例增加系统的功率和电流能力。LT8708-1与LT8708具有相同的传导模式，允许LT8708-1在与主设备相同的方向上传导电流和功率。主设备控制LT8708多相系统的总体电流和电压限制，而从设备则遵守这些限制。LT8708-1s可以通过连接四个信号轻松地与LT8708并联。每个从设备上还有两个额外的电流限制（正向VIN电流和反向VIN电流），可以独立设置。", "applications": ["高压降压-升压转换器", "双向充电系统", "汽车48V系统"], "ordering_information": [{"part_number": "LT8708-1", "order_device": "LT8708EUHG-1#PBF", "package_type": "QFN", "package_drawing_code": "UHG", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "LEAD FREE FINISH", "application_grade": "Industry"}, {"part_number": "LT8708-1", "order_device": "LT8708EUHG-1#TRPBF", "package_type": "QFN", "package_drawing_code": "UHG", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "TAPE AND REEL", "application_grade": "Industry"}, {"part_number": "LT8708-1", "order_device": "LT8708IUHG-1#PBF", "package_type": "QFN", "package_drawing_code": "UHG", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "LEAD FREE FINISH", "application_grade": "Industry"}, {"part_number": "LT8708-1", "order_device": "LT8708IUHG-1#TRPBF", "package_type": "QFN", "package_drawing_code": "UHG", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "TAPE AND REEL", "application_grade": "Industry"}, {"part_number": "LT8708-1", "order_device": "LT8708HUHG-1#PBF", "package_type": "QFN", "package_drawing_code": "UHG", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150", "carrier_description": "LEAD FREE FINISH", "application_grade": "Auto"}, {"part_number": "LT8708-1", "order_device": "LT8708HUHG-1#TRPBF", "package_type": "QFN", "package_drawing_code": "UHG", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150", "carrier_description": "TAPE AND REEL", "application_grade": "Auto"}, {"part_number": "LT8708-1", "order_device": "LT8708IUHG-1#WPBF", "package_type": "QFN", "package_drawing_code": "UHG", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "LEAD FREE FINISH", "application_grade": "Auto"}, {"part_number": "LT8708-1", "order_device": "LT8708IUHG-1#WTRPBF", "package_type": "QFN", "package_drawing_code": "UHG", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "TAPE AND REEL", "application_grade": "Auto"}, {"part_number": "LT8708-1", "order_device": "LT8708HUHG-1#WPBF", "package_type": "QFN", "package_drawing_code": "UHG", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150", "carrier_description": "LEAD FREE FINISH", "application_grade": "Auto"}, {"part_number": "LT8708-1", "order_device": "LT8708HUHG-1#WTRPBF", "package_type": "QFN", "package_drawing_code": "UHG", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150", "carrier_description": "TAPE AND REEL", "application_grade": "Auto"}, {"part_number": "LT8708-1", "order_device": "LT8708ELWE-1#PBF", "package_type": "eLQFP", "package_drawing_code": "LWE", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "TRAY", "application_grade": "Industry"}, {"part_number": "LT8708-1", "order_device": "LT8708ILWE-1#PBF", "package_type": "eLQFP", "package_drawing_code": "LWE", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "TRAY", "application_grade": "Industry"}, {"part_number": "LT8708-1", "order_device": "LT8708HLWE-1#PBF", "package_type": "eLQFP", "package_drawing_code": "LWE", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150", "carrier_description": "TRAY", "application_grade": "Auto"}, {"part_number": "LT8708-1", "order_device": "LT8708ELWE-1#WPBF", "package_type": "eLQFP", "package_drawing_code": "LWE", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "TRAY", "application_grade": "Auto"}, {"part_number": "LT8708-1", "order_device": "LT8708ILWE-1#WPBF", "package_type": "eLQFP", "package_drawing_code": "LWE", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "TRAY", "application_grade": "Auto"}, {"part_number": "LT8708-1", "order_device": "LT8708HLWE-1#WPBF", "package_type": "eLQFP", "package_drawing_code": "LWE", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150", "carrier_description": "TRAY", "application_grade": "Auto"}], "pin_function": [{"product_part_number": "LT8708-1", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "CLKOUT", "pin_description": "时钟输出引脚。用于同步一个或多个兼容的开关稳压器IC。CLKOUT的切换频率与内部振荡器或SYNC引脚相同，但相位大约相差180°。CLKOUT也可用作温度监视器，因为其占空比与器件结温线性相关。CLKOUT引脚可驱动高达200pF的容性负载。"}, {"pin_number": "2", "pin_name": "SS", "pin_description": "软启动引脚。在此引脚与地之间放置一个电容器。建议使用与主LT8708上SS引脚电容器相同的电容器。启动时，此引脚将由内部电阻充电至3.3V。"}, {"pin_number": "3", "pin_name": "SHDN", "pin_description": "关断引脚。接高电平以使能芯片。接地以关断并使静态电流降至最低。不要浮空此引脚。"}, {"pin_number": "4", "pin_name": "CSN", "pin_description": "电感电流检测和DCM检测比较器的(-)输入。"}, {"pin_number": "5", "pin_name": "CSP", "pin_description": "电感电流检测和DCM检测比较器的(+)输入。Vc引脚电压和CSP与CSN引脚之间的内置偏移，结合RSENSE值，设置电感电流跳变阈值。建议使用与主LT8708相同值的RSENSE。"}, {"pin_number": "6", "pin_name": "ICN", "pin_description": "负VOUT电流指令引脚。此引脚上的电压决定了LT8708-1要调节的负VOUT电流。将此引脚连接到主LT8708的ICN引脚。"}, {"pin_number": "7", "pin_name": "DIR", "pin_description": "方向引脚，当MODE设置为DCM或HCM操作时。否则此引脚被忽略。将引脚接地以处理从VOUT到VIN的功率。将引脚连接到LDO33以处理从VIN到VOUT的功率。用相同的控制信号驱动此引脚，或将其连接到与主LT8708相同的电压。"}, {"pin_number": "8", "pin_name": "FBIN", "pin_description": "VIN反馈引脚。此引脚连接到误差放大器EA3的输入。通常，将此引脚连接到LDO33以禁用EA3。"}, {"pin_number": "9", "pin_name": "FBOUT", "pin_description": "VOUT反馈引脚。此引脚连接到误差放大器EA4的输入。通常，将此引脚接地以禁用EA4。"}, {"pin_number": "10", "pin_name": "VC", "pin_description": "误差放大器输出引脚。将外部补偿网络连接到此引脚。"}, {"pin_number": "11", "pin_name": "IMON_INP", "pin_description": "正VIN电流监视器和限制引脚。此引脚输出的电流为20μA加上与正平均VIN电流成比例的电流。IMON_INP也连接到误差放大器EA5，可用于限制最大正VIN电流。"}, {"pin_number": "12", "pin_name": "IMON_INN", "pin_description": "负VIN电流监视器和限制引脚。此引脚输出的电流为20μA加上与负平均VIN电流成比例的电流。IMON_INN也连接到误差放大器EA1，可用于限制最大负VIN电流。"}, {"pin_number": "13", "pin_name": "RT", "pin_description": "时序电阻引脚。调整开关频率。在此引脚与地之间放置一个电阻来设置频率。建议使用与主LT8708相同值的RT电阻。不要浮空此引脚。"}, {"pin_number": "14", "pin_name": "SYNC", "pin_description": "要将开关频率同步到外部时钟，只需用时钟驱动此引脚。时钟的高电平需要超过1.3V，低电平应小于0.5V。在两相系统中，将此引脚连接到主LT8708的CLKOUT引脚以获得180°相移。"}, {"pin_number": "15", "pin_name": "GND", "pin_description": "地。直接连接到本地接地平面。"}, {"pin_number": "16", "pin_name": "BG1", "pin_description": "底部栅极驱动。驱动底部N沟道MOSFET的栅极，电压在接地和GATEVCC之间。"}, {"pin_number": "17", "pin_name": "GATEVCC", "pin_description": "底部栅极驱动器的电源。必须连接到INTVCC引脚。不要从任何其他电源供电。在本地旁路到GND。建议使用与主LT8708相同值的旁路电容。"}, {"pin_number": "18", "pin_name": "BG2", "pin_description": "底部栅极驱动。驱动底部N沟道MOSFET的栅极，电压在接地和GATEVCC之间。"}, {"pin_number": "19", "pin_name": "BOOST2", "pin_description": "升压浮动驱动器电源。自举电容器的(+)端子连接在此处。BOOST2引脚的电压摆幅从低于GATEVCC一个二极管压降到VOUT + GATEVCC。"}, {"pin_number": "20", "pin_name": "TG2", "pin_description": "顶部栅极驱动。驱动顶部N沟道MOSFET，电压摆幅等于GATEVCC，叠加在开关节点电压上。"}, {"pin_number": "21", "pin_name": "SW2", "pin_description": "开关节点。自举电容器的(-)端子连接在此处。"}, {"pin_number": "22", "pin_name": "SW1", "pin_description": "开关节点。自举电容器的(-)端子连接在此处。"}, {"pin_number": "23", "pin_name": "TG1", "pin_description": "顶部栅极驱动。驱动顶部N沟道MOSFET，电压摆幅等于GATEVCC，叠加在开关节点电压上。"}, {"pin_number": "24", "pin_name": "BOOST1", "pin_description": "升压浮动驱动器电源。自举电容器的(+)端子连接在此处。BOOST1引脚的电压摆幅从低于GATEVCC一个二极管压降到VIN + GATEVCC。"}, {"pin_number": "25", "pin_name": "RVSOFF", "pin_description": "反向传导禁用引脚。这是一个需要上拉电阻的输入/输出开漏引脚。将此引脚拉低会禁用反向电流操作。通常，将此引脚连接到LT8708的RVSOFF引脚。"}, {"pin_number": "26", "pin_name": "VOUTLOMON", "pin_description": "VOUT低压监视器引脚。在VOUT、VOUTLOMON和GND之间连接一个±1%的电阻分压器，以设置VOUT的欠压水平。当VOUT低于此水平时，反向传导被禁用，以防止从VOUT汲取电流。"}, {"pin_number": "27", "pin_name": "VINHIMON", "pin_description": "VIN高压监视器引脚。在VIN、VINHIMON和GND之间连接一个±1%的电阻分压器，以设置VIN的过压水平。当VIN高于此水平时，反向传导被禁用，以防止电流流入VIN。"}, {"pin_number": "28", "pin_name": "ICP", "pin_description": "正VOUT电流指令引脚。此引脚上的电压决定了LT8708-1要调节的正VOUT电流。将此引脚连接到LT8708的ICP引脚。"}, {"pin_number": "29", "pin_name": "EXTVCC", "pin_description": "外部VCC输入。当EXTVCC超过6.4V（典型值）时，INTVCC将从此引脚供电。当EXTVCC低于6.4V时，INTVCC将由VINCHIP供电。建议使用与主LT8708相同值的旁路电容。"}, {"pin_number": "30", "pin_name": "CSPOUT", "pin_description": "VOUT电流监视器放大器的(+)输入。此引脚和CSNOUT引脚测量感测电阻RSENSE2上的电压，以提供VOUT电流信号。建议使用与主LT8708在CSPOUT和CSNOUT引脚之间相同值的RSENSE2。"}, {"pin_number": "31", "pin_name": "CSNOUT", "pin_description": "VOUT电流监视器放大器的(-)输入。"}, {"pin_number": "32", "pin_name": "CSNIN", "pin_description": "VIN电流监视器放大器的(-)输入。此引脚和CSPIN引脚测量感测电阻RSENSE1上的电压，以提供VIN电流信号。不使用时将此引脚连接到VIN。"}, {"pin_number": "33", "pin_name": "CSPIN", "pin_description": "VIN电流监视器放大器的(+)输入。不使用时将此引脚连接到VIN。"}, {"pin_number": "34", "pin_name": "VINCHIP", "pin_description": "LT8708-1的主输入电源引脚。必须在本地旁路到地。建议使用与主LT8708相同值的旁路电容。"}, {"pin_number": "35", "pin_name": "INTVCC", "pin_description": "6.35V稳压器输出。必须连接到GATEVCC引脚。当EXTVCC电压高于6.4V时，INTVCC由EXTVCC供电，否则由VINCHIP供电。用至少4.7μF的陶瓷电容旁路到地。建议使用与主LT8708相同值的旁路电容。"}, {"pin_number": "36", "pin_name": "SWEN", "pin_description": "开关稳压器使能引脚。通过电阻接高电平以使能开关。接地以禁用开关。此引脚在关断、热锁定或检测到内部UVLO时被拉低。不要浮空此引脚。将此引脚连接到LT8708的SWEN引脚以实现同步启动。"}, {"pin_number": "37", "pin_name": "MODE", "pin_description": "传导模式选择引脚。施加小于0.4V以使能连续传导模式(CCM)。施加0.8V至1.2V以使能混合传导模式(HCM)。施加1.6V至2.0V以使能不连续传导模式(DCM)。施加大于2.4V以使能Burst Mode操作。建议用相同的控制信号驱动此引脚，或连接到与主LT8708相同值的电阻分压器或电压。"}, {"pin_number": "38", "pin_name": "IMON_OP", "pin_description": "平均VOUT电流调节引脚。此引脚伺服到1.207V以根据ICP和ICN电压调节平均输出电流。始终将一个17.4k电阻与一个补偿网络并联，从此引脚连接到GND。"}, {"pin_number": "39", "pin_name": "IMON_ON", "pin_description": "负VOUT电流监视器引脚。此引脚输出的电流为20μA加上与负平均VOUT电流成比例的电流。"}, {"pin_number": "40", "pin_name": "LDO33", "pin_description": "3.3V稳压器输出。用至少0.1μF的陶瓷电容旁路到地。建议使用与主LT8708相同值的旁路电容。"}, {"pin_number": "41", "pin_name": "GND (Exposed Pad)", "pin_description": "地。直接连接到本地接地平面。"}]}, {"product_part_number": "LT8708-1", "package_type": "eLQFP", "pins": [{"pin_number": "2", "pin_name": "SS", "pin_description": "软启动引脚。在此引脚与地之间放置一个电容器。建议使用与主LT8708上SS引脚电容器相同的电容器。启动时，此引脚将由内部电阻充电至3.3V。"}, {"pin_number": "3", "pin_name": "SHDN", "pin_description": "关断引脚。接高电平以使能芯片。接地以关断并使静态电流降至最低。不要浮空此引脚。"}, {"pin_number": "4", "pin_name": "CSN", "pin_description": "电感电流检测和DCM检测比较器的(-)输入。"}, {"pin_number": "5", "pin_name": "CSP", "pin_description": "电感电流检测和DCM检测比较器的(+)输入。Vc引脚电压和CSP与CSN引脚之间的内置偏移，结合RSENSE值，设置电感电流跳变阈值。建议使用与主LT8708相同值的RSENSE。"}, {"pin_number": "6", "pin_name": "ICN", "pin_description": "负VOUT电流指令引脚。此引脚上的电压决定了LT8708-1要调节的负VOUT电流。将此引脚连接到主LT8708的ICN引脚。"}, {"pin_number": "7", "pin_name": "DIR", "pin_description": "方向引脚，当MODE设置为DCM或HCM操作时。否则此引脚被忽略。将引脚接地以处理从VOUT到VIN的功率。将引脚连接到LDO33以处理从VIN到VOUT的功率。用相同的控制信号驱动此引脚，或将其连接到与主LT8708相同的电压。"}, {"pin_number": "8", "pin_name": "FBIN", "pin_description": "VIN反馈引脚。此引脚连接到误差放大器EA3的输入。通常，将此引脚连接到LDO33以禁用EA3。"}, {"pin_number": "9", "pin_name": "FBOUT", "pin_description": "VOUT反馈引脚。此引脚连接到误差放大器EA4的输入。通常，将此引脚接地以禁用EA4。"}, {"pin_number": "10", "pin_name": "VC", "pin_description": "误差放大器输出引脚。将外部补偿网络连接到此引脚。"}, {"pin_number": "11", "pin_name": "IMON_INP", "pin_description": "正VIN电流监视器和限制引脚。此引脚输出的电流为20μA加上与正平均VIN电流成比例的电流。IMON_INP也连接到误差放大器EA5，可用于限制最大正VIN电流。"}, {"pin_number": "12", "pin_name": "IMON_INN", "pin_description": "负VIN电流监视器和限制引脚。此引脚输出的电流为20μA加上与负平均VIN电流成比例的电流。IMON_INN也连接到误差放大器EA1，可用于限制最大负VIN电流。"}, {"pin_number": "13", "pin_name": "RT", "pin_description": "时序电阻引脚。调整开关频率。在此引脚与地之间放置一个电阻来设置频率。建议使用与主LT8708相同值的RT电阻。不要浮空此引脚。"}, {"pin_number": "14", "pin_name": "SYNC", "pin_description": "要将开关频率同步到外部时钟，只需用时钟驱动此引脚。时钟的高电平需要超过1.3V，低电平应小于0.5V。在两相系统中，将此引脚连接到主LT8708的CLKOUT引脚以获得180°相移。"}, {"pin_number": "19, 23, 27, 30", "pin_name": "GND", "pin_description": "地。直接连接到本地接地平面。"}, {"pin_number": "20", "pin_name": "BG1", "pin_description": "底部栅极驱动。驱动底部N沟道MOSFET的栅极，电压在接地和GATEVCC之间。"}, {"pin_number": "21", "pin_name": "GATEVCC", "pin_description": "底部栅极驱动器的电源。必须连接到INTVCC引脚。不要从任何其他电源供电。在本地旁路到GND。建议使用与主LT8708相同值的旁路电容。"}, {"pin_number": "22", "pin_name": "BG2", "pin_description": "底部栅极驱动。驱动底部N沟道MOSFET的栅极，电压在接地和GATEVCC之间。"}, {"pin_number": "24", "pin_name": "BOOST2", "pin_description": "升压浮动驱动器电源。自举电容器的(+)端子连接在此处。BOOST2引脚的电压摆幅从低于GATEVCC一个二极管压降到VOUT + GATEVCC。"}, {"pin_number": "25", "pin_name": "TG2", "pin_description": "顶部栅极驱动。驱动顶部N沟道MOSFET，电压摆幅等于GATEVCC，叠加在开关节点电压上。"}, {"pin_number": "26", "pin_name": "SW2", "pin_description": "开关节点。自举电容器的(-)端子连接在此处。"}, {"pin_number": "33", "pin_name": "SW1", "pin_description": "开关节点。自举电容器的(-)端子连接在此处。"}, {"pin_number": "34", "pin_name": "TG1", "pin_description": "顶部栅极驱动。驱动顶部N沟道MOSFET，电压摆幅等于GATEVCC，叠加在开关节点电压上。"}, {"pin_number": "35", "pin_name": "BOOST1", "pin_description": "升压浮动驱动器电源。自举电容器的(+)端子连接在此处。BOOST1引脚的电压摆幅从低于GATEVCC一个二极管压降到VIN + GATEVCC。"}, {"pin_number": "37", "pin_name": "RVSOFF", "pin_description": "反向传导禁用引脚。这是一个需要上拉电阻的输入/输出开漏引脚。将此引脚拉低会禁用反向电流操作。通常，将此引脚连接到LT8708的RVSOFF引脚。"}, {"pin_number": "38", "pin_name": "VOUTLOMON", "pin_description": "VOUT低压监视器引脚。在VOUT、VOUTLOMON和GND之间连接一个±1%的电阻分压器，以设置VOUT的欠压水平。当VOUT低于此水平时，反向传导被禁用，以防止从VOUT汲取电流。"}, {"pin_number": "39", "pin_name": "VINHIMON", "pin_description": "VIN高压监视器引脚。在VIN、VINHIMON和GND之间连接一个±1%的电阻分压器，以设置VIN的过压水平。当VIN高于此水平时，反向传导被禁用，以防止电流流入VIN。"}, {"pin_number": "40", "pin_name": "ICP", "pin_description": "正VOUT电流指令引脚。此引脚上的电压决定了LT8708-1要调节的正VOUT电流。将此引脚连接到LT8708的ICP引脚。"}, {"pin_number": "42", "pin_name": "EXTVCC", "pin_description": "外部VCC输入。当EXTVCC超过6.4V（典型值）时，INTVCC将从此引脚供电。当EXTVCC低于6.4V时，INTVCC将由VINCHIP供电。建议使用与主LT8708相同值的旁路电容。"}, {"pin_number": "46", "pin_name": "CSPOUT", "pin_description": "VOUT电流监视器放大器的(+)输入。此引脚和CSNOUT引脚测量感测电阻RSENSE2上的电压，以提供VOUT电流信号。建议使用与主LT8708在CSPOUT和CSNOUT引脚之间相同值的RSENSE2。"}, {"pin_number": "47", "pin_name": "CSNOUT", "pin_description": "VOUT电流监视器放大器的(-)输入。"}, {"pin_number": "52", "pin_name": "CSNIN", "pin_description": "VIN电流监视器放大器的(-)输入。此引脚和CSPIN引脚测量感测电阻RSENSE1上的电压，以提供VIN电流信号。不使用时将此引脚连接到VIN。"}, {"pin_number": "53", "pin_name": "CSPIN", "pin_description": "VIN电流监视器放大器的(+)输入。不使用时将此引脚连接到VIN。"}, {"pin_number": "55", "pin_name": "VINCHIP", "pin_description": "LT8708-1的主输入电源引脚。必须在本地旁路到地。建议使用与主LT8708相同值的旁路电容。"}, {"pin_number": "57", "pin_name": "INTVCC", "pin_description": "6.35V稳压器输出。必须连接到GATEVCC引脚。当EXTVCC电压高于6.4V时，INTVCC由EXTVCC供电，否则由VINCHIP供电。用至少4.7μF的陶瓷电容旁路到地。建议使用与主LT8708相同值的旁路电容。"}, {"pin_number": "58", "pin_name": "SWEN", "pin_description": "开关稳压器使能引脚。通过电阻接高电平以使能开关。接地以禁用开关。此引脚在关断、热锁定或检测到内部UVLO时被拉低。不要浮空此引脚。将此引脚连接到LT8708的SWEN引脚以实现同步启动。"}, {"pin_number": "59", "pin_name": "MODE", "pin_description": "传导模式选择引脚。施加小于0.4V以使能连续传导模式(CCM)。施加0.8V至1.2V以使能混合传导模式(HCM)。施加1.6V至2.0V以使能不连续传导模式(DCM)。施加大于2.4V以使能Burst Mode操作。建议用相同的控制信号驱动此引脚，或连接到与主LT8708相同值的电阻分压器或电压。"}, {"pin_number": "60", "pin_name": "IMON_OP", "pin_description": "平均VOUT电流调节引脚。此引脚伺服到1.207V以根据ICP和ICN电压调节平均输出电流。始终将一个17.4k电阻与一个补偿网络并联，从此引脚连接到GND。"}, {"pin_number": "61", "pin_name": "IMON_ON", "pin_description": "负VOUT电流监视器引脚。此引脚输出的电流为20μA加上与负平均VOUT电流成比例的电流。"}, {"pin_number": "62", "pin_name": "LDO33", "pin_description": "3.3V稳压器输出。用至少0.1μF的陶瓷电容旁路到地。建议使用与主LT8708相同值的旁路电容。"}, {"pin_number": "63", "pin_name": "CLKOUT", "pin_description": "时钟输出引脚。用于同步一个或多个兼容的开关稳压器IC。CLKOUT的切换频率与内部振荡器或SYNC引脚相同，但相位大约相差180°。CLKOUT也可用作温度监视器，因为其占空比与器件结温线性相关。CLKOUT引脚可驱动高达200pF的容性负载。"}, {"pin_number": "65", "pin_name": "GND (Exposed Pad)", "pin_description": "地。直接连接到本地接地平面。"}]}], "datasheet_cn": "未找到", "datasheet_en": {"name": "lt8708-1.pdf", "path": "lt8708-1.pdf", "date": "2025-02-01", "version": "Rev C"}, "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "80V", "min_input_voltage": "5.5V", "max_output_voltage": "80V", "min_output_voltage": "1.2V", "max_output_current": "Dependent on external components", "max_switch_frequency": "400kHz", "quiescent_current": "4700μA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "Adjustable", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode, DCM, HCM", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.25%", "output_reference_voltage": "1.207V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "QFN", "pitch": "0.5", "height": "1.6", "width": "5.0", "length": "8.0", "pin_count": "8708"}]}