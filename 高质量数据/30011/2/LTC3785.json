{"part_number": "LTC3785", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "10V, High Efficiency, Synchronous, No RSENSE Buck-Boost Controller", "features": ["Single Inductor Architecture Allows VIN Above, Below or Equal to VOUT", "2.7V to 10V Input and Output Range", "Up to 96% Efficiency", "Up to 10A of Output Current", "All N-channel MOSFETs, No RSENSE™", "True Output Disconnect During Shutdown", "Programmable Current Limit and Soft-Start", "Optional Short-Circuit Shutdown Timer", "Output Overvoltage and Undervoltage Protection", "Programmable Frequency: 100kHz to 1MHz", "Selectable Burst Mode® Operation", "Available in 24-Lead (4mm × 4mm) Exposed Pad QFN Package"], "description": "The LTC®3785 is a high power synchronous buck-boost controller that drives all N-channel power MOSFETs from input voltages above, below and equal to the output voltage. With an input range of 2.7V to 10V, the LTC3785 is well suited for a wide variety of single or dual cell Li-Ion or multicell alkaline/NiMH applications. The operating frequency can be programmed from 100kHz to 1MHz. The soft-start time and current limit are also programmable. The soft-start capacitor doubles as the fault timer which can program the IC to latch off or recycle after a determined off time. Burst Mode operation is user controlled and can be enabled by driving the MODE pin high. Protection features include foldback current limit, short-circuit and overvoltage protection.", "applications": ["Palmtop Computers", "Handheld Instruments", "Wireless Modems", "Cellular Telephones"], "ordering_information": [{"part_number": "LTC3785", "order_device": "LTC3785EUF#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1697", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3785", "order_device": "LTC3785EUF#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1697", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3785", "order_device": "LTC3785IUF#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1697", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3785", "order_device": "LTC3785IUF#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1697", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3785", "order_device": "LTC3785EUF", "package_type": "QFN", "package_drawing_code": "05-08-1697", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3785", "order_device": "LTC3785EUF#TR", "package_type": "QFN", "package_drawing_code": "05-08-1697", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3785", "order_device": "LTC3785IUF", "package_type": "QFN", "package_drawing_code": "05-08-1697", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3785", "order_device": "LTC3785IUF#TR", "package_type": "QFN", "package_drawing_code": "05-08-1697", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LTC3785", "package_type": "QFN-24", "pins": [{"pin_number": "1", "pin_name": "RUN/SS", "pin_description": "Run Control and Soft-Start Input. An internal 1µA charges the soft-start capacitor. Once the pin drops below 1.225V the IC will enter fault mode. Can be used to program latch-off or recycle behavior."}, {"pin_number": "2", "pin_name": "Vc", "pin_description": "Error Amp Output. A frequency compensation network is connected from this pin to the FB pin to compensate the loop."}, {"pin_number": "3", "pin_name": "FB", "pin_description": "Feedback Pin. Connect resistor divider tap here. The feedback reference voltage is typically 1.225V."}, {"pin_number": "4", "pin_name": "VSENSE", "pin_description": "Overvoltage and Undervoltage Sense. The overvoltage threshold is internally set 10% above the regulated FB voltage and the undervoltage threshold is internally set 6.5% below the FB regulated voltage."}, {"pin_number": "5", "pin_name": "ILSET", "pin_description": "Current Limit Set. A resistor from this pin to ground sets the current limit threshold from the ISVIN and ISSW1 pins."}, {"pin_number": "6", "pin_name": "CCM", "pin_description": "Continuous Conduction Mode Control Pin. When set low, inductor current is allowed to go slightly negative. When driven high, the reverse current limit is set to a similar value of the forward current limit."}, {"pin_number": "7", "pin_name": "RT", "pin_description": "Oscillator Programming Pin. A resistor from this pin to GND sets the free-running frequency of the IC."}, {"pin_number": "8", "pin_name": "MODE", "pin_description": "Burst Mode Control Pin. High enables Burst Mode operation, Low disables it."}, {"pin_number": "9", "pin_name": "NC", "pin_description": "No Connect. There is no electrical connection to this pin inside the package."}, {"pin_number": "10", "pin_name": "ISVOUT", "pin_description": "Reverse Current Limit Comparator Non-inverting Input. Normally connected to the drain of the N-channel MOSFET D (TG2 driven)."}, {"pin_number": "11", "pin_name": "VBST2", "pin_description": "Boosted Floating Driver Supply for Boost Switch D."}, {"pin_number": "12", "pin_name": "TG2", "pin_description": "Top gate drive pin for the top N-channel MOSFET switch D."}, {"pin_number": "13", "pin_name": "SW2", "pin_description": "Ground Reference for Driver D. Common point of output switches C and D."}, {"pin_number": "14", "pin_name": "ISSW2", "pin_description": "Reverse Current Limit Comparator Inverting Input. Normally connected to the source of the N-channel MOSFET D (TG2 driven)."}, {"pin_number": "15", "pin_name": "BG2", "pin_description": "Bottom gate driver pin for the ground referenced N-channel MOSFET switch C."}, {"pin_number": "16", "pin_name": "VDRV", "pin_description": "Driver Supply for Ground Referenced Switches. Connect this pin to VCC potential."}, {"pin_number": "17", "pin_name": "BG1", "pin_description": "Bottom gate driver pin for the ground referenced N-channel MOSFET switch B."}, {"pin_number": "18", "pin_name": "ISSW1", "pin_description": "Forward Current Limit Comparator Non-inverting Input. Normally connected to the source of the N-channel MOSFET A (TG1 driven)."}, {"pin_number": "19", "pin_name": "SW1", "pin_description": "Ground Reference for Driver A. Common point of output switches A and B."}, {"pin_number": "20", "pin_name": "TG1", "pin_description": "Top gate drive pin for the top N-channel MOSFET switch A."}, {"pin_number": "21", "pin_name": "VBST1", "pin_description": "Boosted Floating Driver Supply for the Buck Switch A."}, {"pin_number": "22", "pin_name": "ISVIN", "pin_description": "Forward Current Limit Comparator Inverting Input. Normally connected to the drain of N-channel MOSFET A (TG1 driven)."}, {"pin_number": "23", "pin_name": "VCC", "pin_description": "Internal 4.35V LDO Regulator Output. The driver and control circuits are powered from this voltage."}, {"pin_number": "24", "pin_name": "VIN", "pin_description": "Input Supply Pin for the VCC Regulator."}, {"pin_number": "25", "pin_name": "Ground (Exposed Pad)", "pin_description": "The GND and PGND pins are connected to the exposed pad which must be connected to the PCB ground for electrical contact and rated thermal performance."}]}], "datasheet_cn": "未找到", "datasheet_en": "3785fc", "family_comparison": "The 'RELATED PARTS' table on page 20 compares the LTC3785 with other high-efficiency buck-boost controllers and modules from Linear Technology, highlighting differences in input/output voltage ranges, package types, and integration levels (controller vs. complete module).", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "10V", "min_input_voltage": "2.7V", "max_output_voltage": "10V", "min_output_voltage": "2.7V", "max_output_current": "10A", "max_switch_frequency": "1MHz", "quiescent_current": "86µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Fold Back", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.04%", "output_reference_voltage": "1.225V", "loop_control_mode": "电压模式"}, "package": [{"type": "QFN", "length": "4.35", "width": "1.225", "pin_count": "1", "pitch": "0.5", "height": "0.75"}]}