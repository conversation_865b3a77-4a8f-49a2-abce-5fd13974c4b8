{"part_number": "LTC3534", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "7V, 500mA Synchronous Buck-Boost DC/DC Converter", "features": ["Regulated Output with Input Voltages Above, Below or Equal to the Output", "2.4V to 7V VIN and 1.8V to 7V VOUT Ranges", "5V VOUT at 500mA from 4 AA Cells", "Single Inductor", "Synchronous Rectification: Up to 94% Efficiency", "Burst Mode® Operation with 25μA IQ", "Output Disconnect in Shutdown", "1MHz Switching Frequency", "<1μA Shutdown Current", "Small Thermally Enhanced 16-Lead (5mm × 3mm × 0.75mm) DFN and 16-Lead GN Packages"], "description": "The LTC®3534 is a wide VIN range, highly efficient, fixed frequency, buck-boost DC/DC converter that operates from input voltages above, below or equal to the output voltage. The topology incorporated in the IC provides a continuous transfer function through all operating modes, making the product ideal for multi-cell Alkaline/NiMH or single Lithium-Ion/Polymer applications where the output voltage is within the battery voltage range.\nThe LTC3534 offers extended VIN and VOUT ranges of 2.4V to 7V and 1.8V to 7V, respectively. Quiescent current is only 25μA in Burst Mode operation, maximizing battery life in portable applications. Burst Mode operation is user controlled and can be enabled by driving the PWM pin low. If the PWM pin is driven high then fixed frequency switching is enabled.\nOther features include fixed 1MHz operating frequency, a <1μA shutdown, short-circuit protection, programmable soft-start, current limit and thermal overload protection. The LTC3534 is available in the thermally enhanced 16-lead (3mm × 5mm) DFN and 16-lead GN packages.", "applications": ["Medical Instruments", "Portable Barcode Readers", "Portable Inventory Terminals", "USB to 5V Supply", "Handheld GPS"], "ordering_information": [{"part_number": "LTC3534", "order_device": "LTC3534EDHC#PBF", "package_type": "DFN", "package_drawing_code": "05-08-1706 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3534", "order_device": "LTC3534EDHC#TRPBF", "package_type": "DFN", "package_drawing_code": "05-08-1706 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3534", "order_device": "LTC3534EGN#PBF", "package_type": "SSOP", "package_drawing_code": "05-08-1641 Rev B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3534", "order_device": "LTC3534EGN#TRPBF", "package_type": "SSOP", "package_drawing_code": "05-08-1641 Rev B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3534", "order_device": "LTC3534EDHC", "package_type": "DFN", "package_drawing_code": "05-08-1706 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3534", "order_device": "LTC3534EDHC#TR", "package_type": "DFN", "package_drawing_code": "05-08-1706 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3534", "order_device": "LTC3534EGN", "package_type": "SSOP", "package_drawing_code": "05-08-1641 Rev B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3534", "order_device": "LTC3534EGN#TR", "package_type": "SSOP", "package_drawing_code": "05-08-1641 Rev B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "LTC3534", "package_type": "DFN/SSOP", "pins": [{"pin_number": "1, 8, 9, 16", "pin_name": "GND", "pin_description": "IC Substrate Grounds. These pins MUST be soldered to the printed circuit board ground to provide both electrical contact and a good thermal contact to the PCB."}, {"pin_number": "2", "pin_name": "RUN/SS", "pin_description": "Combined Shutdown and Soft-Start. Applying a voltage below 400mV shuts down the IC. Apply a voltage above 1.4V to enable the IC. An R-C from the enable command signal to this pin will provide a soft-start function."}, {"pin_number": "3", "pin_name": "GND", "pin_description": "Signal Ground for the IC."}, {"pin_number": "4, 7", "pin_name": "PGND1, PGND2", "pin_description": "Power Ground for the Internal N-channel MOSFET Power Switches (Switches B and C)."}, {"pin_number": "5", "pin_name": "SW1", "pin_description": "Switch Pin where Internal Switches A and B are Connected. Connect inductor from SW1 to SW2."}, {"pin_number": "6", "pin_name": "SW2", "pin_description": "Switch Pin where Internal Switches C and D are Connected. Minimize trace length to reduce EMI."}, {"pin_number": "10", "pin_name": "PWM", "pin_description": "Burst Mode Select. PWM must be driven HIGH during start-up. Applying a voltage below 400mV enables Burst Mode operation. Applying a voltage above 1.4V disables Burst Mode operation."}, {"pin_number": "11", "pin_name": "VOUT", "pin_description": "Output of the Synchronous Rectifier. A filter capacitor is placed from VOUT to GND."}, {"pin_number": "12", "pin_name": "PVIN", "pin_description": "Power VIN Supply Pin. A 10μF ceramic capacitor is recommended."}, {"pin_number": "13", "pin_name": "VIN", "pin_description": "Input Supply Pin. Connect the power source to this pin."}, {"pin_number": "14", "pin_name": "VC", "pin_description": "Error Amp Output. An R-C network is connected from this pin to FB for loop compensation."}, {"pin_number": "15", "pin_name": "FB", "pin_description": "Feedback Pin. Connect VOUT resistor divider tap to this pin."}, {"pin_number": "17 (Exposed Pad for DHC)", "pin_name": "Exposed Pad", "pin_description": "IC Substrate Ground. This pin MUST be soldered to the printed circuit board ground."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3534", "family_comparison": "RELATED PARTS table on page 20", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "7V", "min_input_voltage": "2.4V", "max_output_voltage": "7V", "min_output_voltage": "1.8V", "max_output_current": "0.5A", "max_switch_frequency": "1MHz", "quiescent_current": "25µA", "high_side_mosfet_resistance": "260mΩ", "low_side_mosfet_resistance": "275mΩ", "over_current_protection_threshold": "1.8A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Fold Back", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "1V", "loop_control_mode": "Voltage Mode"}, "package": [{"pitch": "0.5", "height": "0.75", "length": "4.9", "width": "3.9", "type": "DESCRIPTION", "pin_count": "16"}]}