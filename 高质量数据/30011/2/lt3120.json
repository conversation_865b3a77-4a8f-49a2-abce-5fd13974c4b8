{"part_number": "LT3120", "manufacturer": "Analog Devices", "country": "United States", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "LT3120 26V, 9A Low-IQ CC/CV Monolithic Buck-Boost Converter", "features": ["Input Voltage Range: 2.5V to 26V", "0.8V to 24V Output Voltage Range", "High Output Current", "6.5A with VOUT = 5V, VIN > 6V", "3A with VOUT = 5V, VIN = 3V", "6.5A with VOUT = 12V, VIN > 14V", "4A with VOUT = 12V, VIN = 9V", "Ultralow Noise Buck-Boost Architecture", "Programmable Output Current Limit", "Programmable Frequency Range: 400kHz to 2MHz", "Accurate Enable Comparator <PERSON><PERSON><PERSON><PERSON>", "Burst Mode® Operation, No-Load IQ = 35µA", "Current Mode Control", "External Clock Synchronization", "Maximum Power Point Control", "28-Lead 4mm × 5mm LQFN Package"], "description": "The LT®3120 is a high efficiency 26V monolithic buck-boost converter. Extensive feature integration and very low resistance internal power switches minimize the total solution footprint for even the most demanding applications. A proprietary 4-switch PWM architecture provides seamless low noise operation from input voltages above, equal to, or below the output voltage. External frequency programming as well as synchronization using an internal PLL enable operation over a wide switching frequency range of 400kHz to 2MHz. The wide 2.5V to 26V input range is well suited for operation from unregulated power sources including battery stacks and backup capacitors. After start-up, operation is possible with input voltages as low as 500mV. Other features include, output short-circuit protection, thermal overload protection, less than 3µA shutdown current, power good indicator, Burst Mode operation, and maximum power point control. The LT3120 is offered in thermally enhanced 28-lead 4mm × 5mm LQFN package.", "applications": ["RF Power Supply", "USB Power Delivery", "System Backup Power Supply", "1-Cell to 5-Cell Lithium Battery Powered Products", "Wide Input Range Power Supply", "Lead Acid to 12V Regulator"], "ordering_information": [{"part_number": "LT3120", "order_device": "LT3120JV#PBF", "package_type": "LQFN", "package_drawing_code": "05-08-1603 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}], "pin_function": [{"product_part_number": "LT3120", "package_type": "LQFN-28", "pins": [{"pin_number": "1", "pin_name": "BST3", "pin_description": "Flying Capacitor Pin for Output Current Sense Amplifier. This pin must be connected to PVOUT through a 22nF capacitor."}, {"pin_number": "2, 4", "pin_name": "PVOUT", "pin_description": "Output Voltage Power Connection. These pins are connected to switch D of the buck-boost converter. Connect a low ESR capacitor between these pins and GND using the lowest impedance path possible."}, {"pin_number": "3, 11, 18, 22, Exposed <PERSON><PERSON> 29", "pin_name": "GND", "pin_description": "Ground Connection. These pins should be connected to the board ground using the shortest and widest connections possible. High via density should be used under the exposed pad to maximize heat transfer away from the part."}, {"pin_number": "5", "pin_name": "ISP", "pin_description": "Current Sense Positive Input. Connect this pin to the IC side of the output current sense resistor. If current sense is not being used, connect this pin to PVOUT."}, {"pin_number": "6", "pin_name": "ISN", "pin_description": "Current Sense Negative Input. Connect this pin to the VOUT side of the output current sense resistor. If current sense is not being used, connect this pin to PVOUT."}, {"pin_number": "7", "pin_name": "PGOOD", "pin_description": "Open Drain Output Indicator. When FB drops too far below its regulated voltage this output pulls to ground. Connect a pull-up resistor from this pin to a positive supply."}, {"pin_number": "8", "pin_name": "PROG", "pin_description": "Output Average Current Limit Set Point. A resistor should be connected between this pin and GND to program the maximum average output current. The output current from this pin can also be used as an analog output current indicator. To disable this function, the pin should be connected to VCC."}, {"pin_number": "9", "pin_name": "FB", "pin_description": "Feedback Voltage Input. A resistive divider connected to this pin sets the output voltage for the buck-boost converter. The nominal FB voltage is 0.795V."}, {"pin_number": "10", "pin_name": "VC", "pin_description": "Error Amplifier Output. A frequency compensation network must be connected between this pin and GND to stabilize the voltage control loop."}, {"pin_number": "12", "pin_name": "MPPC", "pin_description": "Maximum Power Point Control Pin Setpoint. Connect this pin to a resistive divider from VIN to GND to set the input regulation voltage. When not being used, the MPPC pin should be tied to VCC."}, {"pin_number": "13", "pin_name": "VCC", "pin_description": "Internal Regulator Output Voltage. This pin is the output of the internal low voltage linear regulator used to supply the control circuitry. A 4.7μF capacitor should be connected between this pin and GND."}, {"pin_number": "14", "pin_name": "RT", "pin_description": "Oscillator Frequency Programming Pin. Connect a resistor between this pin and GND to set the buck-boost converter switching frequency."}, {"pin_number": "15", "pin_name": "SYNC/MODE", "pin_description": "Automatic Burst Mode Operation/PWM Mode Control Pin and Synchronization Input. Controls fixed frequency PWM mode, Burst Mode operation, or synchronization to an external clock."}, {"pin_number": "16", "pin_name": "EN/UVLO", "pin_description": "Input to Enable and Disable the IC and Set Custom Input UVLO Threshold. Can be driven by logic or a resistive divider from input voltage."}, {"pin_number": "17", "pin_name": "VIN", "pin_description": "Input Voltage Pin for Internal VCC Regulator."}, {"pin_number": "19, 20, 21", "pin_name": "PVIN", "pin_description": "Input Voltage Power Connection. These pins are connected to switch A of the buck-boost converter. Connect a 10μF or larger capacitor between these pins and GND."}, {"pin_number": "23", "pin_name": "BST1", "pin_description": "Flying Capacitor Pin for SW1. This pin must be connected to SW1 through a 0.22μF capacitor. This pin is used to generate the gate drive rail for power switch A."}, {"pin_number": "24, 25", "pin_name": "SW1", "pin_description": "Buck-Boost Converter Power Switch Pin. These pins should be connected to one side of the buck-boost inductor."}, {"pin_number": "26, 27", "pin_name": "SW2", "pin_description": "Buck-Boost Converter Power Switch Pin. These pins should be connected to one side of the buck-boost inductor."}, {"pin_number": "28", "pin_name": "BST2", "pin_description": "Flying Capacitor Pin for SW2. This pin must be connected to SW2 through a 0.22μF capacitor. This pin is used to generate the gate drive rail for power switch D."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT3120", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "26V", "min_input_voltage": "2.5V", "max_output_voltage": "24V", "min_output_voltage": "1V", "max_output_current": "9A", "max_switch_frequency": "2MHz", "quiescent_current": "35µA", "high_side_mosfet_resistance": "25mΩ", "low_side_mosfet_resistance": "23mΩ", "over_current_protection_threshold": "9.5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.0%", "output_reference_voltage": "0.795V", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.5", "height": "0.74", "length": "5", "width": "1.", "type": "DESCRIPTION", "pin_count": "29"}]}