{"part_number": "LTC3112", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压-升压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "15V, 2.5A Synchronous Buck-Boost DC/DC Converter", "features": ["Regulated Output with VIN Above, Below or Equal to VOUT", "2.7V to 15V Input Voltage Range", "2.5V to 14V Output Voltage Range", "2.5A Continuous Output Current: VIN ≥ 5V, VOUT = 5V, PWM Mode", "Output Current Monitor", "Up to 95% Efficiency", "750kHz Switching Frequency, Synchronizable Between 300kHz and 1.5MHz", "Internal N-Channel MOSFETS", "Selectable Burst Mode® Operation, IQ = 50µA", "Shutdown Current < 1µA", "Overvoltage Protection", "Output Disconnect in Shutdown", "Internal Soft-Start", "Small, Thermally Enhanced 16-Lead (4mm × 5mm × 0.75mm) DFN and 20-Lead TSSOP Package"], "description": "The LTC3112 is a fixed frequency synchronous buck-boost DC/DC converter with an extended input and output range. The unique 4-switch, single inductor architecture provides low noise and seamless operation from input voltages above, below or equal to the output voltage. With an input range of 2.7V to 15V, the LTC3112 is well-suited for a wide variety of single or multiple cell battery, backup capacitor or wall adapter source applications. Low RDS(ON) internal N-Channel MOSFET switches provide highly efficient operation in applications with higher load current requirements. The LTC3112 features selectable PWM or Burst Mode operation, an easily synchronized oscillator and output disconnect in shutdown. An output current monitor circuit allows the load current to be controlled or measured. Other features include <1µA shutdown current, short circuit protection, soft-start, current limit and thermal shutdown. The LTC3112 is offered in both a 16-pin (4mm × 5mm × 0.75mm) DFN and 20-pin TSSOP packages.", "applications": ["3.3V or 5V from 1, 2 or 3 Li-Ion, Backup Capacitor <PERSON>", "Hand Held Inventory Terminals", "RF Transmitters", "12V Synchronous Boost Converter", "Multiple Power Input Systems", "LED Lighting with Current Regulation", "12V Lead Acid Battery to 12V"], "ordering_information": [{"part_number": "LTC3112", "order_device": "LTC3112EDHD#PBF", "package_type": "DFN", "package_drawing_code": "DHD16", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3112", "order_device": "LTC3112EDHD#TRPBF", "package_type": "DFN", "package_drawing_code": "DHD16", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3112", "order_device": "LTC3112IDHD#PBF", "package_type": "DFN", "package_drawing_code": "DHD16", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3112", "order_device": "LTC3112IDHD#TRPBF", "package_type": "DFN", "package_drawing_code": "DHD16", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3112", "order_device": "LTC3112HDHD#PBF", "package_type": "DFN", "package_drawing_code": "DHD16", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3112", "order_device": "LTC3112HDHD#TRPBF", "package_type": "DFN", "package_drawing_code": "DHD16", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3112", "order_device": "LTC3112MPDHD#PBF", "package_type": "DFN", "package_drawing_code": "DHD16", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}, {"part_number": "LTC3112", "order_device": "LTC3112MPDHD#TRPBF", "package_type": "DFN", "package_drawing_code": "DHD16", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}, {"part_number": "LTC3112", "order_device": "LTC3112EFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE20", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3112", "order_device": "LTC3112EFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE20", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3112", "order_device": "LTC3112IFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE20", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3112", "order_device": "LTC3112IFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE20", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3112", "order_device": "LTC3112HFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE20", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3112", "order_device": "LTC3112HFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE20", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3112", "order_device": "LTC3112MPFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE20", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}, {"part_number": "LTC3112", "order_device": "LTC3112MPFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE20", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}], "pin_function": [{"product_part_number": "LTC3112", "package_type": "DFN-16", "pins": [{"pin_number": "1", "pin_name": "COMP", "pin_description": "Error Amp Output. An R-C network connected from this pin to FB sets the loop compensation for the voltage converter."}, {"pin_number": "2", "pin_name": "FB", "pin_description": "Feedback Voltage Input. Connect VOUT resistor divider tap to this pin. The output voltage can be adjusted from 2.5V to 14V."}, {"pin_number": "3", "pin_name": "OVP", "pin_description": "Overvoltage Protection Input. The common point of a resistor divider between VOUT and GND can also be used to program the overvoltage protection to a lower voltage."}, {"pin_number": "4, 5", "pin_name": "VIN", "pin_description": "Input Supply Voltage. This pin should be bypassed to the ground plane with at least 10µF of low ESR, low ESL ceramic capacitance."}, {"pin_number": "6", "pin_name": "RUN", "pin_description": "Shutdown Control Input. Operation will be disabled when the voltage is forced below 0.75V (typical) and less than 1µA of quiescent current will be consumed."}, {"pin_number": "7", "pin_name": "IOUT", "pin_description": "A Current approximately 24µA/A of the D Switch Output Current is Sourced from this Pin. An R-C circuit can be used to control the average output current or provide an analog output current monitor."}, {"pin_number": "8", "pin_name": "VOUT", "pin_description": "Regulated Output Voltage. This pin should be connected to a low ESR ceramic capacitor of at least 47µF."}, {"pin_number": "9, 10", "pin_name": "SW2", "pin_description": "Internal switches C and D and the external inductor are connected here."}, {"pin_number": "11", "pin_name": "BST2", "pin_description": "Boosted Floating Driver Supply for D-Switch Driver. Connect a 0.1µF capacitor from this pin to SW2."}, {"pin_number": "12, 13", "pin_name": "SW1", "pin_description": "Internal switches A and B and the external inductor are connected here."}, {"pin_number": "14", "pin_name": "BST1", "pin_description": "Boosted Floating Driver Supply for A-Switch Driver. Connect a 0.1µF capacitor from this pin to SW1."}, {"pin_number": "15", "pin_name": "VCC", "pin_description": "External Capacitor Connection for the Regulated VCC Supply. This supply is used to operate internal circuitry and switch drivers. VCC will track VIN up to 4.2V, but will maintain this voltage when VIN > 4.2V. Connect a 1µF ceramic capacitor from this pin to GND."}, {"pin_number": "16", "pin_name": "PWM/SYNC", "pin_description": "Burst Mode Control and Synchronization Input. A DC voltage <0.5V commands Burst Mode operation, >1.5V commands 750kHz fixed frequency mode. A digital pulse train between 300kHz and 1500kHz applied to this pin will override the internal oscillator and set the operating frequency."}, {"pin_number": "17 (Exposed Pad)", "pin_name": "GND", "pin_description": "Ground. Small-Signal and Power Ground for the IC. The exposed pad must be soldered to the PCB and electrically connected to ground."}]}, {"product_part_number": "LTC3112", "package_type": "TSSOP-20", "pins": [{"pin_number": "1, 10, 11, 20, 21 (Exposed Pad)", "pin_name": "GND", "pin_description": "Ground. Small-Signal and Power Ground for the IC. The exposed pad must be soldered to the PCB and electrically connected to ground."}, {"pin_number": "2", "pin_name": "COMP", "pin_description": "Error Amp Output. An R-C network connected from this pin to FB sets the loop compensation for the voltage converter."}, {"pin_number": "3", "pin_name": "FB", "pin_description": "Feedback Voltage Input. Connect VOUT resistor divider tap to this pin. The output voltage can be adjusted from 2.5V to 14V."}, {"pin_number": "4", "pin_name": "OVP", "pin_description": "Overvoltage Protection Input. The common point of a resistor divider between VOUT and GND can also be used to program the overvoltage protection to a lower voltage."}, {"pin_number": "5, 6", "pin_name": "VIN", "pin_description": "Input Supply Voltage. This pin should be bypassed to the ground plane with at least 10µF of low ESR, low ESL ceramic capacitance."}, {"pin_number": "7", "pin_name": "RUN", "pin_description": "Shutdown Control Input. Operation will be disabled when the voltage is forced below 0.75V (typical) and less than 1µA of quiescent current will be consumed."}, {"pin_number": "8", "pin_name": "IOUT", "pin_description": "A Current approximately 24µA/A of the D Switch Output Current is Sourced from this Pin. An R-C circuit can be used to control the average output current or provide an analog output current monitor."}, {"pin_number": "9", "pin_name": "VOUT", "pin_description": "Regulated Output Voltage. This pin should be connected to a low ESR ceramic capacitor of at least 47µF."}, {"pin_number": "12, 13", "pin_name": "SW2", "pin_description": "Internal switches C and D and the external inductor are connected here."}, {"pin_number": "14", "pin_name": "BST2", "pin_description": "Boosted Floating Driver Supply for D-Switch Driver. Connect a 0.1µF capacitor from this pin to SW2."}, {"pin_number": "15, 16", "pin_name": "SW1", "pin_description": "Internal switches A and B and the external inductor are connected here."}, {"pin_number": "17", "pin_name": "BST1", "pin_description": "Boosted Floating Driver Supply for A-Switch Driver. Connect a 0.1µF capacitor from this pin to SW1."}, {"pin_number": "18", "pin_name": "VCC", "pin_description": "External Capacitor Connection for the Regulated VCC Supply. This supply is used to operate internal circuitry and switch drivers. VCC will track VIN up to 4.2V, but will maintain this voltage when VIN > 4.2V. Connect a 1µF ceramic capacitor from this pin to GND."}, {"pin_number": "19", "pin_name": "PWM/SYNC", "pin_description": "Burst Mode Control and Synchronization Input. A DC voltage <0.5V commands Burst Mode operation, >1.5V commands 750kHz fixed frequency mode. A digital pulse train between 300kHz and 1500kHz applied to this pin will override the internal oscillator and set the operating frequency."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3112.pdf", "family_comparison": {"family_name": "LTC3xxx Buck-Boost Converters", "comparison_table": [{"Part Number": "LTC3531", "Description": "200mA Buck-Boost Synchronous DC/DC Converter", "Comments": "VIN = 1.8V to 5.5V, VOUT = 3.3V, IQ = 16µA, ISD < 1µA, SOT23, DFN Package"}, {"Part Number": "LTC3129", "Description": "15V, 200mA Synchronous Buck-Boost Converter", "Comments": "VIN = 2.42V to 15V, VOUT = 1.4V to 15.75V, IQ = 1.3µA, ISD < 10nA, QFN and MSOP Packages"}, {"Part Number": "LTC3533", "Description": "2A (IOUT), 2MHz Synchronous Buck-Boost DC/DC Converter", "Comments": "VIN = 1.8V to 5.5V, VOUT = 1.8V to 5.25V, IQ = 40µA, ISD < 1µA, DFN Package"}, {"Part Number": "LTC3113", "Description": "3A Low Noise Synchronous Buck-Boost DC/DC Converter", "Comments": "VIN or VOUT = 1.8V to 5.5V, IQ = 40µA, ISD < 1µA, DFN and TSSOP Packages"}, {"Part Number": "LTC3780", "Description": "High Efficiency, Synchronous, 4-<PERSON><PERSON> <PERSON>-<PERSON><PERSON> Converter", "Comments": "VIN = 4V to 36V, VOUT = 0.8V to 30V, IQ = 1500µA, ISD < 55µA, QFN Package"}]}, "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "15V", "min_input_voltage": "2.7V", "max_output_voltage": "14V", "min_output_voltage": "2.5V", "max_output_current": "2.5A", "max_switch_frequency": "1.5MHz", "quiescent_current": "50µA", "high_side_mosfet_resistance": "60mΩ", "low_side_mosfet_resistance": "50mΩ", "over_current_protection_threshold": "10A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Latch", "pass_through_mode": "True", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.5%", "output_reference_voltage": "0.8V", "loop_control_mode": "电压模式"}, "package": [{"pitch": "0.5", "height": "0.75", "length": "6.6", "width": "4.4", "type": "DFN", "pin_count": "16"}]}