[{"part_number": "LT3942", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "36V, 2A Synchronous Buck-Boost Converter and LED Driver", "features": ["4-Switch Single Inductor Architecture Allows VIN Above, Below or Equal to VOUT", "Constant Voltage and Constant Current Regulation", "Proprietary Peak-Buck <PERSON>-Boost Current Mode", "3V to 36V Input Voltage Range", "0V to 36V Output Voltage Range", "±1.5% Output Voltage Regulation", "±3% Output Current Regulation", "5000:1 External and 128:1 Internal PWM Dimming", "Open and Short LED Protection with Fault Reporting", "300kHz to 2MHz Fixed Switching Frequency with External Frequency Synchronization", "Flicker-Free Spread Spectrum for Low EMI", "Available in 28-Lead QFN (4mm × 5mm)", "AEC-Q100 Qualified for Automotive Applications"], "description": "The LT®3942 is a monolithic 4-switch synchronous buck-boost converter with constant voltage and constant current regulation, suitable for both voltage regulator and LED driver applications. The part can regulate the output voltage, or input/output current with input voltages above, below, or equal to the output voltage. The proprietary peak-buck peak-boost current mode control scheme allows adjustable and synchronizable 300kHz to 2MHz fixed frequency operation, or internal 25% triangle spread spectrum operation for low EMI. The LT3942 covers 3V to 36V input and 0V to 36V output with seamless low-noise mode transition. For voltage regulator applications, the LT3942 provides input or output current monitor. For LED driver applications, it provides current regulation with up to 128:1 internal and 5000:1 external PWM dimming using an optional high-side PMOS switch. Fault protection is also provided to detect output short-circuit condition and open or short LED condition.", "applications": ["Voltage Regulator with Accurate Current Limit", "General Purpose LED Driver", "Automotive and Industrial Lighting"], "ordering_information": [{"part_number": "LT3942", "order_device": "LT3942EUFD#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1712 Rev C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT3942", "order_device": "LT3942EUFD#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1712 Rev C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT3942", "order_device": "LT3942JUFD#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1712 Rev C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT3942", "order_device": "LT3942JUFD#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1712 Rev C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT3942", "order_device": "LT3942HUFD#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1712 Rev C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT3942", "order_device": "LT3942HUFD#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1712 Rev C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT3942", "order_device": "LT3942JUFDM#WPBF", "package_type": "Side Solderable QFN", "package_drawing_code": "05-08-1682 Rev 0", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT3942", "order_device": "LT3942JUFDM#WTRPBF", "package_type": "Side Solderable QFN", "package_drawing_code": "05-08-1682 Rev 0", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}], "pin_function": [{"product_part_number": "LT3942", "package_type": "QFN", "pins": [{"pin_number": "1, 2", "pin_name": "PVIN", "pin_description": "Power Input. The PVIN pins connect to the power input of the converter. Bypass this pin to ground with a ceramic capacitor."}, {"pin_number": "3", "pin_name": "VIN", "pin_description": "Bias Supply. The VIN pin supplies the internal circuitry and the INTVCC linear regulator. Connect this pin to PVIN or another power supply. Bypass this pin to ground with a ceramic capacitor."}, {"pin_number": "4", "pin_name": "INTVCC", "pin_description": "Internal 3.6V Linear Regulator Output. Powered from the VIN pin, the INTVCC supplies the internal control circuitry and gate drivers. Bypass this pin to ground with a minimum 1μF ceramic capacitor."}, {"pin_number": "5", "pin_name": "EN/UVLO", "pin_description": "Enable and Undervoltage Lockout. Force the pin below 0.3V to shut down the chip. Force the pin above 1.235V for normal operation. Can be used to program a UVLO threshold."}, {"pin_number": "6", "pin_name": "OVLO", "pin_description": "Overvoltage Lockout. The OVLO pin can be used to program an overvoltage lockout (OVLO) threshold with a resistor divider from PVIN to ground."}, {"pin_number": "7", "pin_name": "RP", "pin_description": "Internal PWM Dimming Frequency Setting. The RP pin is used to set the internal PWM dimming frequency with a resistor to ground."}, {"pin_number": "8", "pin_name": "PWM", "pin_description": "Load Switch Enable Input in Voltage Regulator or PWM Dimming Input in LED Driver. Can be used for external or internal PWM dimming."}, {"pin_number": "9", "pin_name": "VREF", "pin_description": "Voltage Reference Output. The VREF pin provides an accurate 2V reference capable of supplying up to 1mA current."}, {"pin_number": "10", "pin_name": "CTRL", "pin_description": "Control Input for ISP/ISN Current Sense Threshold. Used to program the ISP/ISN regulation current."}, {"pin_number": "11", "pin_name": "ISP", "pin_description": "Positive Terminal of ISP/ISN Current Sense Resistor (RIS). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "12", "pin_name": "ISN", "pin_description": "Negative Terminal of ISP/ISN Current Sense Resistor (RIS). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "13", "pin_name": "ISMON", "pin_description": "ISP/ISN Current Monitor Output. The ISMON pin generates a buffered voltage that is equal to ten times V(ISP-ISN) plus 0.25V offset voltage."}, {"pin_number": "14", "pin_name": "FAULT", "pin_description": "LED Fault Open Drain Output. The FAULT pin is pulled low when open LED or short LED conditions happen."}, {"pin_number": "15", "pin_name": "SS", "pin_description": "Soft-Start Timer Setting. The SS pin is used to set soft-start timer by connecting a capacitor to ground. Also used to set fault modes."}, {"pin_number": "16", "pin_name": "FB", "pin_description": "Voltage Loop Feedback Input. Used for constant-voltage regulation and LED fault protection."}, {"pin_number": "17", "pin_name": "VC", "pin_description": "Error Amplifier Output to Set Inductor Current Comparator Threshold. Used to compensate the control loop."}, {"pin_number": "18", "pin_name": "RT", "pin_description": "Switching Frequency Setting. Connect a resistor from this pin to ground to set the internal oscillator frequency from 300kHz to 2MHz."}, {"pin_number": "19", "pin_name": "SYNC/SPRD", "pin_description": "Switching Frequency Synchronization or Spread Spectrum."}, {"pin_number": "20", "pin_name": "PWMTG", "pin_description": "PWM Dimming Top Gate Drive. Drives an external high side PMOS PWM switch."}, {"pin_number": "21, 22", "pin_name": "PVOUT", "pin_description": "Power Output. The PVOUT pins connect to the power output of the converter and also serve as the positive rail for the PWMTG drive."}, {"pin_number": "23, 24", "pin_name": "SW2", "pin_description": "Boost Side Switch Node. Connects to the internal power switches."}, {"pin_number": "25", "pin_name": "BST2", "pin_description": "Boost Side Bootstrap Floating Driver Supply. Requires an external bootstrap capacitor to the SW2 pin."}, {"pin_number": "26", "pin_name": "BST1", "pin_description": "Buck Side Bootstrap Floating Driver Supply. Requires an external bootstrap capacitor to the SW1 pin."}, {"pin_number": "27, 28", "pin_name": "SW1", "pin_description": "Buck Side Switch Node. Connects to the internal power switches."}, {"pin_number": "29", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Solder the exposed pad directly to the ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT3942.pdf", "family_comparison": "LT8391/LT8391A: 60V,4-Switch Synchronous Buck-Boost LED Controller; LT3922: 40V,2A,2Hz,SychronousBoost,uckoot Mode LEDDriver; LT3932: 40V,2A,2MHz, Synchronous Buck LED Driver; LT8390/LT8390A: 60V,4-Switch Synchronous Buck-Boost DC/DC Controller; LTC3115-1/ LTC3115-2: 40V,2A,Synchronous Buck Boost DC/DC Driver; LTC3789: High Efficincy(Up to 98%) Synchronous 4-Switch Buck-Boost DC/DC Controller.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "3V", "max_output_voltage": "36V", "min_output_voltage": "1V", "max_output_current": "2A", "max_switch_frequency": "2MHz", "quiescent_current": "0.9µA", "high_side_mosfet_resistance": "150mΩ", "low_side_mosfet_resistance": "150mΩ", "over_current_protection_threshold": "2.5A", "operation_mode": "Synchronous", "output_voltage_config_method": "Adjustable", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "Pulse Skipping", "power_good_indicator": "No", "soft_start": "External", "input_over_voltage_protection": "Latch", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "1V", "loop_control_mode": "Peak Current Mode"}, "package": [{"pitch": "0.5", "length": "3.6", "width": "0.3", "type": "QFN", "pin_count": "2", "height": "0.5"}]}]