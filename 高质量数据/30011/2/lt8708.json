{"part_number": "LT8708", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "80V Synchronous 4-Switch Buck-Boost DC/DC Controller with Bidirectional Capability", "features": ["Single Inductor Allows VIN Above, Below, or Equal to VOUT", "Six Independent Forms of Regulation: VIN Current (Forward and Reverse), VOUT Current (Forward and Reverse), VIN and VOUT Voltage", "Forward and Reverse Discontinuous Conduction Mode Supported", "Supports MODE and DIR Pin Changes While Switching", "VINCHIP Range 2.8V (Need EXTVCC > 6.4V) to 80V", "VOUT Range: 1.3V to 80V", "Synchronous Rectification: Up to 99% Efficiency", "Available in 40-Lead (5mm × 8mm) QFN with High Voltage Pin Spacing and 64-Lead (10mm × 10mm) eLQFP", "AEC-Q100 Qualified for Automotive Applications"], "description": "The LT®8708 is a high performance buck-boost switching regulator controller that operates from an input voltage that can be above, below or equal to the output voltage. Features are included to simplify bidirectional power conversion in battery/capacitor backup systems and other applications that may need regulation of VOUT, VIN, IOUT, and/or IIN. Forward and reverse current can be monitored and limited for the input and output sides of the converter. All four current limits (forward input, reverse input, forward output and reverse output) can be set independently using four resistors on the PCB.\nThe MODE pin can select between discontinuous conduction mode (DCM), continuous conduction mode (CCM), hybrid conduction mode (HCM) and Burst Mode® operation. In combination with the DIR (direction) pin, the chip can be configured to process power only from VIN to VOUT or only from VOUT to VIN. With a wide 2.8V to 80V input and 1.3V to 80V output range, the LT8708 is compatible with most solar, automotive, telecom and battery-powered systems.", "applications": ["High Voltage Buck-Boost Converters", "Bidirectional Charging System", "Automotive 48V Systems"], "ordering_information": [{"part_number": "LT8708", "order_device": "LT8708EUHG#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1528 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8708", "order_device": "LT8708EUHG#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1528 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8708", "order_device": "LT8708IUHG#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1528 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8708", "order_device": "LT8708IUHG#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1528 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8708", "order_device": "LT8708HUHG#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1528 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8708", "order_device": "LT8708HUHG#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1528 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8708", "order_device": "LT8708IUHG#WPBF", "package_type": "QFN", "package_drawing_code": "05-08-1528 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8708", "order_device": "LT8708IUHG#WTRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1528 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8708", "order_device": "LT8708HUHG#WPBF", "package_type": "QFN", "package_drawing_code": "05-08-1528 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8708", "order_device": "LT8708HUHG#WTRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1528 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8708", "order_device": "LT8708ELWE#PBF", "package_type": "eLQFP", "package_drawing_code": "05-08-1982 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8708", "order_device": "LT8708ILWE#PBF", "package_type": "eLQFP", "package_drawing_code": "05-08-1982 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8708", "order_device": "LT8708HLWE#PBF", "package_type": "eLQFP", "package_drawing_code": "05-08-1982 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8708", "order_device": "LT8708ELWE#WPBF", "package_type": "eLQFP", "package_drawing_code": "05-08-1982 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8708", "order_device": "LT8708ILWE#WPBF", "package_type": "eLQFP", "package_drawing_code": "05-08-1982 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8708", "order_device": "LT8708HLWE#WPBF", "package_type": "eLQFP", "package_drawing_code": "05-08-1982 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}], "pin_function": [{"product_part_number": "LT8708", "package_type": "QFN/eLQFP", "pins": [{"pin_number": "1/63", "pin_name": "CLKOUT", "pin_description": "Clock Output Pin. Use this pin to synchronize one or more compatible switching regulator ICs to the LT8708. CLKOUT toggles at the same frequency as the internal oscillator or as the SYNC pin, but is approximately 180° out of phase. CLKOUT may also be used as a temperature monitor since the CLKOUT duty cycle varies linearly with the part’s junction temperature. The CLKOUT pin can drive capacitive loads up to 200pF."}, {"pin_number": "2/2", "pin_name": "SS", "pin_description": "Soft-Start Pin. Place at least 220nF of capacitance here. Upon start-up, this pin will be charged by an internal resistor to 3.3V."}, {"pin_number": "3/3", "pin_name": "SHDN", "pin_description": "Shutdown Pin. Tie high to enable chip. Ground to shut down and reduce quiescent current to a minimum. Don’t float this pin."}, {"pin_number": "4/4", "pin_name": "CSN", "pin_description": "The (–) Input to the Inductor Current Sense and DCM Detect Comparator."}, {"pin_number": "5/5", "pin_name": "CSP", "pin_description": "The (+) Input to the Inductor Current Sense and DCM Detect Comparator. The Vc pin voltage and built-in offsets between CSP and CSN pins, in conjunction with the RSENSE value, set the inductor current trip threshold."}, {"pin_number": "6/6", "pin_name": "ICN", "pin_description": "Negative VOUT Current Monitor. The current out of this pin is 20µA plus a current proportional to the negative average VOUT current."}, {"pin_number": "7/7", "pin_name": "DIR", "pin_description": "Direction pin when MODE is set for DCM (discontinuous conduction mode) or HCM (hybrid conduction mode) operation. Otherwise this pin is ignored. Connect the pin to GND to process power from the VOUT to VIN. Connect the pin to LDO33 to process power from the VIN to VOUT."}, {"pin_number": "8/8", "pin_name": "FBIN", "pin_description": "VIN Feedback Pin. This pin is connected to the input of error amplifier EA3 and is used to detect and/or regulate low VIN voltage."}, {"pin_number": "9/9", "pin_name": "FBOUT", "pin_description": "VOUT Feedback Pin. This pin is connected to the input of error amplifier EA4 and is used to detect and/or regulate high VOUT voltage."}, {"pin_number": "10/10", "pin_name": "Vc", "pin_description": "Error Amplifier Output Pin. Tie external compensation network to this pin."}, {"pin_number": "11/11", "pin_name": "IMON_INP", "pin_description": "Positive VIN Current Monitor and Limit Pin. The current out of this pin is 20µA plus a current proportional to the positive average VIN current. IMON_INP also connects to error amplifier EA5 and can be used to limit the maximum positive VIN current."}, {"pin_number": "12/12", "pin_name": "IMON_INN", "pin_description": "Negative VIN Current Monitor and Limit Pin. The current out of this pin is 20µA plus a current proportional to the negative average VIN current. IMON_INN also connects to error amplifier EA1 and can be used to limit the maximum negative VIN current."}, {"pin_number": "13/13", "pin_name": "RT", "pin_description": "Timing Resistor Pin. Adjusts the switching frequency. Place a resistor from this pin to ground to set the frequency. Do not float this pin."}, {"pin_number": "14/14", "pin_name": "SYNC", "pin_description": "To synchronize the switching frequency to an outside clock, simply drive this pin with a clock. The high voltage level of the clock needs to exceed 1.3V, and the low level should be less than 0.5V. Drive this pin to less than 0.5V to revert to the internal free-running clock."}, {"pin_number": "16/20, 18/22", "pin_name": "BG1, BG2", "pin_description": "Bottom Gate Drive. Drives the gate of the bottom N-channel MOSFETs between ground and GATEVCC."}, {"pin_number": "17/21", "pin_name": "GATEVCC", "pin_description": "Power supply for bottom gate drivers. Must be connected to the INTVCC pin. Do not power from any other supply. Locally bypass to GND."}, {"pin_number": "24/35, 19/24", "pin_name": "BOOST1, BOOST2", "pin_description": "Boosted Floating Driver Supply. The (+) terminal of the bootstrap capacitor connects here. The BOOST1 pin swings from a diode voltage below GATEVCC up to VIN + GATEVCC. The BOOST2 pin swings from a diode voltage below GATEVCC up to VOUT + GATEVCC."}, {"pin_number": "23/34, 20/25", "pin_name": "TG1, TG2", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFETs with voltage swings equal to GATEVCC superimposed on the switch node voltages."}, {"pin_number": "22/33, 21/26", "pin_name": "SW1, SW2", "pin_description": "Switch Nodes. The (–) terminals of the bootstrap capacitors connect here."}, {"pin_number": "25/37", "pin_name": "RVSOFF", "pin_description": "Reverse Conduction Disable Pin. This is an input/output open-drain pin that requires a pull up resistor. Pulling this pin low disables reverse current operation."}, {"pin_number": "26/38", "pin_name": "VOUTLOMON", "pin_description": "VOUT Low Voltage Monitor Pin. Connect a ±1% resistor divider between VOUT, VOUTLOMON and GND to set an undervoltage level on VOUT. When VOUT is lower than this level, reverse conduction is disabled to prevent drawing current from VOUT."}, {"pin_number": "27/39", "pin_name": "VINHIMON", "pin_description": "VIN High Voltage Monitor Pin. Connect a ±1% resistor divider between VIN, VINHIMON and GND in order to set an overvoltage level on VIN. When VIN is higher than this level, reverse conduction is disabled to prevent current flow into VIN."}, {"pin_number": "28/40", "pin_name": "ICP", "pin_description": "Positive VOUT Current Monitor Pin. The current out of this pin is 20µA plus a current proportional to the positive average VOUT current."}, {"pin_number": "29/42", "pin_name": "EXTVCC", "pin_description": "External VCC Input. When EXTVCC exceeds 6.4V (typical), INTVCC will be powered from this pin. When EXTVCC is lower than 6.4V, the INTVCC will be powered from VINCHIP."}, {"pin_number": "30/46", "pin_name": "CSPOUT", "pin_description": "The (+) Input to the VOUT Current Monitor Amplifier. This pin and the CSNOUT pin measure the voltage across the sense resistor, RSENSE2, to provide the VOUT current signals. Connect this pin to VOUT when not in use."}, {"pin_number": "31/47", "pin_name": "CSNOUT", "pin_description": "The (–) Input to the VOUT Current Monitor Amplifier. Connect this pin to VOUT when not in use."}, {"pin_number": "32/52", "pin_name": "CSNIN", "pin_description": "The (–) Input to the VIN Current Monitor Amplifier. This pin and the CSPIN pin measure the voltage across the sense resistor, RSENSE1, to provide the VIN current signals. Connect this pin to VIN when not in use."}, {"pin_number": "33/53", "pin_name": "CSPIN", "pin_description": "The (+) Input to the VIN Current Monitor Amplifier. Connect this pin to VIN when not in use."}, {"pin_number": "34/55", "pin_name": "VINCHIP", "pin_description": "Main Input Supply Pin for the LT8708. It must be locally bypassed to ground."}, {"pin_number": "35/57", "pin_name": "INTVCC", "pin_description": "6.3V Regulator Output. Must be connected to the GATEVCC pin. INTVCC is powered from EXTVCC when the EXTVCC voltage is higher than 6.4V, otherwise INTVCC is powered from VINCHIP. Bypass this pin to ground with a minimum 4.7µF ceramic capacitor."}, {"pin_number": "36/58", "pin_name": "SWEN", "pin_description": "Switching Regulator Enable Pin. Tie high through a resistor to enable the switching. Ground to disable switching. This pin is pulled down during shutdown, a thermal lockout or when an internal UVLO (undervoltage lockout) is detected. Don’t float this pin."}, {"pin_number": "37/59", "pin_name": "MODE", "pin_description": "Conduction Mode Select Pin. The voltage applied to this pin sets the conduction mode of the controller. Apply less than 0.4V to enable continuous conduction mode (CCM). Apply 0.8V to 1.2V to enable the hybrid conduction mode (HCM). Apply 1.6V to 2.0V to enable the discontinuous conduction mode (DCM). Apply more than 2.4V to enable Burst Mode operation."}, {"pin_number": "38/60", "pin_name": "IMON_OP", "pin_description": "Positive VOUT Current Monitor and Limit Pin. The current out of this pin is 20µA plus a current proportional to the positive average VOUT current. IMON_OP also connects to error amplifier EA6 and can be used to limit the maximum positive VOUT current."}, {"pin_number": "39/61", "pin_name": "IMON_ON", "pin_description": "Negative VOUT Current Monitor and Limit Pin. The current out of this pin is 20µA plus a current proportional to the negative average VOUT current. IMON_ON also connects to error amplifier EA2 and can be used to limit the maximum negative VOUT current."}, {"pin_number": "40/62", "pin_name": "LDO33", "pin_description": "3.3V Regulator Output. Bypass this pin to ground with a minimum 0.1µF ceramic capacitor."}, {"pin_number": "15/19, Exposed Pad 41/65", "pin_name": "GND", "pin_description": "Ground. Tie directly to local ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT8708 <PERSON>she<PERSON>, Rev. D", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "80V", "min_input_voltage": "2.8V", "max_output_voltage": "80V", "min_output_voltage": "1.3V", "max_output_current": "依赖外部元件", "max_switch_frequency": "0.4MHz", "quiescent_current": "3900µA", "high_side_mosfet_resistance": "不适用(外部MOSFET)", "low_side_mosfet_resistance": "不适用(外部MOSFET)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode, DCM, HCM", "power_good_indicator": "No", "soft_start": "External", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.25%", "output_reference_voltage": "1.207V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "QFN", "length": "8.0", "width": "5.0", "pin_count": "8708", "pitch": "0.5", "height": "1.6"}]}