{"part_number": "LTC3115-2", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "LTC3115-2 40V, 2A Synchronous Buck-Boost DC/DC Converter", "features": ["Wide VIN Range: 2.7V to 40V", "Wide Vout Range: 2.7V to 40V", "0.8A Output Current for Vın ≥ 3.6V, Vout = 5V", "2A Output Current in Step-Down Operation for VIN ≥ 6V", "Programmable Frequency: 100kHz to 2MHz", "Synchronizable Up to 2MHz with an External Clock", "Up to 95% Efficiency", "30μA No-Load Quiescent Current in Burst Mode® Operation", "Ultralow Noise Buck-Boost PWM", "Internal Soft-Start", "3μΑ Supply Current in Shutdown", "Programmable Input Undervoltage Lockout", "Small 4mm x 5mm × 0.75mm DFN Package", "Thermally Enhanced 20-Lead TSSOP Package", "AEC-Q100 Qualified for Automotive Applications"], "description": "The LTC®3115-2 is a high voltage monolithic synchronous buck-boost DC/DC converter optimized for applications subject to fast (<1ms) input voltage transients. For all other applications, the LTC3115-1 is recommended. With its wide 2.7V to 40V input and output voltage ranges, the LTC3115-2 is well suited for use in a wide variety of automotive and industrial power supplies. A proprietary low noise switching algorithm optimizes efficiency with input voltages that are above, below or even equal to the output voltage and ensures seamless transitions between operational modes. Programmable frequency PWM mode operation provides low noise, high efficiency operation and the ability to synchronize switching to an external clock. Switching frequencies up to 2MHz are supported to allow use of small value inductors for miniaturization of the application circuit. Pin selectable Burst Mode operation reduces standby current and improves light load efficiency which, combined with a 3µA shutdown current, make the LTC3115-2 ideally suited for battery-powered applications. Additional features include output disconnect in shutdown, short-circuit protection and internal soft-start. The LTC3115-2 is available in thermally enhanced 16-lead 4mm × 5mm × 0.75mm DFN and 20-lead TSSOP packages.", "applications": ["24V/28V Industrial Applications", "Automotive Power Systems", "Telecom, Servers and Networking Equipment", "FireWire Regulator", "Multiple Power Source Supplies"], "ordering_information": [{"part_number": "LTC3115-2", "order_device": "LTC3115EDHD-2#PBF", "package_type": "16-Lead Plastic DFN", "package_drawing_code": "05-08-1707 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-2", "order_device": "LTC3115EDHD-2#TRPBF", "package_type": "16-Lead Plastic DFN", "package_drawing_code": "05-08-1707 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-2", "order_device": "LTC3115IDHD-2#PBF", "package_type": "16-Lead Plastic DFN", "package_drawing_code": "05-08-1707 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-2", "order_device": "LTC3115IDHD-2#TRPBF", "package_type": "16-Lead Plastic DFN", "package_drawing_code": "05-08-1707 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-2", "order_device": "LTC3115EFE-2#PBF", "package_type": "20-Lead Plastic TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-2", "order_device": "LTC3115EFE-2#TRPBF", "package_type": "20-Lead Plastic TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-2", "order_device": "LTC3115IFE-2#PBF", "package_type": "20-Lead Plastic TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-2", "order_device": "LTC3115IFE-2#TRPBF", "package_type": "20-Lead Plastic TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-2", "order_device": "LTC3115HFE-2#PBF", "package_type": "20-Lead Plastic TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3115-2", "order_device": "LTC3115HFE-2#TRPBF", "package_type": "20-Lead Plastic TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3115-2", "order_device": "LTC3115MPFE-2#PBF", "package_type": "20-Lead Plastic TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}, {"part_number": "LTC3115-2", "order_device": "LTC3115MPFE-2#TRPBF", "package_type": "20-Lead Plastic TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}, {"part_number": "LTC3115-2", "order_device": "LTC3115EDHD-2#WPBF", "package_type": "16-Lead Plastic DFN", "package_drawing_code": "05-08-1707 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-2", "order_device": "LTC3115EDHD-2#WTRPBF", "package_type": "16-Lead Plastic DFN", "package_drawing_code": "05-08-1707 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-2", "order_device": "LTC3115IDHD-2#WPBF", "package_type": "16-Lead Plastic DFN", "package_drawing_code": "05-08-1707 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-2", "order_device": "LTC3115IDHD-2#WTRPBF", "package_type": "16-Lead Plastic DFN", "package_drawing_code": "05-08-1707 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-2", "order_device": "LTC3115EFE-2#WPBF", "package_type": "20-Lead Plastic TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-2", "order_device": "LTC3115EFE-2#WTRPBF", "package_type": "20-Lead Plastic TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-2", "order_device": "LTC3115IFE-2#WPBF", "package_type": "20-Lead Plastic TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-2", "order_device": "LTC3115IFE-2#WTRPBF", "package_type": "20-Lead Plastic TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LTC3115-2", "package_type": "16-Lead Plastic DFN", "pins": [{"pin_number": "1", "pin_name": "RUN", "pin_description": "Input to Enable and Disable the IC and Set Custom Input UVLO Thresholds. The RUN pin can be driven by an external logic signal to enable and disable the IC. In addition, the voltage on this pin can be set by a resistor divider connected to the input voltage in order to provide an accurate undervoltage lockout threshold. The IC is enabled if RUN exceeds 1.21V nominally. Once enabled, a 0.5µA current is sourced by the RUN pin to provide hysteresis. To continuously enable the IC, this pin can be tied directly to the input voltage. The RUN pin cannot be forced more than 0.3V above Vın under any condition."}, {"pin_number": "2", "pin_name": "SW2", "pin_description": "Buck-Boost Converter Power Switch Pin. This pin should be connected to one side of the buck-boost inductor."}, {"pin_number": "3", "pin_name": "PVOUT", "pin_description": "Buck-Boost Converter Power Output. This pin should be connected to a low ESR capacitor with a value of at least 10µF. The capacitor should be placed as close to the IC as possible and should have a short return path to ground. In applications with Vout > 20V that are subject to output overload or short-circuit conditions, it is recommended that a Schottky diode be installed from SW2 (anode) to PVout (cathode). In applications subject to output short circuits through an inductive load, it is recommended that a Schottky diode be installed from ground (anode) to PVout (cathode) to limit the extent that PVOUT is driven below ground during the short-circuit transient."}, {"pin_number": "4, 5", "pin_name": "GND", "pin_description": "Signal Ground. These pins are the ground connections for the control circuitry of the IC and must be tied to ground in the application."}, {"pin_number": "6", "pin_name": "VC", "pin_description": "Error Amplifier Output. A frequency compensation network must be connected between this pin and FB to stabilize the voltage control loop."}, {"pin_number": "7", "pin_name": "FB", "pin_description": "Feedback Voltage Input. A resistor divider connected to this pin sets the output voltage for the buck-boost converter. The nominal FB voltage is 1000mV. Care should be taken in the routing of connections to this pin in order to minimize stray coupling to the switch pin traces."}, {"pin_number": "8", "pin_name": "RT", "pin_description": "Oscillator Frequency Programming Pin. A resistor placed between this pin and ground sets the switching frequency of the buck-boost converter."}, {"pin_number": "9", "pin_name": "Vcc", "pin_description": "Low Voltage Supply Input for IC Control Circuitry. This pin powers internal IC control circuitry and must be connected to the PVcc pin in the application. A 4.7µF or larger bypass capacitor should be connected between this pin and ground. The Vcc and PVcc pins must be connected together in the application."}, {"pin_number": "10", "pin_name": "VIN", "pin_description": "Power Supply Connection for Internal Circuitry and the Vcc Regulator. This pin provides power to the internal Vcc regulator and is the input voltage sense connection for the Vın divider. A 0.1µF bypass capacitor should be connected between this pin and ground. The bypass capacitor should be located as close to the IC as possible and should have a short return path to ground."}, {"pin_number": "11", "pin_name": "PVcc", "pin_description": "Internal Vcc Regulator Output. This pin is the output pin of the internal linear regulator that generates the Vcc rail from Vın. The PVcc pin is also the supply connection for the power switch gate drivers. If the trace connecting PVcc to Vcc cannot be made short in length, an additional bypass capacitor should be connected between this pin and ground. The Vcc and PVcc pins must be connected together in the application."}, {"pin_number": "12", "pin_name": "BST2", "pin_description": "Flying Capacitor Pin for SW2. This pin must be connected to SW2 through a 0.1µF capacitor. This pin is used to generate the gate drive rail for power switch D."}, {"pin_number": "13", "pin_name": "BST1", "pin_description": "Flying Capacitor Pin for SW1. This pin must be connected to SW1 through a 0.1µF capacitor. This pin is used to generate the gate drive rail for power switch A."}, {"pin_number": "14", "pin_name": "PVIN", "pin_description": "Power Input for the Buck-Boost Converter. A 4.7µF or larger bypass capacitor should be connected between this pin and ground. The bypass capacitor should be located as close to the IC as possible and should via directly down to the ground plane. When powered through long leads or from a high ESR power source, a larger bulk input capacitor (typically 47µF to 100µF) may be required to stabilize the input voltage and prevent input filter interactions which could reduce phase margin and output current capability in boost mode operation."}, {"pin_number": "15", "pin_name": "SW1", "pin_description": "Buck-Boost Converter Power Switch Pin. This pin should be connected to one side of the buck-boost inductor."}, {"pin_number": "16", "pin_name": "PWM/SYNC", "pin_description": "Burst Mode/PWM Mode Control Pin and Synchronization Input. Forcing this pin high causes the IC to operate in fixed frequency PWM mode at all loads using the internal oscillator at the frequency set by the RT Pin. Forcing this pin low places the IC into Burst Mode operation regardless of load current. If an external clock signal is connected to this pin, the buck-boost converter will synchronize its switching with the external clock using fixed frequency PWM mode operation. The pulse width (negative or positive) of the applied clock should be at least 100ns. The maximum operating voltage for the PWM/SYNC pin is 5.5V. The PWM/SYNC pin can be connected to Vcc to force it high continuously."}, {"pin_number": "17 (Exposed Pad)", "pin_name": "PGND", "pin_description": "Power Ground Connections. These pins should be connected to the power ground in the application. The exposed pad is the power ground connection. It must be soldered to the PCB and electrically connected to ground through the shortest and lowest impedance connection possible and to the PCB ground plane for rated thermal performance."}]}, {"product_part_number": "LTC3115-2", "package_type": "20-Lead Plastic TSSOP", "pins": [{"pin_number": "1, 10, 11, 20", "pin_name": "PGND", "pin_description": "Power Ground Connections. These pins should be connected to the power ground in the application. The exposed pad is the power ground connection. It must be soldered to the PCB and electrically connected to ground through the shortest and lowest impedance connection possible and to the PCB ground plane for rated thermal performance."}, {"pin_number": "2", "pin_name": "RUN", "pin_description": "Input to Enable and Disable the IC and Set Custom Input UVLO Thresholds. The RUN pin can be driven by an external logic signal to enable and disable the IC. In addition, the voltage on this pin can be set by a resistor divider connected to the input voltage in order to provide an accurate undervoltage lockout threshold. The IC is enabled if RUN exceeds 1.21V nominally. Once enabled, a 0.5µA current is sourced by the RUN pin to provide hysteresis. To continuously enable the IC, this pin can be tied directly to the input voltage. The RUN pin cannot be forced more than 0.3V above Vın under any condition."}, {"pin_number": "3", "pin_name": "SW2", "pin_description": "Buck-Boost Converter Power Switch Pin. This pin should be connected to one side of the buck-boost inductor."}, {"pin_number": "4", "pin_name": "PVOUT", "pin_description": "Buck-Boost Converter Power Output. This pin should be connected to a low ESR capacitor with a value of at least 10µF. The capacitor should be placed as close to the IC as possible and should have a short return path to ground. In applications with Vout > 20V that are subject to output overload or short-circuit conditions, it is recommended that a Schottky diode be installed from SW2 (anode) to PVout (cathode). In applications subject to output short circuits through an inductive load, it is recommended that a Schottky diode be installed from ground (anode) to PVout (cathode) to limit the extent that PVOUT is driven below ground during the short-circuit transient."}, {"pin_number": "5, 6", "pin_name": "GND", "pin_description": "Signal Ground. These pins are the ground connections for the control circuitry of the IC and must be tied to ground in the application."}, {"pin_number": "7", "pin_name": "VC", "pin_description": "Error Amplifier Output. A frequency compensation network must be connected between this pin and FB to stabilize the voltage control loop."}, {"pin_number": "8", "pin_name": "FB", "pin_description": "Feedback Voltage Input. A resistor divider connected to this pin sets the output voltage for the buck-boost converter. The nominal FB voltage is 1000mV. Care should be taken in the routing of connections to this pin in order to minimize stray coupling to the switch pin traces."}, {"pin_number": "9", "pin_name": "RT", "pin_description": "Oscillator Frequency Programming Pin. A resistor placed between this pin and ground sets the switching frequency of the buck-boost converter."}, {"pin_number": "12", "pin_name": "Vcc", "pin_description": "Low Voltage Supply Input for IC Control Circuitry. This pin powers internal IC control circuitry and must be connected to the PVcc pin in the application. A 4.7µF or larger bypass capacitor should be connected between this pin and ground. The Vcc and PVcc pins must be connected together in the application."}, {"pin_number": "13", "pin_name": "VIN", "pin_description": "Power Supply Connection for Internal Circuitry and the Vcc Regulator. This pin provides power to the internal Vcc regulator and is the input voltage sense connection for the Vın divider. A 0.1µF bypass capacitor should be connected between this pin and ground. The bypass capacitor should be located as close to the IC as possible and should have a short return path to ground."}, {"pin_number": "14", "pin_name": "PVcc", "pin_description": "Internal Vcc Regulator Output. This pin is the output pin of the internal linear regulator that generates the Vcc rail from Vın. The PVcc pin is also the supply connection for the power switch gate drivers. If the trace connecting PVcc to Vcc cannot be made short in length, an additional bypass capacitor should be connected between this pin and ground. The Vcc and PVcc pins must be connected together in the application."}, {"pin_number": "15", "pin_name": "BST2", "pin_description": "Flying Capacitor Pin for SW2. This pin must be connected to SW2 through a 0.1µF capacitor. This pin is used to generate the gate drive rail for power switch D."}, {"pin_number": "16", "pin_name": "BST1", "pin_description": "Flying Capacitor Pin for SW1. This pin must be connected to SW1 through a 0.1µF capacitor. This pin is used to generate the gate drive rail for power switch A."}, {"pin_number": "17", "pin_name": "PVIN", "pin_description": "Power Input for the Buck-Boost Converter. A 4.7µF or larger bypass capacitor should be connected between this pin and ground. The bypass capacitor should be located as close to the IC as possible and should via directly down to the ground plane. When powered through long leads or from a high ESR power source, a larger bulk input capacitor (typically 47µF to 100µF) may be required to stabilize the input voltage and prevent input filter interactions which could reduce phase margin and output current capability in boost mode operation."}, {"pin_number": "18", "pin_name": "SW1", "pin_description": "Buck-Boost Converter Power Switch Pin. This pin should be connected to one side of the buck-boost inductor."}, {"pin_number": "19", "pin_name": "PWM/SYNC", "pin_description": "Burst Mode/PWM Mode Control Pin and Synchronization Input. Forcing this pin high causes the IC to operate in fixed frequency PWM mode at all loads using the internal oscillator at the frequency set by the RT Pin. Forcing this pin low places the IC into Burst Mode operation regardless of load current. If an external clock signal is connected to this pin, the buck-boost converter will synchronize its switching with the external clock using fixed frequency PWM mode operation. The pulse width (negative or positive) of the applied clock should be at least 100ns. The maximum operating voltage for the PWM/SYNC pin is 5.5V. The PWM/SYNC pin can be connected to Vcc to force it high continuously."}, {"pin_number": "21 (Exposed Pad)", "pin_name": "PGND", "pin_description": "Power Ground Connections. These pins should be connected to the power ground in the application. The exposed pad is the power ground connection. It must be soldered to the PCB and electrically connected to ground through the shortest and lowest impedance connection possible and to the PCB ground plane for rated thermal performance."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3115-2.pdf", "family_comparison": "The LTC3115-2 belongs to the LTC31xx family of synchronous buck-boost converters, differing from parts like LTC3115-1, LTC3114-1, and LTC3112 in output current, voltage ranges, and quiescent current.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "40V", "min_input_voltage": "2.7V", "max_output_voltage": "40V", "min_output_voltage": "2.7V", "max_output_current": "2A", "max_switch_frequency": "2MHz", "quiescent_current": "50µA", "high_side_mosfet_resistance": "150mΩ", "low_side_mosfet_resistance": "150mΩ", "over_current_protection_threshold": "3A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.3%", "output_reference_voltage": "1V", "loop_control_mode": "Voltage Mode"}, "package": [{"type": "DFN", "pin_count": "20", "length": "4.7", "width": "2.", "pitch": "0.5", "height": "4.7"}]}