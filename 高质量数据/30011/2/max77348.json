[{"part_number": "MAX77348A", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "Ultra-Low IQ, Low-Noise 3.5W Buck-Boost Converter", "features": ["Input Voltage Range: 2.3V to 5.5V", "Output Voltage Range: 2.5V to 4.8V", "Ultra-Low, 3.5µA (typ) Quiescent Current", "Dynamic Voltage Scaling (DVS)", "Optimized Load Regulation Performance", "Power Good Interrupt", "Output Active Discharge", "Low, Continuous Noise Profile", "Eliminates Discontinuities Over Operating Range", "Eliminates Need for Post-Filtering Low Dropout (LDO) in Noise Sensitive Applications", "Protection Features: Undervoltage Lockout (UVLO), Thermal Shutdown, 6.4ms Soft-Start", "I2C Interface with Status Interrupts", "Programmable VOUT", "Peak Current Limit Level (IPSET1/IPSET2)"], "description": "The MAX77348 is an ultra-low quiescent current, non-inverting buck-boost converter capable of supporting up to 3.5W output power. The device employs a unique control algorithm that seamlessly transitions between the buck, buck-boost, and boost modes, minimizing discontinuities and subharmonics in the output voltage ripple. The MAX77348 is ideal for a system handling less than 3.5W output power from a single cell Li-Ion battery and requiring high system efficiency with precise voltage control. The MAX77348's low-noise operation makes it optimum to supply power for RF system power, ear bud applications, and other portable audio systems. Built-in undervoltage lockout (UVLO), output active discharge, and thermal shutdown protection ensures safe operation under abnormal operating conditions. The MAX77348 is available with a highly configurable I2C serial interface. The device is available in 16-bump, 1.77mm x 2.01mm, 0.4mm pitch WLP package.", "applications": ["True Wireless Stereo (TWS) Systems", "Narrow Band Internet of Things (NB IoT)", "Portable Audio/Wireless Systems"], "ordering_information": [{"part_number": "MAX77348A", "order_device": "MAX77348AEWE+T", "package_type": "WLP", "package_drawing_code": "21-100516", "output_voltage": "4.50V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MAX77348A/B", "package_type": "16-BUMP WLP", "pins": [{"pin_number": "A1", "pin_name": "CAP", "pin_description": "Bypass Capacitor Connection for Internal Supply. Connect through 470nF of capacitance to GND."}, {"pin_number": "A2, B2, C2", "pin_name": "GND", "pin_description": "Ground"}, {"pin_number": "A3, A4", "pin_name": "IN", "pin_description": "Input Supply. Bypass to GND with effective capacitance equal to the minimum of 5µF and the value of the derating curve for a bias voltage VIN placed as close to the device as possible."}, {"pin_number": "B1", "pin_name": "EN", "pin_description": "Chip Enable pin"}, {"pin_number": "B3, B4", "pin_name": "LVLX", "pin_description": "Switching Node. Connect to HVLX through a 1µH inductor if FETScale = 0 or a 2.2µH inductor if FETScale = 1"}, {"pin_number": "C1", "pin_name": "SCL", "pin_description": "I2C Serial Clock Input. Note the EN setting. If a version is disabled by default, use an external supplied source for the I2C interface to enable the output by I2C command."}, {"pin_number": "C3, C4", "pin_name": "HVLX", "pin_description": "Switching Node. Connect to LVLX through a 1µH inductor if FETScale = 0 or a 2.2µH inductor if FETScale = 1"}, {"pin_number": "D1", "pin_name": "SDA", "pin_description": "I2C Serial Data Input/Open-Drain Output. Note the EN setting. If a version is disabled by default, use an externally supplied source for the I2C interface to enable the output by I2C command."}, {"pin_number": "D2", "pin_name": "INTb", "pin_description": "Interrupt Output. Open-drain, connect through pullup resistor to system logic supply."}, {"pin_number": "D3, D4", "pin_name": "OUT", "pin_description": "Buck-Boost Output. If FETScale = 0, bypass to GND with effective capacitance equal to twice the value of the derating curve for a bias voltage VOUT, placed as close to the device as possible. If FETScale = 1, bypass to GND with effective capacitance equal to the value of the derating curve for a bias voltage VOUT, placed as close to the device as possible."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77348A, MAX77348B", "family_comparison": "The device offers multiple configurations affecting performance. Key settings include FETSCALE (optimizes for high or light load efficiency), INTEGEN (trades load regulation for transient settling time), and SWOFRCIN (manages internal supply for quiescent current vs. low input voltage operation). For example, with FETSCALE=0 and INTEGEN=1, the device supports 3.5W max power with optimized load regulation. With FETSCALE=1 and INTEGEN=0, it supports 1.75W max power with optimized settling time.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.3V", "max_output_voltage": "4.8V", "min_output_voltage": "2.5V", "max_output_current": "1A", "max_switch_frequency": "未找到", "quiescent_current": "3.5µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "Programmable", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "DCM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "UVLO", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "True", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2.4%", "output_reference_voltage": "不适用", "loop_control_mode": "Hysteresis Mode Control"}, "package": [{"pitch": "0.4", "length": "2.01", "width": "1.77", "type": "WLP", "pin_count": "9", "height": "0.4"}]}, {"part_number": "MAX77348B", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "Ultra-Low IQ, Low-Noise 3.5W Buck-Boost Converter", "features": ["Input Voltage Range: 2.3V to 5.5V", "Output Voltage Range: 2.5V to 4.8V", "Ultra-Low, 3.5µA (typ) Quiescent Current", "Dynamic Voltage Scaling (DVS)", "Optimized Load Regulation Performance", "Power Good Interrupt", "Output Active Discharge", "Low, Continuous Noise Profile", "Eliminates Discontinuities Over Operating Range", "Eliminates Need for Post-Filtering Low Dropout (LDO) in Noise Sensitive Applications", "Protection Features: Undervoltage Lockout (UVLO), Thermal Shutdown, 6.4ms Soft-Start", "I2C Interface with Status Interrupts", "Programmable VOUT", "Peak Current Limit Level (IPSET1/IPSET2)"], "description": "The MAX77348 is an ultra-low quiescent current, non-inverting buck-boost converter capable of supporting up to 3.5W output power. The device employs a unique control algorithm that seamlessly transitions between the buck, buck-boost, and boost modes, minimizing discontinuities and subharmonics in the output voltage ripple. The MAX77348 is ideal for a system handling less than 3.5W output power from a single cell Li-Ion battery and requiring high system efficiency with precise voltage control. The MAX77348's low-noise operation makes it optimum to supply power for RF system power, ear bud applications, and other portable audio systems. Built-in undervoltage lockout (UVLO), output active discharge, and thermal shutdown protection ensures safe operation under abnormal operating conditions. The MAX77348 is available with a highly configurable I2C serial interface. The device is available in 16-bump, 1.77mm x 2.01mm, 0.4mm pitch WLP package.", "applications": ["True Wireless Stereo (TWS) Systems", "Narrow Band Internet of Things (NB IoT)", "Portable Audio/Wireless Systems"], "ordering_information": [{"part_number": "MAX77348B", "order_device": "MAX77348BEWE+T", "package_type": "WLP", "package_drawing_code": "21-100516", "output_voltage": "4.60V", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MAX77348A/B", "package_type": "16-BUMP WLP", "pins": [{"pin_number": "A1", "pin_name": "CAP", "pin_description": "Bypass Capacitor Connection for Internal Supply. Connect through 470nF of capacitance to GND."}, {"pin_number": "A2, B2, C2", "pin_name": "GND", "pin_description": "Ground"}, {"pin_number": "A3, A4", "pin_name": "IN", "pin_description": "Input Supply. Bypass to GND with effective capacitance equal to the minimum of 5µF and the value of the derating curve for a bias voltage VIN placed as close to the device as possible."}, {"pin_number": "B1", "pin_name": "EN", "pin_description": "Chip Enable pin"}, {"pin_number": "B3, B4", "pin_name": "LVLX", "pin_description": "Switching Node. Connect to HVLX through a 1µH inductor if FETScale = 0 or a 2.2µH inductor if FETScale = 1"}, {"pin_number": "C1", "pin_name": "SCL", "pin_description": "I2C Serial Clock Input. Note the EN setting. If a version is disabled by default, use an external supplied source for the I2C interface to enable the output by I2C command."}, {"pin_number": "C3, C4", "pin_name": "HVLX", "pin_description": "Switching Node. Connect to LVLX through a 1µH inductor if FETScale = 0 or a 2.2µH inductor if FETScale = 1"}, {"pin_number": "D1", "pin_name": "SDA", "pin_description": "I2C Serial Data Input/Open-Drain Output. Note the EN setting. If a version is disabled by default, use an externally supplied source for the I2C interface to enable the output by I2C command."}, {"pin_number": "D2", "pin_name": "INTb", "pin_description": "Interrupt Output. Open-drain, connect through pullup resistor to system logic supply."}, {"pin_number": "D3, D4", "pin_name": "OUT", "pin_description": "Buck-Boost Output. If FETScale = 0, bypass to GND with effective capacitance equal to twice the value of the derating curve for a bias voltage VOUT, placed as close to the device as possible. If FETScale = 1, bypass to GND with effective capacitance equal to the value of the derating curve for a bias voltage VOUT, placed as close to the device as possible."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77348A, MAX77348B", "family_comparison": "The device offers multiple configurations affecting performance. Key settings include FETSCALE (optimizes for high or light load efficiency), INTEGEN (trades load regulation for transient settling time), and SWOFRCIN (manages internal supply for quiescent current vs. low input voltage operation). For example, with FETSCALE=0 and INTEGEN=1, the device supports 3.5W max power with optimized load regulation. With FETSCALE=1 and INTEGEN=0, it supports 1.75W max power with optimized settling time.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.3V", "max_output_voltage": "4.8V", "min_output_voltage": "2.5V", "max_output_current": "1A", "max_switch_frequency": "未找到", "quiescent_current": "3.5µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "Programmable", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "DCM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "UVLO", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "True", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2.4%", "output_reference_voltage": "不适用", "loop_control_mode": "Hysteresis Mode Control"}, "package": [{"pitch": "0.4", "length": "2.01", "width": "1.77", "type": "WLP", "pin_count": "9", "height": "0.4"}]}]