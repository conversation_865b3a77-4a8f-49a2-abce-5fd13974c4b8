{"part_number": "LT8292", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "60V Low IQ Full-Featured Synchronous Buck-Boost Controller", "features": ["4-Switch Single Inductor Architecture that Allows VIN Above, Below, or Equal to VOUT", "Proprietary Buck-Boost Peak Current Mode", "Low Ripple Burst Mode: 30µA IQ", "Forced Continuous Mode or Pulse-Skipping Operation", "Split Gate Driver Outputs", "Selectable Dead Time Settings", "Leaderless Current Sharing", "Adjustable and Phase-lockable: 100kHz to 650kHz", "Spread Spectrum Frequency Modulation", "±2% Output Voltage Accuracy: 1V ≤ VOUT ≤ 60V", "±5% Input or Output Average Current Accuracy", "Integrated Bootstrap Diodes", "VOUT Disconnected from VIN During Shutdown", "38-Lead <PERSON> Shrink Small Outline Package (TSSOP) with Exposed Pad", "Automotive Qualified"], "description": "The LT8292 is a synchronous 4-switch buck-boost controller that regulates output voltage and output or input current from an input voltage above, below, or equal to the output voltage. The proprietary buck-boost peak current mode architecture allows adjustable and phase-lockable 100kHz to 650kHz fixed frequency operation, or internal spread spectrum operation for low electromagnetic interference (EMI). When the output is regulated above 5V, the LT8292 can operate from an input supply as low as 3V after start-up. The low quiescent current at no-load extends operating run time in battery-powered systems. At light load, either forced continuous, pulse-skipping, or low-ripple Burst Mode can be selected. The LT8292 features split pull-up/pull-down gate drivers and four selectable dead time settings with shoot-through protection, which allows both optimized efficiency and EMI. Fault protection is also provided to detect output short-circuit conditions, during which the LT8292 runs in low-duty cycle auto-retry mode.", "applications": ["Automotive, Industrial, Telecom Systems", "General Purpose <PERSON>-Boost Power Supplies", "High-Power Battery-Powered Systems"], "ordering_information": [{"part_number": "LT8292", "order_device": "LT8292AFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE38 (AA) TSSOP REV C 0910", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "application_grade": "Industry"}, {"part_number": "LT8292", "order_device": "LT8292AFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE38 (AA) TSSOP REV C 0910", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "application_grade": "Industry"}, {"part_number": "LT8292", "order_device": "LT8292AFE#WPBF", "package_type": "TSSOP", "package_drawing_code": "FE38 (AA) TSSOP REV C 0910", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "application_grade": "Auto"}, {"part_number": "LT8292", "order_device": "LT8292AFE#WTRPBF", "package_type": "TSSOP", "package_drawing_code": "FE38 (AA) TSSOP REV C 0910", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "application_grade": "Auto"}], "pin_function": [{"product_part_number": "LT8292", "package_type": "TSSOP-38", "pins": [{"pin_number": "1", "pin_name": "BG1P", "pin_description": "Buck Side Bottom Gate Driver Pull-Up Output. Drives the gate of buck side bottom N-Channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "2", "pin_name": "BG1N", "pin_description": "<PERSON> Side Bottom Gate Driver Pull-Down Output. Drives the gate of buck side bottom N-Channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "3", "pin_name": "NC", "pin_description": "NO CONNECT"}, {"pin_number": "4", "pin_name": "BST1", "pin_description": "Buck Side Bootstrap Floating Driver Supply. The BST1 pin has an integrated bootstrap Schot<PERSON>ky diode from the INTVCC pin and requires an external bootstrap capacitor to be connected to the SW1 pin. The BST1 pin swings from INTVCC to (VIN + INTVCC)."}, {"pin_number": "5", "pin_name": "TG1P", "pin_description": "Buck Side Top Gate Driver Pull-Up Output. Drives the gate of buck side top N-Channel MOSFET with a voltage swing from SW1 to BST1."}, {"pin_number": "6", "pin_name": "TG1N", "pin_description": "Buck Side Top Gate Driver Pull-Down Output. Drives the gate of buck side top N-Channel MOSFET with a voltage swing from SW1 to BST1."}, {"pin_number": "7", "pin_name": "SW1", "pin_description": "Buck Side Switch Node. The SW1 pin swings from a Schottky diode voltage drop below ground up to VIN."}, {"pin_number": "8", "pin_name": "LSP", "pin_description": "Positive Terminal of the Inductor Current Sense Resistor (RSENSE). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "9", "pin_name": "LSN", "pin_description": "Negative Terminal of the Inductor Current Sense Resistor (RSENSE). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "10", "pin_name": "NC", "pin_description": "NO CONNECT"}, {"pin_number": "11", "pin_name": "VIN", "pin_description": "Input Supply. The VIN pin must be connected to the power input to determine the buck, boost, or buck-boost operation regions. Locally bypass this pin to ground with a ceramic capacitor."}, {"pin_number": "12", "pin_name": "EN/UVLO", "pin_description": "Enable and Undervoltage Lockout. Force the pin to the ground to shut down the part and reduce the VIN quiescent current below 0.3µA. Force the pin above 1.239V for normal operation. The accurate 1.18V falling threshold can be used to program an undervoltage lockout (UVLO) threshold with a resistor divider from VIN to the ground."}, {"pin_number": "13", "pin_name": "INTVCC", "pin_description": "Internal 5V Linear Regulator Output. The INTVCC linear regulator is intelligently powered from either VIN or VOUT pins. The split gate drivers and control circuits are powered from this voltage. Bypass this pin to the ground with a minimum 4.7µF ceramic capacitor."}, {"pin_number": "14", "pin_name": "NC", "pin_description": "NO CONNECT"}, {"pin_number": "15", "pin_name": "VOUT", "pin_description": "Output Supply. The VOUT pin must be tied to the power output to determine the buck, boost, or buck-boost operation regions. Locally bypass this pin to ground with a ceramic capacitor."}, {"pin_number": "16", "pin_name": "ISP", "pin_description": "Positive Terminal of the ISP/ISN Current Sense Resistor (RIS). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "17", "pin_name": "ISN", "pin_description": "Negative Terminal of the ISP/ISN Current Sense Resistor (RIS). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "18", "pin_name": "NC", "pin_description": "NO CONNECT"}, {"pin_number": "19", "pin_name": "IGND", "pin_description": "Local Ground for Leader-less Current Sharing. When paralleling, kelvin all IGND pins to a common ground. When not in use, connect the pin to the ground."}, {"pin_number": "20", "pin_name": "ISHARE", "pin_description": "Leaderless Current Sharing Input for Paralleling. Together with the IGND pin, this pin allows equal output current sharing among multiple LT8292s in parallel. When not in use, leave the pin floating."}, {"pin_number": "21", "pin_name": "ICTRL", "pin_description": "Control Input for ISP/ISN Current Sense Threshold. The ICTRL pin generates a 10µA current and is used to program the ISP/ISN current limit. Connect ICTRL to INTVCC for the 50mV full-scale threshold or when not in use."}, {"pin_number": "22", "pin_name": "VC", "pin_description": "Error Amplifier Output to Set Inductor Current Comparator Threshold. The VC pin is used to compensate the control loop with an external RC network."}, {"pin_number": "23", "pin_name": "FB", "pin_description": "Voltage Loop Feedback Input. The FB pin is used for constant-voltage regulation and output fault protection. The internal error amplifier with output VC regulates VFB to 1.0V."}, {"pin_number": "24", "pin_name": "SS", "pin_description": "Soft-Start Timer Setting. The SS pin is used to set the soft-start timer by connecting a capacitor to the ground."}, {"pin_number": "25", "pin_name": "PGOOD", "pin_description": "Power Good Open Drain Output. The PGOOD pin is pulled high externally when the FB pin is within ±8% of the final regulation voltage."}, {"pin_number": "26", "pin_name": "RDT1", "pin_description": "<PERSON> Side Switching Dead Time Setting. Connect a resistor from this pin to the ground to select one of four dead time settings."}, {"pin_number": "27", "pin_name": "RDT2", "pin_description": "Boost Side Switching Dead Time Setting. Connect a resistor from this pin to the ground to select one of four dead time settings."}, {"pin_number": "28", "pin_name": "RT", "pin_description": "Switching Frequency Setting. Connect a resistor from this pin to the ground to set the internal oscillator frequency from 100kHz to 650kHz."}, {"pin_number": "29", "pin_name": "SYNC/MODE", "pin_description": "External Frequency Synchronization and Operation Mode Selection. This pin allows the selection of different operating modes."}, {"pin_number": "30", "pin_name": "CLKOUT", "pin_description": "Digital Clock Output. Use this pin to synchronize one or more LT8292 ICs in parallel."}, {"pin_number": "31", "pin_name": "NC", "pin_description": "NO CONNECT"}, {"pin_number": "32", "pin_name": "SW2", "pin_description": "Boost Side Switch Node. The SW2 pin swings from a <PERSON>hottky diode voltage drop below ground to VOUT."}, {"pin_number": "33", "pin_name": "TG2N", "pin_description": "Boost Side Top Gate Driver Pull-Down Output. Drives the gate of buck side top N-Channel MOSFET with a voltage swing from SW2 to BST2."}, {"pin_number": "34", "pin_name": "TG2P", "pin_description": "Boost Side Top Gate Driver Pull-Up Output. Drives the gate of buck side top N-Channel MOSFET with a voltage swing from SW2 to BST2."}, {"pin_number": "35", "pin_name": "BST2", "pin_description": "Boost Side Bootstrap Floating Driver Supply. The BST2 pin has an integrated bootstrap Schot<PERSON>ky diode from the INTVCC pin and requires an external bootstrap capacitor to be connected to the SW2 pin. The BST2 pin swings from INTVCC to (VOUT + INTVCC)."}, {"pin_number": "36", "pin_name": "NC", "pin_description": "NO CONNECT"}, {"pin_number": "37", "pin_name": "BG2N", "pin_description": "Boost Side Bottom Gate Driver Pull-Down Output. Drives the gate of buck side bottom N-Channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "38", "pin_name": "BG2P", "pin_description": "Boost Side Bottom Gate Driver Pull-Up Output. Drives the gate of buck side bottom N-Channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "39", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Solder the exposed pad directly to the ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT8292 Data Sheet, Rev. 0, 2024-09", "family_comparison": "The LT8292 is a full-featured 60V controller with low quiescent current, spread spectrum, and leaderless current sharing, suitable for automotive applications. It is compared with other family members like LT8390/A, LT8392, LT3790, LT8705, LTC3789, and LTC3780 based on VIN/VOUT range, accuracy, and package.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "60V", "min_input_voltage": "5.5V", "max_output_voltage": "60V", "min_output_voltage": "1V", "max_output_current": "Dependent on external components", "max_switch_frequency": "650kHz", "quiescent_current": "30µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode, PSM, FCM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "1V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.5", "height": "1.2", "length": "9.7", "width": "4.4", "type": "TSSOP", "pin_count": "23"}]}