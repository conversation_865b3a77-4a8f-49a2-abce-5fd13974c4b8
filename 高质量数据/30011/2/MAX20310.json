[{"part_number": "MAX20310A", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PMIC", "category_lv3": "多通道电源", "part_number_title": "Ultra-Low Quiescent Current PMIC with SIMO Buck-Boost for Wearable Applications", "features": ["Extend System Battery Use Time", "Single Inductor, Multiple Output (SIMO) Ultra-Low IQ Buck-Boost Regulator", "Battery Input Voltage from 0.7V to 2.0V", "Output Voltage Programmable From 0.9V to 4.05V", "250mW Maximum Total Input Power", "Incremental CAP Quiescent Current 1µA per channel", "84% Efficiency for 1.8V, 10mA Output", "Input Current Limited", "Dual Ultra-Low IQ 50mA LDO", "Inputs Supplied by Dual Buck-Boost Outputs", "Output Programmable from 0.5V to 3.65V", "Quiescent Current 1.1µA per LDO / 600nA per Load Switch", "Configurable as <PERSON><PERSON>", "Extend Product Shelf-Life", "Battery Seal Mode: 10nA Battery Current (typ)", "Minimize Board Area: 1.63mm x 1.63mm WLP", "Easy-to-Implement System Control", "Voltage Monitor Multiplexer", "1% Accurate Battery Inverter (±10mV at 1.0V)", "Power Button Monitor", "Buffered Output", "Power Sequencing", "Reset Output", "I2C Control Interface"], "description": "The MAX20310 is a compact power management integrated circuit (PMIC) for space-constrained, battery-powered applications where size and efficiency are critical. The device combines two single inductor, multiple output (SIMO) buck-boosted outputs with two LDOs and other system power management features like a push-button monitor and sequencing controller. The device includes a SIMO buck-boost switching regulator that provides two programmable voltage rails using a single inductor, minimizing solution footprint. The MAX20310 operates with battery voltages down to 0.7V for use with Zinc Air, Silver Oxide, or Alkaline batteries. The architecture allows for output voltages above or below the battery voltage. Additionally, the MAX20310 has two programmable low-dropout (LDO) linear regulators. The linear regulators can also operate as power switches that can disconnect the quiescent load of system peripherals. The MAX20310 includes a programmable power controller that allows the device to be configured for use in applications that require a true off state or for always-on applications. This controller provides a delayed reset signal, voltage sequencing, and customized button timing for on/off control and recovery hard reset. The device also features a multiplexer for monitoring the power inputs and outputs of each function. The MAX20310 is available in a 16-bump 0.4mm pitch 1.63mm x 1.63mm wafer-level package (WLP) and operates over the -40°C to +85°C extended temperature range.", "applications": ["Wearable Medical Devices", "Wearable Fitness Devices", "Portable Medical Devices"], "ordering_information": [{"part_number": "MAX20310A", "order_device": "MAX20310AEWE+", "package_type": "WLP", "package_drawing_code": "21-0491", "output_voltage": "可编程", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "MAX20310A", "order_device": "MAX20310AEWE+T", "package_type": "WLP", "package_drawing_code": "21-0491", "output_voltage": "可编程", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX20310", "package_type": "16 WLP", "pins": [{"pin_number": "A1", "pin_name": "GND", "pin_description": "Ground/Battery Positive Terminal"}, {"pin_number": "A2", "pin_name": "L2OUT", "pin_description": "LDO/Switch 2 Output"}, {"pin_number": "A3", "pin_name": "L1OUT", "pin_description": "LDO/Switch 1 Output"}, {"pin_number": "A4", "pin_name": "CAP", "pin_description": "Internal Supply Decoupling. Connect a minimum 1µF of capacitance to GND."}, {"pin_number": "B1", "pin_name": "MPC", "pin_description": "Multipurpose Control Input"}, {"pin_number": "B2", "pin_name": "SDA", "pin_description": "I2C Serial Data"}, {"pin_number": "B3", "pin_name": "MON", "pin_description": "Monitor Multiplexer Output"}, {"pin_number": "B4", "pin_name": "BB1OUT", "pin_description": "Buck-Boost 1 Output"}, {"pin_number": "C1", "pin_name": "KIN", "pin_description": "Key Input, Internally Pulled to GND. To signal active, short KIN to BATN."}, {"pin_number": "C2", "pin_name": "SCL", "pin_description": "I2C Serial Clock"}, {"pin_number": "C3", "pin_name": "MPO", "pin_description": "Multipurpose Output. Level shifted digital output for controlling devices referenced to the negative battery terminal."}, {"pin_number": "C4", "pin_name": "BB2OUT", "pin_description": "Buck-Boost 2 Output"}, {"pin_number": "D1", "pin_name": "KOUT", "pin_description": "Key Output. Active-low, level-shifted button status output."}, {"pin_number": "D2", "pin_name": "RST", "pin_description": "Reset Output. Active-low, open-drain output indicates completion of sequencer."}, {"pin_number": "D3", "pin_name": "BATN", "pin_description": "Battery Negative Terminal"}, {"pin_number": "D4", "pin_name": "LX", "pin_description": "Inductor Switch Connection"}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX20310 Datasheet, Rev 3, 12/21", "family_comparison": "The MAX20310 family consists of several variants (A-E) factory-programmed with different default settings to cater to various application needs without requiring initial I2C programming. Key differences are detailed in Tables 21 and 22, including default output voltages, current limits, and power-on sequencing.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "2V", "min_input_voltage": "0.7V", "max_output_voltage": "4.05V", "min_output_voltage": "1V", "max_output_current": "0.05A", "max_switch_frequency": "未找到", "quiescent_current": "1.1µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "0.6A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "No", "output_over_voltage_protection": "No", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "未找到", "loop_control_mode": "未找到"}, "package": [{"pitch": "1.63", "type": "Information", "length": "1.65", "width": "1.65", "pin_count": "0491", "height": "1.63"}]}, {"part_number": "MAX20310B", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PMIC", "category_lv3": "多通道电源", "part_number_title": "Ultra-Low Quiescent Current PMIC with SIMO Buck-Boost for Wearable Applications", "features": ["Extend System Battery Use Time", "Single Inductor, Multiple Output (SIMO) Ultra-Low IQ Buck-Boost Regulator", "Battery Input Voltage from 0.7V to 2.0V", "Output Voltage Programmable From 0.9V to 4.05V", "250mW Maximum Total Input Power", "Incremental CAP Quiescent Current 1µA per channel", "84% Efficiency for 1.8V, 10mA Output", "Input Current Limited", "Dual Ultra-Low IQ 50mA LDO", "Inputs Supplied by Dual Buck-Boost Outputs", "Output Programmable from 0.5V to 3.65V", "Quiescent Current 1.1µA per LDO / 600nA per Load Switch", "Configurable as <PERSON><PERSON>", "Extend Product Shelf-Life", "Battery Seal Mode: 10nA Battery Current (typ)", "Minimize Board Area: 1.63mm x 1.63mm WLP", "Easy-to-Implement System Control", "Voltage Monitor Multiplexer", "1% Accurate Battery Inverter (±10mV at 1.0V)", "Power Button Monitor", "Buffered Output", "Power Sequencing", "Reset Output", "I2C Control Interface"], "description": "The MAX20310 is a compact power management integrated circuit (PMIC) for space-constrained, battery-powered applications where size and efficiency are critical. The device combines two single inductor, multiple output (SIMO) buck-boosted outputs with two LDOs and other system power management features like a push-button monitor and sequencing controller. The device includes a SIMO buck-boost switching regulator that provides two programmable voltage rails using a single inductor, minimizing solution footprint. The MAX20310 operates with battery voltages down to 0.7V for use with Zinc Air, Silver Oxide, or Alkaline batteries. The architecture allows for output voltages above or below the battery voltage. Additionally, the MAX20310 has two programmable low-dropout (LDO) linear regulators. The linear regulators can also operate as power switches that can disconnect the quiescent load of system peripherals. The MAX20310 includes a programmable power controller that allows the device to be configured for use in applications that require a true off state or for always-on applications. This controller provides a delayed reset signal, voltage sequencing, and customized button timing for on/off control and recovery hard reset. The device also features a multiplexer for monitoring the power inputs and outputs of each function. The MAX20310 is available in a 16-bump 0.4mm pitch 1.63mm x 1.63mm wafer-level package (WLP) and operates over the -40°C to +85°C extended temperature range.", "applications": ["Wearable Medical Devices", "Wearable Fitness Devices", "Portable Medical Devices"], "ordering_information": [{"part_number": "MAX20310B", "order_device": "MAX20310BEWE+", "package_type": "WLP", "package_drawing_code": "21-0491", "output_voltage": "可编程", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "MAX20310B", "order_device": "MAX20310BEWE+T", "package_type": "WLP", "package_drawing_code": "21-0491", "output_voltage": "可编程", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX20310", "package_type": "16 WLP", "pins": [{"pin_number": "A1", "pin_name": "GND", "pin_description": "Ground/Battery Positive Terminal"}, {"pin_number": "A2", "pin_name": "L2OUT", "pin_description": "LDO/Switch 2 Output"}, {"pin_number": "A3", "pin_name": "L1OUT", "pin_description": "LDO/Switch 1 Output"}, {"pin_number": "A4", "pin_name": "CAP", "pin_description": "Internal Supply Decoupling. Connect a minimum 1µF of capacitance to GND."}, {"pin_number": "B1", "pin_name": "MPC", "pin_description": "Multipurpose Control Input"}, {"pin_number": "B2", "pin_name": "SDA", "pin_description": "I2C Serial Data"}, {"pin_number": "B3", "pin_name": "MON", "pin_description": "Monitor Multiplexer Output"}, {"pin_number": "B4", "pin_name": "BB1OUT", "pin_description": "Buck-Boost 1 Output"}, {"pin_number": "C1", "pin_name": "KIN", "pin_description": "Key Input, Internally Pulled to GND. To signal active, short KIN to BATN."}, {"pin_number": "C2", "pin_name": "SCL", "pin_description": "I2C Serial Clock"}, {"pin_number": "C3", "pin_name": "MPO", "pin_description": "Multipurpose Output. Level shifted digital output for controlling devices referenced to the negative battery terminal."}, {"pin_number": "C4", "pin_name": "BB2OUT", "pin_description": "Buck-Boost 2 Output"}, {"pin_number": "D1", "pin_name": "KOUT", "pin_description": "Key Output. Active-low, level-shifted button status output."}, {"pin_number": "D2", "pin_name": "RST", "pin_description": "Reset Output. Active-low, open-drain output indicates completion of sequencer."}, {"pin_number": "D3", "pin_name": "BATN", "pin_description": "Battery Negative Terminal"}, {"pin_number": "D4", "pin_name": "LX", "pin_description": "Inductor Switch Connection"}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX20310 Datasheet, Rev 3, 12/21", "family_comparison": "The MAX20310 family consists of several variants (A-E) factory-programmed with different default settings to cater to various application needs without requiring initial I2C programming. Key differences are detailed in Tables 21 and 22, including default output voltages, current limits, and power-on sequencing.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "2V", "min_input_voltage": "0.7V", "max_output_voltage": "4.05V", "min_output_voltage": "1V", "max_output_current": "0.05A", "max_switch_frequency": "未找到", "quiescent_current": "1.1µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "0.6A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "No", "output_over_voltage_protection": "No", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "未找到", "loop_control_mode": "未找到"}, "package": [{"pitch": "1.63", "type": "Information", "length": "1.65", "width": "1.65", "pin_count": "0491", "height": "1.63"}]}, {"part_number": "MAX20310C", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PMIC", "category_lv3": "多通道电源", "part_number_title": "Ultra-Low Quiescent Current PMIC with SIMO Buck-Boost for Wearable Applications", "features": ["Extend System Battery Use Time", "Single Inductor, Multiple Output (SIMO) Ultra-Low IQ Buck-Boost Regulator", "Battery Input Voltage from 0.7V to 2.0V", "Output Voltage Programmable From 0.9V to 4.05V", "250mW Maximum Total Input Power", "Incremental CAP Quiescent Current 1µA per channel", "84% Efficiency for 1.8V, 10mA Output", "Input Current Limited", "Dual Ultra-Low IQ 50mA LDO", "Inputs Supplied by Dual Buck-Boost Outputs", "Output Programmable from 0.5V to 3.65V", "Quiescent Current 1.1µA per LDO / 600nA per Load Switch", "Configurable as <PERSON><PERSON>", "Extend Product Shelf-Life", "Battery Seal Mode: 10nA Battery Current (typ)", "Minimize Board Area: 1.63mm x 1.63mm WLP", "Easy-to-Implement System Control", "Voltage Monitor Multiplexer", "1% Accurate Battery Inverter (±10mV at 1.0V)", "Power Button Monitor", "Buffered Output", "Power Sequencing", "Reset Output", "I2C Control Interface"], "description": "The MAX20310 is a compact power management integrated circuit (PMIC) for space-constrained, battery-powered applications where size and efficiency are critical. The device combines two single inductor, multiple output (SIMO) buck-boosted outputs with two LDOs and other system power management features like a push-button monitor and sequencing controller. The device includes a SIMO buck-boost switching regulator that provides two programmable voltage rails using a single inductor, minimizing solution footprint. The MAX20310 operates with battery voltages down to 0.7V for use with Zinc Air, Silver Oxide, or Alkaline batteries. The architecture allows for output voltages above or below the battery voltage. Additionally, the MAX20310 has two programmable low-dropout (LDO) linear regulators. The linear regulators can also operate as power switches that can disconnect the quiescent load of system peripherals. The MAX20310 includes a programmable power controller that allows the device to be configured for use in applications that require a true off state or for always-on applications. This controller provides a delayed reset signal, voltage sequencing, and customized button timing for on/off control and recovery hard reset. The device also features a multiplexer for monitoring the power inputs and outputs of each function. The MAX20310 is available in a 16-bump 0.4mm pitch 1.63mm x 1.63mm wafer-level package (WLP) and operates over the -40°C to +85°C extended temperature range.", "applications": ["Wearable Medical Devices", "Wearable Fitness Devices", "Portable Medical Devices"], "ordering_information": [{"part_number": "MAX20310C", "order_device": "MAX20310CEWE+", "package_type": "WLP", "package_drawing_code": "21-0491", "output_voltage": "可编程", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "MAX20310C", "order_device": "MAX20310CEWE+T", "package_type": "WLP", "package_drawing_code": "21-0491", "output_voltage": "可编程", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX20310", "package_type": "16 WLP", "pins": [{"pin_number": "A1", "pin_name": "GND", "pin_description": "Ground/Battery Positive Terminal"}, {"pin_number": "A2", "pin_name": "L2OUT", "pin_description": "LDO/Switch 2 Output"}, {"pin_number": "A3", "pin_name": "L1OUT", "pin_description": "LDO/Switch 1 Output"}, {"pin_number": "A4", "pin_name": "CAP", "pin_description": "Internal Supply Decoupling. Connect a minimum 1µF of capacitance to GND."}, {"pin_number": "B1", "pin_name": "MPC", "pin_description": "Multipurpose Control Input"}, {"pin_number": "B2", "pin_name": "SDA", "pin_description": "I2C Serial Data"}, {"pin_number": "B3", "pin_name": "MON", "pin_description": "Monitor Multiplexer Output"}, {"pin_number": "B4", "pin_name": "BB1OUT", "pin_description": "Buck-Boost 1 Output"}, {"pin_number": "C1", "pin_name": "KIN", "pin_description": "Key Input, Internally Pulled to GND. To signal active, short KIN to BATN."}, {"pin_number": "C2", "pin_name": "SCL", "pin_description": "I2C Serial Clock"}, {"pin_number": "C3", "pin_name": "MPO", "pin_description": "Multipurpose Output. Level shifted digital output for controlling devices referenced to the negative battery terminal."}, {"pin_number": "C4", "pin_name": "BB2OUT", "pin_description": "Buck-Boost 2 Output"}, {"pin_number": "D1", "pin_name": "KOUT", "pin_description": "Key Output. Active-low, level-shifted button status output."}, {"pin_number": "D2", "pin_name": "RST", "pin_description": "Reset Output. Active-low, open-drain output indicates completion of sequencer."}, {"pin_number": "D3", "pin_name": "BATN", "pin_description": "Battery Negative Terminal"}, {"pin_number": "D4", "pin_name": "LX", "pin_description": "Inductor Switch Connection"}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX20310 Datasheet, Rev 3, 12/21", "family_comparison": "The MAX20310 family consists of several variants (A-E) factory-programmed with different default settings to cater to various application needs without requiring initial I2C programming. Key differences are detailed in Tables 21 and 22, including default output voltages, current limits, and power-on sequencing.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "2V", "min_input_voltage": "0.7V", "max_output_voltage": "4.05V", "min_output_voltage": "1V", "max_output_current": "0.05A", "max_switch_frequency": "未找到", "quiescent_current": "1.1µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "0.6A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "No", "output_over_voltage_protection": "No", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "未找到", "loop_control_mode": "未找到"}, "package": [{"pitch": "1.63", "type": "Information", "length": "1.65", "width": "1.65", "pin_count": "0491", "height": "1.63"}]}, {"part_number": "MAX20310D", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PMIC", "category_lv3": "多通道电源", "part_number_title": "Ultra-Low Quiescent Current PMIC with SIMO Buck-Boost for Wearable Applications", "features": ["Extend System Battery Use Time", "Single Inductor, Multiple Output (SIMO) Ultra-Low IQ Buck-Boost Regulator", "Battery Input Voltage from 0.7V to 2.0V", "Output Voltage Programmable From 0.9V to 4.05V", "250mW Maximum Total Input Power", "Incremental CAP Quiescent Current 1µA per channel", "84% Efficiency for 1.8V, 10mA Output", "Input Current Limited", "Dual Ultra-Low IQ 50mA LDO", "Inputs Supplied by Dual Buck-Boost Outputs", "Output Programmable from 0.5V to 3.65V", "Quiescent Current 1.1µA per LDO / 600nA per Load Switch", "Configurable as <PERSON><PERSON>", "Extend Product Shelf-Life", "Battery Seal Mode: 10nA Battery Current (typ)", "Minimize Board Area: 1.63mm x 1.63mm WLP", "Easy-to-Implement System Control", "Voltage Monitor Multiplexer", "1% Accurate Battery Inverter (±10mV at 1.0V)", "Power Button Monitor", "Buffered Output", "Power Sequencing", "Reset Output", "I2C Control Interface"], "description": "The MAX20310 is a compact power management integrated circuit (PMIC) for space-constrained, battery-powered applications where size and efficiency are critical. The device combines two single inductor, multiple output (SIMO) buck-boosted outputs with two LDOs and other system power management features like a push-button monitor and sequencing controller. The device includes a SIMO buck-boost switching regulator that provides two programmable voltage rails using a single inductor, minimizing solution footprint. The MAX20310 operates with battery voltages down to 0.7V for use with Zinc Air, Silver Oxide, or Alkaline batteries. The architecture allows for output voltages above or below the battery voltage. Additionally, the MAX20310 has two programmable low-dropout (LDO) linear regulators. The linear regulators can also operate as power switches that can disconnect the quiescent load of system peripherals. The MAX20310 includes a programmable power controller that allows the device to be configured for use in applications that require a true off state or for always-on applications. This controller provides a delayed reset signal, voltage sequencing, and customized button timing for on/off control and recovery hard reset. The device also features a multiplexer for monitoring the power inputs and outputs of each function. The MAX20310 is available in a 16-bump 0.4mm pitch 1.63mm x 1.63mm wafer-level package (WLP) and operates over the -40°C to +85°C extended temperature range.", "applications": ["Wearable Medical Devices", "Wearable Fitness Devices", "Portable Medical Devices"], "ordering_information": [{"part_number": "MAX20310D", "order_device": "MAX20310DEWE+", "package_type": "WLP", "package_drawing_code": "21-0491", "output_voltage": "可编程", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "MAX20310D", "order_device": "MAX20310DEWE+T", "package_type": "WLP", "package_drawing_code": "21-0491", "output_voltage": "可编程", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX20310", "package_type": "16 WLP", "pins": [{"pin_number": "A1", "pin_name": "GND", "pin_description": "Ground/Battery Positive Terminal"}, {"pin_number": "A2", "pin_name": "L2OUT", "pin_description": "LDO/Switch 2 Output"}, {"pin_number": "A3", "pin_name": "L1OUT", "pin_description": "LDO/Switch 1 Output"}, {"pin_number": "A4", "pin_name": "CAP", "pin_description": "Internal Supply Decoupling. Connect a minimum 1µF of capacitance to GND."}, {"pin_number": "B1", "pin_name": "MPC", "pin_description": "Multipurpose Control Input"}, {"pin_number": "B2", "pin_name": "SDA", "pin_description": "I2C Serial Data"}, {"pin_number": "B3", "pin_name": "MON", "pin_description": "Monitor Multiplexer Output"}, {"pin_number": "B4", "pin_name": "BB1OUT", "pin_description": "Buck-Boost 1 Output"}, {"pin_number": "C1", "pin_name": "KIN", "pin_description": "Key Input, Internally Pulled to GND. To signal active, short KIN to BATN."}, {"pin_number": "C2", "pin_name": "SCL", "pin_description": "I2C Serial Clock"}, {"pin_number": "C3", "pin_name": "MPO", "pin_description": "Multipurpose Output. Level shifted digital output for controlling devices referenced to the negative battery terminal."}, {"pin_number": "C4", "pin_name": "BB2OUT", "pin_description": "Buck-Boost 2 Output"}, {"pin_number": "D1", "pin_name": "KOUT", "pin_description": "Key Output. Active-low, level-shifted button status output."}, {"pin_number": "D2", "pin_name": "RST", "pin_description": "Reset Output. Active-low, open-drain output indicates completion of sequencer."}, {"pin_number": "D3", "pin_name": "BATN", "pin_description": "Battery Negative Terminal"}, {"pin_number": "D4", "pin_name": "LX", "pin_description": "Inductor Switch Connection"}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX20310 Datasheet, Rev 3, 12/21", "family_comparison": "The MAX20310 family consists of several variants (A-E) factory-programmed with different default settings to cater to various application needs without requiring initial I2C programming. Key differences are detailed in Tables 21 and 22, including default output voltages, current limits, and power-on sequencing.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "2V", "min_input_voltage": "0.7V", "max_output_voltage": "4.05V", "min_output_voltage": "1V", "max_output_current": "0.05A", "max_switch_frequency": "未找到", "quiescent_current": "1.1µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "0.6A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "No", "output_over_voltage_protection": "No", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "未找到", "loop_control_mode": "未找到"}, "package": [{"pitch": "1.63", "type": "Information", "length": "1.65", "width": "1.65", "pin_count": "0491", "height": "1.63"}]}, {"part_number": "MAX20310E", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PMIC", "category_lv3": "多通道电源", "part_number_title": "Ultra-Low Quiescent Current PMIC with SIMO Buck-Boost for Wearable Applications", "features": ["Extend System Battery Use Time", "Single Inductor, Multiple Output (SIMO) Ultra-Low IQ Buck-Boost Regulator", "Battery Input Voltage from 0.7V to 2.0V", "Output Voltage Programmable From 0.9V to 4.05V", "250mW Maximum Total Input Power", "Incremental CAP Quiescent Current 1µA per channel", "84% Efficiency for 1.8V, 10mA Output", "Input Current Limited", "Dual Ultra-Low IQ 50mA LDO", "Inputs Supplied by Dual Buck-Boost Outputs", "Output Programmable from 0.5V to 3.65V", "Quiescent Current 1.1µA per LDO / 600nA per Load Switch", "Configurable as <PERSON><PERSON>", "Extend Product Shelf-Life", "Battery Seal Mode: 10nA Battery Current (typ)", "Minimize Board Area: 1.63mm x 1.63mm WLP", "Easy-to-Implement System Control", "Voltage Monitor Multiplexer", "1% Accurate Battery Inverter (±10mV at 1.0V)", "Power Button Monitor", "Buffered Output", "Power Sequencing", "Reset Output", "I2C Control Interface"], "description": "The MAX20310 is a compact power management integrated circuit (PMIC) for space-constrained, battery-powered applications where size and efficiency are critical. The device combines two single inductor, multiple output (SIMO) buck-boosted outputs with two LDOs and other system power management features like a push-button monitor and sequencing controller. The device includes a SIMO buck-boost switching regulator that provides two programmable voltage rails using a single inductor, minimizing solution footprint. The MAX20310 operates with battery voltages down to 0.7V for use with Zinc Air, Silver Oxide, or Alkaline batteries. The architecture allows for output voltages above or below the battery voltage. Additionally, the MAX20310 has two programmable low-dropout (LDO) linear regulators. The linear regulators can also operate as power switches that can disconnect the quiescent load of system peripherals. The MAX20310 includes a programmable power controller that allows the device to be configured for use in applications that require a true off state or for always-on applications. This controller provides a delayed reset signal, voltage sequencing, and customized button timing for on/off control and recovery hard reset. The device also features a multiplexer for monitoring the power inputs and outputs of each function. The MAX20310 is available in a 16-bump 0.4mm pitch 1.63mm x 1.63mm wafer-level package (WLP) and operates over the -40°C to +85°C extended temperature range.", "applications": ["Wearable Medical Devices", "Wearable Fitness Devices", "Portable Medical Devices"], "ordering_information": [{"part_number": "MAX20310E", "order_device": "MAX20310EEWE+", "package_type": "WLP", "package_drawing_code": "21-0491", "output_voltage": "可编程", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "MAX20310E", "order_device": "MAX20310EEWE+T", "package_type": "WLP", "package_drawing_code": "21-0491", "output_voltage": "可编程", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX20310", "package_type": "16 WLP", "pins": [{"pin_number": "A1", "pin_name": "GND", "pin_description": "Ground/Battery Positive Terminal"}, {"pin_number": "A2", "pin_name": "L2OUT", "pin_description": "LDO/Switch 2 Output"}, {"pin_number": "A3", "pin_name": "L1OUT", "pin_description": "LDO/Switch 1 Output"}, {"pin_number": "A4", "pin_name": "CAP", "pin_description": "Internal Supply Decoupling. Connect a minimum 1µF of capacitance to GND."}, {"pin_number": "B1", "pin_name": "MPC", "pin_description": "Multipurpose Control Input"}, {"pin_number": "B2", "pin_name": "SDA", "pin_description": "I2C Serial Data"}, {"pin_number": "B3", "pin_name": "MON", "pin_description": "Monitor Multiplexer Output"}, {"pin_number": "B4", "pin_name": "BB1OUT", "pin_description": "Buck-Boost 1 Output"}, {"pin_number": "C1", "pin_name": "KIN", "pin_description": "Key Input, Internally Pulled to GND. To signal active, short KIN to BATN."}, {"pin_number": "C2", "pin_name": "SCL", "pin_description": "I2C Serial Clock"}, {"pin_number": "C3", "pin_name": "MPO", "pin_description": "Multipurpose Output. Level shifted digital output for controlling devices referenced to the negative battery terminal."}, {"pin_number": "C4", "pin_name": "BB2OUT", "pin_description": "Buck-Boost 2 Output"}, {"pin_number": "D1", "pin_name": "KOUT", "pin_description": "Key Output. Active-low, level-shifted button status output."}, {"pin_number": "D2", "pin_name": "RST", "pin_description": "Reset Output. Active-low, open-drain output indicates completion of sequencer."}, {"pin_number": "D3", "pin_name": "BATN", "pin_description": "Battery Negative Terminal"}, {"pin_number": "D4", "pin_name": "LX", "pin_description": "Inductor Switch Connection"}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX20310 Datasheet, Rev 3, 12/21", "family_comparison": "The MAX20310 family consists of several variants (A-E) factory-programmed with different default settings to cater to various application needs without requiring initial I2C programming. Key differences are detailed in Tables 21 and 22, including default output voltages, current limits, and power-on sequencing.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "2V", "min_input_voltage": "0.7V", "max_output_voltage": "4.05V", "min_output_voltage": "1V", "max_output_current": "0.05A", "max_switch_frequency": "未找到", "quiescent_current": "1.1µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "0.6A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "No", "output_over_voltage_protection": "No", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "未找到", "loop_control_mode": "未找到"}, "package": [{"pitch": "1.63", "type": "Information", "length": "1.65", "width": "1.65", "pin_count": "0491", "height": "1.63"}]}]