[{"part_number": "MAX77640", "manufacturer": "Maxim Integrated", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "多通道电源管理芯片(PMIC)", "category_lv3": "降压升压型稳压器", "part_number_title": "Ultra-Low Power PMIC with 3-Output SIMO, 150mA LDO, and Power Sequencer", "features": ["Compact, High-Efficiency Power Solution", "3-Output Single-Inductor Multiple-Output (SIMO) Buck-Boost Regulator", "150mA LDO", "3-Channel Current-<PERSON><PERSON>", "Flexible Power Sequencing", "GPIO and Reset Output", "3-Output SIMO Extends Battery Life", "2.7V to 5.5V Input Voltage Range from Single Cell Li-Ion", "0.8V to 5.25V Output Voltage Range (Table 1)", "Supports >300mA loads (1.8VOUT, 3.7VIN)", "Improves Overall System Efficiency while Reducing Size", "Maintains Regulation without Dropout unlike Traditional Bucks", "Glitchless Buck-Boost Operation", "Low Quiescent Current", "300nA Shutdown Current", "5.6μA Operating Current (3 SIMO Channels and LDO On)", "Small Size", "2.75mm x 2.15mm (0.7mm max heigh) WLP", "30-Bump, 0.4mm Pitch, 6 x 5 Array", "16mm² Total Solution Size"], "description": "The MAX77640/MAX77641 are a low-Iq power solution for applications where size and efficiency are critical. The device integrates a 3-output single-inductor multiple-output (SIMO) buck-boost regulator, a 150mA LDO, and a 3-channel current-sink driver.\nThe SIMO operates on an input between 2.7V and 5.5V. The outputs are independently programmable between 0.8V and 5.25V depending on ordering option. Each output is a buck-boost with glitchless transition between buck and boost operation. The SIMO can support >300mA loads (1.8VOUT, 3.7VIN).\nThe 150mA LDO provides ripple rejection for noise-sensitive applications. The current sinks can be programmed to blink LEDs in custom patterns. The device integrates a power sequencer to control power-up/down order of each output. Default output voltages and sequence order are factory-programmable. An I2C serial interface further configures the device.\nThe MAX77640/MAX77641 are available in a 30-bump wafer-level package (WLP). Total solution size is 16mm2. For a similar product with a battery charger, refer to the MAX77650.", "applications": ["Hearables: Bluetooth Headphones and Earbuds", "Wearables: Fitness, Health, and Activity Monitors", "Action Cameras, Wearable/Body Cameras", "Internet of Things (IoT) Gadgets"], "ordering_information": [{"part_number": "MAX77640", "order_device": "MAX77640EWV+*", "status": "Custom samples only", "package_type": "WLP", "package_code": "W302H2+1", "carrier_description": "未找到", "carrier_quantity": "未找到", "package_drawing_code": "21-100047", "marking": "未找到", "pin_count": "30", "length": "2.75", "width": "2.15", "height": "0.7", "pitch": "0.4", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "SBB0: up to 2.375V, SBB1: up to 1.5875V, SBB2: up to 3.95V", "application_grade": "Industry"}, {"part_number": "MAX77640", "order_device": "MAX77640AEWV+T", "status": "Production", "package_type": "WLP", "package_code": "W302H2+1", "carrier_description": "Ta<PERSON> and Reel", "carrier_quantity": "未找到", "package_drawing_code": "21-100047", "marking": "未找到", "pin_count": "30", "length": "2.75", "width": "2.15", "height": "0.7", "pitch": "0.4", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "SBB0: up to 2.375V, SBB1: up to 1.5875V, SBB2: up to 3.95V", "application_grade": "Industry"}], "pin_function": [{"product_part_number": "MAX77640/MAX77641", "package_type": "WLP", "pins": [{"pin_number": "A1", "pin_name": "PWR_HLD", "pin_description": "Active-High Power Hold Input. Assert PWR_HLD to keep the on/off controller in its on state. If PWR_HLD is not needed, connect it to SYS and use the SFT_RST bits to power the device down."}, {"pin_number": "A2", "pin_name": "nEN", "pin_description": "Active-Low Enable Input. nEN supports push-button or slide-switch configurations. An external pullup resistor (10kΩ to 100kΩ) to SYS is required."}, {"pin_number": "A3", "pin_name": "SDA", "pin_description": "I2C Data"}, {"pin_number": "B4", "pin_name": "SCL", "pin_description": "I2C Clock"}, {"pin_number": "B1", "pin_name": "GPIO", "pin_description": "General Purpose Input/Output. The GPIO I/O stage is internally biased with VIO."}, {"pin_number": "B2", "pin_name": "nRST", "pin_description": "Active-Low, Open-Drain Reset Output. Connect a 100kΩ pullup resistor between nRST and a voltage equal to or less than VSYS."}, {"pin_number": "C2", "pin_name": "nIRQ", "pin_description": "Active-Low, Open-Drain Interrupt Output. Connect a 100kΩ pullup resistor between nIRQ and a voltage equal to or less than VSYS."}, {"pin_number": "E2, E3", "pin_name": "SYS", "pin_description": "System Power Output. SYS provides power to the system resources as well as the control logic of the device. Connect to IN_SBB and bypass to GND with a 22µF ceramic capacitor."}, {"pin_number": "C3, D1, D2, E1", "pin_name": "GND", "pin_description": "Quiet Ground. Connect GND to PGND, LGND, and the low-impedance ground plane of the PCB."}, {"pin_number": "C4", "pin_name": "Vio", "pin_description": "I2C Interface and GPIO Driver Power"}, {"pin_number": "C1, D3", "pin_name": "N.C.", "pin_description": "No Connection. Leave this pin unconnected."}, {"pin_number": "B5", "pin_name": "LDO", "pin_description": "Linear Regulator Output. Connect to GND if unused."}, {"pin_number": "B6", "pin_name": "IN_LDO", "pin_description": "Linear Regulator Input. Connect to GND if unused."}, {"pin_number": "A6", "pin_name": "LED0", "pin_description": "Current Sink Port 0. LED0 is typically connected to the cathode of an LED and is capable of sinking up to 12.5mA. Connect to ground if unused."}, {"pin_number": "A5", "pin_name": "LED1", "pin_description": "Current Sink Port 1. LED1 is typically connected to the cathode of an LED and is capable of sinking up to 12.5mA. Connect to ground if unused."}, {"pin_number": "A4", "pin_name": "LED2", "pin_description": "Current Sink Port 2. LED2 is typically connected to the cathode of an LED and is capable of sinking up to 12.5mA. Connect to ground if unused."}, {"pin_number": "B3", "pin_name": "LGND", "pin_description": "Current Sink Ground. Connect LGND to GND, PGND, and the low-impedance ground plane of the PCB."}, {"pin_number": "E4", "pin_name": "IN_SBB", "pin_description": "SIMO Power Input. Connect IN_SBB to SYS and bypass to PGND with a 22µF ceramic capacitor as close as possible to the IN_SBB pin."}, {"pin_number": "C6", "pin_name": "SBB0", "pin_description": "SIMO Buck-Boost Output 0. SBB0 is the power output for channel 0 of the SIMO buck-boost. Bypass SBB0 to PGND with a 10µF ceramic capacitor."}, {"pin_number": "D6", "pin_name": "SBB1", "pin_description": "SIMO Buck-Boost Output 1. SBB1 is the power output for channel 1 of the SIMO buck-boost. Bypass SBB1 to PGND with a 10µF ceramic capacitor."}, {"pin_number": "E6", "pin_name": "SBB2", "pin_description": "SIMO Buck-Boost Output 2. SBB2 is the power output for channel 2 of the SIMO buck-boost. Bypass SBB2 to PGND with a 10µF ceramic capacitor."}, {"pin_number": "C5", "pin_name": "BST", "pin_description": "SIMO Power Input for the High-Side Output NMOS Drivers. Connect a 3300pF ceramic capacitor between BST and LXB."}, {"pin_number": "D4", "pin_name": "LXA", "pin_description": "Switching Node A. LXA is driven between PGND and IN_SBB when any SIMO channel is enabled. LXA is driven to PGND when all SIMO channels are disabled. Connect a 1.5μH inductor between LXA and LXB."}, {"pin_number": "D5", "pin_name": "LXB", "pin_description": "Switching Node B. LXB is driven between PGND and SBBx when SBBx is enabled. LXB is driven to PGND when all SIMO channels are disabled. Connect a 1.5μH inductor between LXA and LXB."}, {"pin_number": "E5", "pin_name": "PGND", "pin_description": "Power ground for the SIMO low-side FETs. Connect PGND to GND, LGND, and the low-impedance ground plane of the PCB."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77640/MAX77641", "family_comparison": "| REGULATOR NAME | REGULATOR TOPOLOGY | MAXIMUM IOUT (mA) | VIN RANGE (V) | MAX77640 VOUT RANGE/RESOLUTION | MAX77641 VOUT RANGE/RESOLUTION |\n|---|---|---|---|---|---|\n| SBB0 | SIMO | up to 300* | 2.5 to 5.5 | 0.8 to 2.375V in 25mV steps | 0.8 to 2.375V in 25mV steps |\n| SBB1 | SIMO | up to 300* | 2.5 to 5.5 | 0.8 to 1.5875V in 12.5mV steps | 2.4 to 5.25V in 50mV steps |\n| SBB2 | SIMO | up to 300* | 2.5 to 5.5 | 0.8 to 3.95V in 50mV steps | 2.4 to 5.25V in 50mV steps |\n| LDO | PMOS LDO | 150 | 1.8 to 5.5 | 1.35 to 2.9375V in 12.5mV steps | 1.35 to 2.9375V in 12.5mV steps |", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "5.5V", "min_input_voltage": "2.7V", "max_output_voltage": "3.95V", "min_output_voltage": "1V", "max_output_current": "0.3A", "max_switch_frequency": "未找到", "quiescent_current": "5.6µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "1A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "Latch", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±4%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.7", "length": "2.75", "width": "2.15", "type": "Information", "pin_count": "1"}]}, {"part_number": "MAX77641", "manufacturer": "Maxim Integrated", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "多通道电源管理芯片(PMIC)", "category_lv3": "降压升压型稳压器", "part_number_title": "Ultra-Low Power PMIC with 3-Output SIMO, 150mA LDO, and Power Sequencer", "features": ["Compact, High-Efficiency Power Solution", "3-Output Single-Inductor Multiple-Output (SIMO) Buck-Boost Regulator", "150mA LDO", "3-Channel Current-<PERSON><PERSON>", "Flexible Power Sequencing", "GPIO and Reset Output", "3-Output SIMO Extends Battery Life", "2.7V to 5.5V Input Voltage Range from Single Cell Li-Ion", "0.8V to 5.25V Output Voltage Range (Table 1)", "Supports >300mA loads (1.8VOUT, 3.7VIN)", "Improves Overall System Efficiency while Reducing Size", "Maintains Regulation without Dropout unlike Traditional Bucks", "Glitchless Buck-Boost Operation", "Low Quiescent Current", "300nA Shutdown Current", "5.6μA Operating Current (3 SIMO Channels and LDO On)", "Small Size", "2.75mm x 2.15mm (0.7mm max heigh) WLP", "30-Bump, 0.4mm Pitch, 6 x 5 Array", "16mm² Total Solution Size"], "description": "The MAX77640/MAX77641 are a low-Iq power solution for applications where size and efficiency are critical. The device integrates a 3-output single-inductor multiple-output (SIMO) buck-boost regulator, a 150mA LDO, and a 3-channel current-sink driver.\nThe SIMO operates on an input between 2.7V and 5.5V. The outputs are independently programmable between 0.8V and 5.25V depending on ordering option. Each output is a buck-boost with glitchless transition between buck and boost operation. The SIMO can support >300mA loads (1.8VOUT, 3.7VIN).\nThe 150mA LDO provides ripple rejection for noise-sensitive applications. The current sinks can be programmed to blink LEDs in custom patterns. The device integrates a power sequencer to control power-up/down order of each output. Default output voltages and sequence order are factory-programmable. An I2C serial interface further configures the device.\nThe MAX77640/MAX77641 are available in a 30-bump wafer-level package (WLP). Total solution size is 16mm2. For a similar product with a battery charger, refer to the MAX77650.", "applications": ["Hearables: Bluetooth Headphones and Earbuds", "Wearables: Fitness, Health, and Activity Monitors", "Action Cameras, Wearable/Body Cameras", "Internet of Things (IoT) Gadgets"], "ordering_information": [{"part_number": "MAX77641", "order_device": "MAX77641EWV+*", "status": "Custom samples only", "package_type": "WLP", "package_code": "W302H2+1", "carrier_description": "未找到", "carrier_quantity": "未找到", "package_drawing_code": "21-100047", "marking": "未找到", "pin_count": "30", "length": "2.75", "width": "2.15", "height": "0.7", "pitch": "0.4", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "SBB0: up to 2.375V, SBB1: up to 5.25V, SBB2: up to 5.25V", "application_grade": "Industry"}, {"part_number": "MAX77641", "order_device": "MAX77641AEWV+T", "status": "Production", "package_type": "WLP", "package_code": "W302H2+1", "carrier_description": "Ta<PERSON> and Reel", "carrier_quantity": "未找到", "package_drawing_code": "21-100047", "marking": "未找到", "pin_count": "30", "length": "2.75", "width": "2.15", "height": "0.7", "pitch": "0.4", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "SBB0: up to 2.375V, SBB1: up to 5.25V, SBB2: up to 5.25V", "application_grade": "Industry"}], "pin_function": [{"product_part_number": "MAX77640/MAX77641", "package_type": "WLP", "pins": [{"pin_number": "A1", "pin_name": "PWR_HLD", "pin_description": "Active-High Power Hold Input. Assert PWR_HLD to keep the on/off controller in its on state. If PWR_HLD is not needed, connect it to SYS and use the SFT_RST bits to power the device down."}, {"pin_number": "A2", "pin_name": "nEN", "pin_description": "Active-Low Enable Input. nEN supports push-button or slide-switch configurations. An external pullup resistor (10kΩ to 100kΩ) to SYS is required."}, {"pin_number": "A3", "pin_name": "SDA", "pin_description": "I2C Data"}, {"pin_number": "B4", "pin_name": "SCL", "pin_description": "I2C Clock"}, {"pin_number": "B1", "pin_name": "GPIO", "pin_description": "General Purpose Input/Output. The GPIO I/O stage is internally biased with VIO."}, {"pin_number": "B2", "pin_name": "nRST", "pin_description": "Active-Low, Open-Drain Reset Output. Connect a 100kΩ pullup resistor between nRST and a voltage equal to or less than VSYS."}, {"pin_number": "C2", "pin_name": "nIRQ", "pin_description": "Active-Low, Open-Drain Interrupt Output. Connect a 100kΩ pullup resistor between nIRQ and a voltage equal to or less than VSYS."}, {"pin_number": "E2, E3", "pin_name": "SYS", "pin_description": "System Power Output. SYS provides power to the system resources as well as the control logic of the device. Connect to IN_SBB and bypass to GND with a 22µF ceramic capacitor."}, {"pin_number": "C3, D1, D2, E1", "pin_name": "GND", "pin_description": "Quiet Ground. Connect GND to PGND, LGND, and the low-impedance ground plane of the PCB."}, {"pin_number": "C4", "pin_name": "Vio", "pin_description": "I2C Interface and GPIO Driver Power"}, {"pin_number": "C1, D3", "pin_name": "N.C.", "pin_description": "No Connection. Leave this pin unconnected."}, {"pin_number": "B5", "pin_name": "LDO", "pin_description": "Linear Regulator Output. Connect to GND if unused."}, {"pin_number": "B6", "pin_name": "IN_LDO", "pin_description": "Linear Regulator Input. Connect to GND if unused."}, {"pin_number": "A6", "pin_name": "LED0", "pin_description": "Current Sink Port 0. LED0 is typically connected to the cathode of an LED and is capable of sinking up to 12.5mA. Connect to ground if unused."}, {"pin_number": "A5", "pin_name": "LED1", "pin_description": "Current Sink Port 1. LED1 is typically connected to the cathode of an LED and is capable of sinking up to 12.5mA. Connect to ground if unused."}, {"pin_number": "A4", "pin_name": "LED2", "pin_description": "Current Sink Port 2. LED2 is typically connected to the cathode of an LED and is capable of sinking up to 12.5mA. Connect to ground if unused."}, {"pin_number": "B3", "pin_name": "LGND", "pin_description": "Current Sink Ground. Connect LGND to GND, PGND, and the low-impedance ground plane of the PCB."}, {"pin_number": "E4", "pin_name": "IN_SBB", "pin_description": "SIMO Power Input. Connect IN_SBB to SYS and bypass to PGND with a 22µF ceramic capacitor as close as possible to the IN_SBB pin."}, {"pin_number": "C6", "pin_name": "SBB0", "pin_description": "SIMO Buck-Boost Output 0. SBB0 is the power output for channel 0 of the SIMO buck-boost. Bypass SBB0 to PGND with a 10µF ceramic capacitor."}, {"pin_number": "D6", "pin_name": "SBB1", "pin_description": "SIMO Buck-Boost Output 1. SBB1 is the power output for channel 1 of the SIMO buck-boost. Bypass SBB1 to PGND with a 10µF ceramic capacitor."}, {"pin_number": "E6", "pin_name": "SBB2", "pin_description": "SIMO Buck-Boost Output 2. SBB2 is the power output for channel 2 of the SIMO buck-boost. Bypass SBB2 to PGND with a 10µF ceramic capacitor."}, {"pin_number": "C5", "pin_name": "BST", "pin_description": "SIMO Power Input for the High-Side Output NMOS Drivers. Connect a 3300pF ceramic capacitor between BST and LXB."}, {"pin_number": "D4", "pin_name": "LXA", "pin_description": "Switching Node A. LXA is driven between PGND and IN_SBB when any SIMO channel is enabled. LXA is driven to PGND when all SIMO channels are disabled. Connect a 1.5μH inductor between LXA and LXB."}, {"pin_number": "D5", "pin_name": "LXB", "pin_description": "Switching Node B. LXB is driven between PGND and SBBx when SBBx is enabled. LXB is driven to PGND when all SIMO channels are disabled. Connect a 1.5μH inductor between LXA and LXB."}, {"pin_number": "E5", "pin_name": "PGND", "pin_description": "Power ground for the SIMO low-side FETs. Connect PGND to GND, LGND, and the low-impedance ground plane of the PCB."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77640/MAX77641", "family_comparison": "| REGULATOR NAME | REGULATOR TOPOLOGY | MAXIMUM IOUT (mA) | VIN RANGE (V) | MAX77640 VOUT RANGE/RESOLUTION | MAX77641 VOUT RANGE/RESOLUTION |\n|---|---|---|---|---|---|\n| SBB0 | SIMO | up to 300* | 2.5 to 5.5 | 0.8 to 2.375V in 25mV steps | 0.8 to 2.375V in 25mV steps |\n| SBB1 | SIMO | up to 300* | 2.5 to 5.5 | 0.8 to 1.5875V in 12.5mV steps | 2.4 to 5.25V in 50mV steps |\n| SBB2 | SIMO | up to 300* | 2.5 to 5.5 | 0.8 to 3.95V in 50mV steps | 2.4 to 5.25V in 50mV steps |\n| LDO | PMOS LDO | 150 | 1.8 to 5.5 | 1.35 to 2.9375V in 12.5mV steps | 1.35 to 2.9375V in 12.5mV steps |", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "5.5V", "min_input_voltage": "2.7V", "max_output_voltage": "5.25V", "min_output_voltage": "1V", "max_output_current": "0.3A", "max_switch_frequency": "未找到", "quiescent_current": "5.6µA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "1A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "Latch", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±4%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.7", "length": "2.75", "width": "2.15", "type": "Information", "pin_count": "1"}]}]