{"part_number": "LTC7813", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "Low IQ, 60V Synchronous Boost+Buck Controller", "features": ["Synchronous Boost and Buck Controllers", "When Cascaded, Allows Vın Above, Below, or Equal to Regulated Vout of Up to 60V", "Wide Bias Input Voltage Range: 4.5V to 60V", "Output Remains in Regulation Through Input Dips (e.g. Cold Crank) Down to 2.2V", "Adjustable Gate Drive Level 5V to 10V (OPTI-DRIVE)", "Low EMI with Low Input and Output Ripple", "Fast Output Transient Response", "No External Bootstrap Diodes Required", "High Light Load Efficiency", "Low Operating Io: 29µA (One Channel On)", "Low Operating Io: 34µA (Both Channels On)", "RSENSE or Lossless DCR Current Sensing", "Buck Output Voltage Range: 0.8V ≤ Vout ≤ 60V", "Boost Output Voltage Up 60V", "Phase-Lockable Frequency (75kHz to 850kHz)", "Small 32-Pin 5mm × 5mm QFN Package"], "description": "The LTC7813 is a high performance synchronous Boost+Buck DC/DC switching regulator controller that drives all N-channel power MOSFET stages. It contains independent step-up (boost) and step-down (buck) controllers that can regulate two separate outputs or be cascaded to regulate an output voltage from an input voltage that can be above, below, or equal to the output voltage. The LTC7813 operates from a wide 4.5V to 60V input supply range. When biased from the output of the boost regulator, the LTC7813 can operate from an input supply as low as 2.2V after start-up. The 34µA no-load quiescent current (both channels on) extends operating runtime in battery-powered systems. Unlike conventional buck-boost regulators, the LTC7813's cascaded Boost+Buck solution has continuous, non-pulsating, input and output currents, substantially reducing voltage ripple and EMI. The LTC7813 has independent feedback and compensation points for the boost and buck regulation loops, enabling a fast output transient response that can be externally optimized.", "applications": ["Automotive and Industrial Power Systems", "High Power Battery Operated Systems"], "ordering_information": [{"part_number": "LTC7813", "order_device": "LTC7813EUH#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "<PERSON><PERSON>"}, {"part_number": "LTC7813", "order_device": "LTC7813EUH#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "Ta<PERSON> and Reel"}, {"part_number": "LTC7813", "order_device": "LTC7813IUH#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "<PERSON><PERSON>"}, {"part_number": "LTC7813", "order_device": "LTC7813IUH#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "Ta<PERSON> and Reel"}, {"part_number": "LTC7813", "order_device": "LTC7813HUH#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "min_operation_temp": "-40", "max_operation_temp": "150", "carrier_description": "<PERSON><PERSON>"}, {"part_number": "LTC7813", "order_device": "LTC7813HUH#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "min_operation_temp": "-40", "max_operation_temp": "150", "carrier_description": "Ta<PERSON> and Reel"}, {"part_number": "LTC7813", "order_device": "LTC7813MPUH#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "min_operation_temp": "-55", "max_operation_temp": "150", "carrier_description": "<PERSON><PERSON>"}, {"part_number": "LTC7813", "order_device": "LTC7813MPUH#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "min_operation_temp": "-55", "max_operation_temp": "150", "carrier_description": "Ta<PERSON> and Reel"}], "pin_function": [{"product_part_number": "LTC7813", "package_type": "QFN-32", "pins": [{"pin_number": "1, 30", "pin_name": "SW1, SW2", "pin_description": "Switch Node Connections to Inductors."}, {"pin_number": "2, 29", "pin_name": "TG1, TG2", "pin_description": "High Current Gate Drives for Top N-Channel MOSFETs. These are the outputs of floating drivers with a voltage swing equal to DRVCC superimposed on the switch node voltage SW."}, {"pin_number": "3, 23", "pin_name": "TRACK/SS1, SS2", "pin_description": "External Tracking and Soft-Start Input. For the buck channel, the LTC7813 regulates the VFB1 voltage to the smaller of 0.8V, or the voltage on the TRACK/SS1 pin. For the boost channel, the LTC7813 regulates the VFB2 voltage to the smaller of 1.2V, or the voltage on the SS2 pin. An internal 10µA pull-up current source is connected to this pin. A capacitor to ground at this pin sets the ramp time to final regulated output voltage. Alternatively, a resistor divider on another voltage supply connected to the TRACK/SS1 pin allows the LTC7813 buck output to track the other supply during start-up."}, {"pin_number": "4", "pin_name": "VPRG2", "pin_description": "Channel 2 Output Control Pin. This pin sets the boost channel to adjustable output mode using external feedback resistors or fixed 10V/12V output mode using internal resistive dividers. Grounding this pin allows the output to be programmed through the VFB2 pin using external resistors, regulating VFB2 to the 1.2V reference. Floating this pin or connecting it to INTVCC programs the output to 10V or 12V (respectively), with VFB2 used to sense the output voltage."}, {"pin_number": "5, 20", "pin_name": "ITH1, ITH2", "pin_description": "Error Amplifier Outputs and Switching Regulator Compensation Points. Each associated channel's current comparator trip point increases with this control voltage."}, {"pin_number": "6", "pin_name": "VFB1", "pin_description": "This pin receives the remotely sensed feedback voltage for the buck controller from an external resistive divider across the output."}, {"pin_number": "7, 12", "pin_name": "SENSE1+, SENSE2+", "pin_description": "The (+) Input to the Differential Current Comparators. The ITH pin voltage and controlled offsets between the SENSE¯ and SENSE+ pins in conjunction with RSENSE set the current trip threshold. For the boost channel, the SENSE2+ pin supplies current to the current comparator."}, {"pin_number": "8, 13", "pin_name": "SENSE1¯, SENSE2¯", "pin_description": "The (−) Input to the Differential Current Comparators. When SENSE1¯ for the buck channel is greater than INTVCC, the SENSE1¯ pin supplies current to the current comparator."}, {"pin_number": "9", "pin_name": "FREQ", "pin_description": "The frequency control pin for the internal VCO. Connecting this pin to GND forces the VCO to a fixed low frequency of 350kHz. Connecting this pin to INTVCC forces the VCO to a fixed high frequency of 535kHz. Other frequencies between 50kHz and 900kHz can be programmed using a resistor between FREQ and GND. The resistor and an internal 20µA source current create a voltage used by the internal oscillator to set the frequency."}, {"pin_number": "10", "pin_name": "PLLIN/MODE", "pin_description": "External Synchronization Input to Phase Detector and Forced Continuous Mode Input. When an external clock is applied to this pin, the phase-locked loop will force the rising TG1 and BG2 signals to be synchronized with the rising edge of the external clock, and the regulators will operate in forced continuous mode. When not synchronizing to an external clock, this input, which acts on both controllers, determines how the LTC7813 operates at light loads. Pulling this pin to ground selects Burst Mode® operation. An internal 100k resistor to ground also invokes Burst Mode operation when the pin is floated. Tying this pin to INTVCC forces continuous inductor current operation. Tying this pin to a voltage greater than 1.1V and less than INTVCC – 1.3V selects pulse-skipping operation. This can be done by connecting a 100k resistor from this pin to INTVCC."}, {"pin_number": "11, 33", "pin_name": "GND, Exposed Pad", "pin_description": "Ground. The exposed pad must be soldered to the PCB for rated electrical and thermal performance."}, {"pin_number": "14", "pin_name": "PGOOD1", "pin_description": "Open-Drain Logic Output. PGOOD1 is pulled to ground when the voltage on the VFB1 pin is not within ±10% of its set point."}, {"pin_number": "15", "pin_name": "INTVCC", "pin_description": "Output of the Internal 5V Low Dropout Regulator. The low voltage analog and digital circuits are powered from this voltage source. A low ESR 0.1μF ceramic bypass capacitor should be connected between INTVCC and GND, as close as possible to the IC."}, {"pin_number": "16, 17", "pin_name": "RUN1, RUN2", "pin_description": "Run Control Inputs for Each Controller. Forcing either of these pins below 1.2V shuts down that controller. Forcing both of these pins below 0.7V shuts down the entire LTC7813, reducing quiescent current to approximately 3.6µA."}, {"pin_number": "18", "pin_name": "ILIM", "pin_description": "Current Comparator Sense Voltage Range Input. Tying this pin to GND or INTVCC or floating it sets the maximum current sense threshold (for both channels) to one of three different levels (50mV, 100mV, or 75mV, respectively)."}, {"pin_number": "19", "pin_name": "VFB2", "pin_description": "If VPRG2 is grounded, this pin receives the remotely sensed feedback voltage for the boost controller from an external resistive divider across the output. If VPRG2 is floated or tied to INTVCC, this pin receives the remotely sensed output voltage of the boost controller."}, {"pin_number": "21", "pin_name": "DRVUV", "pin_description": "Determines the higher or lower DRVCC UVLO and EXTVCC switchover thresholds, as listed on the Electrical Characteristics table. Connecting DRVUV to GND chooses the lower thresholds whereas tying DRVUV to INTVCC chooses the higher thresholds."}, {"pin_number": "22", "pin_name": "DRVSET", "pin_description": "Sets the regulated output voltage of the DRVCC LDO regulator. Connecting this pin to GND sets DRVCC to 6V whereas connecting it to INTVCC sets DRVCC to 10V. Voltages between 5V and 10V can be programmed by placing a resistor (50k to 100k) between the DRVSET pin and GND."}, {"pin_number": "24", "pin_name": "DRVCC", "pin_description": "Output of the Internal or External Low Dropout (LDO) Regulator. The gate drivers are powered from this voltage source. The DRVCC voltage is set by the DRVSET pin. Must be decoupled to ground with a minimum of 4.7µF ceramic or other low ESR capacitor. Do not use the DRVCC pin for any other purpose."}, {"pin_number": "25", "pin_name": "EXTVCC", "pin_description": "External Power Input to an Internal LDO Connected to DRVCC. This LDO supplies DRVCC power, bypassing the internal LDO powered from VBIAS whenever EXTVCC is higher than its switchover threshold (4.7V or 7.7V depending on the DRVSET pin). See EXTVCC Connection in the Applications Information section. Do not float or exceed 14V on this pin. Do not connect EXTVCC to a voltage greater than VBIAS. Connect to GND if not used."}, {"pin_number": "26", "pin_name": "VBIAS", "pin_description": "Main Supply Pin. A bypass capacitor should be tied between this pin and the GND pin."}, {"pin_number": "27, 31", "pin_name": "BG2, BG1", "pin_description": "High Current Gate Drives for Bottom N-Channel MOSFETs. Voltage swing at these pins is from ground to DRVCC."}, {"pin_number": "28, 32", "pin_name": "BOOST2, BOOST1", "pin_description": "Bootstrapped Supplies to the Topside Floating Drivers. Capacitors are connected between the BOOST and SW pins. Voltage swing at BOOST1 is from approximately DRVCC to (VIN1 + DRVCC). Voltage swing at BOOST2 is from approximately DRVCC to (VOUT2 + DRVCC)."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC7813.pdf", "family_comparison": "RELATED PARTS table on page 42", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 2, "channel_count": 2, "max_input_voltage": "60V", "min_input_voltage": "4.5V", "max_output_voltage": "60V", "min_output_voltage": "1V", "max_output_current": "10A", "max_switch_frequency": "0.85MHz", "quiescent_current": "34µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "50mV, 75mV, 100mV", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "<PERSON>urst Mode, Pulse Skipping, FCCM", "power_good_indicator": "Yes", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Fold Back", "output_short_circuit_protection": "Fold Back", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "True", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.5", "height": "0.75", "length": "5", "width": "1.2", "type": "QFN", "pin_count": "5"}]}