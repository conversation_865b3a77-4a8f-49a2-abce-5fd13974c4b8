{"part_number": "LM5177", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "LM5177 60V 宽 VIN双向四开关降压/升压控制器", "features": ["宽输入电压范围 ( 3.5V 至 60V,绝对最大值为 85V), V(BIAS) > 3.5V时,最小值为 2.8V", "输出电压范围为3.3V 至 60V", "低关断IQ (3 μA)", "低工作 IQ (60 μA)", "3%的反向电流限制精度可实现精确的充电电流", "平均输入和输出电流监测器或限制器", "对PWM 或模拟输入信号进行输出电压动态跟踪", "可选的省电模式 (PSM) 可实现高轻负载效率", "两个集成式高压电源 LDO,支持自动选择", "2A 峰值电流逻辑电平栅极驱动器，包括集成式自举二极管和自举过压和欠压保护", "在所有工作模式下(升压、降压/升压、降压)频率固定，包括可选的强制 PWM 模式、开关频率高达 600kHz 可实现小解决方案和元件尺寸、外部时钟同步", "可选展频运行", "可调节欠压保护", "断续过流保护和短路保护"], "description": "LM5177 是一款四开关降压/升压控制器。无论输入电压是高于、等于还是低于调节后的输出电压,该器件均可提供稳定的输出电压。在省电模式下,该器件具有低静态电流,因此可在低输出负载条件下实现高效率。LM5177 以固定开关频率运行,该开关频率可通过 RT或SYNC 引脚设置。在降压、升压和降压/升压运行期间,开关频率保持不变。集成的可选平均电流监测器可帮助监测并限制 LM5177 的输入和输出电流。此功能还支持使用恒流 (CC) 和恒压 (CV) 模式为备用电源元件(如电池或电容器)充电。", "applications": ["非隔离式直流/直流电源(商用直流/直流、远程无线电单元、电机驱动控制)", "备用电源系统(备用电池、消防安全)", "工业 PC(单板计算机)", "医疗 PSU (制氧机)", "以太网供电(路由器)", "太阳能电源(太阳能充电控制器)"], "ordering_information": [{"part_number": "LM5177", "order_device": "LM5177DCPR", "package_type": "HTSSOP", "package_drawing_code": "DCP0038A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5177", "order_device": "LM5177DCPR.A", "package_type": "HTSSOP", "package_drawing_code": "DCP0038A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5177", "order_device": "LM5177DCPRG4.A", "package_type": "HTSSOP", "package_drawing_code": "DCP0038A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LM5177", "package_type": "HTSSOP", "pins": [{"pin_number": "1", "pin_name": "BIAS", "pin_description": "VCC 偏置稳压器的可选输入。从外部电源而不是VIN为VCC 供电可以降低高VIN时的功率损耗。如果应用未使用 BIAS 引脚电源,则连接引脚 GND"}, {"pin_number": "2", "pin_name": "NC", "pin_description": "无内部连接"}, {"pin_number": "3", "pin_name": "VIN", "pin_description": "器件的输入电源和检测输入。将VIN连接到功率级的电源电压。"}, {"pin_number": "4", "pin_name": "EN/UVLO", "pin_description": "使能引脚。该引脚用于启用和禁用器件。如果该引脚低于 0.6V,器件将关断。该引脚必须升至0.65V以上才能启用器件。该引脚是器件内部基准电路和输入电压UVLO比较器输入的使能引脚。"}, {"pin_number": "5", "pin_name": "NC", "pin_description": "无内部连接"}, {"pin_number": "6", "pin_name": "IMONOUT", "pin_description": "电流监控输出引脚。可选电流监控的电压控制电流源的输出。将引脚连接到电阻器以检测其两端的电压。如果输出或输入电流检测放大器配置为限流器、则在IMONOUT 和AGND之间连接的外部RC 网络会补偿电流反馈环路的稳压器。将IMONOUT 引脚连接至 VCC 可禁用相应的块并降低静态电流"}, {"pin_number": "7", "pin_name": "nFLT", "pin_description": "用于故障指示或电源正常状态指示的开漏输出引脚。当FB超出标称输出电压调节窗口的±10% 调节窗口时,该引脚被拉至低电平。如果未使用 nFLT 引脚功能,该引脚可保持悬空。"}, {"pin_number": "8", "pin_name": "HO1_LL", "pin_description": "HO1 栅极信号的逻辑电平输出。将该接地基准PWM信号连接到可选的外部栅极驱动器输入。如果未使用此功能,则不对此引脚进行外部连接。"}, {"pin_number": "9", "pin_name": "HO2_LL", "pin_description": "HO2 栅极信号的逻辑电平输出。将该接地基准PWM信号连接到可选的外部栅极驱动器输入。如果未使用此功能,则不对此引脚进行外部连接。"}, {"pin_number": "10", "pin_name": "DTRK", "pin_description": "用于输出电压动态跟踪的数字PWM 输入引脚。不要将这个引脚悬空。如果不使用此功能,则将该引脚连接至 VCC 或GND。"}, {"pin_number": "11", "pin_name": "SYNC", "pin_description": "同步时钟输入。内部振荡器可以在运行期间与外部时钟同步。如果输出或输入电流检测放大器配置为限流器拉取,则该引脚在启动期间处于低电平,器件会将电流限制方向切换到负极性。不要将这个引脚悬空。如果不使用此功能,则将该引脚连接至VCC。"}, {"pin_number": "12", "pin_name": "MODE", "pin_description": "用于选择器件运行模式的数字输入。如果该引脚被拉至低电平,则会启用省电模式(PSM)。如果该引脚被拉至高电平,则会启用强制 PWM 或CCM 运行模式。运行期间可以动态更改此配置。不要将这个引脚悬空。"}, {"pin_number": "13", "pin_name": "CFG", "pin_description": "器件配置引脚。在CFG 引脚之间连接一个电阻器以选择器件在展频(DRSS)、短路保护(断续模式)、电流限制或电流监控下的运行。"}, {"pin_number": "14", "pin_name": "SLOPE", "pin_description": "在SLOPE 引脚和AGND 之间连接的电阻器提供斜率补偿斜坡,以在降压和升压模式下实现稳定的电流模式运行。"}, {"pin_number": "15", "pin_name": "RT", "pin_description": "开关频率编程引脚。一个外部电阻器连接到RT 引脚和AGND 以设置开关频率。"}, {"pin_number": "16", "pin_name": "SS/ATRK", "pin_description": "软启动编程引脚。SS引脚和AGND 引脚之间的电容器可对软启动时间进行编程。模拟输出电压跟踪引脚。可通过将引脚连接至可变电压基准(例如,通过数模转换器)对VOUT 调节目标进行编程。内部电路选择施加到引脚的最低电压。"}, {"pin_number": "17", "pin_name": "AGND", "pin_description": "器件的模拟接地"}, {"pin_number": "18", "pin_name": "COMP", "pin_description": "误差放大器的输出。COMP 和AGND 之间连接的外部RC 网络对输出电压反馈环路的稳压器进行补偿。"}, {"pin_number": "19", "pin_name": "FB", "pin_description": "用于输出电压调节的反馈引脚。在转换器的输出端到FB引脚之间连接一个电阻分压器网络。"}, {"pin_number": "20", "pin_name": "VOUT", "pin_description": "VOUT 检测输入。连接到功率级输出轨。"}, {"pin_number": "21", "pin_name": "NC", "pin_description": "无内部连接"}, {"pin_number": "22", "pin_name": "ISNSN", "pin_description": "输出或输入电流检测放大器的负检测输入。ISNSN 和ISNSP之间连接的可选电流检测电阻器可以位于功率级的输入侧或输出侧。如果禁用了电流监测器,请将 ISNSN 接地"}, {"pin_number": "23", "pin_name": "ISNSP", "pin_description": "输出或输入电流检测放大器的正检测输入。ISNSN 和 ISNSP之间连接的可选电流检测电阻器可以位于功率级的输入侧或输出侧。如果禁用了电流监测器,请将ISNSN 接地"}, {"pin_number": "24", "pin_name": "NC", "pin_description": "无内部连接"}, {"pin_number": "25", "pin_name": "SW2", "pin_description": "升压半桥的电感器开关节点"}, {"pin_number": "26", "pin_name": "HB2", "pin_description": "升压半桥的自举电源引脚。HB2引脚和SW2引脚之间各自需要一个外部电容器,以便为高侧MOSFET 栅极驱动器提供偏置。"}, {"pin_number": "27", "pin_name": "HO2", "pin_description": "升压半桥的高侧栅极驱动器输出"}, {"pin_number": "28", "pin_name": "NC", "pin_description": "无内部连接"}, {"pin_number": "29", "pin_name": "LO2", "pin_description": "升压半桥的低侧栅极驱动器输出"}, {"pin_number": "30", "pin_name": "PGND", "pin_description": "电源地。此引脚是低侧栅极驱动器的高电流接地连接,用于内部VCC 稳压器。"}, {"pin_number": "31", "pin_name": "VCC", "pin_description": "内部线性偏置稳压器输出。在VCC与PGND 之间连接一个陶瓷去耦电容器。"}, {"pin_number": "32", "pin_name": "LO1", "pin_description": "降压半桥的低侧栅极驱动器输出"}, {"pin_number": "33", "pin_name": "NC", "pin_description": "无内部连接"}, {"pin_number": "34", "pin_name": "HO1", "pin_description": "降压半桥的高侧栅极驱动器输出"}, {"pin_number": "35", "pin_name": "HB1", "pin_description": "降压半桥的自举电源引脚。HB1 引脚和 SW1引脚之间各自需要一个外部电容器,以便为高侧MOSFET 栅极驱动器提供偏置。"}, {"pin_number": "36", "pin_name": "SW1", "pin_description": "降压半桥的电感器开关节点"}, {"pin_number": "37", "pin_name": "CSA", "pin_description": "电感器峰值电流检测正输入。使用低电流开尔文连接将CSA连接到外部电流检测电阻器的正极侧。"}, {"pin_number": "38", "pin_name": "CSB", "pin_description": "电感器峰值电流检测负输入。使用低电流开尔文连接将CSB连接到外部电流检测电阻器的负极侧。"}, {"pin_number": "PAD", "pin_name": "PowerPAD", "pin_description": "将 PowerPAD 连接到模拟接地。使用散热过孔连接到PCB 接地平面以改善功率耗散。"}]}], "datasheet_cn": "ZHCSQO1F", "datasheet_en": "SNVSBU4", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "60V", "min_input_voltage": "2.9V", "max_output_voltage": "60V", "min_output_voltage": "3.3V", "max_output_current": "由外部元件决定", "max_switch_frequency": "0.6MHz", "quiescent_current": "60µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "50mV", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PSM, PWM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "1V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "OPTION", "pitch": "0.5", "height": "1.2", "length": "9.7", "width": "4.4", "pin_count": "38"}]}