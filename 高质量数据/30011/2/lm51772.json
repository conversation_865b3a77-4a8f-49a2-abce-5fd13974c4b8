{"part_number": "LM51772", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "LM51772 具有 I2C 接口的 55V 4 开关降压/升压控制器", "features": ["输入电压范围为 0V (V(BIAS) ≥ 3.5V) 至 55V", "输出电压范围为 1V 至 55V", "通过 I2C 进行动态 Vo 编程，范围为：– 3.3V 至 48V，单调阶跃为 20mV– 1V 至 24V，单调阶跃为 10mV", "峰值电流调节控制", "在所有工作模式下均具有低电压转换纹波", "动态输出电压跟踪（数字 PWM 跟踪输入、模拟跟踪输入）– 通过 I2C 接口编程", "关断静态电流为 3μA", "工作静态电流为 60μA", "用于外部 FET 控制的 DRV 引脚", "可在轻负载和高负载条件下实现高效率的运行模式选项：– 省电模式（突发/μSleep）– 自动/可编程导通模式", "集成高压电源 LDO", "辅助高压 LDO/基准", "集成式全桥栅极驱动– 2A 峰值电流能力– 自举过压和欠压保护– 集成式自举二极管", "独立于工作模式（升压、降压/升压、降压）的固定频率– 可选的强制 PWM 模式– 开关频率范围为 100kHz 至 2.2MHz– 外部时钟同步和时钟输出", "可选展频运行", "平均输入或输出电流传感器– 可在 0.5A (5mV) 至 7A (70mV) 范围内以 50mA (500μV) 阶跃进行编程– ISET 引脚可选", "可通过 I2C 接口读取监控功能", "使用 LM51772 并借助 WEBENCH® Power Designer 创建定制设计方案"], "description": "LM51772 是一款四开关降压/升压控制器。无论输入电压是高于、等于还是低于调节后的输出电压，该器件均可提供稳定的输出电压。在省电模式下，该器件支持在整个输出工作范围内实现非常高的效率。LM51772 以固定的开关频率运行，该频率可通过 RT/SYNC 引脚进行设置。在强制 PWM 下的降压、升压和降压/升压运行期间，开关频率保持恒定。外部补偿引脚可针对不同应用实现非常快速的瞬态响应。该器件在所有工作模式下均可保持小模式转换纹波。可通过集成式 I2C 接口对输出电压和器件配置进行动态编程。集成和可选的高侧电流传感器具有精确的输出或输入电流限制功能。LM51772 的平均电流限值还可通过 I2C 接口进行配置。", "applications": ["USB Type-C 电力输送（扩展坞、PC 监视器、台式计算机）", "无线充电", "工业 PC/耐用型 PC", "电池备份单元", "商用直流/直流", "航空电子设备/航海声纳", "非公路用车"], "ordering_information": [{"part_number": "LM51772", "order_device": "LM51772RHAR", "package_type": "VQFN", "package_drawing_code": "RHA0040P", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM51772", "order_device": "LM51772RHAR.A", "package_type": "VQFN", "package_drawing_code": "RHA0040P", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LM51772", "package_type": "VQFN", "pins": [{"pin_number": "1", "pin_name": "VCC1", "pin_description": "辅助 5V 稳压器输出。放置一个靠近引脚的电容器以实现良好的去耦。如果输出被逻辑禁用，它可以通过一个电阻器连接到 GND 或上拉到 VCC2。请勿使该引脚悬空。"}, {"pin_number": "2", "pin_name": "SS/ATRK", "pin_description": "软启动编程引脚。SS 引脚和 AGND 引脚之间的一个电容器可对软启动时间进行编程。模拟输出电压跟踪引脚。VOUT 调节目标可通过将该引脚连接到可变电压基准（例如，通过数模转换器）进行编程。内部电路选择引脚电压和内部电压基准之间的最低电压。"}, {"pin_number": "3", "pin_name": "SYNC", "pin_description": "同步时钟输入/输出。内部振荡器可在运行期间与外部时钟同步。请勿使该引脚悬空。如果不使用此功能，请将该引脚连接到 VCC2 或 GND。SYNC 引脚可配置为时钟同步输出信号。时钟相位可选择为 0° 和 180°，以直接在并联（双相）运行中操作两个器件。"}, {"pin_number": "4", "pin_name": "DTRK", "pin_description": "用于动态输出电压跟踪的数字 PWM 输入引脚。请勿使该引脚悬空。如果不使用此功能，请将该引脚连接到 VCC 或 GND。"}, {"pin_number": "5", "pin_name": "SDA/CFG3", "pin_description": "I2C 接口串行数据线。连接一个外部上拉电阻。如果 I2C 被禁用，此引脚是进一步的配置引脚。在 CFG3 引脚和 AGND 之间连接一个电阻器，以根据第 7.3.22 节选择器件操作。"}, {"pin_number": "6", "pin_name": "SCL/CFG4", "pin_description": "I2C 接口串行时钟线。连接一个外部上拉电阻。如果 I2C 被禁用，此引脚是进一步的配置引脚。在 CFG4 引脚和 AGND 之间连接一个电阻器，以根据第 7.3.22 节选择器件操作。"}, {"pin_number": "7", "pin_name": "MODE", "pin_description": "选择器件操作模式的数字输入。如果引脚被拉低，则启用省电模式 (PSM)。如果引脚被拉高，则启用强制 PWM 或 CCM 操作。配置可在操作期间动态更改。请勿使该引脚悬空。"}, {"pin_number": "8", "pin_name": "CFG2", "pin_description": "器件配置引脚。在 CFG2 引脚和 GND 之间连接一个电阻器，以根据第 7.3.22 节选择器件操作。"}, {"pin_number": "9", "pin_name": "ADDR/SLOPE(CFG1)", "pin_description": "斜坡补偿和地址选择。此引脚还禁用 I2C 接口，以使用 SCL、SCA 作为额外的斜坡配置引脚。在 CFG1 引脚和 AGND 之间连接一个电阻器，以根据第 7.3.22 节选择器件操作。"}, {"pin_number": "10", "pin_name": "CDC", "pin_description": "电缆压降补偿或电流监视器输出引脚。在 CDC 引脚和 AGND 之间连接一个电阻器，以选择电缆压降补偿的增益。默认情况下，此引脚提供 ISNSP 和 ISNSN 引脚之间感测电压的电流监视信号。如果电流监视器被禁用，请将 CDC 连接到地。"}, {"pin_number": "11", "pin_name": "nFLT/nINT", "pin_description": "用于故障指示或电源正常的开漏输出引脚。此引脚可配置为中断引脚。如果 STATUS 寄存器发生变化，该引脚会切换为低电平 256μs。"}, {"pin_number": "12", "pin_name": "RT", "pin_description": "开关频率编程引脚。一个外部电阻器连接在 RT 引脚和 AGND 之间，以设置开关频率。"}, {"pin_number": "13", "pin_name": "COMP", "pin_description": "误差放大器的输出。需要在 COMP 和 AGND 之间连接一个外部 RC 网络，以稳定/补偿稳压器电压环路。"}, {"pin_number": "14", "pin_name": "FB/SEL_intFB", "pin_description": "用于输出电压调节的反馈引脚。将转换器输出的电阻分压器网络连接到 FB 引脚。将 FB 引脚连接到 VCC2，以在器件的固定输出电压默认设置下运行。要选择内部反馈，请在器件启动前将该引脚连接到 VCC2。"}, {"pin_number": "15", "pin_name": "VIN-FB", "pin_description": "VIN 感测引脚。连接到与 VOUT 分压器具有相同增益的 VIN 分压器，以与外部二分压器一起使用 PCM。如果未使用内部 Vout 分压器或未使用 PCM，请连接到 AGND。请勿悬空。"}, {"pin_number": "16", "pin_name": "ILIMCOMP/ISET", "pin_description": "平均电流限制环路的补偿引脚。如果电流限制由内部 DAC 设置，则连接一个电容器或一个 2 型 R-C 网络。如果内部 DAC 被禁用，该引脚设置平均电流限制的阈值。连接一个电阻器到 AGND。根据应用要求，建议使用并联的电容器滤波器。如果电流限制由 ISET 设置，则连接一个电阻器到 AGND。将 ISET 引脚连接到 VCC2 以禁用该块并降低静态电流。"}, {"pin_number": "17", "pin_name": "AGND", "pin_description": "模拟地"}, {"pin_number": "18", "pin_name": "VOUT", "pin_description": "输出电压感测输入。连接到功率级输出轨。"}, {"pin_number": "19", "pin_name": "ISNSN", "pin_description": "输出或输入平均电流感测放大器的负感测输入。一个可选的电流感测电阻器连接在 ISNSN 和 ISNSP 之间，可以位于功率级的输入侧或输出侧。如果禁用了可选的电流传感器，请将 ISNSN 和 ISNSP 一起连接到 AGND。"}, {"pin_number": "20", "pin_name": "ISNSP", "pin_description": "输出或输入平均电流感测放大器的正感测输入。一个可选的电流感测电阻器连接在 ISNSN 和 ISNSP 之间，可以位于功率级的输入侧或输出侧。如果禁用了可选的电流传感器，请将 ISNSP 连接到地。"}, {"pin_number": "21", "pin_name": "CSB", "pin_description": "电感峰值电流感测负输入。使用开尔文连接将 CSB 连接到外部电流感测电阻器的负侧。"}, {"pin_number": "22", "pin_name": "CSA", "pin_description": "电感峰值电流感测正输入。使用开尔文连接将 CSA 连接到外部电流感测电阻器的正侧。"}, {"pin_number": "23", "pin_name": "SW1", "pin_description": "降压半桥的电感开关节点"}, {"pin_number": "24", "pin_name": "HO1", "pin_description": "降压半桥的高侧栅极驱动器输出"}, {"pin_number": "25", "pin_name": "HB1", "pin_description": "降压半桥的自举电源引脚。在 HB1 引脚和 SW1 引脚之间需要一个外部电容器，为高侧 MOSFET 栅极驱动器提供偏置。将外部电容器靠近引脚放置，引脚和电容器之间没有任何电阻，以实现良好的去耦。"}, {"pin_number": "26", "pin_name": "NC", "pin_description": "未连接"}, {"pin_number": "27", "pin_name": "LO1", "pin_description": "降压半桥的低侧栅极驱动器输出"}, {"pin_number": "28", "pin_name": "PGND", "pin_description": "功率地"}, {"pin_number": "29", "pin_name": "VCC2", "pin_description": "内部线性偏置稳压器输出。从 VCC 连接一个陶瓷去耦电容器到 PGND。该轨为内部逻辑和栅极驱动器供电。将外部电容器靠近引脚放置，引脚和电容器之间没有任何电阻，以实现良好的去耦。"}, {"pin_number": "30", "pin_name": "LO2", "pin_description": "升压半桥的低侧栅极驱动器输出"}, {"pin_number": "31", "pin_name": "HB2", "pin_description": "升压半桥的自举电源引脚。在 HB2 引脚和 SW2 引脚之间需要一个外部电容器，为高侧 MOSFET 栅极驱动器提供偏置。将外部电容器靠近引脚放置，引脚和电容器之间没有任何电阻，以实现良好的去耦。"}, {"pin_number": "32", "pin_name": "HO2", "pin_description": "升压半桥的高侧栅极驱动器输出"}, {"pin_number": "33", "pin_name": "SW2", "pin_description": "升压半桥的电感开关节点"}, {"pin_number": "34", "pin_name": "NC", "pin_description": "未连接"}, {"pin_number": "35", "pin_name": "DRV1", "pin_description": "外部 FET 驱动引脚。此引脚根据所选配置具有高压推挽级、开漏输出或电荷泵驱动级。如果未使用可选的 DRV 引脚，可以使 DRV 悬空。"}, {"pin_number": "36", "pin_name": "VIN", "pin_description": "器件的输入电源和感测输入。将 VIN 连接到功率级的电源电压。"}, {"pin_number": "37", "pin_name": "EN/UVLO", "pin_description": "使能引脚。用于使能转换器开关的数字输入引脚。该输入具有一个精确的模拟比较器和一个迟滞，用于监视输入电压。从输入电压连接一个电阻分压器，以维持欠压锁定 (UVLO) 功能。"}, {"pin_number": "38", "pin_name": "nRST", "pin_description": "用于使能器件内部逻辑、接口操作和所选 VCC1 稳压器的数字输入引脚。"}, {"pin_number": "39", "pin_name": "NC", "pin_description": "未连接"}, {"pin_number": "40", "pin_name": "BIAS", "pin_description": "VCC2 偏置稳压器的可选输入。通过外部电源而不是 VIN 为 VCC2 供电可以减少高 VIN 时的功率损耗。"}, {"pin_number": "PAD", "pin_name": "GND", "pin_description": "散热焊盘"}]}], "datasheet_cn": "ZHCSTC5B", "datasheet_en": "SNVSC22", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 2, "channel_count": 1, "max_input_voltage": "55V", "min_input_voltage": "3.5V", "max_output_voltage": "55V", "min_output_voltage": "1V", "max_output_current": "7A", "max_switch_frequency": "2.2MHz", "quiescent_current": "65µA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "0.5-7A(可调)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM, PSM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "True", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±3%", "output_reference_voltage": "1V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.5", "height": "5.5", "length": "6", "width": "6", "type": "40-<PERSON><PERSON>", "pin_count": "2"}]}