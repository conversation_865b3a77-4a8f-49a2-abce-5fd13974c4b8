{"part_number": "LM251772", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "LM251772 适用于 USB-PD 供电且具有 I2C 接口的 36V VIN 4 开关降压/升压控制器", "features": ["输入电压范围为 3.5V 至 36V", "通过 I2C 进行动态 Vo 编程，范围为：– 3.3V 至 48V，阶跃为 20mV – 1V 至 24V，阶跃为 10mV", "峰值电流调节控制", "在所有工作模式下均具有低电压转换纹波", "关断静态电流为 3μA", "工作静态电流为 60μA", "用于双角色端口电源路径的驱动 (DRV) 引脚 – 通过推挽输出实现快速 pMOS FET 控制 – 可配置为 nMOS FET 的电荷泵驱动器级", "可在轻负载和高负载条件下实现高效率的运行模式选项：– 省电模式（单脉冲/μSleep）– 自动导通模式", "集成高压电源 LDO", "用于 PD 控制器电源的辅助高压 LDO", "集成式全桥栅极驱动 – 2A 峰值电流能力 – 自举过压和欠压保护 – 集成式自举二极管", "独立于工作模式（升压、降压/升压、降压）的固定频率 – 可选的强制 PWM 模式 – 开关频率范围为 100kHz 至 600kHz – 外部时钟同步和时钟输出", "平均输入或输出电流传感器 – 可在 0.5A 至 7A 范围内以 50mA 阶跃进行编程", "可通过 I2C 接口读取监控功能", "商用温度范围（Tj = 0°C 至 70°C）", "使用 LM251772 并借助 WEBENCH® Power Designer 创建定制设计方案"], "description": "LM251772 是一款四开关降压/升压控制器。无论输入电压是高于、等于还是低于调节后的输出电压，该器件均可提供稳定的输出电压。在省电模式下，该器件支持在整个输出工作范围内实现非常高的效率。可通过集成式 I2C 接口对输出电压和平均电流进行动态编程。输出电压和平均电流的配置范围符合 USB-USB PD 标准要求。集成的 DRV 引脚可以控制一个断开 FET 来满足双角色端口 (DRP) 要求。", "applications": ["USB Type-C 供电来源：– 集线站 – PC 监视器 – 工业 PC/耐用型 PC – USB 交流/直流适配器", "无线充电"], "ordering_information": [{"part_number": "LM251772", "order_device": "LM251772RHAR", "package_type": "VQFN", "package_drawing_code": "RHA0040P", "output_voltage": "Adjustable", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "LM251772", "order_device": "LM251772RHAR.A", "package_type": "VQFN", "package_drawing_code": "RHA0040P", "output_voltage": "Adjustable", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "LM251772", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "VCC1", "pin_description": "辅助 5V 稳压器输出。为了实现良好的去耦，应将电容器放置在靠近引脚的位置。如果输出被逻辑禁用，则可以通过电阻器将其连接到 GND 或拉至 VCC2。请勿将此引脚悬空。"}, {"pin_number": "2", "pin_name": "SS/ATRK", "pin_description": "软启动编程引脚。SS 引脚和 AGND 引脚之间的电容器可对软启动时间进行编程。模拟输出电压跟踪引脚。可通过将引脚连接至可变电压基准（例如，通过数模转换器）对 VOUT 调节目标进行编程。内部电路在引脚电压和内部电压基准之间选择最低电压。"}, {"pin_number": "3", "pin_name": "SYNC", "pin_description": "同步时钟输入/输出。内部振荡器可以在运行期间与外部时钟同步。不要将这个引脚悬空。如果不使用此功能，则将该引脚连接至 VCC2 或 GND。SYNC 引脚可配置为时钟同步输出信号。若要直接并行（双相）运行两个器件，可以选择时钟相位为 0° 和 180°。"}, {"pin_number": "4", "pin_name": "DTRK", "pin_description": "用于输出电压动态跟踪的数字 PWM 输入引脚。不要将这个引脚悬空。如果不使用此功能，则将该引脚连接至 VCC 或 GND。"}, {"pin_number": "5", "pin_name": "SDA", "pin_description": "I2C 接口串行数据线路。连接一个外部上拉电阻器"}, {"pin_number": "6", "pin_name": "SCL", "pin_description": "I2C 接口串行时钟线路。连接一个外部上拉电阻器"}, {"pin_number": "7", "pin_name": "MODE", "pin_description": "用于选择器件运行模式的数字输入。如果该引脚被拉至低电平，则会启用省电模式 (PSM)。如果该引脚被拉至高电平，则会启用强制 PWM 或 CCM 运行模式。运行期间可以动态更改此配置。不要将这个引脚悬空。"}, {"pin_number": "8", "pin_name": "CFG2", "pin_description": "器件配置引脚。在 CFG2 引脚和 GND 之间连接一个电阻器，以便根据节 8.3.21 选择器件运行模式"}, {"pin_number": "9", "pin_name": "ADDR(CFG1)", "pin_description": "地址选择。对于 I2C 目标地址 LSB = 0，拉至 GND。对于 I2C 目标地址 LSB = 1，拉至 VCC2"}, {"pin_number": "10", "pin_name": "CDC", "pin_description": "电缆压降补偿或电流监测输出引脚。在 CDC 引脚和 AGND 之间连接一个电阻器以便选择电缆压降补偿的增益。默认情况下，该引脚提供 ISNSP 和 ISNSN 引脚之间检测到的电压的电流监测信号。如果禁用了电流监测器，请将 CDC 接地"}, {"pin_number": "11", "pin_name": "nFLT/nINT", "pin_description": "用于故障指示或电源正常状态指示的开漏输出引脚。该引脚可配置为中断引脚。如果 STATUS 寄存器发生变化，该引脚会切换为低电平并持续 256μs。"}, {"pin_number": "12", "pin_name": "RT", "pin_description": "开关频率编程引脚。一个外部电阻器连接到 RT 引脚和 AGND 以设置开关频率"}, {"pin_number": "13", "pin_name": "COMP", "pin_description": "误差放大器的输出。需要在 COMP 和 AGND 之间连接一个外部 RC 网络以稳定/补偿稳压器电压环路。"}, {"pin_number": "14", "pin_name": "FB/SEL_intFB", "pin_description": "用于输出电压调节的反馈引脚。在转换器的输出端到 FB 引脚之间连接一个电阻分压器网络。将 FB 引脚连接到 VCC2 可以在器件的固定输出电压默认设置下运行。要选择内部反馈，请在器件启动之前将该引脚连接到 VCC2"}, {"pin_number": "15", "pin_name": "AGND", "pin_description": "接至 AGND"}, {"pin_number": "16", "pin_name": "ILIMCOMP", "pin_description": "用于平均电流限制环路的补偿引脚。如果电流限制由内部 DAC 进行设置，则连接一个电容器或 2 型 R-C 网络。如果禁用内部 DAC，该引脚将设置平均电流限制的电流限制阈值。将一个电阻器连接到 AGND。根据应用要求，建议使用并联电容器滤波器。将 ILIMCOMP 引脚连接至 VCC2 可禁用相应的块并降低静态电流"}, {"pin_number": "17", "pin_name": "AGND", "pin_description": "模拟接地"}, {"pin_number": "18", "pin_name": "VOUT", "pin_description": "输出电压检测输入。连接到功率级输出轨。"}, {"pin_number": "19", "pin_name": "ISNSN", "pin_description": "输出或输入平均电流检测放大器的负检测输入。ISNSN 和 ISNSP 之间连接的可选电流检测电阻器可以位于功率级的输入侧或输出侧。如果禁用了可选电流传感器，请将 ISNSN 和 ISNSP 一起连接到 AGND"}, {"pin_number": "20", "pin_name": "ISNSP", "pin_description": "输出或输入电流检测放大器的正检测输入。ISNSN 和 ISNSP 之间连接的可选电流检测电阻器可以置于功率级的输入侧或输出侧。如果禁用了可选电流传感器，请将 ISNSP 接地"}, {"pin_number": "21", "pin_name": "CSB", "pin_description": "电感器峰值电流检测负输入。使用开尔文连接将 CSB 连接到外部电流检测电阻器的负极侧。"}, {"pin_number": "22", "pin_name": "CSA", "pin_description": "电感器峰值电流检测正输入。使用开尔文连接将 CSA 连接到外部电流检测电阻器的正极侧。"}, {"pin_number": "23", "pin_name": "SW1", "pin_description": "降压半桥的电感器开关节点"}, {"pin_number": "24", "pin_name": "HO1", "pin_description": "降压半桥的高侧栅极驱动器输出"}, {"pin_number": "25", "pin_name": "HB1", "pin_description": "降压半桥的自举电源引脚。HB1 引脚和 SW1 引脚之间需要一个外部电容器，以便为高侧 MOSFET 栅极驱动器提供偏置。将外部电容器放置在靠近引脚的位置，引脚和电容器之间没有任何电阻，以便实现良好的去耦"}, {"pin_number": "26", "pin_name": "NC", "pin_description": "未连接"}, {"pin_number": "27", "pin_name": "LO1", "pin_description": "降压半桥的低侧栅极驱动器输出"}, {"pin_number": "28", "pin_name": "PGND", "pin_description": "电源地"}, {"pin_number": "29", "pin_name": "VCC2", "pin_description": "内部线性偏置稳压器输出。在 VCC 与 PGND 之间连接一个陶瓷去耦电容器。该电源轨为内部逻辑和栅极驱动器供电。将外部电容器放置在靠近引脚的位置，引脚和电容器之间没有任何电阻，以便实现良好的去耦。"}, {"pin_number": "30", "pin_name": "LO2", "pin_description": "升压半桥的低侧栅极驱动器输出"}, {"pin_number": "31", "pin_name": "HB2", "pin_description": "升压半桥的自举电源引脚。HB2 引脚和 SW2 引脚之间需要一个外部电容器，以便为高侧 MOSFET 栅极驱动器提供偏置。将外部电容器放置在靠近引脚的位置，引脚和电容器之间没有任何电阻，以便实现良好的去耦"}, {"pin_number": "32", "pin_name": "HO2", "pin_description": "升压半桥的高侧栅极驱动器输出"}, {"pin_number": "33", "pin_name": "SW2", "pin_description": "升压半桥的电感器开关节点"}, {"pin_number": "34", "pin_name": "NC", "pin_description": "未连接"}, {"pin_number": "35", "pin_name": "DRV1", "pin_description": "外部 FET 驱动引脚。根据所选配置，该引脚具有高压推挽级、开漏输出或电荷泵驱动器级。如果不使用可选的 DRV 引脚，则可以使 DRV 保持断开状态。"}, {"pin_number": "36", "pin_name": "VIN", "pin_description": "器件的输入电源和检测输入。将 VIN 连接到功率级的电源电压。"}, {"pin_number": "37", "pin_name": "EN/UVLO", "pin_description": "使能引脚。用于启用转换器开关的数字输入引脚。该输入有一个精密模拟比较器和一个迟滞可以监测输入电压。在输入电压端连接一个电阻分压器以便保持欠压锁定 (UVLO) 功能。"}, {"pin_number": "38", "pin_name": "nRST", "pin_description": "用于启用器件内部逻辑、接口运行和 VCC1 稳压器（如果选择）的数字输入引脚。"}, {"pin_number": "39", "pin_name": "NC", "pin_description": "未连接"}, {"pin_number": "40", "pin_name": "BIAS", "pin_description": "VCC2 偏置稳压器的可选输入。从外部电源而不是 VIN 为 VCC2 供电可以降低高 VIN 时的功率损耗。"}, {"pin_number": "PAD", "pin_name": "GND", "pin_description": "散热焊盘"}]}], "datasheet_cn": "LM251772 适用于 USB-PD 供电且具有 I2C 接口的 36V VIN 4 开关降压/升压控制器", "datasheet_en": "SNVSCT9", "family_comparison": "LM251772是该系列中输入电压和温度范围较低的版本，但具有I2C接口和禁用无编程启动的特性。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "3.5V", "max_output_voltage": "55V", "min_output_voltage": "1V", "max_output_current": "7A", "max_switch_frequency": "0.6MHz", "quiescent_current": "60µA", "high_side_mosfet_resistance": "不适用(外部器件)", "low_side_mosfet_resistance": "不适用(外部器件)", "over_current_protection_threshold": "0.5-7A(可调)", "operation_mode": "同步", "output_voltage_config_method": "外部电阻分压器/I2C", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PSM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "True", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2%", "output_reference_voltage": "1V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.5", "height": "1", "length": "6", "width": "6", "type": "-C", "pin_count": "37"}]}