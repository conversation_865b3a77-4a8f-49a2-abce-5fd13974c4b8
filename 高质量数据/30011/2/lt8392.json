{"part_number": "LT8392", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto, Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "升降压(<PERSON><PERSON><PERSON><PERSON>)控制器", "part_number_title": "60V Synchronous 4-Switch Buck-Boost Controller with Spread Spectrum", "features": ["4-Switch Single Inductor Architecture Allows VIN Above, Below or Equal to VOUT", "Up to 98% Efficiency", "Proprietary Peak-Buck <PERSON>-Boost Current Mode", "Wide VIN Range: 3V (Need EXTVCC ≥ 4.5V) to 60V", "±1.5% Output Voltage Accuracy: 1V ≤ VOUT ≤ 60V", "±4% Input or Output Current Accuracy with Monitor", "Spread Spectrum Frequency Modulation for Low EMI", "Integrated Bootstrap Diodes", "Adjustable and Synchronizable: 150kHz to 650kHz", "VOUT Disconnected from VIN During Shutdown", "Available in 28-Lead TSSOP with Exposed Pad and 28-Lead QFN (4mm × 5mm)"], "description": "The LT®8392 is a synchronous 4-switch buck-boost DC/DC controller that regulates output voltage, input or output current from input voltage above, below, or equal to the output voltage. The proprietary peak-buck peak-boost current mode control scheme allows adjustable and synchronizable 150kHz to 650kHz fixed frequency operation, or internal ±15% triangle spread spectrum operation for low EMI. With 3V to 60V input voltage range, 1V to 60V output voltage capability, and seamless low noise transitions between operation regions, the LT8392 is ideal for voltage regulator, battery and super-capacitor charger applications in automotive, industrial, telecom, and even battery-powered systems. The LT8392 provides input or output current monitor and power good flag. Fault protection function detects output short-circuit condition, during which the LT8392 retries, latches off, or keeps running.", "applications": ["Automotive Systems", "Industrial Systems", "Telecom Systems", "High Frequency Battery-Powered System"], "ordering_information": [{"part_number": "LT8392", "order_device": "LT8392EFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE28 (EB) TSSOP", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8392", "order_device": "LT8392EFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE28 (EB) TSSOP", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8392", "order_device": "LT8392JFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE28 (EB) TSSOP", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8392", "order_device": "LT8392JFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE28 (EB) TSSOP", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8392", "order_device": "LT8392HFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE28 (EB) TSSOP", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8392", "order_device": "LT8392HFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE28 (EB) TSSOP", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8392", "order_device": "LT8392EUFD#PBF", "package_type": "QFN", "package_drawing_code": "UFD28", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8392", "order_device": "LT8392EUFD#TRPBF", "package_type": "QFN", "package_drawing_code": "UFD28", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8392", "order_device": "LT8392JUFD#PBF", "package_type": "QFN", "package_drawing_code": "UFD28", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8392", "order_device": "LT8392JUFD#TRPBF", "package_type": "QFN", "package_drawing_code": "UFD28", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8392", "order_device": "LT8392HUFD#PBF", "package_type": "QFN", "package_drawing_code": "UFD28", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT8392", "order_device": "LT8392HUFD#TRPBF", "package_type": "QFN", "package_drawing_code": "UFD28", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}], "pin_function": [{"product_part_number": "LT8392", "package_type": "TSSOP", "pins": [{"pin_number": "1", "pin_name": "BG1", "pin_description": "Buck Side Bottom Gate Drive. Drives the gate of buck side bottom N-Channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "2", "pin_name": "BST1", "pin_description": "Buck Side Bootstrap Floating Driver Supply. The BST1 pin has an integrated bootstrap Schottky diode from the INTVCC pin and requires an external bootstrap capacitor to the SW1 pin."}, {"pin_number": "3", "pin_name": "SW1", "pin_description": "Buck Side Switch Node."}, {"pin_number": "4", "pin_name": "TG1", "pin_description": "Buck Side Top Gate Drive. Drives the gate of buck side top N-Channel MOSFET with a voltage swing from SW1 to BST1."}, {"pin_number": "5", "pin_name": "LSP", "pin_description": "Positive Terminal of the Buck Side Inductor Current Sense Resistor (RSENSE). Ensure accurate current sense with <PERSON><PERSON> connection."}, {"pin_number": "6", "pin_name": "LSN", "pin_description": "Negative Terminal of the Buck Side Inductor Current Sense Resistor (RSENSE). Ensure accurate current sense with <PERSON><PERSON> connection."}, {"pin_number": "7", "pin_name": "VIN", "pin_description": "Input Supply. The VIN pin must be tied to the power input to determine the buck, buck-boost, or boost operation regions. Locally bypass this pin to ground with a minimum 1μF ceramic capacitor."}, {"pin_number": "8", "pin_name": "INTVCC", "pin_description": "Internal 5V Linear Regulator Output. The INTVCC linear regulator is supplied from either the VIN pin or the BIAS pin, and powers the internal control circuitry and gate drivers. Locally bypass this pin to ground with a minimum 4.7μF ceramic capacitor."}, {"pin_number": "9", "pin_name": "EN/UVLO", "pin_description": "Enable and Undervoltage Lockout. Force the pin below 0.3V to shut down the part and reduce VIN quiescent current below 2μA. Force the pin above 1.233V for normal operation. The accurate 1.220V falling threshold can be used to program an undervoltage lockout (UVLO) threshold with a resistor divider from VIN to ground. An accurate 2.5μA pull-down current allows the programming of VIN UVLO hysteresis. If neither function is used, tie this pin directly to VIN."}, {"pin_number": "10", "pin_name": "TEST", "pin_description": "Factory Test. This pin is used for testing purpose only and must be directly connected to ground for the part to operate properly."}, {"pin_number": "11", "pin_name": "TRIM", "pin_description": "Factory Trim. This pin is used for trim purposes only and must be directly connected to VREF for the part to operate properly."}, {"pin_number": "12", "pin_name": "VREF", "pin_description": "Voltage Reference Output. The VREF pin provides an accurate 2V reference capable of supplying 1mA current. Locally bypass this pin to ground with a 0.47μF ceramic capacitor."}, {"pin_number": "13", "pin_name": "CTRL", "pin_description": "Control Input for ISP/ISN Current Sense Threshold. The CTRL pin is used to program the ISP/ISN current limit."}, {"pin_number": "14", "pin_name": "ISP", "pin_description": "Positive Terminal of the ISP/ISN Current Sense Resistor (RIS). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "15", "pin_name": "ISN", "pin_description": "Negative Terminal of the ISP/ISN Current Sense Resistor (RIS). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "16", "pin_name": "ISMON", "pin_description": "ISP/ISN Current Sense Monitor Output. The ISMON pin generates a voltage that is equal to twenty times V(ISP-ISN) plus 0.25V offset voltage."}, {"pin_number": "17", "pin_name": "PGOOD", "pin_description": "Power Good Open Drain Output. The PGOOD pin is pulled low when the FB pin is within ±8% of the final regulation voltage. To function, the pin requires an external pull-up resistor."}, {"pin_number": "18", "pin_name": "SS", "pin_description": "Soft-Start Timer Setting. The SS pin is used to set soft-start timer by connecting a capacitor to ground. It can also be used to set fault protection modes."}, {"pin_number": "19", "pin_name": "FB", "pin_description": "Voltage Loop Feedback Input. The FB pin is used for constant-voltage regulation and output fault protection."}, {"pin_number": "20", "pin_name": "Vc", "pin_description": "Error Amplifier Output to Set Inductor Current Comparator Threshold. The Vc pin is used to compensate the control loop with an external RC network."}, {"pin_number": "21", "pin_name": "RT", "pin_description": "Switching Frequency Setting. Connect a resistor from this pin to ground to set the internal oscillator frequency from 150kHz to 650kHz."}, {"pin_number": "22", "pin_name": "SYNC/SPRD", "pin_description": "Switching Frequency Synchronization or Spread Spectrum. Ground this pin for switching at internal oscillator frequency. Apply a clock signal for external frequency synchronization. Tie to INTVCC for ±15% triangle spread spectrum."}, {"pin_number": "23", "pin_name": "EXTVCC", "pin_description": "Second Input Supply for Powering INTVCC. The part intelligently chooses either VIN or EXTVCC for INTVCC LDO to improve efficiency. Tie this pin ground if not used."}, {"pin_number": "24", "pin_name": "VOUT", "pin_description": "Output Supply. The VOUT pin must be tied to the power output to determine the buck, buck-boost, or boost operation regions. Locally bypass this pin to ground with a minimum 1μF ceramic capacitor."}, {"pin_number": "25", "pin_name": "TG2", "pin_description": "Boost Side Top Gate Drive. Drives the gate of boost side top N-Channel MOSFET with a voltage swing from SW2 to BST2."}, {"pin_number": "26", "pin_name": "SW2", "pin_description": "Boost Side Switch Node."}, {"pin_number": "27", "pin_name": "BST2", "pin_description": "Boost Side Bootstrap Floating Driver Supply. The BST2 pin has an integrated bootstrap Schottky diode from the INTVCC pin and requires an external bootstrap capacitor to the SW2 pin."}, {"pin_number": "28", "pin_name": "BG2", "pin_description": "Boost Side Bottom Gate Drive. Drives the gate of boost side bottom N-Channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "29", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Solder the exposed pad directly to the ground plane."}]}, {"product_part_number": "LT8392", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "TG1", "pin_description": "Buck Side Top Gate Drive. Drives the gate of buck side top N-Channel MOSFET with a voltage swing from SW1 to BST1."}, {"pin_number": "2", "pin_name": "LSP", "pin_description": "Positive Terminal of the Buck Side Inductor Current Sense Resistor (RSENSE). Ensure accurate current sense with <PERSON><PERSON> connection."}, {"pin_number": "3", "pin_name": "LSN", "pin_description": "Negative Terminal of the Buck Side Inductor Current Sense Resistor (RSENSE). Ensure accurate current sense with <PERSON><PERSON> connection."}, {"pin_number": "4", "pin_name": "VIN", "pin_description": "Input Supply. The VIN pin must be tied to the power input to determine the buck, buck-boost, or boost operation regions. Locally bypass this pin to ground with a minimum 1μF ceramic capacitor."}, {"pin_number": "5", "pin_name": "INTVCC", "pin_description": "Internal 5V Linear Regulator Output. The INTVCC linear regulator is supplied from either the VIN pin or the BIAS pin, and powers the internal control circuitry and gate drivers. Locally bypass this pin to ground with a minimum 4.7μF ceramic capacitor."}, {"pin_number": "6", "pin_name": "EN/UVLO", "pin_description": "Enable and Undervoltage Lockout. Force the pin below 0.3V to shut down the part and reduce VIN quiescent current below 2μA. Force the pin above 1.233V for normal operation."}, {"pin_number": "7", "pin_name": "TEST", "pin_description": "Factory Test. This pin is used for testing purpose only and must be directly connected to ground for the part to operate properly."}, {"pin_number": "8", "pin_name": "TRIM", "pin_description": "Factory Trim. This pin is used for trim purposes only and must be directly connected to VREF for the part to operate properly."}, {"pin_number": "9", "pin_name": "VREF", "pin_description": "Voltage Reference Output. The VREF pin provides an accurate 2V reference capable of supplying 1mA current. Locally bypass this pin to ground with a 0.47μF ceramic capacitor."}, {"pin_number": "10", "pin_name": "CTRL", "pin_description": "Control Input for ISP/ISN Current Sense Threshold. The CTRL pin is used to program the ISP/ISN current limit."}, {"pin_number": "11", "pin_name": "ISP", "pin_description": "Positive Terminal of the ISP/ISN Current Sense Resistor (RIS). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "12", "pin_name": "ISN", "pin_description": "Negative Terminal of the ISP/ISN Current Sense Resistor (RIS). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "13", "pin_name": "ISMON", "pin_description": "ISP/ISN Current Sense Monitor Output. The ISMON pin generates a voltage that is equal to twenty times V(ISP-ISN) plus 0.25V offset voltage."}, {"pin_number": "14", "pin_name": "PGOOD", "pin_description": "Power Good Open Drain Output. The PGOOD pin is pulled low when the FB pin is within ±8% of the final regulation voltage. To function, the pin requires an external pull-up resistor."}, {"pin_number": "15", "pin_name": "SS", "pin_description": "Soft-Start Timer Setting. The SS pin is used to set soft-start timer by connecting a capacitor to ground. It can also be used to set fault protection modes."}, {"pin_number": "16", "pin_name": "FB", "pin_description": "Voltage Loop Feedback Input. The FB pin is used for constant-voltage regulation and output fault protection."}, {"pin_number": "17", "pin_name": "Vc", "pin_description": "Error Amplifier Output to Set Inductor Current Comparator Threshold. The Vc pin is used to compensate the control loop with an external RC network."}, {"pin_number": "18", "pin_name": "RT", "pin_description": "Switching Frequency Setting. Connect a resistor from this pin to ground to set the internal oscillator frequency from 150kHz to 650kHz."}, {"pin_number": "19", "pin_name": "SYNC/SPRD", "pin_description": "Switching Frequency Synchronization or Spread Spectrum. Ground this pin for switching at internal oscillator frequency. Apply a clock signal for external frequency synchronization. Tie to INTVCC for ±15% triangle spread spectrum."}, {"pin_number": "20", "pin_name": "EXTVCC", "pin_description": "Second Input Supply for Powering INTVCC. The part intelligently chooses either VIN or EXTVCC for INTVCC LDO to improve efficiency. Tie this pin ground if not used."}, {"pin_number": "21", "pin_name": "VOUT", "pin_description": "Output Supply. The VOUT pin must be tied to the power output to determine the buck, buck-boost, or boost operation regions. Locally bypass this pin to ground with a minimum 1μF ceramic capacitor."}, {"pin_number": "22", "pin_name": "TG2", "pin_description": "Boost Side Top Gate Drive. Drives the gate of boost side top N-Channel MOSFET with a voltage swing from SW2 to BST2."}, {"pin_number": "23", "pin_name": "BG2", "pin_description": "Boost Side Bottom Gate Drive. Drives the gate of boost side bottom N-Channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "24", "pin_name": "SW2", "pin_description": "Boost Side Switch Node."}, {"pin_number": "25", "pin_name": "BST2", "pin_description": "Boost Side Bootstrap Floating Driver Supply. The BST2 pin has an integrated bootstrap Schottky diode from the INTVCC pin and requires an external bootstrap capacitor to the SW2 pin."}, {"pin_number": "26", "pin_name": "SW1", "pin_description": "Buck Side Switch Node."}, {"pin_number": "27", "pin_name": "BST1", "pin_description": "Buck Side Bootstrap Floating Driver Supply. The BST1 pin has an integrated bootstrap Schottky diode from the INTVCC pin and requires an external bootstrap capacitor to the SW1 pin."}, {"pin_number": "28", "pin_name": "BG1", "pin_description": "Buck Side Bottom Gate Drive. Drives the gate of buck side bottom N-Channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "29", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Solder the exposed pad directly to the ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT8392 Datasheet Rev. 0", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "60V", "min_input_voltage": "3V", "max_output_voltage": "60V", "min_output_voltage": "1V", "max_output_current": "外部可调", "max_switch_frequency": "0.65MHz", "quiescent_current": "1µA", "high_side_mosfet_resistance": "不适用(外部器件)", "low_side_mosfet_resistance": "不适用(外部器件)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Pulse Skipping, DCM", "power_good_indicator": "Yes", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "No", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "1V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "TSSOP", "pitch": "0.65", "height": "1.2", "width": "4.4", "length": "9.7", "pin_count": "1"}]}