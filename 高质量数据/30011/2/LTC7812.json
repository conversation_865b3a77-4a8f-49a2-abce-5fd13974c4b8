{"part_number": "LTC7812", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "Low IQ, 38V Synchronous Boost+Buck Controller", "features": ["Synchronous Boost and Buck Controllers", "When Cascaded, Allows VIN Above, Below or Equal to Regulated VOUT", "Output Remains in Regulation Through Input Dips (e.g., Cold Crank) Down to 2.5V", "Wide Bias Input Voltage Range: 4.5V to 38V", "Low Input and Output Ripple", "Low EMI", "Fast Output Transient Response", "High Light Load Efficiency", "Low Operating IQ: 33µA (Both Channels On)", "Low Operating IQ: 28µA (Buck Channel On)", "RSENSE or Lossless DCR Current Sensing", "Buck Output Voltage Range: 0.8V ≤ VOUT ≤ 24V", "Boost Output Voltage Up to 60V", "Phase-Lockable Frequency (75kHz to 850kHz)", "Small 32-Pin 5mm × 5mm QFN Package"], "description": "The LTC7812 is a high performance synchronous Boost+Buck DC/DC switching regulator controller that drives all N-channel power MOSFET stages. It contains independent step-up (boost) and step-down (buck) controllers that can regulate two separate outputs or be cascaded to regulate an output voltage from an input voltage that can be above, below or equal to the output voltage. The LTC7812 operates from a wide 4.5V to 38V input supply range. When biased from the output of the boost regulator, the LTC7812 can operate from an input supply as low as 2.5V after start-up. The 33µA no-load quiescent current extends operating run time in battery-powered systems. Unlike conventional buck-boost regulators, the LTC7812’s cascaded Boost+Buck solution has continuous, non-pulsating, input and output currents, substantially reducing voltage ripple and EMI. The LTC7812 has independent feedback and compensation points for the boost and buck regulation loops, enabling a fast output transient response that can be easily optimized externally.", "applications": ["Automotive and Industrial Power Systems", "High Power Battery Operated Systems"], "ordering_information": [{"part_number": "LTC7812", "order_device": "LTC7812EUH#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC7812", "order_device": "LTC7812EUH#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC7812", "order_device": "LTC7812IUH#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC7812", "order_device": "LTC7812IUH#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC7812", "order_device": "LTC7812HUH#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC7812", "order_device": "LTC7812HUH#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1693 Rev D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}], "pin_function": [{"product_part_number": "LTC7812", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "SW1", "pin_description": "Switch Node Connection to Inductor for buck channel."}, {"pin_number": "2", "pin_name": "TG1", "pin_description": "High Current Gate Drive for Top N-Channel MOSFET (buck channel)."}, {"pin_number": "3", "pin_name": "PGOOD1", "pin_description": "Open-Drain Logic Output. Pulled to ground when VFB1 is not within ±10% of its set point."}, {"pin_number": "4", "pin_name": "TRACK/SS1", "pin_description": "External Tracking and Soft-Start Input for buck channel."}, {"pin_number": "5", "pin_name": "ITH1", "pin_description": "Error Amplifier Output and Switching Regulator Compensation Point for buck channel."}, {"pin_number": "6", "pin_name": "VFB1", "pin_description": "Feedback voltage input for buck channel from an external resistive divider."}, {"pin_number": "7", "pin_name": "SENSE1+", "pin_description": "The (+) Input to the Differential Current Comparator for buck channel."}, {"pin_number": "8", "pin_name": "SENSE1-", "pin_description": "The (-) Input to the Differential Current Comparator for buck channel."}, {"pin_number": "9", "pin_name": "FREQ", "pin_description": "Frequency Control Pin for the Internal VCO."}, {"pin_number": "10", "pin_name": "PLLIN/MODE", "pin_description": "External Synchronization Input and Forced Continuous/Burst/Pulse-Skipping Mode Input."}, {"pin_number": "11", "pin_name": "SS2", "pin_description": "External Soft-Start Input for boost channel."}, {"pin_number": "12", "pin_name": "SENSE2+", "pin_description": "The (+) Input to the Differential Current Comparator for boost channel."}, {"pin_number": "13", "pin_name": "SENSE2-", "pin_description": "The (-) Input to the Differential Current Comparator for boost channel."}, {"pin_number": "14", "pin_name": "VFB2", "pin_description": "Feedback voltage input for boost channel from an external resistive divider."}, {"pin_number": "15", "pin_name": "ITH2", "pin_description": "Error Amplifier Output and Switching Regulator Compensation Point for boost channel."}, {"pin_number": "16", "pin_name": "SGND", "pin_description": "Small Signal Ground common to both controllers."}, {"pin_number": "17", "pin_name": "RUN1", "pin_description": "Run Control Input for buck controller."}, {"pin_number": "18", "pin_name": "SGND", "pin_description": "Small Signal Ground common to both controllers."}, {"pin_number": "19", "pin_name": "RUN2", "pin_description": "Run Control Input for boost controller."}, {"pin_number": "20", "pin_name": "SGND", "pin_description": "Small Signal Ground common to both controllers."}, {"pin_number": "21", "pin_name": "SGND", "pin_description": "Small Signal Ground common to both controllers."}, {"pin_number": "22", "pin_name": "OV2", "pin_description": "Overvoltage Open-Drain Logic Output for the Boost Regulator."}, {"pin_number": "23", "pin_name": "NC", "pin_description": "No Connect. No external connection is required."}, {"pin_number": "24", "pin_name": "INTVCC", "pin_description": "Output of the Internal Linear Low Dropout Regulator."}, {"pin_number": "25", "pin_name": "EXTVCC", "pin_description": "External Power Input to an Internal LDO Connected to INTVCC."}, {"pin_number": "26", "pin_name": "VBIAS", "pin_description": "Main Bias Input Supply Pin."}, {"pin_number": "27", "pin_name": "BG2", "pin_description": "High Current Gate Drive for Bottom N-Channel MOSFET (boost channel)."}, {"pin_number": "28", "pin_name": "BOOST2", "pin_description": "Bootstrapped Supply to the Top Side Floating Driver for boost channel."}, {"pin_number": "29", "pin_name": "TG2", "pin_description": "High Current Gate Drive for Top N-Channel MOSFET (boost channel)."}, {"pin_number": "30", "pin_name": "SW2", "pin_description": "Switch Node Connection to Inductor for boost channel."}, {"pin_number": "31", "pin_name": "BG1", "pin_description": "High Current Gate Drive for Bottom N-Channel MOSFET (buck channel)."}, {"pin_number": "32", "pin_name": "BOOST1", "pin_description": "Bootstrapped Supply to the Top Side Floating Driver for buck channel."}, {"pin_number": "33", "pin_name": "PGND", "pin_description": "Driver Power Ground. Exposed Pad."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC7812.pdf", "family_comparison": "Related parts include LTM4609, LTM8056, LTC3789, LT3790, LT8705, etc., differing in topology (Buck-Boost, Step-Up, Step-Down), I/O voltage ranges, number of outputs, quiescent current, and package types.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 2, "max_input_voltage": "38V", "min_input_voltage": "4.5V", "max_output_voltage": "60V", "min_output_voltage": "1V", "max_output_current": "不适用(控制器)", "max_switch_frequency": "0.85MHz", "quiescent_current": "33µA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "50mV", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "<PERSON><PERSON><PERSON> Mode, <PERSON><PERSON> Skipping, Forced Continuous", "power_good_indicator": "Yes", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "True", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "0.8V/1.2V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.5", "length": "5", "width": "1.17", "type": "QFN", "pin_count": "5", "height": "0.5"}]}