{"part_number": "LTC3789", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "High Efficiency, Synchronous, 4-<PERSON><PERSON> <PERSON>-Boost Controller", "features": ["Single Inductor Architecture Allows VIN Above, Below or Equal to the Regulated VOUT", "Programmable Input or Output Current", "Wide VIN Range: 4V to 38V", "1% Output Voltage Accuracy: 0.8V < VOUT < 38V", "Synchronous Rectification: Up to 98% Efficiency", "Current Mode Control", "Phase-Lockable Fixed Frequency: 200kHz to 600kHz", "No Reverse Current During Start-Up", "Power Good Output Voltage Monitor", "Internal 5.5V LDO", "Quad N-Channel MOSFET Synchronous Drive", "VOUT Disconnected from VIN During Shutdown", "True Soft-Start and VOUT Short Protection, Even in Boost Mode", "Available in 28-Lead QFN (4mm × 5mm) and 28-Lead SSOP Packages"], "description": "The LTC®3789 is a high performance buck-boost switching regulator controller that operates from input voltages above, below or equal to the output voltage. The constant-frequency, current mode architecture allows a phase-lockable frequency of up to 600kHz, while an output current feedback loop provides support for battery charging. With a wide 4V to 38V (40V maximum) input and output range and seamless, low noise transitions between operating regions, the LTC3789 is ideal for automotive, telecom and battery-powered systems.\nThe operating mode of the controller is determined through the MODE/PLLIN pin. The MODE/PLLIN pin can select between pulse-skipping mode and forced continuous mode operation and allows the IC to be synchronized to an external clock. Pulse-skipping mode offers high efficiency and low ripple at light loads, while forced continuous mode operates at a constant frequency for noise-sensitive applications.\nA PGOOD pin indicates when the output is within 10% of its designed set point. The LTC3789 is available in low profile 28-pin 4mm × 5mm QFN and narrow SSOP packages.", "applications": ["Automotive Systems", "Distributed DC Power Systems", "High Power Battery-Operated Devices", "Industrial Control"], "ordering_information": [{"part_number": "LTC3789", "order_device": "LTC3789EGN#PBF", "package_type": "28-Lead Narrow Plastic SSOP", "package_drawing_code": "05-08-1641", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3789", "order_device": "LTC3789EGN#TRPBF", "package_type": "28-Lead Narrow Plastic SSOP", "package_drawing_code": "05-08-1641", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3789", "order_device": "LTC3789IGN#PBF", "package_type": "28-Lead Narrow Plastic SSOP", "package_drawing_code": "05-08-1641", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3789", "order_device": "LTC3789IGN#TRPBF", "package_type": "28-Lead Narrow Plastic SSOP", "package_drawing_code": "05-08-1641", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3789", "order_device": "LTC3789EUFD#PBF", "package_type": "28-Lead (4mm x 5mm) Plastic QFN", "package_drawing_code": "05-08-1712 Rev B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3789", "order_device": "LTC3789EUFD#TRPBF", "package_type": "28-Lead (4mm x 5mm) Plastic QFN", "package_drawing_code": "05-08-1712 Rev B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3789", "order_device": "LTC3789IUFD#PBF", "package_type": "28-Lead (4mm x 5mm) Plastic QFN", "package_drawing_code": "05-08-1712 Rev B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3789", "order_device": "LTC3789IUFD#TRPBF", "package_type": "28-Lead (4mm x 5mm) Plastic QFN", "package_drawing_code": "05-08-1712 Rev B", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LTC3789", "package_type": "SSOP-28", "pins": [{"pin_number": "1", "pin_name": "VFB", "pin_description": "Error Amplifier Feedback Pin. Receives the feedback voltage for the controller from an external resistive divider across the output."}, {"pin_number": "2", "pin_name": "SS", "pin_description": "External Soft-Start Input. The LTC3789 regulates the VFB voltage to the smaller of 0.8V or the voltage on the SS pin. An internal 3µA pull-up current source is connected to this pin. A capacitor to ground at this pin sets the ramp time to final regulated output voltage."}, {"pin_number": "3", "pin_name": "SENSE+", "pin_description": "The (+) Input to the Current Sense Comparator. The ITH pin voltage and controlled offsets between the SENSE¯ and SENSE+ pins, in conjunction with RSENSE, set the current trip threshold."}, {"pin_number": "4", "pin_name": "SENSE-", "pin_description": "The (–) Input to the Current Sense Comparator."}, {"pin_number": "5", "pin_name": "ITH", "pin_description": "Error Amplifier Output and Switching Regulator Compensation Point. The channel's current comparator trip point increases with this control voltage."}, {"pin_number": "6", "pin_name": "SGND", "pin_description": "Small Signal Ground. Must be routed separately from high current grounds to the common (–) terminals of the CIN capacitors."}, {"pin_number": "7", "pin_name": "MODE/PLLIN", "pin_description": "Mode Selection or External Synchronization Input to Phase Detector. This is a dual-purpose pin. When external frequency synchronization is not used, this pin selects the operating mode. The pin can be tied to SGND or INTVCC. SGND or below 0.8V enables forced continuous mode. INTVCC enables pulse-skipping mode. For external sync, apply a clock signal to this pin."}, {"pin_number": "8", "pin_name": "FREQ", "pin_description": "Frequency Set Pin. There is a precision 10µA current flowing out of this pin. A resistor to ground sets a voltage which, in turn, programs the frequency. Alternatively, this pin can be driven with a DC voltage to vary the frequency of the internal oscillator."}, {"pin_number": "9", "pin_name": "RUN", "pin_description": "Run Control Input. Forcing the pin below 0.5V shuts down the controller, reducing quiescent current. There are 1.2µA pull-up currents for this pin. Once the RUN pin rises above 1.22V, the IC is turned on, and an additional 5µA pull-up current is added to the pin."}, {"pin_number": "10", "pin_name": "VINSNS", "pin_description": "VIN Sense Input to the Buck-Boost Transition Comparator. Connect this pin to the drain of the top N-channel MOSFET on the input side."}, {"pin_number": "11", "pin_name": "VOUTSNS", "pin_description": "VOUT Sense Input to the Buck-Boost Transition Comparator. Connect this pin to the VOUT."}, {"pin_number": "12", "pin_name": "ILIM", "pin_description": "Input/Output Average Current Sense Range Input. This pin tied to SGND, INTVCC or left floating, sets the maximum average current sense threshold."}, {"pin_number": "13", "pin_name": "IOSENSE+", "pin_description": "The (+) Input to the Input/Output Average Current Sense Amplifier."}, {"pin_number": "14", "pin_name": "IOSENSE-", "pin_description": "The (–) Input to the Input/Output Average Current Sense Amplifier."}, {"pin_number": "15", "pin_name": "TRIM", "pin_description": "Tie this pin to GND for normal operation. Do not allow this pin to float."}, {"pin_number": "16", "pin_name": "SW2", "pin_description": "Switch Node Connections to Inductors. Voltage swing at the SW2 pin is from a <PERSON><PERSON><PERSON>ky diode voltage drop below ground to VOUT."}, {"pin_number": "17", "pin_name": "TG2", "pin_description": "High Current Gate Drives for Top N-Channel MOSFETs. These are the outputs of floating drivers with a voltage swing equal to INTVCC – 0.5V superimposed on the switch node voltage SW."}, {"pin_number": "18", "pin_name": "BOOST2", "pin_description": "Bootstrapped Supplies to the Top Side Floating Drivers. Capacitors are connected between the BOOST and SW pins and <PERSON><PERSON><PERSON>ky diodes are tied between the BOOST and INTVCC pins. Voltage swing at the BOOST2 pin is from INTVCC to (VOUT + INTVCC)."}, {"pin_number": "19", "pin_name": "BG2", "pin_description": "High Current Gate Drives for Bottom (Synchronous) N-Channel MOSFETs. Voltage swing at these pins is from ground to INTVCC."}, {"pin_number": "20", "pin_name": "EXTVCC", "pin_description": "External Power Input to an Internal LDO Connected to INTVCC. This LDO supplies INTVCC power, bypassing the internal LDO powered from VIN whenever EXTVCC is higher than 4.8V. Do not exceed 14V on this pin."}, {"pin_number": "21", "pin_name": "INTVCC", "pin_description": "Output of the Internal Linear Low Dropout Regulator. The driver and control circuits are powered from this voltage source. Must be bypassed to power ground with a minimum of 4.7µF tantalum, ceramic, or other low ESR capacitor."}, {"pin_number": "22", "pin_name": "VIN", "pin_description": "Main Supply Pin. A bypass capacitor should be tied between this pin and the power ground pin."}, {"pin_number": "23", "pin_name": "BG1", "pin_description": "High Current Gate Drives for Bottom (Synchronous) N-Channel MOSFETs. Voltage swing at these pins is from ground to INTVCC."}, {"pin_number": "24", "pin_name": "PGND", "pin_description": "Driver Power Ground. Connects to COUT and RSENSE (–) terminal(s) of CIN."}, {"pin_number": "25", "pin_name": "BOOST1", "pin_description": "Bootstrapped Supplies to the Top Side Floating Drivers. Capacitors are connected between the BOOST and SW pins and <PERSON><PERSON><PERSON>ky diodes are tied between the BOOST and INTVCC pins. Voltage swing at the BOOST1 pin is from INTVCC to (VIN + INTVCC)."}, {"pin_number": "26", "pin_name": "TG1", "pin_description": "High Current Gate Drives for Top N-Channel MOSFETs. These are the outputs of floating drivers with a voltage swing equal to INTVCC – 0.5V superimposed on the switch node voltage SW."}, {"pin_number": "27", "pin_name": "SW1", "pin_description": "Switch Node Connections to Inductors. Voltage swing at the SW1 pin is from a <PERSON><PERSON><PERSON>ky diode (external) voltage drop below ground to VIN."}, {"pin_number": "28", "pin_name": "PGOOD", "pin_description": "Open-Drain Logic Output. PGOOD is pulled to ground when the voltage on the VFB pin is not within ±10% of its regulation window, after the internal 20µs power-bad mask timer expires."}]}, {"product_part_number": "LTC3789", "package_type": "QFN-28", "pins": [{"pin_number": "1", "pin_name": "SENSE-", "pin_description": "The (–) Input to the Current Sense Comparator."}, {"pin_number": "2", "pin_name": "ITH", "pin_description": "Error Amplifier Output and Switching Regulator Compensation Point. The channel's current comparator trip point increases with this control voltage."}, {"pin_number": "3", "pin_name": "SGND", "pin_description": "Small Signal Ground. Must be routed separately from high current grounds to the common (–) terminals of the CIN capacitors. In the QFN package, the exposed pad is SGND. It must be soldered to PCB ground for rated thermal performance."}, {"pin_number": "4", "pin_name": "MODE/PLLIN", "pin_description": "Mode Selection or External Synchronization Input to Phase Detector. This is a dual-purpose pin. When external frequency synchronization is not used, this pin selects the operating mode. The pin can be tied to SGND or INTVCC. SGND or below 0.8V enables forced continuous mode. INTVCC enables pulse-skipping mode. For external sync, apply a clock signal to this pin."}, {"pin_number": "5", "pin_name": "FREQ", "pin_description": "Frequency Set Pin. There is a precision 10µA current flowing out of this pin. A resistor to ground sets a voltage which, in turn, programs the frequency. Alternatively, this pin can be driven with a DC voltage to vary the frequency of the internal oscillator."}, {"pin_number": "6", "pin_name": "RUN", "pin_description": "Run Control Input. Forcing the pin below 0.5V shuts down the controller, reducing quiescent current. There are 1.2µA pull-up currents for this pin. Once the RUN pin rises above 1.22V, the IC is turned on, and an additional 5µA pull-up current is added to the pin."}, {"pin_number": "7", "pin_name": "VINSNS", "pin_description": "VIN Sense Input to the Buck-Boost Transition Comparator. Connect this pin to the drain of the top N-channel MOSFET on the input side."}, {"pin_number": "8", "pin_name": "VOUTSNS", "pin_description": "VOUT Sense Input to the Buck-Boost Transition Comparator. Connect this pin to the VOUT."}, {"pin_number": "9", "pin_name": "ILIM", "pin_description": "Input/Output Average Current Sense Range Input. This pin tied to SGND, INTVCC or left floating, sets the maximum average current sense threshold."}, {"pin_number": "10", "pin_name": "IOSENSE+", "pin_description": "The (+) Input to the Input/Output Average Current Sense Amplifier."}, {"pin_number": "11", "pin_name": "IOSENSE-", "pin_description": "The (–) Input to the Input/Output Average Current Sense Amplifier."}, {"pin_number": "12", "pin_name": "TRIM", "pin_description": "Tie this pin to GND for normal operation. Do not allow this pin to float."}, {"pin_number": "13", "pin_name": "SW2", "pin_description": "Switch Node Connections to Inductors. Voltage swing at the SW2 pin is from a <PERSON><PERSON><PERSON>ky diode voltage drop below ground to VOUT."}, {"pin_number": "14", "pin_name": "TG2", "pin_description": "High Current Gate Drives for Top N-Channel MOSFETs. These are the outputs of floating drivers with a voltage swing equal to INTVCC – 0.5V superimposed on the switch node voltage SW."}, {"pin_number": "15", "pin_name": "BOOST2", "pin_description": "Bootstrapped Supplies to the Top Side Floating Drivers. Capacitors are connected between the BOOST and SW pins and <PERSON><PERSON><PERSON>ky diodes are tied between the BOOST and INTVCC pins. Voltage swing at the BOOST2 pin is from INTVCC to (VOUT + INTVCC)."}, {"pin_number": "16", "pin_name": "BG2", "pin_description": "High Current Gate Drives for Bottom (Synchronous) N-Channel MOSFETs. Voltage swing at these pins is from ground to INTVCC."}, {"pin_number": "17", "pin_name": "EXTVCC", "pin_description": "External Power Input to an Internal LDO Connected to INTVCC. This LDO supplies INTVCC power, bypassing the internal LDO powered from VIN whenever EXTVCC is higher than 4.8V. Do not exceed 14V on this pin."}, {"pin_number": "18", "pin_name": "INTVCC", "pin_description": "Output of the Internal Linear Low Dropout Regulator. The driver and control circuits are powered from this voltage source. Must be bypassed to power ground with a minimum of 4.7µF tantalum, ceramic, or other low ESR capacitor."}, {"pin_number": "19", "pin_name": "VIN", "pin_description": "Main Supply Pin. A bypass capacitor should be tied between this pin and the power ground pin."}, {"pin_number": "20", "pin_name": "BG1", "pin_description": "High Current Gate Drives for Bottom (Synchronous) N-Channel MOSFETs. Voltage swing at these pins is from ground to INTVCC."}, {"pin_number": "21", "pin_name": "PGND", "pin_description": "Driver Power Ground. Connects to COUT and RSENSE (–) terminal(s) of CIN."}, {"pin_number": "22", "pin_name": "BOOST1", "pin_description": "Bootstrapped Supplies to the Top Side Floating Drivers. Capacitors are connected between the BOOST and SW pins and <PERSON><PERSON><PERSON>ky diodes are tied between the BOOST and INTVCC pins. Voltage swing at the BOOST1 pin is from INTVCC to (VIN + INTVCC)."}, {"pin_number": "23", "pin_name": "TG1", "pin_description": "High Current Gate Drives for Top N-Channel MOSFETs. These are the outputs of floating drivers with a voltage swing equal to INTVCC – 0.5V superimposed on the switch node voltage SW."}, {"pin_number": "24", "pin_name": "SW1", "pin_description": "Switch Node Connections to Inductors. Voltage swing at the SW1 pin is from a <PERSON><PERSON><PERSON>ky diode (external) voltage drop below ground to VIN."}, {"pin_number": "25", "pin_name": "PGOOD", "pin_description": "Open-Drain Logic Output. PGOOD is pulled to ground when the voltage on the VFB pin is not within ±10% of its regulation window, after the internal 20µs power-bad mask timer expires."}, {"pin_number": "26", "pin_name": "VFB", "pin_description": "Error Amplifier Feedback Pin. Receives the feedback voltage for the controller from an external resistive divider across the output."}, {"pin_number": "27", "pin_name": "SS", "pin_description": "External Soft-Start Input. The LTC3789 regulates the VFB voltage to the smaller of 0.8V or the voltage on the SS pin. An internal 3µA pull-up current source is connected to this pin. A capacitor to ground at this pin sets the ramp time to final regulated output voltage."}, {"pin_number": "28", "pin_name": "SENSE+", "pin_description": "The (+) Input to the Current Sense Comparator. The ITH pin voltage and controlled offsets between the SENSE¯ and SENSE+ pins, in conjunction with RSENSE, set the current trip threshold."}, {"pin_number": "29", "pin_name": "Exposed Pad", "pin_description": "Small Signal Ground. Must be routed separately from high current grounds to the common (–) terminals of the CIN capacitors. In the QFN package, the exposed pad is SGND. It must be soldered to PCB ground for rated thermal performance."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3789 Datasheet Rev. C", "family_comparison": "The 'RELATED PARTS' table on page 30 lists other synchronous buck-boost controllers and modules from Linear Technology, providing alternatives with different voltage/current ratings and package options.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "38V", "min_input_voltage": "4V", "max_output_voltage": "38V", "min_output_voltage": "1V", "max_output_current": "Programmable", "max_switch_frequency": "0.6MHz", "quiescent_current": "3000µA", "high_side_mosfet_resistance": "不适用(外部器件)", "low_side_mosfet_resistance": "不适用(外部器件)", "over_current_protection_threshold": "140mV (Boost), 90mV (Buck)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Pulse Skipping", "power_good_indicator": "Yes", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Fold Back", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "No", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.8V", "loop_control_mode": "Peak Current Mode"}, "package": [{"pitch": "0.635", "height": "1.75", "length": "9.9", "width": "3.9", "type": "SSOP", "pin_count": "4"}]}