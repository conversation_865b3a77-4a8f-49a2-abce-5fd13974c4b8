{"part_number": "LTC3127", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "1A Buck-Boost DC/DC Converter with Programmable Input Current Limit", "features": ["Programmable (0.2A to 1A) ±4% Accurate Average Input Current Limit", "Regulated Output with Input Voltages Above, Below or Equal to the Output", "1.8V to 5.5V (Input) and 1.8V to 5.25V (Output) Voltage Range", "0.6A Continuous Output Current: VIN > 1.8V", "1A Continuous Output Current: VIN > 3V", "Single Inductor", "Synchronous Rectification: Up to 96% Efficiency", "Burst Mode® Operation: IQ = 35µA (Pin Selectable)", "Output Disconnect in Shutdown", "<1µA Shutdown Current", "Small, Thermally Enhanced 10-Lead (3mm × 3mm × 0.75mm) DFN and 12-Lead MSOP Packages"], "description": "The LTC®3127 is a wide VIN range, highly efficient, 1.35MHz fixed frequency buck-boost DC/DC converter that operates from input voltages above, below or equal to the output voltage. The LTC3127 features programmable average input current limit, making it ideal for power-limited input sources. The input current limit is programmed with a single resistor and is accurate from 0.2A to 1A of average input current. The topology incorporated provides a continuous transfer function through all operating modes. Other features include <1µA shutdown current, pin-selectable Burst Mode operation and thermal overload protection. The LTC3127 is housed in thermally enhanced 10-lead (3mm x 3mm × 0.75mm) DFN packages and 12-lead MSOP packages.", "applications": ["USB Powered GSM Modems", "Supercap Charger", "Handheld Test Instruments", "PC Card Modems", "Wireless Terminals"], "ordering_information": [{"part_number": "LTC3127", "order_device": "LTC3127EDD#PBF", "package_type": "DFN", "package_drawing_code": "05-08-1699 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3127", "order_device": "LTC3127EDD#TRPBF", "package_type": "DFN", "package_drawing_code": "05-08-1699 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3127", "order_device": "LTC3127EMSE#PBF", "package_type": "MSOP", "package_drawing_code": "05-08-1666 <PERSON> G", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3127", "order_device": "LTC3127EMSE#TRPBF", "package_type": "MSOP", "package_drawing_code": "05-08-1666 <PERSON> G", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "LTC3127", "package_type": "DD", "pins": [{"pin_number": "1", "pin_name": "SW1", "pin_description": "Switch Pin Where Internal Switches A and B Are Connected. Connect inductor from SW1 to SW2. Minimize trace length to reduce EMI."}, {"pin_number": "2", "pin_name": "VIN", "pin_description": "Input Supply Pin. Internal VCC for the IC. A 10µF or greater ceramic capacitor should be placed as close to VIN and PGND as possible."}, {"pin_number": "3", "pin_name": "SHDN", "pin_description": "Logic-Controlled Shutdown Input. SHDN = High: Normal Operation, SHDN = Low: Shutdown."}, {"pin_number": "4", "pin_name": "MODE", "pin_description": "Pulse Width Modulation/Burst Mode Selection Input. MODE = High: Burst Mode Operation, MODE = Low: PWM Operation Only. Forced continuous conduction mode."}, {"pin_number": "5", "pin_name": "PROG", "pin_description": "Sets the Average Input Current Limit Threshold. Connect a resistor from PROG to ground."}, {"pin_number": "6", "pin_name": "SGND", "pin_description": "Signal Ground for the IC. Terminate the PROG resistor, compensation components and the output voltage divider to SGND."}, {"pin_number": "7", "pin_name": "FB", "pin_description": "Feedback Pin. Connect resistor divider tap here. The output voltage can be adjusted from 1.8V to 5.25V. The feedback reference voltage is 1.195V."}, {"pin_number": "8", "pin_name": "Vc", "pin_description": "Error Amplifier Output. Place compensation components from this pin to SGND."}, {"pin_number": "9", "pin_name": "VOUT", "pin_description": "Output of the Synchronous Rectifier. Connect the output filter capacitor from this pin to GND. A minimum value of 22µF is recommended. Output capacitors must be low ESR."}, {"pin_number": "10", "pin_name": "SW2", "pin_description": "Switch Pin Where Internal Switches C and D Are Connected. Minimize trace length to reduce EMI."}, {"pin_number": "11", "pin_name": "PGND (Exposed Pad)", "pin_description": "Power Ground. The exposed pad must be soldered to the PCB ground plane."}]}, {"product_part_number": "LTC3127", "package_type": "MSE", "pins": [{"pin_number": "1", "pin_name": "PGND", "pin_description": "Power Ground. The exposed pad must be soldered to the PCB ground plane."}, {"pin_number": "2", "pin_name": "SW1", "pin_description": "Switch Pin Where Internal Switches A and B Are Connected. Connect inductor from SW1 to SW2. Minimize trace length to reduce EMI."}, {"pin_number": "3", "pin_name": "VIN", "pin_description": "Input Supply Pin. Internal VCC for the IC. A 10µF or greater ceramic capacitor should be placed as close to VIN and PGND as possible."}, {"pin_number": "4", "pin_name": "SHDN", "pin_description": "Logic-Controlled Shutdown Input. SHDN = High: Normal Operation, SHDN = Low: Shutdown."}, {"pin_number": "5", "pin_name": "MODE", "pin_description": "Pulse Width Modulation/Burst Mode Selection Input. MODE = High: Burst Mode Operation, MODE = Low: PWM Operation Only. Forced continuous conduction mode."}, {"pin_number": "6", "pin_name": "PROG", "pin_description": "Sets the Average Input Current Limit Threshold. Connect a resistor from PROG to ground."}, {"pin_number": "7", "pin_name": "SGND", "pin_description": "Signal Ground for the IC. Terminate the PROG resistor, compensation components and the output voltage divider to SGND."}, {"pin_number": "8", "pin_name": "FB", "pin_description": "Feedback Pin. Connect resistor divider tap here. The output voltage can be adjusted from 1.8V to 5.25V. The feedback reference voltage is 1.195V."}, {"pin_number": "9", "pin_name": "Vc", "pin_description": "Error Amplifier Output. Place compensation components from this pin to SGND."}, {"pin_number": "10", "pin_name": "VOUT", "pin_description": "Output of the Synchronous Rectifier. Connect the output filter capacitor from this pin to GND. A minimum value of 22µF is recommended. Output capacitors must be low ESR."}, {"pin_number": "11", "pin_name": "SW2", "pin_description": "Switch Pin Where Internal Switches C and D Are Connected. Minimize trace length to reduce EMI."}, {"pin_number": "12", "pin_name": "PGND", "pin_description": "Power Ground. The exposed pad must be soldered to the PCB ground plane."}, {"pin_number": "13 (Exposed Pad)", "pin_name": "PGND", "pin_description": "Power Ground. The exposed pad must be soldered to the PCB ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3127", "family_comparison": "Related Parts: LTC3125 (1.2A IOUT, 1.6MHz, Synchronous Boost DC/DC Converter With Adjustable Input Current Limit), LTC3606B (800mA IOUT, Synchronous Step-Down DC/DC Converter with Average Input Current Limit), LTC3128 (3A Buck-Boost Supercapacitor Charger and Balancer with Accurate Current Limit), LTC3619B (400mA/800mA Synchronous Step-Down DC/DC with Average Input Current Limit), LTC3625/LTC3625-1 (1A High-Efficiency 2-Cell Supercapacitor Charger with Automatic Cell Balancing)", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.25V", "min_output_voltage": "1.8V", "max_output_current": "1A", "max_switch_frequency": "1.7MHz", "quiescent_current": "35µA", "high_side_mosfet_resistance": "190mΩ", "low_side_mosfet_resistance": "170mΩ", "over_current_protection_threshold": "2.5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.51%", "output_reference_voltage": "1.195V", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.5", "height": "0.75", "length": "2.", "width": "2.", "type": "DESCRIPTION", "pin_count": "35"}]}