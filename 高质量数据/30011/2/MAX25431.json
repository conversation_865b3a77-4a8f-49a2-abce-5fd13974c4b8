{"part_number": "MAX25431", "manufacturer": "Maxim Integrated", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "Automotive 40V, 2.2MHz, H-<PERSON> Buck-Boost Controller", "features": ["Meets Stringent Automotive Quality and Reliability Requirements", "Operating VIN Range: 6V to 36V Allows Operation In Cold-Crank Conditions", "Tolerates Input Transients to 40V", "EN Pin Compatible from +3.3V to +40V", "-40°C to +125°C Automotive Temperature Range", "AEC-100 Qualified", "Efficient Solution in a Small Solutions Size", "Fixed 5V Output Voltage and Adjustable 3V to 25V", "High Switching Frequency Allows Use of Small External Components", "Small 4mm x 4mm 24-Pin SWTQFN Package", "Low Quiescent Current Helps Designers Meet Stringent OEM Current Requirements", "10µA (max) Quiescent Current in Shutdown", "EMI Mitigation to meet CISPR25 Class 5 Requirements", "220kHz to 2.2MHz Operating Frequency", "Fixed-Frequency PWM Mode with Spread Spectrum", "External Frequency Synchronization or SYNC OUT Capability"], "description": "The MAX25431 is a current-mode buck-boost controller. The device operates with input voltages from 6V to 36V. The switching frequency is resistor programmable from 220kHz to 2.2MHz and can be synchronized to an external clock. The device output voltage is available as 5V fixed or adjustable from 3V to 25V. The wide input voltage range, along with its ability to maintain constant output voltage during battery transients, makes the devices ideal for automotive applications. In light-load applications, a logic input (FSYNC) allows the devices to operate in fixed-frequency, forced-PWM mode to eliminate frequency variation and help minimize EMI. Protection features include cycle-by-cycle current limit followed by hiccup during sustained overloads, input under-voltage lockout (UVLO), output overvoltage protection and thermal shut- down with automatic recovery. The MAX25431 is available in a small 4mm x 4mm 24-pin TQFN-EP SW package.", "applications": ["USB Hubs, Breakout Boxes and Multimedia Hubs", "Dedicated Charging <PERSON>", "Rear-Seat Entertainment Modules"], "ordering_information": [{"part_number": "MAX25431", "order_device": "MAX25431ATGA/VY+", "package_type": "SWTQFN", "package_drawing_code": "21-100290", "output_voltage": "5V (fixed), ADJ", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX25431", "order_device": "MAX25431ATGB/VY+", "package_type": "SWTQFN", "package_drawing_code": "21-100290", "output_voltage": "5V (fixed), ADJ", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MAX25431", "package_type": "TQFN", "pins": [{"pin_number": "1", "pin_name": "DL1", "pin_description": "<PERSON> Side Gate Drive"}, {"pin_number": "2", "pin_name": "PGND", "pin_description": "Power Ground"}, {"pin_number": "3", "pin_name": "CSN1", "pin_description": "Negative Input of the Input Side Current-Sense Amplifier. Connect CSN1 to the negative side of the input current-sense resistor."}, {"pin_number": "4", "pin_name": "CSP1", "pin_description": "Positive Input of the Input Side Current-Sense Amplifier. Connect CSP1 to the positive side of the input current-sense resistor."}, {"pin_number": "5", "pin_name": "IN", "pin_description": "Voltage Supply Input. IN powers up the internal linear regulator. Bypass IN to PGND with a ceramic capacitor as suggested in the Typical Application Circuit."}, {"pin_number": "6", "pin_name": "EN", "pin_description": "High-Voltage Enable Input. Driving EN high enables the buck-boost controller."}, {"pin_number": "7", "pin_name": "SLP", "pin_description": "Slope Compensation for Peak Current Mode Control. Connect a resistor between SLP and AGND to set the desired slope compensation for the current feedback loop."}, {"pin_number": "8", "pin_name": "FB", "pin_description": "Feedback Analog Input. Connect an external resistive divider from OUT to FB and AGND to set the desired output voltage. Connect to VCC to set the output voltage to 5V."}, {"pin_number": "9", "pin_name": "FSW", "pin_description": "Switching Frequency Setting. Connect a resistor between FSW and AGND to set the desired frequency."}, {"pin_number": "10", "pin_name": "COMP", "pin_description": "Error Amplifier Output. Connect the external compensation network of the feedback loop between COMP and AGND for stable operation."}, {"pin_number": "11", "pin_name": "PGOOD", "pin_description": "Open-Drain, Power Good Output Indicator. An external pullup is required."}, {"pin_number": "12", "pin_name": "FSYNC/SYNCOUT", "pin_description": "Connect FSYNC to an external frequency source for synchronization. SYNCOUT is available to output 180° out of phase clock for dual port synchronization."}, {"pin_number": "13", "pin_name": "OUT", "pin_description": "Switching Regulator Voltage Output. Connect recommended capacitor values between OUT and PGND as per the Typical Application Circuit."}, {"pin_number": "14", "pin_name": "CSN2", "pin_description": "Negative Input of the Output Side Current-Sense Amplifier. Connect CSN2 to the negative side of the output current-sense resistor."}, {"pin_number": "15", "pin_name": "CSP2", "pin_description": "Positive Input of the Output Side Current-Sense Amplifier. Connect CSP2 to the positive side of the output current-sense resistor."}, {"pin_number": "16", "pin_name": "AGND", "pin_description": "Analog Ground of the IC. Connect to ground plane reference of the PCB."}, {"pin_number": "17", "pin_name": "VCC", "pin_description": "Linear Regulator Output. VCC powers up the internal circuitry. Bypass with 4.7uF ceramic capacitor to AGND."}, {"pin_number": "18", "pin_name": "DL2", "pin_description": "Boost Low-Side Gate Drive"}, {"pin_number": "19", "pin_name": "BST2", "pin_description": "Bootstrap Capacitor for High-Side Driver of the LX2 Node. Connect a 0.1µF capacitor from BST2 to LX2."}, {"pin_number": "20", "pin_name": "DH2", "pin_description": "Boost High-Side Gate Drive."}, {"pin_number": "21", "pin_name": "LX2", "pin_description": "OUT to PGND Switching Output Node. High impedance when the part is off. Connect to one of the external inductor terminals."}, {"pin_number": "22", "pin_name": "LX1", "pin_description": "IN to PGND Switching Input Node. High impedance when the part is off. Connect to the other external inductor terminal."}, {"pin_number": "23", "pin_name": "DH1", "pin_description": "<PERSON> High-Side Gate Drive."}, {"pin_number": "24", "pin_name": "BST1", "pin_description": "Bootstrap Capacitor for High-Side Driver of LX1 Node. Connect a 0.1µF capacitor between BST1 and LX1."}, {"pin_number": "EP", "pin_name": "EP", "pin_description": "Exposed Pad. EP must be connected to the ground plane on the PCB, but it is not a current-carrying path and is needed only for thermal transfer."}]}], "datasheet_cn": "未找到", "datasheet_en": "19-100641; Rev 1; 8_20.pdf", "family_comparison": "未找到", "attributes": {"power_device_type": "不适用(外部器件)", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "6V", "max_output_voltage": "25V", "min_output_voltage": "3V", "max_output_current": "不适用(外部器件决定)", "max_switch_frequency": "2.2MHz", "quiescent_current": "5µA", "high_side_mosfet_resistance": "不适用(外部器件)", "low_side_mosfet_resistance": "不适用(外部器件)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "Fixed/Adjustable", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "FPWM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.36%", "output_reference_voltage": "1.25V", "loop_control_mode": "峰值电流模式"}, "package": [{"type": "Information", "width": "4", "length": "4", "pin_count": "8", "pitch": "1", "height": "4.7"}]}