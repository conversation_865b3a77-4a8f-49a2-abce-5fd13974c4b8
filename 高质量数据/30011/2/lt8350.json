{"part_number": "LT8350", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "40VIN, 18VOUT, 6A Synchronous Buck-<PERSON><PERSON> Switcher", "features": ["4-Switch Single Inductor Architecture Allows VIN Above, Below or Equal to VOUT", "Silent Switcher® Architecture for Low EMI", "Up to 95% Efficiency at 2MHz", "Proprietary Peak Current Mode", "3V to 40V Input Voltage Range", "1V to 18V Output Voltage Range", "±1.5% Output Voltage Regulation", "Output/Input Current Regulation and Monitor", "Constant-Voltage/Constant-Current Regulation", "High Side PMOS Load Switch Driver", "No Top MOSFET Refresh Noise in Buck or Boost", "200kHz to 2MHz Fixed Switching Frequency with External Frequency Synchronization and SSFM", "VOUT Disconnected from VIN During Shutdown", "Small 4mm × 6mm 32-Pin LQFN Package", "AEC-Q100 Qualified for Automotive Applications"], "description": "The LT®8350 is a monolithic 4-switch synchronous buck-boost converter with Silent Switcher architecture to minimize EMI emissions while delivering high efficiency at high switching frequency. The switcher can regulate the output voltage, input or output current from input voltages above, below, or equal to the output voltage. The proprietary peak current mode control scheme allows adjustable and synchronizable 200kHz to 2MHz fixed frequency operation, or spread spectrum frequency modulation (SSFM) operation. With 3V to 40V input voltage range, 1V to 18V output voltage capability, and seamless low noise transitions between operation regions, the LT8350 is ideal for voltage regulator, battery and super-capacitor charger applications in automotive, industrial, telecom, and battery powered systems.\nThe LT8350 provides input or output current monitor and power good flag. Robust fault protection is provided to detect output short-circuit condition, during which the LT8350 retries, latches off, or keeps running.", "applications": ["Automotive, Industrial, Telecom Systems", "Voltage Regulator with Accurate Current Limit", "High Frequency Battery-Powered System", "USB-PD Source"], "ordering_information": [{"part_number": "LT8350", "order_device": "LT8350RV#PBF", "package_type": "LQFN", "package_drawing_code": "未找到", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150", "application_grade": "Industry"}, {"part_number": "LT8350", "order_device": "LT8350RV#WPBF", "package_type": "LQFN", "package_drawing_code": "未找到", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150", "application_grade": "Auto"}], "pin_function": [{"product_part_number": "LT8350", "package_type": "LQFN", "pins": [{"pin_number": "2, 3", "pin_name": "VIN", "pin_description": "Input Voltage Pin. The VIN pin supplies the internal circuitry and connects to the power input of the converter. Bypass this pin to ground with a ceramic capacitor. The bypass capacitor should be placed as close to the chip as possible with vias directly down to the ground plane."}, {"pin_number": "5", "pin_name": "EN/UVLO", "pin_description": "Enable and Undervoltage Lockout Pin. Force the pin below 0.3V to shut down the chip and reduce VIN quiescent current below 2µA. Force the pin above 1.235V (typical) for normal operation. The accurate 1.220V falling threshold can be used to program an undervoltage lockout (UVLO) threshold with a resistor divider from VIN to ground. An accurate 2.5µA pull-down current allows the programming of VIN UVLO hysteresis. If neither function is used, tie this pin directly to VIN."}, {"pin_number": "6", "pin_name": "INTVCC", "pin_description": "Internal 3.6V Linear Regulator Output Pin. Powered from the VIN pin, the INTVCC supplies the internal control circuitry and gate drivers. Do not force any voltage on this pin. Place a 1µF bypass capacitor to GND close to the package."}, {"pin_number": "7", "pin_name": "RP", "pin_description": "Factory Test Pin. Always tie this pin to INTVCC."}, {"pin_number": "8", "pin_name": "LOADEN", "pin_description": "Load Switch Enable Input. The LOADEN pin is used to control the ON/OFF of the high side PMOS load switch. If the load switch control is not used, tie this pin to VREF or INTVCC. Forcing the pin low turns off all power switches, disconnects the VC pin from all internal loads, and turns off LOADTG."}, {"pin_number": "9", "pin_name": "VREF", "pin_description": "Voltage Reference Output Pin. The VREF pin provides an accurate 2V reference capable of supplying up to 2mA current. It can be used to supply resistor networks for setting the voltages at the CTRL pin. Place a 220nF bypass capacitor to GND close to the package."}, {"pin_number": "10", "pin_name": "ISMON", "pin_description": "ISP/ISN Current Monitor Output Pin. The ISMON pin generates a buffered voltage that is equal to ten times V(ISP-ISN) plus 0.25V offset voltage. The voltage on the ISMON pin will be 1.25V when V(ISP-ISN) is equal to 100mV full-scale."}, {"pin_number": "11", "pin_name": "ISP", "pin_description": "Positive Terminal of ISP/ISN Current Sense Resistor (RIS). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "12", "pin_name": "ISN", "pin_description": "Negative Terminal of ISP/ISN Current Sense Resistor (RIS). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "13", "pin_name": "CTRL", "pin_description": "Control Input for ISP/ISN Current Sense Threshold. The CTRL pin is used to program the ISP/ISN regulation current. Tie CTRL pin to VREF for the 100mV full-scale threshold. Force the pin below 0.1V to stop switching."}, {"pin_number": "14", "pin_name": "FB", "pin_description": "Voltage Loop Feedback Input. The FB pin is used for constant-voltage regulation and output fault protection. The internal error amplifier with its output VC regulates VFB to 1V through the DC/DC converter."}, {"pin_number": "15", "pin_name": "VC", "pin_description": "Error Amplifier Output to Set Inductor Current Comparator Threshold. The VC pin is used to compensate the control loop with an external RC network."}, {"pin_number": "16", "pin_name": "PGOOD", "pin_description": "Power Good Open Drain Output. The PGOOD pin is pulled low when the FB pin is within ±10% of the final regulation voltage. To function, this pin requires an external pull-up resistor."}, {"pin_number": "17", "pin_name": "SS", "pin_description": "Soft-Start Timer Setting. The SS pin is used to set soft-start timer by connecting a capacitor to ground. An internal 12.5µA pull-up current charging the external SS capacitor gradually ramps up FB regulation voltage."}, {"pin_number": "18", "pin_name": "RT", "pin_description": "Switching Frequency Setting. Connect a resistor from this pin to ground to set the internal oscillator frequency from 200kHz to 2MHz."}, {"pin_number": "19", "pin_name": "SYNC/MODE", "pin_description": "External Switching Frequency Synchronization and Operation Mode Selection. This pin allows five selectable modes for optimization of performance."}, {"pin_number": "20", "pin_name": "CLKOUT", "pin_description": "Clock output. The CLKOUT pin provides a 50% duty cycle square wave with 180 degrees out of phase with the system clock. Float this pin if the CLKOUT function is not used."}, {"pin_number": "21", "pin_name": "EXTVCC", "pin_description": "Second Input Supply for Powering INTVCC. The part intelligently chooses either VIN or EXTVCC for INTVCC LDO to improve efficiency. Tie this pin to GND if not used."}, {"pin_number": "22", "pin_name": "LOADTG", "pin_description": "High Side PMOS Load Switch Top Gate Drive. The LOADTG pin produces a buffered and inverted version of the LOADEN input signal and drives an external high side PMOS load switch. Leave this pin open if not used."}, {"pin_number": "24, 25", "pin_name": "VOUT", "pin_description": "Power Output. The VOUT pins connect to the power output of the converter, and also serve as the positive rail for the LOADTG drive. Bypass this pin to ground with a ceramic capacitor."}, {"pin_number": "27, 28", "pin_name": "SW2", "pin_description": "Boost Side Switch Node. The SW2 pins connect to the internal power switches, and swing from ground to a diode voltage above VOUT."}, {"pin_number": "29", "pin_name": "BST2", "pin_description": "Boost Side Bootstrap Floating Driver Supply. The BST2 pin connects to an integrated bootstrap diode from the INTVCC pin and supplies the boost side top power switch gate driver. Place a 100nF bypass capacitor to SW2 close to the package."}, {"pin_number": "30", "pin_name": "BST1", "pin_description": "Buck Side Bootstrap Floating Driver Supply. The BST1 pin connects to an integrated bootstrap diode from the INTVCC pin and supplies the buck side top power switch gate driver. Place a 100nF bypass capacitor to SW1 close to the package."}, {"pin_number": "31, 32", "pin_name": "SW1", "pin_description": "<PERSON> Side Switch Node. The SW1 pins connect to the internal power switches, and swing from a diode voltage drop below ground up to VIN."}, {"pin_number": "33, 34, 35, 36", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Solder the exposed pads directly to the ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT8350 Datasheet, Rev. B", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "40V", "min_input_voltage": "3V", "max_output_voltage": "18V", "min_output_voltage": "1V", "max_output_current": "6A", "max_switch_frequency": "2MHz", "quiescent_current": "400µA", "high_side_mosfet_resistance": "50mΩ", "low_side_mosfet_resistance": "50mΩ", "over_current_protection_threshold": "6.8A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Pulse Skipping", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "1V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.5", "height": "0.94", "length": "3.6", "width": "0.3", "pin_count": "14", "type": "DESCRIPTION"}]}