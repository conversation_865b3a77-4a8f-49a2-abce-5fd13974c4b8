{"part_number": "LTC3440", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "Micropower Synchronous Buck-Boost DC/DC Converter", "features": ["Single Inductor", "Fixed Frequency Operation with Battery Voltages Above, Below or Equal to the Output", "Synchronous Rectification: Up to 96% Efficiency", "25µA Quiescent Current in Burst Mode® Operation", "Up to 600mA Continuous Output Current", "No Schottky Diodes Required (VOUT < 4.3V)", "VOUT Disconnected from VIN During Shutdown", "2.5V to 5.5V Input and Output Range", "Programmable Oscillator Frequency from 300kHz to 2MHz", "Synchronizable Oscillator", "Burst Mode Enable Control", "<1µA Shutdown Current", "Small Thermally Enhanced 10-Pin MSOP and (3mm × 3mm) DFN Packages"], "description": "The LTC®3440 is a high efficiency, fixed frequency, Buck-Boost DC/DC converter that operates from input voltages above, below or equal to the output voltage. The topology incorporated in the IC provides a continuous transfer function through all operating modes, making the product ideal for single lithium-ion, multicell alkaline or NiMH applications where the output voltage is within the battery voltage range. The device includes two 0.19Ω N-channel MOSFET switches and two 0.22Ω P-channel switches. Switching frequencies up to 2MHz are programmed with an external resistor and the oscillator can be synchronized to an external clock. Quiescent current is only 25µA in Burst Mode operation, maximizing battery life in portable applications. Burst Mode operation is user controlled and can be enabled by driving the MODE/SYNC pin high. If the MODE/SYNC pin has either a clock or is driven low, then fixed frequency switching is enabled. Other features include a 1µA shutdown, soft-start control, thermal shutdown and current limit. The LTC3440 is available in the 10-pin thermally enhanced MSOP and (3mm × 3mm) DFN packages.", "applications": ["Palmtop Computers", "Handheld Instruments", "MP3 Players", "Digital Cameras"], "ordering_information": [{"part_number": "LTC3440", "order_device": "LTC3440EDD#PBF", "package_type": "DFN", "package_drawing_code": "05-08-1699 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3440", "order_device": "LTC3440EDD#TRPBF", "package_type": "DFN", "package_drawing_code": "05-08-1699 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3440", "order_device": "LTC3440EMS#PBF", "package_type": "MSOP", "package_drawing_code": "05-08-1661 Rev F", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3440", "order_device": "LTC3440EMS#TRPBF", "package_type": "MSOP", "package_drawing_code": "05-08-1661 Rev F", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "LTC3440", "package_type": "DFN", "pins": [{"pin_number": "1", "pin_name": "RT", "pin_description": "Timing Resistor to Program the Oscillator Frequency. The programming frequency range is 300kHz to 2MHz."}, {"pin_number": "2", "pin_name": "MODE/SYNC", "pin_description": "MODE/SYNC = External CLK : Synchronization of the internal oscillator. A clock frequency of twice the desired switching frequency and with a pulse width between 100ns and 2µs is applied. The oscillator free running frequency is set slower than the desired synchronized switching frequency to guarantee sync."}, {"pin_number": "3", "pin_name": "SW1", "pin_description": "Switch Pin Where the Internal Switches A and B are Connected. Connect inductor from SW1 to SW2. An optional Schottky diode can be connected from SW1 to ground. Minimize trace length to keep EMI down."}, {"pin_number": "4", "pin_name": "SW2", "pin_description": "Switch Pin Where the Internal Switches C and D are Connected. For applications with output voltages over 4.3V, a Schottky diode is required from SW2 to VOUT to ensure the SW pin does not exhibit excess voltage."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Signal and Power Ground for the IC."}, {"pin_number": "6", "pin_name": "VOUT", "pin_description": "Output of the Synchronous Rectifier. A filter capacitor is placed from VOUT to GND."}, {"pin_number": "7", "pin_name": "VIN", "pin_description": "Input Supply Pin. Internal VCC for the IC. A ceramic bypass capacitor as close to the VIN pin and GND (Pin 5) is required."}, {"pin_number": "8", "pin_name": "SHDN/SS", "pin_description": "Combined Soft-Start and Shutdown. Grounding this pin shuts down the IC. Tie to >1.5V to enable the IC and >2.5V to ensure the error amp is not clamped from soft-start. An RC from the shutdown command signal to this pin will provide a soft-start function by limiting the rise time of the VC pin."}, {"pin_number": "9", "pin_name": "FB", "pin_description": "Feedback Pin. Connect resistor divider tap here. The output voltage can be adjusted from 2.5V to 5.5V. The feedback reference voltage is typically 1.22V."}, {"pin_number": "10", "pin_name": "VC", "pin_description": "Error Amp Output. A frequency compensation network is connected from this pin to the FB pin to compensate the loop."}, {"pin_number": "11", "pin_name": "Exposed Pad", "pin_description": "Ground. This pin must be soldered to the PCB and electrically connected to ground."}]}, {"product_part_number": "LTC3440", "package_type": "MSOP", "pins": [{"pin_number": "1", "pin_name": "RT", "pin_description": "Timing Resistor to Program the Oscillator Frequency. The programming frequency range is 300kHz to 2MHz."}, {"pin_number": "2", "pin_name": "MODE/SYNC", "pin_description": "MODE/SYNC = External CLK : Synchronization of the internal oscillator. A clock frequency of twice the desired switching frequency and with a pulse width between 100ns and 2µs is applied. The oscillator free running frequency is set slower than the desired synchronized switching frequency to guarantee sync."}, {"pin_number": "3", "pin_name": "SW1", "pin_description": "Switch Pin Where the Internal Switches A and B are Connected. Connect inductor from SW1 to SW2. An optional Schottky diode can be connected from SW1 to ground. Minimize trace length to keep EMI down."}, {"pin_number": "4", "pin_name": "SW2", "pin_description": "Switch Pin Where the Internal Switches C and D are Connected. For applications with output voltages over 4.3V, a Schottky diode is required from SW2 to VOUT to ensure the SW pin does not exhibit excess voltage."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Signal and Power Ground for the IC."}, {"pin_number": "6", "pin_name": "VOUT", "pin_description": "Output of the Synchronous Rectifier. A filter capacitor is placed from VOUT to GND."}, {"pin_number": "7", "pin_name": "VIN", "pin_description": "Input Supply Pin. Internal VCC for the IC. A ceramic bypass capacitor as close to the VIN pin and GND (Pin 5) is required."}, {"pin_number": "8", "pin_name": "SHDN/SS", "pin_description": "Combined Soft-Start and Shutdown. Grounding this pin shuts down the IC. Tie to >1.5V to enable the IC and >2.5V to ensure the error amp is not clamped from soft-start. An RC from the shutdown command signal to this pin will provide a soft-start function by limiting the rise time of the VC pin."}, {"pin_number": "9", "pin_name": "FB", "pin_description": "Feedback Pin. Connect resistor divider tap here. The output voltage can be adjusted from 2.5V to 5.5V. The feedback reference voltage is typically 1.22V."}, {"pin_number": "10", "pin_name": "VC", "pin_description": "Error Amp Output. A frequency compensation network is connected from this pin to the FB pin to compensate the loop."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3440 Datasheet Rev D", "family_comparison": "RELATED PARTS\nPART NUMBER | DESCRIPTION | COMMENTS\n---|---|---\nLT1613 | 550mA(ISW), 1.4MHz, High Efficiency Step-Up DC/DC Converter | 90% Efficiency, VIN: 0.9V to 10V, VOUT(MIN) = 34V, IQ = 3mA, ISD = <1μA, ThinSOT™ Package\nLT1618 | 1.5A(ISW), 1.25MHz, High Efficiency Step-Up DC/DC Converter | 90% Efficiency, VIN: 1.6V to 18V, VOUT(MIN) = 35V, IQ = 1.8mA, ISD = <1μA, MS10 Package\nLTC1877 | 600mA(IOUT), 550kHz, Synchronous Step-Down DC/DC Converter | 95% Efficiency, VIN: 2.7V to 10V, VOUT(MIN) = 0.8V, IQ = 10μA, ISD = <1μA, MS8 Package\nLTC1878 | 600mA(IOUT), 550kHz, Synchronous Step-Down DC/DC Converter | 95% Efficiency, VIN: 2.7V to 6V, VOUT(MIN) = 0.8V, IQ = 10μA, ISD = <1μA, MS8 Package\nLTC1879 | 1.2A(IOUT), 550kHz, Synchronous Step-Down DC/DC Converter | 95% Efficiency, VIN: 2.7V to 10V, VOUT(MIN) = 0.8V, IQ = 15μA, ISD = <1μA, TSSOP16 Package\nLT1961 | 1.5A(ISW), 1.25MHz, High Efficiency Step-Up DC/DC Converter | 90% Efficiency, VIN: 3V to 25V, VOUT(MIN) = 35V, IQ = 0.9mA, ISD = 6μA, MS8E Package\nLTC3400/LTC3400B | 600mA(ISW), 1.2MHz, Synchronous Step-Up DC/DC Converter | 92% Efficiency, VIN: 0.85V to 5V, VOUT(MIN) = 5V, IQ = 19μA/300μA, ISD = <1μA, ThinSOT Package\nLTC3401 | 1A(ISW), 3MHz, Synchronous Step-Up DC/DC Converter | 97% Efficiency, VIN: 0.5V to 5V, VOUT(MIN) = 6V, IQ = 38μA, ISD = <1μA, MS10 Package\nLTC3402 | 2A(ISW), 3MHz, Synchronous Step-Up DC/DC Converter | 97% Efficiency, VIN: 0.5V to 5V, VOUT(MIN) = 6V, IQ = 38μA, ISD = <1μA, MS10 Package\nLTC3405/LTC3405A | 300mA(IOUT), 1.5MHz, Synchronous Step-Down DC/DC Converter | 95% Efficiency, VIN: 2.7V to 6V, VOUT(MIN) = 0.8V, IQ = 20μA, ISD = <1μA, ThinSOT Package\nLTC3406/LTC3406B | 600mA(IOUT), 1.5MHz, Synchronous Step-Down DC/DC Converter | 95% Efficiency, VIN: 2.5V to 5.5V, VOUT(MIN) = 0.6V, IQ = 20μA, ISD = <1μA, ThinSOT Package\nLTC3411 | 1.25A(IOUT), 4MHz, Synchronous Step-Down DC/DC Converter | 95% Efficiency, VIN: 2.5V to 5.5V, VOUT(MIN) = 0.8V, IQ = 60μA, ISD = <1μA, MS10 Package\nLTC3412 | 2.5A(IOUT), 4MHz, Synchronous Step-Down DC/DC Converter | 95% Efficiency, VIN: 2.5V to 5.5V, VOUT(MIN) = 0.8V, IQ = 60μA, ISD = <1μA, TSSOP16E Package\nLTC3441/LTC3443 | 1.2A(IOUT), 1MHz/0.6MHz, Micropower Synchronous Buck-Boost DC/DC Converter | 95% Efficiency, VIN: 2.4V to 5.5V, VOUT(MIN): 2.4V to 5.25V, IQ = 25μA, ISD = <1μA, DFN Package", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.5V", "max_output_voltage": "5.5V", "min_output_voltage": "2.5V", "max_output_current": "0.6A", "max_switch_frequency": "2MHz", "quiescent_current": "25µA", "high_side_mosfet_resistance": "220mΩ", "low_side_mosfet_resistance": "190mΩ", "over_current_protection_threshold": "2.7A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Latch", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Fold Back", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1.97%", "output_reference_voltage": "1.22V", "loop_control_mode": "电压模式"}, "package": [{"pitch": "0.5", "height": "0.75", "length": "3", "width": "2.", "type": "DESCRIPTION", "pin_count": "5"}]}