{"part_number": "LM5170-Q1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "多相控制器", "part_number_title": "LM5170-Q1 多相双向电流控制器", "features": ["符合面向汽车应用的 AEC-Q100 标准: - 器件温度等级 1 : -40℃ 至 +125℃ 环境工作温度范围, - 器件 HBM ESD 分类等级 2, - 器件 CDM ESD 分类等级 C4B", "提供功能安全: 可帮助进行功能安全系统设计的文档", "高压 (HV) 端口和低压 (LV) 端口的最高额定电压分别为 100V 和 65V", "1% 精密双向电流调节", "1% 精密通道电流监测", "5A 峰值半桥栅极驱动器", "可编程或自适应死区时间控制", "可选择与外部时钟同步的可编程振荡器频率", "独立通道使能控制输入", "模拟和数字通道电流控制输入", "可编程逐周期峰值电流限制", "HV 和 LV 端口过压保护", "二极管仿真可防止负电流", "可编程软启动计时器", "启动时执行 MOSFET 故障检测以及断路器控制", "多相操作实现增相/减相"], "description": "LM5170-Q1 控制器为汽车类 48V 和 12V 双电池系统的双通道双向转换器提供必要的高电压和精密元器件。该器件可按照 DIR 输入信号指定的方向调节高压和低压端口间的平均电流。电流调节水平可通过模拟或数字 PWM 输入以编程方式设定。双通道差分电流感测传感器和专用通道电流监测计可实现 1% 的典型电流精度。稳定的 5A 半桥栅极驱动器能够驱动功率不低于 500W/通道的并联金属氧化物半导体场效应晶体管 (MOSFET) 开关。同步整流器的二极管仿真模式可避免出现负向电流，但也支持通过非连续操作模式提升轻载效率。通用保护特性包括逐周期电流限制、HV 和 LV 端口过压保护、MOSFET 故障检测和过热保护。", "applications": ["双电池汽车系统", "超级电容或备用电池电源转换器", "可堆叠降压或升压转换器"], "ordering_information": [{"part_number": "LM5170-Q1", "order_device": "LM5170QPHPRQ1", "package_type": "HTQFP", "package_drawing_code": "PHP0048C", "output_voltage": "不适用", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LM5170-Q1", "order_device": "LM5170QPHPRQ1.A", "package_type": "HTQFP", "package_drawing_code": "PHP0048C", "output_voltage": "不适用", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LM5170-Q1", "order_device": "LM5170QPHPTQ1", "package_type": "HTQFP", "package_drawing_code": "PHP0048C", "output_voltage": "不适用", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LM5170-Q1", "order_device": "LM5170QPHPTQ1.A", "package_type": "HTQFP", "package_drawing_code": "PHP0048C", "output_voltage": "不适用", "min_operation_temp": "-40", "max_operation_temp": "150"}], "pin_function": [{"product_part_number": "LM5170-Q1", "package_type": "TQFP", "pins": [{"pin_number": "1", "pin_name": "CSA2", "pin_description": "CH-2 差分电流感应输入。CSA2 引脚连接到 CH-2 功率电感器。CSB2 引脚连接到断路器，如果未使用断路器，则直接连接到 LV 端口。CH-2 电流感应电阻器放置在这两个引脚之间。"}, {"pin_number": "2", "pin_name": "CSB2", "pin_description": "CH-2 差分电流感应输入。CSA2 引脚连接到 CH-2 功率电感器。CSB2 引脚连接到断路器，如果未使用断路器，则直接连接到 LV 端口。CH-2 电流感应电阻器放置在这两个引脚之间。"}, {"pin_number": "3", "pin_name": "NC", "pin_description": "No Connect"}, {"pin_number": "4", "pin_name": "VINX", "pin_description": "通过一个截止开关在内部连接到 VIN 引脚。当控制器关闭时，VINX 与 VIN 断开，从而打开漏电流路径。当控制器启用时，VINX 连接到 VIN，并用作 RAMP1 和 RAMP2 引脚上 RC 斜坡发生器的上拉电源。VINX 还通过一个内部 3-MΩ 电阻器上拉 OVPA 引脚。"}, {"pin_number": "5", "pin_name": "NC", "pin_description": "No Connect"}, {"pin_number": "6", "pin_name": "VIN", "pin_description": "连接到 HV 端口线路电压的输入引脚。它通过一个内部 330-μA 电流源为 BRKG 引脚供电。"}, {"pin_number": "7", "pin_name": "NC", "pin_description": "No Connect"}, {"pin_number": "8", "pin_name": "RAMP2", "pin_description": "CH-2 PWM 比较器的反相输入。连接在 VINX、RAMP2 和 AGND 之间的外部 RC 电路构成了斜坡发生器，可产生与 HV 端口电压成比例的斜坡信号，从而实现电压前馈功能。RAMP2 电容器电压在每个开关周期结束时复位为 AGND。"}, {"pin_number": "9", "pin_name": "OVPA", "pin_description": "连接到 HV 端口过压比较器的同相输入。一个内部 3-MΩ 上拉电阻器和 OVPA 与 AGND 引脚之间的外部电阻器构成一个分压器，用于感应 HV 端口电压。当 OVPA 引脚电压高于 1.185-V 阈值时，SS 电容器会放电并保持低电平，直到过压条件消除。"}, {"pin_number": "10", "pin_name": "ULVO", "pin_description": "UVLO 引脚用作主使能引脚。当 UVLO 被拉至低于 1.25 V 时，整个 LM5170-Q1 处于低静态电流关断模式。当 UVLO 被拉至高于 1.25 V 但低于 2.5 V 时，LM5170-Q1 进入初始化阶段，其中 nFAULT 引脚首先被上拉至 5 V，而 LM5170-Q1 的其余部分保持在 OFF 状态。当 UVLO 被拉至高于 2.5 V 时，LM5170-Q1 进入 MOSFET 故障检测阶段。如果未检测到故障，断路器栅极驱动器 (BRKS 和 BRKG) 开启，LM5170-Q1 启用振荡器和 RAMP 发生器，并待机直到 EN1 和 EN2 命令启用通道。"}, {"pin_number": "11", "pin_name": "COMP2", "pin_description": "CH-2 跨导 (gm) 误差放大器的输出和 CH-2 PWM 比较器的同相输入。必须连接一个环路补偿网络到此引脚。"}, {"pin_number": "12", "pin_name": "SS", "pin_description": "软启动编程引脚。一个外部电容器和一个内部 25-μA 电流源设置软启动期间 COMP 引脚电压的斜坡率。如果在 CH-1 完成软启动后启用 CH-2，则 CH-2 的开启将不受 SS 引脚控制。"}, {"pin_number": "13", "pin_name": "SW2", "pin_description": "CH-2 开关节点。连接到 CH-2 高侧 MOSFET 源极、低侧 MOSFET 漏极和自举电容器返回端。"}, {"pin_number": "14", "pin_name": "HB2", "pin_description": "CH-2 高侧栅极驱动器自举电源输入"}, {"pin_number": "15", "pin_name": "HO2", "pin_description": "CH-2 高侧栅极驱动器输出"}, {"pin_number": "16", "pin_name": "NC", "pin_description": "No Connect"}, {"pin_number": "17", "pin_name": "LO2", "pin_description": "CH-2 低侧栅极驱动器输出"}, {"pin_number": "18", "pin_name": "PGND", "pin_description": "低侧栅极驱动器和外部 VCC 偏置电源的电源地连接引脚"}, {"pin_number": "19", "pin_name": "VCC", "pin_description": "VCC 偏置电源引脚，为驱动器供电。必须在 VCC 和 PGND 引脚之间施加一个 9 V 到 12 V 的外部偏置电源。"}, {"pin_number": "20", "pin_name": "LO1", "pin_description": "CH-1 低侧栅极驱动器输出"}, {"pin_number": "21", "pin_name": "NC", "pin_description": "No Connect"}, {"pin_number": "22", "pin_name": "HO1", "pin_description": "CH-1 高侧栅极驱动器输出"}, {"pin_number": "23", "pin_name": "HB1", "pin_description": "CH-1 高侧栅极驱动器自举电源输入"}, {"pin_number": "24", "pin_name": "SW1", "pin_description": "CH-1 开关节点。连接到 CH-1 高侧 MOSFET 源极、低侧 MOSFET 漏极和自举电容器返回端。"}, {"pin_number": "25", "pin_name": "OVPB", "pin_description": "连接到 LV 端口过压比较器的同相输入。一个内部 1-MΩ 上拉电阻器和 OVPB 与 AGND 引脚之间的外部电阻器构成一个分压器，用于感应 LV 端口电压。当转换器在升压模式下工作时，OVPB 引脚状态被忽略。在降压模式下，当 OVPB 引脚电压高于 1.185-V 阈值时，SS 电容器会放电并保持低电平，直到过压条件消除。"}, {"pin_number": "26", "pin_name": "COMP1", "pin_description": "CH-1 跨导 (gm) 误差放大器的输出和 CH-1 PWM 比较器的同相输入。必须连接一个环路补偿网络到此引脚。"}, {"pin_number": "27", "pin_name": "nFAULT", "pin_description": "故障标志引脚或外部关断引脚。当在启动前检测到 MOSFET 漏源短路故障时，nFAULT 引脚在内部被拉低以报告短路故障，并且 LM5170-Q1 将保持在禁用状态。nFAULT 引脚也可以被外部拉低以关闭 LM5170-Q1，用作强制关断引脚。在强制关断中，所有栅极驱动器关闭，nFAULT 被锁存为低电平，直到 UVLO 引脚被拉至低于 1.25 V 以释放锁存并启动新的启动。"}, {"pin_number": "28", "pin_name": "RAMP1", "pin_description": "CH-1 PWM 比较器的反相输入。连接在 VINX、RAMP1 和 AGND 之间的外部 RC 电路构成了斜坡发生器，可产生与 HV 端口电压成比例的斜坡信号，从而实现电压前馈功能。RAMP1 电容器电压在每个开关周期结束时复位为 AGND。"}, {"pin_number": "29", "pin_name": "OPT", "pin_description": "多相配置引脚。连接到 VCCA 或 AGND，OPT 引脚设置 SYNCOUT 信号的相位延迟，分别对应 4 相或 3 相操作。"}, {"pin_number": "30", "pin_name": "IPK", "pin_description": "连接在 IPK 和 AGND 之间的电阻器为逐周期电流限制比较器设置阈值。"}, {"pin_number": "31", "pin_name": "VCCA", "pin_description": "模拟偏置电源引脚。通过一个外部 25-Ω 电阻器将 VCCA 连接到 VCC。需要一个从 VCCA 引脚到 AGND 的低通滤波器电容器。"}, {"pin_number": "32", "pin_name": "NC", "pin_description": "No Connect"}, {"pin_number": "33", "pin_name": "BRKS", "pin_description": "连接到断路器 MOSFET 对的公共源极。当断路器功能被禁用时，只需通过一个 20-kΩ 电阻器连接到 AGND。"}, {"pin_number": "34", "pin_name": "BRKG", "pin_description": "连接到断路器 MOSFET 对的栅极引脚。一旦 LM5170-Q1 启用，一个内部 330-μA 电流源开始为断路器 MOSFET 栅极充电。BRKG 到 BRKS 的电压在内部被钳位在 12 V。"}, {"pin_number": "35", "pin_name": "CSB1", "pin_description": "CH-1 差分电流感应输入。CSA1 引脚连接到 CH-1 功率电感器。CSB1 引脚连接到断路器，如果未使用断路器，则直接连接到 LV 端口。CH-1 电流感应电阻器放置在这两个电流感应引脚之间。一个内部 1-MΩ 电阻器通过一个内部截止开关连接在 CSB1 和 OVPB 引脚之间。在操作期间，截止开关闭合，这个内部电阻器上拉 OVPB 引脚。在关断模式下，内部电阻器通过截止开关断开。"}, {"pin_number": "36", "pin_name": "CSA1", "pin_description": "CH-1 差分电流感应输入。CSA1 引脚连接到 CH-1 功率电感器。CSB1 引脚连接到断路器，如果未使用断路器，则直接连接到 LV 端口。CH-1 电流感应电阻器放置在这两个电流感应引脚之间。"}, {"pin_number": "37", "pin_name": "IOUT1", "pin_description": "CH-1 电感电流监视器引脚。一个与 CH-1 电感电流成比例的电流源从此引脚流出。从 IOUT1 到 AGND 放置一个终端电阻和滤波电容器，可产生一个代表 CH-1 直流电流水平的直流电压。IOUT1 引脚上的一个内部 25-μA 偏置直流电流源将有效信号提升到地噪声之上，从而提高监视器的噪声抗扰度。"}, {"pin_number": "38", "pin_name": "IOUT2", "pin_description": "CH-2 电感电流监视器引脚。一个与 CH-2 电感电流成比例的电流源从此引脚流出。从 IOUT2 到 AGND 放置一个终端电阻和滤波电容器，可产生一个代表 CH-2 直流电流水平的直流电压。IOUT2 引脚上的一个内部 25-μA 偏置直流电流源将有效信号提升到地噪声之上，从而提高监视器的噪声抗扰度。"}, {"pin_number": "39", "pin_name": "EN1", "pin_description": "CH-1 使能引脚。将 EN1 拉至高于 2.4 V 会关闭 SS 下拉并允许 CH-1 开始软启动序列。将 EN1 拉至低于 1 V 会使 SS 电容器放电并保持低电平。当 SS 放电时，两个通道的高侧和低侧栅极驱动器都保持在低状态。"}, {"pin_number": "40", "pin_name": "SYNCIN", "pin_description": "用于覆盖自由运行内部振荡器的外部时钟输入。当不使用时，SYNCIN 引脚可以悬空或接地。"}, {"pin_number": "41", "pin_name": "SYNCOUT", "pin_description": "时钟输出引脚和故障检查模式选择器。在 3 相或 4 相配置中，SYNCOUT 连接到下游的 LM5170-Q1。它在启动期间也用作断路器选择引脚。从 SYNCOUT 到 AGND 引脚放置一个 10-kΩ 电阻器会禁用故障检查功能。如果 SYNCOUT 到 AGND 之间没有连接电阻器，则启用故障检查。"}, {"pin_number": "42", "pin_name": "ISETD", "pin_description": "PWM 电流编程引脚。电感直流电流水平与 PWM 占空比成比例。使用 ISETA 或 ISETD 进行通道电流编程，但不能同时使用。当不使用 ISETD 时，将 ISETD 短接到 AGND。"}, {"pin_number": "43", "pin_name": "EN2", "pin_description": "CH-2 使能引脚。将 EN2 拉至高于 2.4 V 会启用 CH-2。将 EN2 拉至低于 1 V 会关闭 HO2 和 LO2 驱动器。"}, {"pin_number": "44", "pin_name": "DIR", "pin_description": "方向命令输入。将 DIR 拉至高于 2 V 会将转换器设置为降压模式，命令电流从 HV 端口流向 LV 端口。将 DIR 拉至低于 1 V 会将转换器设置为升压模式，命令电流从 LV 端口流向 HV 端口。如果 DIR 引脚悬空，LM5170-Q1 会检测到无效命令，并禁用两个通道，MOSFET 栅极驱动器处于低状态。"}, {"pin_number": "45", "pin_name": "ISETA", "pin_description": "模拟电流编程引脚。电感直流电流与 ISETA 电压成比例。使用 ISETA 或 ISETD 进行通道电流编程，但不能同时使用。当不使用 ISETA 时，从 ISETA 到 AGND 连接一个 100-pF 到 0.1-μF 的电容器。"}, {"pin_number": "46", "pin_name": "AGND", "pin_description": "模拟地参考。AGND 必须通过单点连接外部连接到 PGND，以提高 LM5170-Q1 的噪声抗扰度。"}, {"pin_number": "47", "pin_name": "OSC", "pin_description": "内部振荡器频率由 OSC 和 AGND 之间的一个电阻器编程。"}, {"pin_number": "48", "pin_name": "DT", "pin_description": "连接在 DT 和 AGND 之间的电阻器设置高侧和低侧驱动器输出之间的死区时间。将 DT 引脚连接到 VCCA 以激活内部自适应死区时间控制。"}]}], "datasheet_cn": "LM5170-Q1_ZHCSFO3D.pdf", "datasheet_en": "SNVSAQ6", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 2, "channel_count": 2, "max_input_voltage": "85V", "min_input_voltage": "6V", "max_output_voltage": "60V", "min_output_voltage": "3V", "max_output_current": "30A", "max_switch_frequency": "0.5MHz", "quiescent_current": "1000μA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "二极管仿真", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "自动重启", "input_under_voltage_protection": "欠压锁定", "output_over_voltage_protection": "自动重启", "output_under_voltage_protection": "无", "output_over_load_protection": "自动重启", "output_short_circuit_protection": "自动重启", "over_temperature_protection": "自动重启", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "不适用", "output_reference_voltage": "不适用", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.5", "height": "1.2", "length": "7.00", "width": "7.00", "type": "OPTION", "pin_count": "6"}]}