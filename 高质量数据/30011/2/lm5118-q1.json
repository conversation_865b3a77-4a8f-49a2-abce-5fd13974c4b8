{"part_number": "LM5118-Q1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "LM5118-Q1 宽电压范围降压/升压控制器", "features": ["符合 AEC-Q100 的汽车应用 标准", "器件温度1级: -40℃ 至 +125℃ 的环境运行温度范围", "器件 HBM ESD 分类等级 2", "器件组件充电模式 (CDM) ESD 分类等级 C6", "3V 至 75V 的超宽输入电压范围", "仿真峰值电流模式控制", "在降压和升压模式之间平滑转换", "开关频率最高可通过编程设定为 500kHz", "振荡器同步功能", "内部高电压偏置稳压器", "集成了高侧和低侧栅极驱动器", "可编程软启动时间", "超低关断电流", "使能输入宽带宽误差放大器", "1.5% 反馈基准精度", "热关断", "封装: 20 引脚 HTSSOP (裸露焊盘)", "结合使用 LM5118-Q1 和 WEBENCH® 电源设计器创建定制设计"], "description": "LM5118-Q1 宽电压范围降压/升压开关稳压控制器具有使用最少外部组件实现高性能且具成本效益的降压/升压稳压器所需的所有功能。当输入电压低于或高于输出电压时，降压/升压拓扑可使输出电压保持稳定，因此，这款器件非常适合汽车应用。当输入电压比调节后的输出电压足够大时，LM5118-Q1 将作为降压稳压器运行，然后随着输入电压接近输出电压逐渐过渡到相应的降压/升压模式。这种双模式方法可在宽输入电压范围内保持稳压，并且在降压模式下提供最佳的转换效率，同时在模式转换期间提供无干扰的输出。该控制器易于使用，其中包含适用于高侧降压 MOSFET 和低侧升压 MOSFET 的驱动器。此稳压器控制方法基于采用仿真电流斜坡的电流模式控制。仿真电流模式控制可降低脉宽调制电路的噪声敏感度，以便可靠地控制高输入电压应用中所需的极小占空比。额外保护功能包括电流限制、热关断和使能输入。该器件采用功耗增强型 20 引脚 HTSSOP 封装，并且配有利于散热的裸露芯片连接焊盘。", "applications": ["车用信息娱乐", "汽车启动和停止系统", "工业降压/升压电源"], "ordering_information": [{"part_number": "LM5118-Q1", "order_device": "LM5118Q1MH/NOPB", "package_type": "HTSSOP", "package_drawing_code": "PWP0020A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5118-Q1", "order_device": "LM5118Q1MHX/NOPB", "package_type": "HTSSOP", "package_drawing_code": "PWP0020A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LM5118-Q1", "package_type": "HTSSOP", "pins": [{"pin_number": "1", "pin_name": "VIN", "pin_description": "输入电源电压。"}, {"pin_number": "2", "pin_name": "UVLO", "pin_description": "如果 UVLO 引脚电压低于 1.23V，稳压器将处于待机模式（VCC 稳压器运行，开关稳压器禁用）。当 UVLO 引脚电压超过 1.23V 时，稳压器进入正常工作模式。可使用外部电阻分压器来设定欠压关断阈值。UVLO 引脚会拉出固定的 5µA 电流。如果电流限制条件持续 256 个连续开关周期，内部开关会将 UVLO 引脚拉至接地然后释放。"}, {"pin_number": "3", "pin_name": "RT", "pin_description": "内部振荡器频率通过此引脚与 AGND 引脚之间的单个电阻器进行设定。建议频率范围为 50kHz 至 500kHz。"}, {"pin_number": "4", "pin_name": "EN", "pin_description": "如果 EN 引脚电压低于 0.5V，稳压器将进入低功耗状态，从 VIN 吸收的电流低于 10µA。EN 必须升至 3V 以上才能正常工作。"}, {"pin_number": "5", "pin_name": "RAMP", "pin_description": "斜坡控制信号。连接在此引脚和 AGND 引脚之间的外部电容器可设定用于仿真电流模式控制的斜坡。"}, {"pin_number": "6", "pin_name": "AGND", "pin_description": "模拟地。"}, {"pin_number": "7", "pin_name": "SS", "pin_description": "软启动。外部电容器和内部 10µA 电流源可设定误差放大器基准的上升时间。当 VCC 低于 VCC 欠压阈值 (< 3.7V)、UVLO 引脚为低电平 (< 1.23V)、EN 为低电平 (< 0.5V) 或热关断激活时，SS 引脚保持低电平。"}, {"pin_number": "8", "pin_name": "FB", "pin_description": "来自稳压输出的反馈信号。连接到内部误差放大器的反相输入端。"}, {"pin_number": "9", "pin_name": "COMP", "pin_description": "内部误差放大器的输出。环路补偿网络应连接在 COMP 和 FB 引脚之间。"}, {"pin_number": "10", "pin_name": "VOUT", "pin_description": "用于仿真电流模式控制的输出电压监视器。将此引脚直接连接到稳压输出。"}, {"pin_number": "11", "pin_name": "SYNC", "pin_description": "用于将开关稳压器与外部时钟同步的同步输入。"}, {"pin_number": "12", "pin_name": "CS", "pin_description": "电流检测输入。连接到电流检测电阻器的二极管侧。"}, {"pin_number": "13", "pin_name": "CSG", "pin_description": "电流检测接地输入。连接到电流检测电阻器的接地侧。"}, {"pin_number": "14", "pin_name": "PGND", "pin_description": "电源地。"}, {"pin_number": "15", "pin_name": "LO", "pin_description": "升压 MOSFET 栅极驱动输出。连接到外部升压 MOSFET 的栅极。"}, {"pin_number": "16", "pin_name": "VCC", "pin_description": "偏置稳压器的输出。使用尽可能靠近控制器的低 ESR/ESL 电容器在本地去耦至 PGND。"}, {"pin_number": "17", "pin_name": "VCCX", "pin_description": "用于外部偏置电源的可选输入。如果 VCCX 引脚上的电压大于 3.9V，则内部 VCC 稳压器被禁用，VCC 引脚在内部连接到 VCCX 引脚电源。如果不使用 VCCX，请连接到 AGND。"}, {"pin_number": "18", "pin_name": "HB", "pin_description": "用于自举操作的高侧栅极驱动器电源。自举电容器为高侧 MOSFET 栅极充电提供电流。此电容器应尽可能靠近控制器放置，并连接在 HB 和 HS 之间。"}, {"pin_number": "19", "pin_name": "HO", "pin_description": "降压 MOSFET 栅极驱动输出。通过短的低电感路径连接到高侧降压 MOSFET 的栅极。"}, {"pin_number": "20", "pin_name": "HS", "pin_description": "降压 MOSFET 源极引脚。连接到高侧降压 MOSFET 的源极端子和自举电容器。"}, {"pin_number": "EP", "pin_name": "EP", "pin_description": "焊接到 IC 下方的接地层以帮助散热。"}]}], "datasheet_cn": "LM5118-Q1_ZHCSGG9.pdf", "datasheet_en": "SNVSAX9", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "75V", "min_input_voltage": "3V", "max_output_voltage": "Adjustable", "min_output_voltage": "1.23V", "max_output_current": "可编程", "max_switch_frequency": "500kHz", "quiescent_current": "4.5mA", "high_side_mosfet_resistance": "不适用(外部)", "low_side_mosfet_resistance": "不适用(外部)", "over_current_protection_threshold": "可编程", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "自动重启", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "1.23V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.65", "height": "1.2", "length": "6.5", "width": "4.4", "type": "HTSSOP", "pin_count": "8"}]}