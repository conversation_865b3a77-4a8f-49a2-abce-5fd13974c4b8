[{"part_number": "LM3668-2833", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "LM3668 1-A, High-Efficiency Dual-Mode Single-Inductor Buck-Boost DC-DC Converter", "features": ["45-μΑ Typical Quiescent Current", "For 2.8-V-3.3-V and 3-V-3.4-V Versions: 1-A Maximum Load Current for VIN = 2.8 V to 5.5 V, 800-mA Maximum Load Current for VIN = 2.7 V, 600-mA Maximum Load Current for VIN = 2.5 V", "For 4.5 V-5 V: 1-A Maximum Load Current for VIN = 3.9 V to 5.5 V, 800-mA Maximum Load Current for VIN = 3.4 V to 3.8 V, 700-mA Maximum Load Current for VIN = 3 V to 3.3 V, 600-mA Maximum Load Current for VIN = 2.7 V to 2.9 V", "2.2-MHz PWM Fixed Switching Frequency (Typical)", "Automatic PFM-PWM Mode or Forced PWM Mode", "Wide Input Voltage Range: 2.5 V to 5.5 V", "Internal Synchronous Rectification for High Efficiency", "Internal Soft Start: 600-µs Maximum Start-Up Time After VIN Settled", "0.01-μΑ Typical Shutdown Current", "Current Overload and Thermal Shutdown Protection", "Frequency Sync Pin: 1.6 MHz to 2.7 MHz"], "description": "The LM3668 is a synchronous buck-boost DC-DC converter optimized for powering low voltage circuits from a Li-lon battery and input voltage rails between 2.5 V and 5.5 V. It has the capability to support up to 1-A output current over the output voltage range. The LM3668 regulates the output voltage over the complete input voltage range by automatically switching between buck or boost modes depending on the input voltage. The LM3668 has 2 N-channel MOSFETS and 2 P-channel MOSFETS arranged in a topology that provides continuous operation through the buck and boost operating modes. There is a MODE pin that allows the user to choose between an intelligent automatic PFM-PWM mode operation and forced PWM operation. During PWM mode, a fixed-frequency 2.2 MHz (typical) is used. PWM mode drives load up to 1 A. Hysteretic PFM mode extends the battery life through reduction of the quiescent current to 45 μΑ (typical) at light loads during system standby. Internal synchronous rectification provides high efficiency. In shutdown mode (EN pin pulled low), the device turns off and reduces battery consumption to 0.01 µA (typical). A high switching frequency of 2.2 MHz (typical) allows the use of tiny surface-mount components including a 2.2-µΗ inductor, a 10-µF input capacitor, and a 22-µF output capacitor.", "applications": ["Handset Peripherals", "MP3 Players", "Pre-Regulation for Linear Regulators", "PDAs", "Portable Hard Disk Drives", "WiMax Modems"], "ordering_information": [{"part_number": "LM3668-2833", "order_device": "LM3668SD-2833/NOPB", "package_type": "WSON", "package_drawing_code": "DQB", "output_voltage": "2.8V/3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LM3668-2833", "order_device": "LM3668SDX-2833/NOPB", "package_type": "WSON", "package_drawing_code": "DQB", "output_voltage": "2.8V/3.3V", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "LM3668", "package_type": "WSON", "pins": [{"pin_number": "1", "pin_name": "VOUT", "pin_description": "Connect to output capacitor."}, {"pin_number": "2", "pin_name": "SW2", "pin_description": "Switching node connection to the internal PFET switch (P2) and NFET synchronous rectifier (N2)."}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power ground."}, {"pin_number": "4", "pin_name": "SW1", "pin_description": "Switching node connection to the internal PFET switch (P1) and NFET synchronous rectifier (N1)."}, {"pin_number": "5", "pin_name": "PVIN", "pin_description": "Supply to the power switch, connect to the input capacitor."}, {"pin_number": "6", "pin_name": "EN", "pin_description": "Enable input. Set this digital input high for normal operation. For shutdown, set low."}, {"pin_number": "7", "pin_name": "VDD", "pin_description": "Signal supply input. If board layout is not optimum an optional 1-µF ceramic capacitor is suggested as close to this pin as possible."}, {"pin_number": "8", "pin_name": "NC", "pin_description": "No connect. Connect this pin to SGND on PCB layout."}, {"pin_number": "9", "pin_name": "SGND", "pin_description": "Analog and Control Ground."}, {"pin_number": "10", "pin_name": "MODE/SYNC", "pin_description": "Mode = LOW, Automatic Mode. Mode= HI, forced PWM Mode. SYNC = external clock synchronization from 1.6 MHz to 2.7 MHz.(When SYNC function is used, device is forced in PWM mode)."}, {"pin_number": "11", "pin_name": "VSEL", "pin_description": "Voltage selection pin; (for example, 2.8-V-3.3-V option) logic input low (or GND) = 2.8 V and logic high = 3.3 V (or VIN) to set output voltage."}, {"pin_number": "12", "pin_name": "FB", "pin_description": "Feedback analog input. Connect to the output at the output filter."}, {"pin_number": "DAP", "pin_name": "DAP", "pin_description": "Die Attach Pad, connect the DAP to SGND on PCB layout to enhance thermal performance. It should not be used as a primary ground connection."}]}], "datasheet_cn": "未找到", "datasheet_en": "LM3668 (Version: SNVS449O, Date: 2015-04-01)", "family_comparison": "Device Comparison: LM3668-2833 (2.8V/3.3V), LM3668-3034 (3V/3.4V), LM3668-4550 (4.5V/5V)", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.5V", "max_output_voltage": "3.3V", "min_output_voltage": "2.8V", "max_output_current": "1A", "max_switch_frequency": "2.7MHz", "quiescent_current": "45μA", "high_side_mosfet_resistance": "130mΩ", "low_side_mosfet_resistance": "100mΩ", "over_current_protection_threshold": "1.85A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "未找到", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "未找到", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "未找到", "output_under_voltage_protection": "未找到", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Fold Back", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "True", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±3%", "output_reference_voltage": "未找到", "loop_control_mode": "Voltage Mode"}, "package": [{"pitch": "0.5", "height": "1.0", "length": "3.0", "width": "3.0", "type": "OPTION", "pin_count": "1"}]}, {"part_number": "LM3668-3034", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "NRND", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "LM3668 1-A, High-Efficiency Dual-Mode Single-Inductor Buck-Boost DC-DC Converter", "features": ["45-μΑ Typical Quiescent Current", "For 2.8-V-3.3-V and 3-V-3.4-V Versions: 1-A Maximum Load Current for VIN = 2.8 V to 5.5 V, 800-mA Maximum Load Current for VIN = 2.7 V, 600-mA Maximum Load Current for VIN = 2.5 V", "For 4.5 V-5 V: 1-A Maximum Load Current for VIN = 3.9 V to 5.5 V, 800-mA Maximum Load Current for VIN = 3.4 V to 3.8 V, 700-mA Maximum Load Current for VIN = 3 V to 3.3 V, 600-mA Maximum Load Current for VIN = 2.7 V to 2.9 V", "2.2-MHz PWM Fixed Switching Frequency (Typical)", "Automatic PFM-PWM Mode or Forced PWM Mode", "Wide Input Voltage Range: 2.5 V to 5.5 V", "Internal Synchronous Rectification for High Efficiency", "Internal Soft Start: 600-µs Maximum Start-Up Time After VIN Settled", "0.01-μΑ Typical Shutdown Current", "Current Overload and Thermal Shutdown Protection", "Frequency Sync Pin: 1.6 MHz to 2.7 MHz"], "description": "The LM3668 is a synchronous buck-boost DC-DC converter optimized for powering low voltage circuits from a Li-lon battery and input voltage rails between 2.5 V and 5.5 V. It has the capability to support up to 1-A output current over the output voltage range. The LM3668 regulates the output voltage over the complete input voltage range by automatically switching between buck or boost modes depending on the input voltage. The LM3668 has 2 N-channel MOSFETS and 2 P-channel MOSFETS arranged in a topology that provides continuous operation through the buck and boost operating modes. There is a MODE pin that allows the user to choose between an intelligent automatic PFM-PWM mode operation and forced PWM operation. During PWM mode, a fixed-frequency 2.2 MHz (typical) is used. PWM mode drives load up to 1 A. Hysteretic PFM mode extends the battery life through reduction of the quiescent current to 45 μΑ (typical) at light loads during system standby. Internal synchronous rectification provides high efficiency. In shutdown mode (EN pin pulled low), the device turns off and reduces battery consumption to 0.01 µA (typical). A high switching frequency of 2.2 MHz (typical) allows the use of tiny surface-mount components including a 2.2-µΗ inductor, a 10-µF input capacitor, and a 22-µF output capacitor.", "applications": ["Handset Peripherals", "MP3 Players", "Pre-Regulation for Linear Regulators", "PDAs", "Portable Hard Disk Drives", "WiMax Modems"], "ordering_information": [{"part_number": "LM3668-3034", "order_device": "LM3668SD-3034/NOPB", "package_type": "WSON", "package_drawing_code": "DQB", "output_voltage": "3.0V/3.4V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LM3668-3034", "order_device": "LM3668SDX-3034/NOPB", "package_type": "WSON", "package_drawing_code": "DQB", "output_voltage": "3.0V/3.4V", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "LM3668", "package_type": "WSON", "pins": [{"pin_number": "1", "pin_name": "VOUT", "pin_description": "Connect to output capacitor."}, {"pin_number": "2", "pin_name": "SW2", "pin_description": "Switching node connection to the internal PFET switch (P2) and NFET synchronous rectifier (N2)."}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power ground."}, {"pin_number": "4", "pin_name": "SW1", "pin_description": "Switching node connection to the internal PFET switch (P1) and NFET synchronous rectifier (N1)."}, {"pin_number": "5", "pin_name": "PVIN", "pin_description": "Supply to the power switch, connect to the input capacitor."}, {"pin_number": "6", "pin_name": "EN", "pin_description": "Enable input. Set this digital input high for normal operation. For shutdown, set low."}, {"pin_number": "7", "pin_name": "VDD", "pin_description": "Signal supply input. If board layout is not optimum an optional 1-µF ceramic capacitor is suggested as close to this pin as possible."}, {"pin_number": "8", "pin_name": "NC", "pin_description": "No connect. Connect this pin to SGND on PCB layout."}, {"pin_number": "9", "pin_name": "SGND", "pin_description": "Analog and Control Ground."}, {"pin_number": "10", "pin_name": "MODE/SYNC", "pin_description": "Mode = LOW, Automatic Mode. Mode= HI, forced PWM Mode. SYNC = external clock synchronization from 1.6 MHz to 2.7 MHz.(When SYNC function is used, device is forced in PWM mode)."}, {"pin_number": "11", "pin_name": "VSEL", "pin_description": "Voltage selection pin; (for example, 2.8-V-3.3-V option) logic input low (or GND) = 2.8 V and logic high = 3.3 V (or VIN) to set output voltage."}, {"pin_number": "12", "pin_name": "FB", "pin_description": "Feedback analog input. Connect to the output at the output filter."}, {"pin_number": "DAP", "pin_name": "DAP", "pin_description": "Die Attach Pad, connect the DAP to SGND on PCB layout to enhance thermal performance. It should not be used as a primary ground connection."}]}], "datasheet_cn": "未找到", "datasheet_en": "LM3668 (Version: SNVS449O, Date: 2015-04-01)", "family_comparison": "Device Comparison: LM3668-2833 (2.8V/3.3V), LM3668-3034 (3V/3.4V), LM3668-4550 (4.5V/5V)", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.5V", "max_output_voltage": "3.4V", "min_output_voltage": "3.0V", "max_output_current": "1A", "max_switch_frequency": "2.7MHz", "quiescent_current": "45μA", "high_side_mosfet_resistance": "130mΩ", "low_side_mosfet_resistance": "100mΩ", "over_current_protection_threshold": "1.85A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "未找到", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "未找到", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "未找到", "output_under_voltage_protection": "未找到", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Fold Back", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "True", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±3%", "output_reference_voltage": "未找到", "loop_control_mode": "Voltage Mode"}, "package": [{"pitch": "0.5", "height": "1.0", "length": "3.0", "width": "3.0", "type": "OPTION", "pin_count": "1"}]}, {"part_number": "LM3668-4550", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "NRND", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "LM3668 1-A, High-Efficiency Dual-Mode Single-Inductor Buck-Boost DC-DC Converter", "features": ["45-μΑ Typical Quiescent Current", "For 2.8-V-3.3-V and 3-V-3.4-V Versions: 1-A Maximum Load Current for VIN = 2.8 V to 5.5 V, 800-mA Maximum Load Current for VIN = 2.7 V, 600-mA Maximum Load Current for VIN = 2.5 V", "For 4.5 V-5 V: 1-A Maximum Load Current for VIN = 3.9 V to 5.5 V, 800-mA Maximum Load Current for VIN = 3.4 V to 3.8 V, 700-mA Maximum Load Current for VIN = 3 V to 3.3 V, 600-mA Maximum Load Current for VIN = 2.7 V to 2.9 V", "2.2-MHz PWM Fixed Switching Frequency (Typical)", "Automatic PFM-PWM Mode or Forced PWM Mode", "Wide Input Voltage Range: 2.5 V to 5.5 V", "Internal Synchronous Rectification for High Efficiency", "Internal Soft Start: 600-µs Maximum Start-Up Time After VIN Settled", "0.01-μΑ Typical Shutdown Current", "Current Overload and Thermal Shutdown Protection", "Frequency Sync Pin: 1.6 MHz to 2.7 MHz"], "description": "The LM3668 is a synchronous buck-boost DC-DC converter optimized for powering low voltage circuits from a Li-lon battery and input voltage rails between 2.5 V and 5.5 V. It has the capability to support up to 1-A output current over the output voltage range. The LM3668 regulates the output voltage over the complete input voltage range by automatically switching between buck or boost modes depending on the input voltage. The LM3668 has 2 N-channel MOSFETS and 2 P-channel MOSFETS arranged in a topology that provides continuous operation through the buck and boost operating modes. There is a MODE pin that allows the user to choose between an intelligent automatic PFM-PWM mode operation and forced PWM operation. During PWM mode, a fixed-frequency 2.2 MHz (typical) is used. PWM mode drives load up to 1 A. Hysteretic PFM mode extends the battery life through reduction of the quiescent current to 45 μΑ (typical) at light loads during system standby. Internal synchronous rectification provides high efficiency. In shutdown mode (EN pin pulled low), the device turns off and reduces battery consumption to 0.01 µA (typical). A high switching frequency of 2.2 MHz (typical) allows the use of tiny surface-mount components including a 2.2-µΗ inductor, a 10-µF input capacitor, and a 22-µF output capacitor.", "applications": ["Handset Peripherals", "MP3 Players", "Pre-Regulation for Linear Regulators", "PDAs", "Portable Hard Disk Drives", "WiMax Modems"], "ordering_information": [{"part_number": "LM3668-4550", "order_device": "LM3668SD-4550/NOPB", "package_type": "WSON", "package_drawing_code": "DQB", "output_voltage": "4.5V/5.0V", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LM3668-4550", "order_device": "LM3668SDX-4550/NOPB", "package_type": "WSON", "package_drawing_code": "DQB", "output_voltage": "4.5V/5.0V", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "LM3668", "package_type": "WSON", "pins": [{"pin_number": "1", "pin_name": "VOUT", "pin_description": "Connect to output capacitor."}, {"pin_number": "2", "pin_name": "SW2", "pin_description": "Switching node connection to the internal PFET switch (P2) and NFET synchronous rectifier (N2)."}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Power ground."}, {"pin_number": "4", "pin_name": "SW1", "pin_description": "Switching node connection to the internal PFET switch (P1) and NFET synchronous rectifier (N1)."}, {"pin_number": "5", "pin_name": "PVIN", "pin_description": "Supply to the power switch, connect to the input capacitor."}, {"pin_number": "6", "pin_name": "EN", "pin_description": "Enable input. Set this digital input high for normal operation. For shutdown, set low."}, {"pin_number": "7", "pin_name": "VDD", "pin_description": "Signal supply input. If board layout is not optimum an optional 1-µF ceramic capacitor is suggested as close to this pin as possible."}, {"pin_number": "8", "pin_name": "NC", "pin_description": "No connect. Connect this pin to SGND on PCB layout."}, {"pin_number": "9", "pin_name": "SGND", "pin_description": "Analog and Control Ground."}, {"pin_number": "10", "pin_name": "MODE/SYNC", "pin_description": "Mode = LOW, Automatic Mode. Mode= HI, forced PWM Mode. SYNC = external clock synchronization from 1.6 MHz to 2.7 MHz.(When SYNC function is used, device is forced in PWM mode)."}, {"pin_number": "11", "pin_name": "VSEL", "pin_description": "Voltage selection pin; (for example, 2.8-V-3.3-V option) logic input low (or GND) = 2.8 V and logic high = 3.3 V (or VIN) to set output voltage."}, {"pin_number": "12", "pin_name": "FB", "pin_description": "Feedback analog input. Connect to the output at the output filter."}, {"pin_number": "DAP", "pin_name": "DAP", "pin_description": "Die Attach Pad, connect the DAP to SGND on PCB layout to enhance thermal performance. It should not be used as a primary ground connection."}]}], "datasheet_cn": "未找到", "datasheet_en": "LM3668 (Version: SNVS449O, Date: 2015-04-01)", "family_comparison": "Device Comparison: LM3668-2833 (2.8V/3.3V), LM3668-3034 (3V/3.4V), LM3668-4550 (4.5V/5V)", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.5V", "max_output_voltage": "5.0V", "min_output_voltage": "4.5V", "max_output_current": "1A", "max_switch_frequency": "2.7MHz", "quiescent_current": "45μA", "high_side_mosfet_resistance": "130mΩ", "low_side_mosfet_resistance": "100mΩ", "over_current_protection_threshold": "1.85A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "未找到", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "未找到", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "未找到", "output_under_voltage_protection": "未找到", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Fold Back", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "True", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±3%", "output_reference_voltage": "未找到", "loop_control_mode": "Voltage Mode"}, "package": [{"pitch": "0.5", "height": "1.0", "length": "3.0", "width": "3.0", "type": "OPTION", "pin_count": "1"}]}]