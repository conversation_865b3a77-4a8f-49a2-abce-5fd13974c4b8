{"part_number": "LTC3335", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "升降压(<PERSON><PERSON><PERSON><PERSON>)芯片", "part_number_title": "Nanopower Buck-Boost DC/DC with Integrated Coulomb Counter", "features": ["680nA Input Quiescent Current (Output in Regulation at No Load)", "1.8V to 5.5V Input Operating Range", "Selectable Output Voltages of 1.8V, 2.5V, 2.8V, 3V, 3.3V, 3.6V, 4.5V, 5V", "Integrated Coulomb Counter Measures Accumulated Battery Discharge", "±5% Battery Discharge Measurement Accuracy", "Programmable Peak Input Current of 5mA, 10mA, 15mA, 25mA, 50mA, 100mA, 150mA, 250mA", "Up to 50mA of Output Current", "Up to 90% Efficiency", "Programmable Coulomb Counter Prescaler for Wide Range of Battery Sizes", "Programmable Discharge Alarm Threshold", "I2C Interface", "Low Profile (0.75mm) 20-Lead (3mm × 4mm) QFN Package"], "description": "The LTC®3335 is a high efficiency, low quiescent current (680nA) buck-boost DC/DC converter with an integrated precision coulomb counter which monitors accumulated battery discharge in long life battery powered applications. The buck-boost can operate down to 1.8V on its input and provides eight pin-selectable output voltages with up to 50mA of output current. The coulomb counter stores the accumulated battery discharge in an internal register accessible via an I2C interface. The LTC3335 features a programmable discharge alarm threshold. When the threshold is reached, an interrupt is generated at the IRQ pin. To accommodate a wide range of battery types and sizes, the peak input current can be selected from as low as 5mA to as high as 250mA and the full-scale coulomb counter has a programmable range of 32,768:1. The LTC3335 is available in a 3mm × 4mm QFN-20 package.", "applications": ["Long Lifetime Primary Cell Battery Applications", "Wireless Sensors", "Remote Monitors", "Dust Networks® SmartMesh® Applications"], "ordering_information": [{"part_number": "LTC3335", "order_device": "LTC3335EUDC#PBF", "package_type": "20-Lead (3mm x 4mm) Plastic QFN", "package_drawing_code": "UDC20", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "LEAD FREE FINISH"}, {"part_number": "LTC3335", "order_device": "LTC3335EUDC#TRPBF", "package_type": "20-Lead (3mm x 4mm) Plastic QFN", "package_drawing_code": "UDC20", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "TAPE AND REEL"}, {"part_number": "LTC3335", "order_device": "LTC3335IUDC#PBF", "package_type": "20-Lead (3mm x 4mm) Plastic QFN", "package_drawing_code": "UDC20", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "LEAD FREE FINISH"}, {"part_number": "LTC3335", "order_device": "LTC3335IUDC#TRPBF", "package_type": "20-Lead (3mm x 4mm) Plastic QFN", "package_drawing_code": "UDC20", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "TAPE AND REEL"}], "pin_function": [{"product_part_number": "LTC3335", "package_type": "QFN-20", "pins": [{"pin_number": "1", "pin_name": "SDA", "pin_description": "Serial Data Input/Output for the I2C Serial Port. The I2C input levels are scaled with respect to DVCC for I2C compliance. Do not float."}, {"pin_number": "2", "pin_name": "DVCC", "pin_description": "Supply Rail for the I2C Serial Bus. DVCC sets the reference level of the SDA and SCL pins for I2C compliance. The external I2C pull-up resistors on SDA and SCL should connect to DVCC. Depending on the particular application, DVCC can be connected to BAT, to VOUT, or to a separate external supply between 1.8V and 5.5V. In most applications DVCC will be connected to the I/O rail of the microprocessor reading the I2C registers."}, {"pin_number": "3, 4, 5", "pin_name": "OUT[2:0]", "pin_description": "VOUT Voltage Select Bits. Tie high to BAT or low to GNDA to select the desired VOUT (see Table 2). Do not float."}, {"pin_number": "6", "pin_name": "GNDD", "pin_description": "Signal ground for internal digital circuits. Connect to GNDA and PGND."}, {"pin_number": "7", "pin_name": "BAT", "pin_description": "Buck-Boost Input Voltage Sense Pin. Connect to PBAT."}, {"pin_number": "8", "pin_name": "PBAT", "pin_description": "Buck-Boost Input Voltage. This pin is the power input of the regulator. Connect to BAT."}, {"pin_number": "9", "pin_name": "SW1", "pin_description": "Buck-Boost Switch Pin. Connected to internal power switches A and B. Connect an inductor (value in Table 8) between this node and SW2."}, {"pin_number": "10", "pin_name": "SW2", "pin_description": "Buck-Boost Switch Pin. Connected to internal power switches C and D. Connect an inductor (value in Table 8) between this node and SW1."}, {"pin_number": "11", "pin_name": "PVOUT", "pin_description": "Buck-Boost Output Voltage. This pin is the power output of the regulator. Connect to VOUT."}, {"pin_number": "12", "pin_name": "VOUT", "pin_description": "Buck-Boost Output Voltage Sense Pin. Connect to PVOUT."}, {"pin_number": "15, 14, 13", "pin_name": "IPK[2:0]", "pin_description": "Peak Input Current Select Bits. Tie high to BAT or low to GNDA to select desired IPEAK (see Table 1). Do not float."}, {"pin_number": "16", "pin_name": "EN", "pin_description": "Buck-Boost Enable Input. Tie high to BAT or low to GNDA to enable/disable the buck-boost. If EN is pulled low, the buck-boost is disabled but internal register contents are saved. Do not float."}, {"pin_number": "17", "pin_name": "GNDA", "pin_description": "Signal ground for internal analog circuits. Connect to GNDD and PGND."}, {"pin_number": "18", "pin_name": "PGOOD", "pin_description": "Power Good Output. Logic level output referenced to DVCC. This output is pulled low after the buck-boost is enabled and remains low until VOUT reaches regulation."}, {"pin_number": "19", "pin_name": "IRQ", "pin_description": "Interrupt Output. Logic level output referenced to DVCC. Active low. This pin is normally logic high but will transition low when the preset alarm level is reached or if there is an overflow in either the coulomb counter or the AC(ON) time measurement."}, {"pin_number": "20", "pin_name": "SCL", "pin_description": "Serial Clock Input for the I2C Serial Port. The I2C input levels are scaled with respect to DVCC for I2C compliance. Do not float."}, {"pin_number": "21", "pin_name": "PGND (Exposed Pad)", "pin_description": "Power Ground. The Exposed Pad connects to the sources of the internal N-channel power MOSFETs. It should be soldered to the PCB and electrically connected to system ground through the shortest and lowest impedance connection possible. Connect to GNDA and GNDD."}]}], "datasheet_cn": "未找到", "datasheet_en": "3335f", "family_comparison": "存在", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5V", "min_output_voltage": "1.8V", "max_output_current": "0.05A", "max_switch_frequency": "未找到", "quiescent_current": "0.68µA", "high_side_mosfet_resistance": "380mΩ", "low_side_mosfet_resistance": "570mΩ", "over_current_protection_threshold": "5mA to 250mA", "operation_mode": "同步", "output_voltage_config_method": "外部引脚/I2C", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "Hysteretic/Sleep Mode", "power_good_indicator": "Yes", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "pass_through_mode": "No", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±3.3%", "output_reference_voltage": "不适用", "loop_control_mode": "迟滞模式控制"}, "package": [{"pitch": "0.5", "height": "0.75", "length": "1.8", "width": "2.", "type": "DESCRIPTION", "pin_count": "16"}]}