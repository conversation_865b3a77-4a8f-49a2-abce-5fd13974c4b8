{"part_number": "LTC3118", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "18V, 2A Buck-Boost DC/DC Converter with Low Loss Dual Input PowerPath", "features": ["Integrated High Efficiency Dual Input PowerPath™ Plus Buck-Boost DC/DC Converter", "Ideal Diode or Priority VIN Select Modes", "VIN1 and VIN2 Range: 2.2V to 18V", "VOUT Range: 2V to 18V", "Either VIN Can Be Above, Below or Equal to VOUT", "Generates 5V at 2A for VIN > 6V", "1.2MHz Low Noise Fixed Frequency Operation", "Current Mode Control", "All Internal N-Channel MOSFETs", "Pin-Selectable PWM or Burst Mode® Operation", "<PERSON><PERSON><PERSON><PERSON>, Independent RUN Pin Thresholds", "Up to 94% Efficiency", "VIN and VOUT Power Good Indicators", "IQ of 50μA in Sleep, 2μA in Shutdown", "4mm × 5mm 24-Lead QFN or 28-Lead TSSOP Packages"], "description": "The LTC3118 is a dual-input, wide voltage range synchronous buck-boost DC/DC converter with an intelligent, integrated, low loss PowerPath control. The unique power switch architecture provides efficient operation from either input source to a programmable output voltage above, below or equal to the input. Voltage capability of up to 18V provides flexibility and voltage margin for a wide variety of applications and power sources.\nThe LTC3118 uses a low noise, current mode architecture with a fixed 1.2MHz PWM mode frequency that minimizes the solution footprint. For high efficiency at light loads, automatic Burst Mode operation can be selected consuming only 50μA of quiescent current in sleep.\nSystem level features include ideal diode or VIN priority modes, VIN and VOUT power good indicators, accurate RUN comparators to program independent UVLO thresholds, and output disconnect in shutdown. Other features include 2μA shutdown current, short-circuit protection, soft-start, current limit and thermal overload protection.\nThe LTC3118 is offered in thermally enhanced 24-lead 4mm × 5mm QFN and 28-lead TSSOP packages.", "applications": ["Systems with Multiple Input Sources", "Back Up Power Systems", "Wall Adapter or Li-Ion(s) Input to 5VOUT", "Battery or Super Capacitor Input for Reserve Power", "Replace Diode-OR Designs with Higher Efficiency, Flexibility and Performance"], "ordering_information": [{"part_number": "LTC3118", "order_device": "LTC3118EUFD#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1696 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3118", "order_device": "LTC3118EUFD#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1696 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3118", "order_device": "LTC3118IUFD#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1696 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3118", "order_device": "LTC3118IUFD#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1696 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3118", "order_device": "LTC3118HUFD#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1696 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3118", "order_device": "LTC3118HUFD#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1696 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3118", "order_device": "LTC3118MPUFD#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1696 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}, {"part_number": "LTC3118", "order_device": "LTC3118MPUFD#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1696 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}, {"part_number": "LTC3118", "order_device": "LTC3118EFE#PBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3118", "order_device": "LTC3118EFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3118", "order_device": "LTC3118IFE#PBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3118", "order_device": "LTC3118IFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3118", "order_device": "LTC3118HFE#PBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3118", "order_device": "LTC3118HFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3118", "order_device": "LTC3118MPFE#PBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}, {"part_number": "LTC3118", "order_device": "LTC3118MPFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}], "pin_function": [{"product_part_number": "LTC3118", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "SEL", "pin_description": "Input Select Pin. SEL = Logic Low (ground): VIN1 priority mode. SEL = Logic High (connect to VCC): Ideal diode mode."}, {"pin_number": "2", "pin_name": "VIN1", "pin_description": "The first input voltage source for the converter. Connect a minimum of 22μF ceramic decoupling capacitor from this pin to ground."}, {"pin_number": "3", "pin_name": "RUN1", "pin_description": "Input to enable and disable the IC and program the UVLO threshold for VIN1. Pull RUN1 above 1.22V to enable the converter."}, {"pin_number": "4", "pin_name": "RUN2", "pin_description": "Input to enable and disable the IC and program the UVLO threshold for VIN2. Pull RUN2 above 1.22V to enable the converter."}, {"pin_number": "5", "pin_name": "VCC", "pin_description": "Output voltage of the internal VCC regulator. Bypass this output with a 4.7μF ceramic capacitor."}, {"pin_number": "6", "pin_name": "MODE", "pin_description": "PWM or Auto Burst Mode Select Pin. MODE = Logic Low (ground): Enables automatic Burst Mode operation. MODE = Logic High (connect to VCC): Forces PWM mode operation."}, {"pin_number": "7", "pin_name": "GND", "pin_description": "Signal Ground for the IC."}, {"pin_number": "8", "pin_name": "VC", "pin_description": "Output of the voltage error amplifier used to program average inductor current."}, {"pin_number": "9", "pin_name": "FB", "pin_description": "Feedback input to the voltage error amplifier. Connect to a resistor divider from VOUT to ground."}, {"pin_number": "10", "pin_name": "V1GD", "pin_description": "Open-drain indicator that pulls to ground when both VIN1 and RUN1 are above their respective thresholds."}, {"pin_number": "11", "pin_name": "V2GD", "pin_description": "Open-drain indicator that pulls to ground when both VIN2 and RUN2 are above their respective thresholds."}, {"pin_number": "12", "pin_name": "PGD", "pin_description": "Open-drain output that pulls to ground when VOUT is greater than 92% of the programmed output voltage."}, {"pin_number": "13", "pin_name": "VOUT", "pin_description": "Regulated Output Voltage. Connect a minimum of 47μF ceramic or low ESR decoupling capacitor from this pin to ground."}, {"pin_number": "14", "pin_name": "SW2", "pin_description": "Switch Pin. Connect to the other side of the inductor."}, {"pin_number": "15", "pin_name": "BST2", "pin_description": "Bootstrapped floating supply for high side N-channel MOSFET gate drive. Connect to SW2 through a 0.1μF capacitor."}, {"pin_number": "16", "pin_name": "BST1", "pin_description": "Bootstrapped floating supply for high side N-channel MOSFET gate drive for VIN1 or VIN2. Connect to SW1 through a 0.1μF capacitor."}, {"pin_number": "17", "pin_name": "SW1", "pin_description": "Switch Pin. Connect to one side of the inductor."}, {"pin_number": "18", "pin_name": "VIN2", "pin_description": "The second input voltage source for the converter. Connect a minimum of 22μF ceramic decoupling capacitor from this pin to ground."}, {"pin_number": "19", "pin_name": "CP2", "pin_description": "Positive pin for the VIN2 top N-channel MOSFET charge-pump capacitor. This pin toggles between VIN2 and VIN2 + VCC when VIN2 is active."}, {"pin_number": "20", "pin_name": "CN2", "pin_description": "Negative pin for the VIN2 top N-channel MOSFET charge-pump capacitor. Connect a 10nF ceramic capacitor between CN2 and CP2."}, {"pin_number": "21", "pin_name": "CM2", "pin_description": "Filter pin for the common connection of VIN2 to SW1 N-channel MOSFETs. Connect a 47nF capacitor from this pin to the ground plane."}, {"pin_number": "22", "pin_name": "CM1", "pin_description": "Filter pin for the common connection of VIN1 to SW1 N-channel MOSFETs. Connect a 47nF capacitor from this pin to the ground plane."}, {"pin_number": "23", "pin_name": "CN1", "pin_description": "Negative pin for the VIN1 top N-channel MOSFET charge-pump capacitor. Connect a 10nF ceramic capacitor between CN1 and CP1."}, {"pin_number": "24", "pin_name": "CP1", "pin_description": "Positive pin for the VIN1 top N-channel MOSFET charge-pump capacitor. This pin toggles between VIN1 and VIN1 + VCC when VIN1 is active."}, {"pin_number": "25", "pin_name": "PGND", "pin_description": "Power Ground for the IC. The exposed pad must be soldered to the PCB ground plane."}]}, {"product_part_number": "LTC3118", "package_type": "TSSOP", "pins": [{"pin_number": "1", "pin_name": "CM1", "pin_description": "Filter pin for the common connection of VIN1 to SW1 N-channel MOSFETs. Connect a 47nF capacitor from this pin to the ground plane."}, {"pin_number": "2", "pin_name": "CN1", "pin_description": "Negative pin for the VIN1 top N-channel MOSFET charge-pump capacitor. Connect a 10nF ceramic capacitor between CN1 and CP1."}, {"pin_number": "3", "pin_name": "CP1", "pin_description": "Positive pin for the VIN1 top N-channel MOSFET charge-pump capacitor. This pin toggles between VIN1 and VIN1 + VCC when VIN1 is active."}, {"pin_number": "4", "pin_name": "SEL", "pin_description": "Input Select Pin. SEL = Logic Low (ground): VIN1 priority mode. SEL = Logic High (connect to VCC): Ideal diode mode."}, {"pin_number": "5", "pin_name": "VIN1", "pin_description": "The first input voltage source for the converter. Connect a minimum of 22μF ceramic decoupling capacitor from this pin to ground."}, {"pin_number": "6", "pin_name": "RUN1", "pin_description": "Input to enable and disable the IC and program the UVLO threshold for VIN1. Pull RUN1 above 1.22V to enable the converter."}, {"pin_number": "7", "pin_name": "RUN2", "pin_description": "Input to enable and disable the IC and program the UVLO threshold for VIN2. Pull RUN2 above 1.22V to enable the converter."}, {"pin_number": "8", "pin_name": "VCC", "pin_description": "Output voltage of the internal VCC regulator. Bypass this output with a 4.7μF ceramic capacitor."}, {"pin_number": "9", "pin_name": "MODE", "pin_description": "PWM or Auto Burst Mode Select Pin. MODE = Logic Low (ground): Enables automatic Burst Mode operation. MODE = Logic High (connect to VCC): Forces PWM mode operation."}, {"pin_number": "10, 11", "pin_name": "GND", "pin_description": "Signal Ground for the IC."}, {"pin_number": "12", "pin_name": "VC", "pin_description": "Output of the voltage error amplifier used to program average inductor current."}, {"pin_number": "13", "pin_name": "FB", "pin_description": "Feedback input to the voltage error amplifier. Connect to a resistor divider from VOUT to ground."}, {"pin_number": "14", "pin_name": "V1GD", "pin_description": "Open-drain indicator that pulls to ground when both VIN1 and RUN1 are above their respective thresholds."}, {"pin_number": "15", "pin_name": "V2GD", "pin_description": "Open-drain indicator that pulls to ground when both VIN2 and RUN2 are above their respective thresholds."}, {"pin_number": "16", "pin_name": "PGD", "pin_description": "Open-drain output that pulls to ground when VOUT is greater than 92% of the programmed output voltage."}, {"pin_number": "17, 18, 26, 29 (Exposed Pad)", "pin_name": "PGND", "pin_description": "Power Ground for the IC. The exposed pad must be soldered to the PCB ground plane."}, {"pin_number": "19", "pin_name": "VOUT", "pin_description": "Regulated Output Voltage. Connect a minimum of 47μF ceramic or low ESR decoupling capacitor from this pin to ground."}, {"pin_number": "20", "pin_name": "SW2", "pin_description": "Switch Pin. Connect to the other side of the inductor."}, {"pin_number": "21", "pin_name": "BST2", "pin_description": "Bootstrapped floating supply for high side N-channel MOSFET gate drive. Connect to SW2 through a 0.1μF capacitor."}, {"pin_number": "22", "pin_name": "BST1", "pin_description": "Bootstrapped floating supply for high side N-channel MOSFET gate drive for VIN1 or VIN2. Connect to SW1 through a 0.1μF capacitor."}, {"pin_number": "23", "pin_name": "SW1", "pin_description": "Switch Pin. Connect to one side of the inductor."}, {"pin_number": "24", "pin_name": "VIN2", "pin_description": "The second input voltage source for the converter. Connect a minimum of 22μF ceramic decoupling capacitor from this pin to ground."}, {"pin_number": "25", "pin_name": "CP2", "pin_description": "Positive pin for the VIN2 top N-channel MOSFET charge-pump capacitor. This pin toggles between VIN2 and VIN2 + VCC when VIN2 is active."}, {"pin_number": "27", "pin_name": "CN2", "pin_description": "Negative pin for the VIN2 top N-channel MOSFET charge-pump capacitor. Connect a 10nF ceramic capacitor between CN2 and CP2."}, {"pin_number": "28", "pin_name": "CM2", "pin_description": "Filter pin for the common connection of VIN2 to SW1 N-channel MOSFETs. Connect a 47nF capacitor from this pin to the ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "3118fa", "family_comparison": "Compares LTC3118 with other buck-boost converters like LTC3111, LTC3112, LTC3115-1, focusing on differences in output current, voltage ranges, and quiescent current.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "2.2V", "max_output_voltage": "18V", "min_output_voltage": "2V", "max_output_current": "2A", "max_switch_frequency": "1.4MHz", "quiescent_current": "50µA", "high_side_mosfet_resistance": "80mΩ", "low_side_mosfet_resistance": "80mΩ", "over_current_protection_threshold": "3.6A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Fold Back", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "1V", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.5", "length": "4.4", "width": "4.4", "type": "QFN", "pin_count": "2", "height": "0.5"}]}