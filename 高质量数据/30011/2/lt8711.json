{"part_number": "LT8711", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "DC-DC控制器", "part_number_title": "Micropower Synchronous Multitopology Controller with 42V Input Capability", "features": ["Easily Configurable as a Synchronous Buck, Boost, SEPIC, ZETA or Nonsynchronous Buck-Boost Converter", "Wide Input Range: 4.5V to 42V (VIN Can Operate to 0V, when EXTVCC > 4.5V)", "Automatic Low Noise Burst Mode® Operation", "Low IQ in Burst Mode Operation (15µA Operating)", "Input Voltage Regulation for High Impedance Source", "100% Duty Cycle in Dropout (Buck Mode)", "2A Gate Drivers (BG and TG)", "Adjustable Soft-Start with One Capacitor", "Frequency Programmable from 100kHz to 750kHz", "Can Be Synchronized to External Clock", "Available in 20-Lead TSSOP and 20-Lead 3mm×4mm QFN Packages"], "description": "The LT®8711 is a multitopology current mode PWM controller that can easily be configured as a synchronous buck, boost, SEPIC, ZETA or as a nonsynchronous buck-boost converter. Its dual gate drive voltage inputs optimize gate driver efficiency. The 15µA no-load quiescent current with the output voltage in regulation extends operating run time in battery powered systems. Low ripple Burst Mode operation enables high efficiency at very light loads while maintaining low output voltage ripple. The LT8711’s fixed switching frequency can be set from 100kHz to 750kHz or can be synchronized to an external clock. The additional features include 100% duty cycle capability when in buck mode, a topology selection pin and adjustable soft-start. LT8711 is available in the 20-lead TSSOP and 20-lead 3mm × 4mm QFN packages.", "applications": ["General Purpose DC/DC Conversion", "Automotive Systems", "Industrial Supplies", "Solar Panel Power Converter"], "ordering_information": [{"part_number": "LT8711", "order_device": "LT8711EFE#PBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8711", "order_device": "LT8711EFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8711", "order_device": "LT8711IFE#PBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8711", "order_device": "LT8711IFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8711", "order_device": "LT8711EUDC#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1742 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8711", "order_device": "LT8711EUDC#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1742 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8711", "order_device": "LT8711IUDC#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1742 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8711", "order_device": "LT8711IUDC#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1742 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LT8711", "package_type": "TSSOP", "pins": [{"pin_number": "1", "pin_name": "EN/FBIN", "pin_description": "Enable and Input Voltage Regulation Pin. Used to enable/disable the chip, restart soft-start, and limit input current. Drive below 0.2V to disable, above 1.03V to activate."}, {"pin_number": "2", "pin_name": "FB", "pin_description": "Feedback Input Pin. The LT8711 regulates the FB pin to 0.8V. Connect the feedback resistor divider tap to this pin."}, {"pin_number": "3", "pin_name": "Vc", "pin_description": "Error Amplifier Output Pin. Tie external compensation network to this pin."}, {"pin_number": "4", "pin_name": "SS", "pin_description": "Soft Start Pin. Place a soft-start capacitor here. Charged by a 410k resistor to about 4.3V upon start-up."}, {"pin_number": "5", "pin_name": "OPMODE", "pin_description": "Topology Selection Pin. Tie to GND for buck/ZETA, to INTVCC for SEPIC/boost, or to a 100pF capacitor to GND for nonsynchronous buck-boost mode."}, {"pin_number": "6", "pin_name": "ISP", "pin_description": "Current Sense Positive Input Pin. <PERSON><PERSON> connect to a sense resistor."}, {"pin_number": "7", "pin_name": "ISN", "pin_description": "Current Sense Negative Input Pin. <PERSON><PERSON> connect to a sense resistor."}, {"pin_number": "8", "pin_name": "INTVEE", "pin_description": "5V Below BIAS LDO Regulator Pin. Sets the bottom rail for the TG gate driver. Must be locally bypassed with a minimum 2.2µF capacitor to BIAS."}, {"pin_number": "9", "pin_name": "BIAS", "pin_description": "Power Supply for the TG PFET Driver. Sets the top rail for the TG gate driver. Must be locally bypassed with a minimum 2.2µF capacitor to INTVEE."}, {"pin_number": "10", "pin_name": "TG", "pin_description": "PFET Gate Drive Pin. Low and high levels are INTVEE and BIAS respectively with a 2A drive capability."}, {"pin_number": "11", "pin_name": "BG", "pin_description": "NFET Gate Drive Pin. Low and high levels are GND and INTVCC respectively with a 2A drive capability."}, {"pin_number": "12", "pin_name": "NC", "pin_description": "No Connection. Do not connect. Must be floated."}, {"pin_number": "13", "pin_name": "INTVCC", "pin_description": "5V Dual Input LDO Regulator Pin. Must be locally bypassed with a minimum 2.2µF to GND. Powers BG gate driver."}, {"pin_number": "14", "pin_name": "VIN", "pin_description": "Input Supply Pin. Must be locally bypassed. Can run down to 0V as long as EXTVCC > 4.5V."}, {"pin_number": "15", "pin_name": "EXTVCC", "pin_description": "Alternate Input Supply Pin. Must be locally bypassed. Can run down to 0V as long as VIN > 4.5V."}, {"pin_number": "16", "pin_name": "CSN", "pin_description": "Current Sense Negative Input Pin. <PERSON><PERSON> connect to a sense resistor to limit the input current."}, {"pin_number": "17", "pin_name": "CSP", "pin_description": "Current Sense Positive Input Pin. <PERSON><PERSON> connect to a sense resistor to limit the input current."}, {"pin_number": "18", "pin_name": "NC", "pin_description": "No Connection. Do not connect. Must be floated."}, {"pin_number": "19", "pin_name": "SYNC", "pin_description": "Synchronization Pin. Drive with an external clock to synchronize frequency, or drive low to use internal oscillator."}, {"pin_number": "20", "pin_name": "RT", "pin_description": "Timing Resistor <PERSON><PERSON>. Adjusts the switching frequency with a resistor to ground."}, {"pin_number": "21", "pin_name": "GND", "pin_description": "Ground. Must be soldered directly to the local ground plane."}]}, {"product_part_number": "LT8711", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "Vc", "pin_description": "Error Amplifier Output Pin. Tie external compensation network to this pin."}, {"pin_number": "2", "pin_name": "SS", "pin_description": "Soft Start Pin. Place a soft-start capacitor here. Charged by a 410k resistor to about 4.3V upon start-up."}, {"pin_number": "3", "pin_name": "OPMODE", "pin_description": "Topology Selection Pin. Tie to GND for buck/ZETA, to INTVCC for SEPIC/boost, or to a 100pF capacitor to GND for nonsynchronous buck-boost mode."}, {"pin_number": "4", "pin_name": "ISP", "pin_description": "Current Sense Positive Input Pin. <PERSON><PERSON> connect to a sense resistor."}, {"pin_number": "5", "pin_name": "ISN", "pin_description": "Current Sense Negative Input Pin. <PERSON><PERSON> connect to a sense resistor."}, {"pin_number": "6", "pin_name": "INTVEE", "pin_description": "5V Below BIAS LDO Regulator Pin. Sets the bottom rail for the TG gate driver. Must be locally bypassed with a minimum 2.2µF capacitor to BIAS."}, {"pin_number": "7", "pin_name": "BIAS", "pin_description": "Power Supply for the TG PFET Driver. Sets the top rail for the TG gate driver. Must be locally bypassed with a minimum 2.2µF capacitor to INTVEE."}, {"pin_number": "8", "pin_name": "TG", "pin_description": "PFET Gate Drive Pin. Low and high levels are INTVEE and BIAS respectively with a 2A drive capability."}, {"pin_number": "9", "pin_name": "NC", "pin_description": "No Connection. Do not connect. Must be floated."}, {"pin_number": "10", "pin_name": "BG", "pin_description": "NFET Gate Drive Pin. Low and high levels are GND and INTVCC respectively with a 2A drive capability."}, {"pin_number": "11", "pin_name": "NC", "pin_description": "No Connection. Do not connect. Must be floated."}, {"pin_number": "12", "pin_name": "INTVCC", "pin_description": "5V Dual Input LDO Regulator Pin. Must be locally bypassed with a minimum 2.2µF to GND. Powers BG gate driver."}, {"pin_number": "13", "pin_name": "VIN", "pin_description": "Input Supply Pin. Must be locally bypassed. Can run down to 0V as long as EXTVCC > 4.5V."}, {"pin_number": "14", "pin_name": "EXTVCC", "pin_description": "Alternate Input Supply Pin. Must be locally bypassed. Can run down to 0V as long as VIN > 4.5V."}, {"pin_number": "15", "pin_name": "CSN", "pin_description": "Current Sense Negative Input Pin. <PERSON><PERSON> connect to a sense resistor to limit the input current."}, {"pin_number": "16", "pin_name": "CSP", "pin_description": "Current Sense Positive Input Pin. <PERSON><PERSON> connect to a sense resistor to limit the input current."}, {"pin_number": "17", "pin_name": "EN/FBIN", "pin_description": "Enable and Input Voltage Regulation Pin. Used to enable/disable the chip, restart soft-start, and limit input current. Drive below 0.2V to disable, above 1.03V to activate."}, {"pin_number": "18", "pin_name": "SYNC", "pin_description": "Synchronization Pin. Drive with an external clock to synchronize frequency, or drive low to use internal oscillator."}, {"pin_number": "19", "pin_name": "FB", "pin_description": "Feedback Input Pin. The LT8711 regulates the FB pin to 0.8V. Connect the feedback resistor divider tap to this pin."}, {"pin_number": "20", "pin_name": "RT", "pin_description": "Timing Resistor <PERSON><PERSON>. Adjusts the switching frequency with a resistor to ground."}, {"pin_number": "21", "pin_name": "GND", "pin_description": "Ground. Must be soldered directly to the local ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT8711.pdf", "family_comparison": "{\"family_name\":\"Related Parts\",\"comparison_table\":[{\"PART NUMBER\":\"LT3757A\",\"DESCRIPTION\":\"Boost, Flyback, SEPIC and Inverting Controller\",\"COMMENTS\":\"2.9V ≤ VIN ≤ 40V, 100kHz to 1MHz Programmable Operating Frequency, 3mm × 3mm DFN-10 and MSOP-10E\"},{\"PART NUMBER\":\"LT3758A\",\"DESCRIPTION\":\"Boost, Flyback, SEPIC and Inverting Controller\",\"COMMENTS\":\"5.5V ≤ VIN ≤ 100V, 100kHz to 1MHz Programmable Operating Frequency, 3mm × 3mm DFN-10 and MSOP-10E\"},{\"PART NUMBER\":\"LT3957A\",\"DESCRIPTION\":\"Boost, Flyback, SEPIC and Inverting Converter with 5A, 40V Switch\",\"COMMENTS\":\"3V ≤ VIN ≤ 40V, 100kHz to 1MHz Programmable Operating Frequency, 5mm × 6mm QFN\"},{\"PART NUMBER\":\"LT3958\",\"DESCRIPTION\":\"Boost, Flyback, SEPIC and Inverting Converter with 3.3A, 84V Switch\",\"COMMENTS\":\"5V ≤ VIN ≤ 80V, 100kHz to 1MHz Programmable Operating Frequency, 5mm × 6mm QFN\"},{\"PART NUMBER\":\"LT8705A\",\"DESCRIPTION\":\"80V VIN and VOUT Synchronous 4-Switch Buck-Boost DC/DC Controller\",\"COMMENTS\":\"2.8V ≤ VIN ≤ 80V, 100kHz to 400kHz Programmable Operating Frequency, 5mm × 7mm QFN-38 and TSSOP-38\"},{\"PART NUMBER\":\"LT8709\",\"DESCRIPTION\":\"Negative Input Synchronous Multitopology DC/DC Control\",\"COMMENTS\":\"–80V ≤ VIN ≤ –4.5V, Up to 400kHz Programmable Operating Frequency, TSSOP-20\"},{\"PART NUMBER\":\"LT8710\",\"DESCRIPTION\":\"Synchronous SEPIC/Inverting/Boost Controller with Output Current Control\",\"COMMENTS\":\"4.5V ≤ VIN ≤ 80V, 100kHz to 1MHz Programmable Operating Frequency, TSSOP-20\"},{\"PART NUMBER\":\"LT8714\",\"DESCRIPTION\":\"Bipolar Output Synchronous Controller with Seamless Four Quadrant Operation\",\"COMMENTS\":\"4.5V ≤ VIN ≤ 80V, Output Can Source or Sink Current for Any Output Voltage, Switching Frequency Up to 750kHz, 20-Lead TSSOP\"}]}", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "42V", "min_input_voltage": "4.5V", "max_output_voltage": "10V", "min_output_voltage": "1V", "max_output_current": "Depends on external components", "max_switch_frequency": "750kHz", "quiescent_current": "15µA", "high_side_mosfet_resistance": "不适用(外部MOSFET)", "low_side_mosfet_resistance": "不适用(外部MOSFET)", "over_current_protection_threshold": "Adjustable", "operation_mode": "同步/非同步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "True", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±0.625%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.65", "height": "1.2", "length": "6.5", "width": "4.4", "type": "DESCRIPTION", "pin_count": "20"}]}