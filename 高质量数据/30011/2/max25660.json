[{"part_number": "MAX25660AFFA", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "Automotive Low-Noise Synchronous Buck-Boost Converter", "features": ["Meets Stringent Automotive Quality and Reliability Requirements", "3.6V to 36V Operating Input Voltage Range", "Allows Operation in Cold-Crank Conditions", "Tolerates Input Transients up to 42V", "Low Audible Noise for 6A Load Transients", "9A or 10.8A Typical Input Current Limit", "Loop Bandwidth Adjustment for Fast Transients", "-40°C to +125°C Grade 1 Automotive Temperature Range", "AEC-Q100 Qualified High Integration and Thermally Enhanced Package Reduces BOM Cost and Board Space", "2.1MHz/400kHz Switching Frequency Options", "<PERSON><PERSON><PERSON> Enhanced, 22-Pin FC2QFN Package", "5μA Maximum Shutdown Current", "Protection Features Improve System Reliability", "Supply Undervoltage Lockout and Thermal Protection"], "description": "The MAX25660 is a small, synchronous, buck-boost converter with integrated H-bridge switches. This IC provides a fixed-output regulation voltage and an externally adjustable output voltage in the 2.75V to 14V range with an input voltage above, below, or equal to the output regulation voltage. The MAX25660AFFA has a 9A (typ) input current limit, while the MAX25660AFFB has a 10.8A (typ) input current limit. Both versions can support continuous load currents up to 6A, which depends on the input-to-output voltage ratio and operating frequency. It also has a wide input voltage range of 3.6V to 36V. The MAX25660 features automatic loop bandwidth adjustment that automatically changes the loop bandwidth, which depends on the operating mode. This allows for a very fast transient response time when in Buck mode, which reduces audible noise caused by large changes in the load. The MAX25660 has two switching frequency options: 2.1MHz and 400kHz. The 2.1MHz high switching frequency allows for small external components and reduced output ripple, and guarantees no AM band interference, while the 400kHz switching frequency offers better efficiency and relieves the power consumption concern. The IC also includes spread-spectrum frequency modulation to minimize electromagnetic interference (EMI). The MAX25660 features a power-good (PGOOD) indicator, undervoltage lockout, overvoltage protection, cycle-by-cycle current limit, and thermal shutdown. The IC is available in a small, 4.25mm x 4.25mm x 0.75mm, 22-pin FC2QFN package.", "applications": ["Display Applications", "Tail-Light and Front-Light Applications", "Point-of-Load Power Supplies"], "ordering_information": [{"part_number": "MAX25660AFFA", "order_device": "MAX25660AFFA", "package_type": "FC2QFN", "pin_count": "22", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "Adjustable"}], "pin_function": [], "datasheet_cn": "未找到", "datasheet_en": "19-101859; Rev 2; 2/25", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "3.6V", "max_output_voltage": "14V", "min_output_voltage": "2.75V", "max_output_current": "6A", "max_switch_frequency": "2.1MHz", "quiescent_current": "5μA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "9A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "Yes", "soft_start": "无", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "未找到", "output_reference_voltage": "未找到", "loop_control_mode": "未找到"}, "package": [{"pitch": "0.75", "height": "2.1", "length": "4.25", "width": "10.8", "type": "Reduces", "pin_count": "22"}]}, {"part_number": "MAX25660AFFB", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "Automotive Low-Noise Synchronous Buck-Boost Converter", "features": ["Meets Stringent Automotive Quality and Reliability Requirements", "3.6V to 36V Operating Input Voltage Range", "Allows Operation in Cold-Crank Conditions", "Tolerates Input Transients up to 42V", "Low Audible Noise for 6A Load Transients", "9A or 10.8A Typical Input Current Limit", "Loop Bandwidth Adjustment for Fast Transients", "-40°C to +125°C Grade 1 Automotive Temperature Range", "AEC-Q100 Qualified High Integration and Thermally Enhanced Package Reduces BOM Cost and Board Space", "2.1MHz/400kHz Switching Frequency Options", "<PERSON><PERSON><PERSON> Enhanced, 22-Pin FC2QFN Package", "5μA Maximum Shutdown Current", "Protection Features Improve System Reliability", "Supply Undervoltage Lockout and Thermal Protection"], "description": "The MAX25660 is a small, synchronous, buck-boost converter with integrated H-bridge switches. This IC provides a fixed-output regulation voltage and an externally adjustable output voltage in the 2.75V to 14V range with an input voltage above, below, or equal to the output regulation voltage. The MAX25660AFFA has a 9A (typ) input current limit, while the MAX25660AFFB has a 10.8A (typ) input current limit. Both versions can support continuous load currents up to 6A, which depends on the input-to-output voltage ratio and operating frequency. It also has a wide input voltage range of 3.6V to 36V. The MAX25660 features automatic loop bandwidth adjustment that automatically changes the loop bandwidth, which depends on the operating mode. This allows for a very fast transient response time when in Buck mode, which reduces audible noise caused by large changes in the load. The MAX25660 has two switching frequency options: 2.1MHz and 400kHz. The 2.1MHz high switching frequency allows for small external components and reduced output ripple, and guarantees no AM band interference, while the 400kHz switching frequency offers better efficiency and relieves the power consumption concern. The IC also includes spread-spectrum frequency modulation to minimize electromagnetic interference (EMI). The MAX25660 features a power-good (PGOOD) indicator, undervoltage lockout, overvoltage protection, cycle-by-cycle current limit, and thermal shutdown. The IC is available in a small, 4.25mm x 4.25mm x 0.75mm, 22-pin FC2QFN package.", "applications": ["Display Applications", "Tail-Light and Front-Light Applications", "Point-of-Load Power Supplies"], "ordering_information": [{"part_number": "MAX25660AFFB", "order_device": "MAX25660AFFB", "package_type": "FC2QFN", "pin_count": "22", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "Adjustable"}], "pin_function": [], "datasheet_cn": "未找到", "datasheet_en": "19-101859; Rev 2; 2/25", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "3.6V", "max_output_voltage": "14V", "min_output_voltage": "2.75V", "max_output_current": "6A", "max_switch_frequency": "2.1MHz", "quiescent_current": "5μA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "10.8A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "Yes", "soft_start": "无", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "未找到", "output_reference_voltage": "未找到", "loop_control_mode": "未找到"}, "package": [{"pitch": "0.75", "height": "2.1", "length": "4.25", "width": "10.8", "type": "Reduces", "pin_count": "22"}]}]