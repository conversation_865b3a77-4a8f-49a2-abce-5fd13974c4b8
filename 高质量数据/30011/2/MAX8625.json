{"part_number": "MAX8625A", "manufacturer": "Maxim Integrated", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "高效、无缝切换 升/降压型DC-DC转换器", "features": ["4个内部MOSFET实现真正的H桥升/降压转换", "升、降压之间无扰动切换", "切换过程中输出纹波变化最小", "效率高达92%", "跳脉冲模式下, 静态电流只有37µA (典型值)", "输入范围: 2.5V至5.5V", "固定3.3V输出或输出可调", "1µA (最大值)逻辑控制关断模式", "真关断", "输出过载保护", "内部补偿", "内部软启动", "1MHz开关频率", "热过载保护", "小尺寸3mm x 3mm、14引脚TDFN封装"], "description": "MAX8625A PWM升/降压调节器针对便携式电池供电设备中的数字逻辑电路、硬盘驱动、电机及其它负载供电而设计，适用于PDA、蜂窝电话、数码相机(DSC)、MP3播放器等产品。MAX8625A在2.5V至5.5V输入范围内可提供固定3.3V或可调(1.25V至4V)输出，电流高达0.8A。MAX8625A采用2A峰值限流。Maxim专有的H桥技术在所有工作模式下可提供无缝切换，消除了其它器件中存在的尖峰干扰。4个内部MOSFET (2个开关和2个同步整流器)提供内部补偿，大大减少了外部元件数。SKIP输入用于选择低噪声、固定频率PWM模式或高效跳脉冲模式。如果选择跳脉冲模式，轻载时转换器将自动切换至PFM模式，进一步提高轻载效率。内部振荡器工作在1MHz，允许使用小尺寸外部电感和电容。MAX8625A的限流电路在输出过载时关断器件。此外，软启动功能有助于抑制启动过程的浪涌电流。该器件还具有真正的关断(True Shutdown™)功能，器件禁用时，断开输入与输出的连接。MAX8625A采用3mm x 3mm、14引脚TDFN封装。", "applications": ["PDA与智能电话", "DSC和便携式摄像机", "MP3播放器和蜂窝电话", "电池供电的硬盘驱动器(HDD)"], "ordering_information": [{"part_number": "MAX8625A", "order_device": "MAX8625AETD+", "package_type": "TDFN-EP", "package_drawing_code": "21-0137", "output_voltage": "Fixed/Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX8625A", "package_type": "14 TDFN-EP", "pins": [{"pin_number": "1, 2", "pin_name": "LX1", "pin_description": "电感连接1。在LX1和LX2引脚之间连接电感, 两个LX1引脚必须在外部连接在一起。关断期间, LX1在内部连接至GND。"}, {"pin_number": "3, 4", "pin_name": "LX2", "pin_description": "电感连接2。在LX1和LX2引脚之间连接电感, 两个LX2引脚必须在外部连接在一起。在关断期间, LX2在内部连接至GND。"}, {"pin_number": "5", "pin_name": "ON", "pin_description": "使能输入。将ON引脚连接至输入, 或者将其驱动为逻辑高电平以使能IC。将ON驱动为逻辑低电平则禁用IC。"}, {"pin_number": "6", "pin_name": "SKIP", "pin_description": "模式选择输入。将SKIP连接至GND时选择跳脉冲模式, 该模式提供了最佳的总体效率。将SKIP连接至IN时选择强制PWM模式。该模式具有最低噪声, 但与跳脉冲模式相比, 降低了轻载效率。"}, {"pin_number": "7", "pin_name": "FB", "pin_description": "反馈输入。接地时将输出电压设置为固定3.3V。将FB连接到输出与GND之间的外部分压电阻的中心抽头, 可将输出电压设置为不同值。VFB稳定在1.25V。"}, {"pin_number": "8", "pin_name": "REF", "pin_description": "基准输出。利用一个0.1µF的陶瓷电容将REF引脚旁路至GND。VREF为1.25V, 关断期间由内部下拉至GND。"}, {"pin_number": "9, 10", "pin_name": "OUT", "pin_description": "电源输出。利用两个22µF的陶瓷电容将OUT引脚旁路至GND。两个OUT引脚必须在外部连接在一起。"}, {"pin_number": "11, 12", "pin_name": "GND", "pin_description": "地。在IC下方直接将裸焊盘和GND连接在一起。"}, {"pin_number": "13, 14", "pin_name": "IN", "pin_description": "电源输入。利用两个22µF的陶瓷电容将IN旁路至GND。将IN连接至2.5V至5.5V电源。两个IN引脚必须在外部连接在一起。"}, {"pin_number": "EP", "pin_name": "EP", "pin_description": "裸焊盘。在IC下方直接将裸焊盘和GND连接在一起。连接到一个大的接地面, 以改善散热。"}]}], "datasheet_cn": "MAX8625A Rev 4 4/09", "datasheet_en": "未找到", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.5V", "max_output_voltage": "4V", "min_output_voltage": "1.25V", "max_output_current": "0.8A", "max_switch_frequency": "1.15MHz", "quiescent_current": "37µA", "high_side_mosfet_resistance": "50mΩ", "low_side_mosfet_resistance": "50mΩ", "over_current_protection_threshold": "2A", "operation_mode": "同步", "output_voltage_config_method": "Fixed/Adjustable", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "1.25V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.8", "length": "3", "width": "3", "type": "TDFN-EP", "pin_count": "14"}]}