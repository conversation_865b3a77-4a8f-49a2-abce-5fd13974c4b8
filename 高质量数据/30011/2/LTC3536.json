{"part_number": "LTC3536", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "LTC3536 1A Low Noise, Buck-Boost DC/DC Converter", "features": ["Regulated Output with Input Voltage Above, Below or Equal to the Output Voltage", "1.8V to 5.5V Input and Output Voltage Range", "1A Continuous Output Current for VIN ≥ 3V, VOUT = 3.3V", "±1% Output Voltage Accuracy", "Low Noise Buck-Boost Architecture", "Up to 95% Efficiency", "Programmable Frequency from 300kHz to 2MHz", "Synchronizable Oscillator", "Burst Mode® Operation: 32µA IQ", "Internal 1ms Soft-Start", "Output Disconnect in Shutdown", "Shutdown Current: 1µA", "Short-Circuit Protection", "Small Thermally Enhanced 12-Pin MSOP and 10-Pin (3mm × 3mm) DFN Packages"], "description": "The LTC®3536 is an extended VIN range, fixed frequency, synchronous buck-boost DC/DC converter that operates from input voltages above, below or equal to the regulated output voltage. The topology incorporated in the LTC3536 provides low noise operation, making it ideal for RF and precision measurement applications. The device can produce up to 1A of continuous output current, and it includes two N-channel and two P-channel MOSFET switches. Switching frequencies up to 2MHz can be programmed with an external resistor and the oscillator can be synchronized to an external clock. Quiescent current is only 32µA in Burst Mode operation, maximizing battery life in portable applications. Burst Mode operation is user controlled and improves efficiency at light loads. Other features include a 1µA shutdown current, internal soft-start, overtemperature protection and current limit. The LTC3536 is available in 12-pin thermally enhanced MSOP and 10-pin (3mm × 3mm) DFN packages.", "applications": ["Wireless Inventory Terminals", "Handheld Medical Instruments", "Wireless Locators, Microphones", "Supercapacitor Backup Power Supply"], "ordering_information": [{"part_number": "LTC3536", "order_device": "LTC3536EDD#PBF", "package_type": "DFN", "package_drawing_code": "05-08-1699 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "LEAD FREE FINISH"}, {"part_number": "LTC3536", "order_device": "LTC3536EDD#TRPBF", "package_type": "DFN", "package_drawing_code": "05-08-1699 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "TAPE AND REEL"}, {"part_number": "LTC3536", "order_device": "LTC3536IDD#PBF", "package_type": "DFN", "package_drawing_code": "05-08-1699 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "LEAD FREE FINISH"}, {"part_number": "LTC3536", "order_device": "LTC3536IDD#TRPBF", "package_type": "DFN", "package_drawing_code": "05-08-1699 <PERSON>", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "TAPE AND REEL"}, {"part_number": "LTC3536", "order_device": "LTC3536EMSE#PBF", "package_type": "MSOP", "package_drawing_code": "05-08-1666 Rev D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "LEAD FREE FINISH"}, {"part_number": "LTC3536", "order_device": "LTC3536EMSE#TRPBF", "package_type": "MSOP", "package_drawing_code": "05-08-1666 Rev D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "TAPE AND REEL"}, {"part_number": "LTC3536", "order_device": "LTC3536IMSE#PBF", "package_type": "MSOP", "package_drawing_code": "05-08-1666 Rev D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "LEAD FREE FINISH"}, {"part_number": "LTC3536", "order_device": "LTC3536IMSE#TRPBF", "package_type": "MSOP", "package_drawing_code": "05-08-1666 Rev D", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "TAPE AND REEL"}], "pin_function": [{"product_part_number": "LTC3536", "package_type": "DFN", "pins": [{"pin_number": "1", "pin_name": "RT", "pin_description": "Oscillator Frequency Programming Input. Connect a resistor from RT to GND to program the internal oscillator frequency. The frequency is given by: fosc (MHz) = 100/RT (kΩ) where RT is in kΩ and fosc is between 0.3MHz and 2MHz. Tying the RT pin to VIN enables the internal 1.2MHz default oscillator frequency."}, {"pin_number": "2", "pin_name": "SGND", "pin_description": "Ground Connection for the LTC3536. A ground plane is highly recommended. Sensitive analog components terminated at ground should connect to the GND pin with a Kelvin connection, separated from the high current path."}, {"pin_number": "3", "pin_name": "MODE/SYNC", "pin_description": "Pulse Width Modulation/Burst Mode Selection and Synchronization Input. Driving MODE to a logic 0 state programs fixed frequency, low noise PWM operation. Driving MODE to a logic 1 state programs Burst Mode operation for highest efficiency at light loads. Frequency synchronization is achieved if a clock pulse is applied to MODE/SYNC."}, {"pin_number": "4", "pin_name": "SW1", "pin_description": "Switch Pin. Connect to internal power switches A and B. Connect one side of the buck-boost inductor to SW1."}, {"pin_number": "5", "pin_name": "SW2", "pin_description": "Switch Pin. Connect to internal power switches C and D. Connect one side of the buck-boost inductor to SW2."}, {"pin_number": "6", "pin_name": "VOUT", "pin_description": "Output Voltage. This pin is the power output for the regulator. A low ESR capacitor should be placed between this pin and the ground plane."}, {"pin_number": "7", "pin_name": "VIN", "pin_description": "Power Input for the Converter. A low ESR 10µF or larger bypass capacitor should be connected between this pin and ground."}, {"pin_number": "8", "pin_name": "SHDN", "pin_description": "Enable Input. A logic 1 on SHDN activates the buck-boost regulator. A logic 0 on SHDN deactivates the buck-boost regulator."}, {"pin_number": "9", "pin_name": "FB", "pin_description": "Output Voltage Programming Feedback Divider Input. The regulator output voltage is programmed by the voltage divider connected to FB."}, {"pin_number": "10", "pin_name": "VC", "pin_description": "Error Amplifier Output. Frequency compensation components are connected between VC and FB to provide stable operation of the converter."}, {"pin_number": "11 (Exposed Pad)", "pin_name": "PGND", "pin_description": "Power Ground. The exposed pad must be soldered to the PCB and electrically connected to ground through the shortest and lowest impedance connection possible."}]}, {"product_part_number": "LTC3536", "package_type": "MSOP", "pins": [{"pin_number": "1", "pin_name": "RT", "pin_description": "Oscillator Frequency Programming Input. Connect a resistor from RT to GND to program the internal oscillator frequency."}, {"pin_number": "2", "pin_name": "SGND", "pin_description": "Ground Connection for the LTC3536. A ground plane is highly recommended."}, {"pin_number": "3", "pin_name": "MODE/SYNC", "pin_description": "Pulse Width Modulation/Burst Mode Selection and Synchronization Input."}, {"pin_number": "4", "pin_name": "SW1", "pin_description": "Switch Pin. Connect to internal power switches A and B. Connect one side of the buck-boost inductor to SW1."}, {"pin_number": "5", "pin_name": "PGND", "pin_description": "Power Ground. The exposed pad must be soldered to the PCB and electrically connected to ground."}, {"pin_number": "6", "pin_name": "SW2", "pin_description": "Switch Pin. Connect to internal power switches C and D. Connect one side of the buck-boost inductor to SW2."}, {"pin_number": "7", "pin_name": "VOUT", "pin_description": "Output Voltage. This pin is the power output for the regulator."}, {"pin_number": "8, 9", "pin_name": "VIN", "pin_description": "Power Input for the Converter. A low ESR 10µF or larger bypass capacitor should be connected between this pin and ground."}, {"pin_number": "10", "pin_name": "SHDN", "pin_description": "Enable Input. A logic 1 on SHDN activates the buck-boost regulator. A logic 0 on SHDN deactivates the buck-boost regulator."}, {"pin_number": "11", "pin_name": "FB", "pin_description": "Output Voltage Programming Feedback Divider Input."}, {"pin_number": "12", "pin_name": "VC", "pin_description": "Error Amplifier Output. Frequency compensation components are connected between VC and FB."}, {"pin_number": "13 (Exposed Pad)", "pin_name": "PGND", "pin_description": "Power Ground. The exposed pad must be soldered to the PCB and electrically connected to ground."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3536.pdf", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.5V", "min_output_voltage": "1.8V", "max_output_current": "1A", "max_switch_frequency": "2MHz", "quiescent_current": "32µA", "high_side_mosfet_resistance": "120mΩ", "low_side_mosfet_resistance": "110mΩ", "over_current_protection_threshold": "2.5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.6V", "loop_control_mode": "Voltage Mode"}, "package": [{"pitch": "0.5", "length": "1.2", "width": "4.9", "type": "DFN", "pin_count": "12", "height": "1.1"}]}