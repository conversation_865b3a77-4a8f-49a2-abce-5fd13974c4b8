{"part_number": "LTC3111", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "15V, 1.5A Synchronous Buck-Boost DC/DC Converter", "features": ["Regulated Output with VIN Above, Below or Equal to VOUT", "2.5V to 15V Input and Output Voltage Range", "1.5A Continuous Output Current: VIN ≥ 5V, VOUT = 5V, PWM Mode", "Single Inductor", "Accurate RUN Threshold", "Up to 95% Efficiency", "800kHz Switching Frequency, Synchronizable Between 600kHz and 1.5MHz", "49μA No-Load Quiescent Current in Burst Mode® Operation", "Output Disconnect in Shutdown", "Shutdown Current < 1μA", "Internal Soft-Start", "Small, Thermally Enhanced 14-Lead (3mm × 4mm × 0.75mm) DFN and 16-Lead MSOP Packages"], "description": "The LTC®3111 is a fixed frequency, synchronous buck-boost DC/DC converter with an extended input and output range. The unique 4-switch, single inductor architecture provides low noise and seamless operation from input voltages above, below or equal to the output voltage. With an input and output range of 2.5V to 15V, the LTC3111 is well suited for a wide variety of single or multiple-cell batteries, back-up capacitor or wall adapter source applications. Low RDS(ON) internal N-channel MOSFET switches and selectable PWM or Burst Mode operation produce high efficiency over a wide range of operating conditions. An accurate RUN pin allows the user to program the turn-on threshold voltage of the converter. Other features include: short-circuit protection, internal soft-start and thermal shutdown. The LTC3111 is offered in both thermally enhanced 14-lead (3mm × 4mm × 0.75mm) DFN and 16-lead MSOP packages.", "applications": ["3.3V or 5V from 1, 2 or 3 Li-Ion, Multiple-Cell Alkaline/NiMH Batteries", "RF Transmitters", "Military, Industrial Power Systems"], "ordering_information": [{"part_number": "LTC3111", "order_device": "LTC3111EDE#PBF", "package_type": "DFN", "package_drawing_code": "05-08-1708 Rev B", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3111", "order_device": "LTC3111EDE#TRPBF", "package_type": "DFN", "package_drawing_code": "05-08-1708 Rev B", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3111", "order_device": "LTC3111IDE#PBF", "package_type": "DFN", "package_drawing_code": "05-08-1708 Rev B", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3111", "order_device": "LTC3111IDE#TRPBF", "package_type": "DFN", "package_drawing_code": "05-08-1708 Rev B", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3111", "order_device": "LTC3111HDE#PBF", "package_type": "DFN", "package_drawing_code": "05-08-1708 Rev B", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3111", "order_device": "LTC3111HDE#TRPBF", "package_type": "DFN", "package_drawing_code": "05-08-1708 Rev B", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3111", "order_device": "LTC3111MPDE#PBF", "package_type": "DFN", "package_drawing_code": "05-08-1708 Rev B", "output_voltage": null, "min_operation_temp": "-55", "max_operation_temp": "150"}, {"part_number": "LTC3111", "order_device": "LTC3111MPDE#TRPBF", "package_type": "DFN", "package_drawing_code": "05-08-1708 Rev B", "output_voltage": null, "min_operation_temp": "-55", "max_operation_temp": "150"}, {"part_number": "LTC3111", "order_device": "LTC3111EMSE#PBF", "package_type": "MSOP", "package_drawing_code": "05-08-1667 Rev F", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3111", "order_device": "LTC3111EMSE#TRPBF", "package_type": "MSOP", "package_drawing_code": "05-08-1667 Rev F", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3111", "order_device": "LTC3111IMSE#PBF", "package_type": "MSOP", "package_drawing_code": "05-08-1667 Rev F", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3111", "order_device": "LTC3111IMSE#TRPBF", "package_type": "MSOP", "package_drawing_code": "05-08-1667 Rev F", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3111", "order_device": "LTC3111HMSE#PBF", "package_type": "MSOP", "package_drawing_code": "05-08-1667 Rev F", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3111", "order_device": "LTC3111HMSE#TRPBF", "package_type": "MSOP", "package_drawing_code": "05-08-1667 Rev F", "output_voltage": null, "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3111", "order_device": "LTC3111MPMSE#PBF", "package_type": "MSOP", "package_drawing_code": "05-08-1667 Rev F", "output_voltage": null, "min_operation_temp": "-55", "max_operation_temp": "150"}, {"part_number": "LTC3111", "order_device": "LTC3111MPMSE#TRPBF", "package_type": "MSOP", "package_drawing_code": "05-08-1667 Rev F", "output_voltage": null, "min_operation_temp": "-55", "max_operation_temp": "150"}], "pin_function": [{"product_part_number": "LTC3111", "package_type": "DFN", "pins": [{"pin_number": "1", "pin_name": "COMP", "pin_description": "Error Amp Output. An R-C network connected from this pin to FB sets the loop compensation for the voltage converter."}, {"pin_number": "2", "pin_name": "FB", "pin_description": "Feedback Voltage Input. Connect the VOUT resistor divider tap to this pin."}, {"pin_number": "3", "pin_name": "SNSGND", "pin_description": "This pin must be connected to ground."}, {"pin_number": "4", "pin_name": "RUN", "pin_description": "Input to Enable or Disable the IC and Set Custom Input Undervoltage Lockout (UVLO) Thresholds."}, {"pin_number": "5", "pin_name": "VIN", "pin_description": "Input Supply Voltage."}, {"pin_number": "6", "pin_name": "SW1", "pin_description": "The external inductor and internal switches A and B are connected here."}, {"pin_number": "7", "pin_name": "BST1", "pin_description": "Boosted Floating Driver Supply for A-Switch Driver. Connect a 0.1μF capacitor from this pin to SW1."}, {"pin_number": "8", "pin_name": "BST2", "pin_description": "Boosted Floating Driver Supply for D-Switch Driver. Connect a 0.1μF capacitor from this pin to SW2."}, {"pin_number": "9", "pin_name": "SW2", "pin_description": "The external inductor and internal switches C and D are connected here."}, {"pin_number": "10", "pin_name": "VOUT", "pin_description": "Regulated Output Voltage."}, {"pin_number": "11", "pin_name": "NC", "pin_description": "Not Connected. This pin should be connected to ground."}, {"pin_number": "12", "pin_name": "VCC", "pin_description": "External Capacitor Connection for the Regulated VCC Supply."}, {"pin_number": "13", "pin_name": "PWM/SYNC", "pin_description": "Burst Mode Control and Synchronization Input."}, {"pin_number": "14", "pin_name": "SGND", "pin_description": "Signal Ground. Terminate the RUN input voltage divider and output voltage divider to SGND."}, {"pin_number": "15 (Exposed Pad)", "pin_name": "PGND", "pin_description": "Power Ground. The exposed pad must be soldered to the PCB and electrically connected to ground."}]}, {"product_part_number": "LTC3111", "package_type": "MSOP", "pins": [{"pin_number": "1", "pin_name": "COMP", "pin_description": "Error Amp Output. An R-C network connected from this pin to FB sets the loop compensation for the voltage converter."}, {"pin_number": "2", "pin_name": "FB", "pin_description": "Feedback Voltage Input. Connect the VOUT resistor divider tap to this pin."}, {"pin_number": "3", "pin_name": "SNSGND", "pin_description": "This pin must be connected to ground."}, {"pin_number": "4", "pin_name": "RUN", "pin_description": "Input to Enable or Disable the IC and Set Custom Input Undervoltage Lockout (UVLO) Thresholds."}, {"pin_number": "5", "pin_name": "VIN", "pin_description": "Input Supply Voltage."}, {"pin_number": "6", "pin_name": "SW1", "pin_description": "The external inductor and internal switches A and B are connected here."}, {"pin_number": "7", "pin_name": "BST1", "pin_description": "Boosted Floating Driver Supply for A-Switch Driver. Connect a 0.1μF capacitor from this pin to SW1."}, {"pin_number": "8, 9", "pin_name": "PGND", "pin_description": "Power Ground."}, {"pin_number": "10", "pin_name": "BST2", "pin_description": "Boosted Floating Driver Supply for D-Switch Driver. Connect a 0.1μF capacitor from this pin to SW2."}, {"pin_number": "11", "pin_name": "SW2", "pin_description": "The external inductor and internal switches C and D are connected here."}, {"pin_number": "12", "pin_name": "VOUT", "pin_description": "Regulated Output Voltage."}, {"pin_number": "13", "pin_name": "NC", "pin_description": "Not Connected. This pin should be connected to ground."}, {"pin_number": "14", "pin_name": "VCC", "pin_description": "External Capacitor Connection for the Regulated VCC Supply."}, {"pin_number": "15", "pin_name": "PWM/SYNC", "pin_description": "Burst Mode Control and Synchronization Input."}, {"pin_number": "16", "pin_name": "SGND", "pin_description": "Signal Ground. Terminate the RUN input voltage divider and output voltage divider to SGND."}, {"pin_number": "17 (Exposed Pad)", "pin_name": "PGND", "pin_description": "Power Ground. The exposed pad must be soldered to the PCB and electrically connected to ground."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3111 Datasheet, Rev A, Jan 2014", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "15V", "min_input_voltage": "2.5V", "max_output_voltage": "15V", "min_output_voltage": "2.5V", "max_output_current": "1.5A", "max_switch_frequency": "1.5MHz", "quiescent_current": "49µA", "high_side_mosfet_resistance": "90mΩ", "low_side_mosfet_resistance": "105mΩ", "over_current_protection_threshold": "3A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "True", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.5%", "output_reference_voltage": "0.8V", "loop_control_mode": "Voltage Mode"}, "package": [{"type": "DFN", "length": "4.9", "width": "1.", "pin_count": "14", "pitch": "0.5", "height": "0.75"}]}