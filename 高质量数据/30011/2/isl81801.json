{"part_number": "ISL81801", "manufacturer": "Renesas", "country": "日本", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "ISL81801 80V Bidirectional 4-Switch Synchronous Buck-Boost Controller", "features": ["Single inductor 4-switch buck-boost controller", "On-the-fly bidirectional operation with independent control of voltage and current on both ends", "Proprietary algorithm for smoothest mode transition", "MOSFET drivers with adaptive shoot-through protection", "Wide input voltage range: 4.5V to 80V", "Wide output voltage range: 0.8V to 80V", "Supports startup into pre-biased rails", "Programmable frequency: 100kHz to 600kHz", "Supports parallel operation current sharing with cascade phase interleaving", "External sync with clock out or frequency dithering", "External bias for higher efficiency for 8V to 36V input", "Output and input current monitor", "Selectable PWM mode operation between PWM/DE/Burst modes", "Accurate EN/UVLO and PGOOD indicator", "Low shutdown current: 2.7μA", "Complete protection: OCP, SCP, OVP, OTP, and UVP", "Dual-level OCP with average current and pulse-by-pulse peak current limit, also provides Short Circuit Protection (SCP)", "Selectable OCP response with either hiccup or constant current mode", "Negative pulse-by-pulse peak current limit"], "description": "The ISL81801 is a true bidirectional 4-switch synchronous buck-boost controller with peak and average current sensing and monitoring at both ends. With wide input and output voltage ranges, the controller is suitable for industrial, telecommunication, and after-market automotive applications. The ISL81801 uses a proprietary buck-boost control algorithm with valley current modulation for Boost mode and peak current modulation for Buck mode control. The ISL81801 has four independent control loops for input and output voltages and currents. Inherent peak current sensing at both ends and cycle-by-cycle current limit of this family of products ensures high operational reliability by providing instant current limit in fast transient conditions at either end and in both directions. It also has two current monitoring pins at both input and output to facilitate Constant Current (CC) limit and other system management functions. CC operation down to low voltages avoids any runaway condition at over load or short-circuit conditions. In addition to multilayer overcurrent protection, it also provides full protection features such as OVP, UVP, OTP, average and peak current limit on both input and output to ensure high reliability in both unidirectional and bidirectional operation.", "applications": ["Battery backup", "UPS/storage systems", "Battery powered industrial applications", "Renewable energy", "Aftermarket automotive", "Redundant power supplies", "Robots and drones", "Medical equipment", "Building and industrial automation", "Security surveillance"], "ordering_information": [{"part_number": "ISL81801", "order_device": "ISL81801FRTZ-T", "package_type": "TQFN", "package_drawing_code": "L32.5x5A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ISL81801", "order_device": "ISL81801FRTZ-T7A", "package_type": "TQFN", "package_drawing_code": "L32.5x5A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ISL81801", "order_device": "ISL81801FVEZ-T", "package_type": "HTSSOP", "package_drawing_code": "M38.173C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "ISL81801", "order_device": "ISL81801FVEZ-T7A", "package_type": "HTSSOP", "package_drawing_code": "M38.173C", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "ISL81801", "package_type": "TQFN", "pins": [{"pin_number": "1", "pin_name": "BSTEN", "pin_description": "DE Burst mode enable signal. The pin is pulled up to 5V by an internal 250k resistor in PWM and DE mode. It is pulled low in Burst mode. Connect this pin together in the multi-chip parallel operation application to sync all the chips together for Burst mode operation."}, {"pin_number": "2", "pin_name": "FB_IN", "pin_description": "Input voltage feedback pin for reverse direction operation. Use a resistor divider to feed the input voltage back to this pin. When the input voltage drops and pulls the pin voltage below 0.8V, the internal control loop reduces the duty cycle to sink in current from output to input to keep the pin voltage regulated at 0.8V. Keep the pin voltage below 0.3V to disable the reverse direction operation. When the reverse operation function is not used, tie this pin to VCC5V or SGND to set up the phase shift for the interleaving parallel operation."}, {"pin_number": "3", "pin_name": "VCC5V", "pin_description": "Output of the internal 5V linear regulator. This output supplies bias for the IC. The VCC5V pin must always be decoupled to SGND with a minimum of 4.7uF ceramic capacitor placed close to the pin."}, {"pin_number": "4, 22, 24", "pin_name": "NC", "pin_description": "No connection pin."}, {"pin_number": "5", "pin_name": "RT/SYNC", "pin_description": "A resistor from this pin to ground adjusts the default switching frequency from 100kHz to 600kHz. When an external clock signal is applied to this pin, the internal frequency is synchronized to the external clock frequency."}, {"pin_number": "6", "pin_name": "PLL_COMP", "pin_description": "Compensation pin for the internal PLL circuit."}, {"pin_number": "7", "pin_name": "CLKOUT/DITHER", "pin_description": "Dual function pin. When there is no capacitor connected to this pin, it provides a clock signal to synchronize the other ISL81801(s). When a capacitor is connected, the frequency dither function is enabled."}, {"pin_number": "8", "pin_name": "SS/TRK", "pin_description": "Dual function pin for soft-start control or tracking control."}, {"pin_number": "9", "pin_name": "COMP", "pin_description": "Voltage error GM amplifier output. It sets the reference of the inner current loop."}, {"pin_number": "10", "pin_name": "FB_OUT", "pin_description": "Output voltage feedback input. Connect FB_OUT to a resistive voltage divider between the output and SGND to adjust the output voltage. The FB_OUT pin voltage is regulated to the internal 0.8V reference."}, {"pin_number": "11", "pin_name": "IMON_OUT", "pin_description": "Output current monitor. The current from this pin is proportional to the differential voltage between the ISEN+ and ISEN- pins."}, {"pin_number": "12", "pin_name": "OV", "pin_description": "OVP comparator output signal. The pin is pulled low to GND by an internal 250k resistor in normal operation. It is pulled high when output OVP trips."}, {"pin_number": "13", "pin_name": "ISEN-", "pin_description": "Output current sense signal negative input pin."}, {"pin_number": "14", "pin_name": "ISEN+", "pin_description": "Output current sense signal positive input pin."}, {"pin_number": "15", "pin_name": "PGOOD", "pin_description": "Power Good open-drain logic output that indicates the status of output voltage."}, {"pin_number": "16", "pin_name": "UG2", "pin_description": "High-side MOSFET gate driver output controlled by the boost PWM signal."}, {"pin_number": "17", "pin_name": "PHASE2", "pin_description": "Phase node connection of the boost converter."}, {"pin_number": "18", "pin_name": "BOOT2", "pin_description": "Bootstrap pin to provide bias for the boost high-side driver."}, {"pin_number": "19", "pin_name": "LG2/OC_MODE", "pin_description": "Low-side MOSFET gate driver output controlled by the boost PWM signal and OCP mode set pin."}, {"pin_number": "20", "pin_name": "VDD", "pin_description": "Output of the internal 8V linear regulator supplied by either VIN or EXTBIAS."}, {"pin_number": "21", "pin_name": "PGND", "pin_description": "Power ground connection."}, {"pin_number": "23", "pin_name": "BOOT1", "pin_description": "Bootstrap pin to provide bias for the buck high-side driver."}, {"pin_number": "25", "pin_name": "UG1", "pin_description": "High-side MOSFET gate driver output controlled by the buck PWM signal."}, {"pin_number": "26", "pin_name": "EXTBIAS", "pin_description": "External bias input for the optional VDD LDO."}, {"pin_number": "27", "pin_name": "VIN", "pin_description": "Tie this pin to the input rail using a 5-10Ω resistor. It provides power to the internal LDO for VDD."}, {"pin_number": "28", "pin_name": "CS+", "pin_description": "Input current sense signal positive input pin."}, {"pin_number": "29", "pin_name": "CS-", "pin_description": "Input current sense signal negative input pin."}, {"pin_number": "30", "pin_name": "EN/UVLO", "pin_description": "This pin provides enable/disable and accurate UVLO functions."}, {"pin_number": "31", "pin_name": "IMON_IN", "pin_description": "Input current monitor. The current from this pin is proportional to the differential voltage between the CS+ and CS- pins."}, {"pin_number": "32", "pin_name": "CLKEN", "pin_description": "DE mode burst operation off state enable signal."}, {"pin_number": "EPAD", "pin_name": "SGND", "pin_description": "Small-signal ground common to all control circuitries. EPAD at ground potential."}]}, {"product_part_number": "ISL81801", "package_type": "HTSSOP", "pins": [{"pin_number": "1, 2, 3", "pin_name": "VIN, CS1+, CS1-", "pin_description": "Corresponds to TQFN pins VIN, CS+, CS-."}, {"pin_number": "4, 22, 24, 28, 33, 37", "pin_name": "NC", "pin_description": "No connection pin."}, {"pin_number": "5", "pin_name": "EN/UVLO", "pin_description": "This pin provides enable/disable and accurate UVLO functions."}, {"pin_number": "6", "pin_name": "IMON_IN", "pin_description": "Input current monitor."}, {"pin_number": "7", "pin_name": "CLKEN", "pin_description": "DE mode burst operation off state enable signal."}, {"pin_number": "8", "pin_name": "BSTEN", "pin_description": "DE Burst mode enable signal."}, {"pin_number": "9", "pin_name": "FB_IN", "pin_description": "Input voltage feedback pin for reverse direction operation."}, {"pin_number": "10", "pin_name": "VCC5V", "pin_description": "Output of the internal 5V linear regulator."}, {"pin_number": "11", "pin_name": "SGND", "pin_description": "Small-signal ground common to all control circuitries."}, {"pin_number": "12", "pin_name": "RT/SYNC", "pin_description": "Frequency setting and synchronization pin."}, {"pin_number": "13", "pin_name": "PLL_COMP", "pin_description": "Compensation pin for the internal PLL circuit."}, {"pin_number": "14", "pin_name": "CLKOUT/DITHER", "pin_description": "Clock output and frequency dithering pin."}, {"pin_number": "15", "pin_name": "SS/TRK", "pin_description": "Soft-start and tracking pin."}, {"pin_number": "16", "pin_name": "COMP", "pin_description": "Voltage error GM amplifier output."}, {"pin_number": "17", "pin_name": "FB_OUT", "pin_description": "Output voltage feedback input."}, {"pin_number": "18", "pin_name": "IMON_OUT", "pin_description": "Output current monitor."}, {"pin_number": "19", "pin_name": "OV", "pin_description": "OVP comparator output signal."}, {"pin_number": "20", "pin_name": "ISEN-", "pin_description": "Output current sense signal negative input pin."}, {"pin_number": "21", "pin_name": "ISEN+", "pin_description": "Output current sense signal positive input pin."}, {"pin_number": "23", "pin_name": "PGOOD", "pin_description": "Power Good open-drain logic output."}, {"pin_number": "25", "pin_name": "UG2", "pin_description": "High-side MOSFET gate driver output (boost)."}, {"pin_number": "26", "pin_name": "PHASE2", "pin_description": "Phase node connection of the boost converter."}, {"pin_number": "27", "pin_name": "BOOT2", "pin_description": "Bootstrap pin for the boost high-side driver."}, {"pin_number": "29", "pin_name": "LG2/OC_MODE", "pin_description": "Low-side MOSFET gate driver output (boost) and OCP mode set pin."}, {"pin_number": "30", "pin_name": "VDD", "pin_description": "Output of the internal 8V linear regulator."}, {"pin_number": "31", "pin_name": "PGND", "pin_description": "Power ground connection."}, {"pin_number": "32", "pin_name": "LG1/PWM_MODE", "pin_description": "Low-side MOSFET gate driver output (buck) and PWM mode set pin."}, {"pin_number": "34", "pin_name": "BOOT1", "pin_description": "Bootstrap pin for the buck high-side driver."}, {"pin_number": "35", "pin_name": "PHASE1", "pin_description": "Phase node connection of the buck converter."}, {"pin_number": "36", "pin_name": "UG1", "pin_description": "High-side MOSFET gate driver output (buck)."}, {"pin_number": "38", "pin_name": "EXTBIAS", "pin_description": "External bias input for the optional VDD LDO."}, {"pin_number": "EPAD", "pin_name": "SGND", "pin_description": "Small-signal ground common to all control circuitries. EPAD at ground potential."}]}], "datasheet_cn": "未找到", "datasheet_en": "ISL81801 Datasheet Rev.1.4", "family_comparison": "The ISL81801 is the highest voltage (80V) member of the ISL81xxx family, featuring full bidirectional control and all features enabled. It is compared with ISL81601, ISL81401, and ISL81401A on key differences like VIN, VDD, Current Control, and Parallel capability.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "80V", "min_input_voltage": "4.5V", "max_output_voltage": "80V", "min_output_voltage": "1V", "max_output_current": "依赖外部元件", "max_switch_frequency": "0.6MHz", "quiescent_current": "50μA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "DE/Burst Mode", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值/谷值电流模式"}, "package": [{"pitch": "0.5", "height": "0.8", "length": "9.7", "width": "4.4", "type": "Outline", "pin_count": "1"}]}