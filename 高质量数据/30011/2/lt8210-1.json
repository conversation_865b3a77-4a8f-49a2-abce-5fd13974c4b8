{"part_number": "LT8210-1", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "100V VIN and VOUT Synchronous 4-Switch Multiphase Buck-Boost DC/DC Controller", "features": ["Built-In Multiphase Current Sharing", "18μA Pass-Thru Mode IQ with 99.9% Efficiency", "Pass-Thru or Fixed Output CCM, DCM Operation", "Programmable Non-Switching Pass-Thru Window", "VIN Range: 2.8V to 100V (4.5V for Start-Up)", "VOUT Range: 1V to 100V", "Reverse Input Protection to -40V", "±2% Output Voltage Accuracy (–40°C to 125°C)", "Programmable Current Limit", "10V Quad N-Channel MOSFET Gate Drivers", "±20% Cycle-by-Cycle Inductor Current Limit", "No Top MOSFET Refresh Noise in Buck or Boost", "Fixed/Phase-Lockable Frequency: 80kHz to 400kHz", "Spread-Spectrum Frequency Modulation for Low EMI", "Power Good Output Voltage/Overcurrent Monitor", "Available in a 40-Lead (6mm x 6mm) QFN Package"], "description": "The LT®8210-1 is a 4-switch synchronous buck-boost DC/DC controller that can operate in pass-thru mode, forced continuous conduction, or pulse-skipping mode. Pass-Thru is a feature that passes the input directly to the output when the input is within a user programmable window. Pass-Thru mode eliminates switching losses and EMI while maximizing efficiency. For input voltages above or below the pass-thru window, the buck or boost regulation loops maintain the output at the set maximum or minimum values, respectively. The LT8210-1 is pin compatible with the LT8210. The IMON pin on the LT8210-1 has an ISHARE function, which allows for masterless current sharing. Multiple LT8210-1 can be connected in parallel for higher output current and reduced voltage ripple. A masterless closed loop current sharing loop balances the current per phase and allows phase shedding. Optional reverse input protection down to -40V can be implemented with the addition of a single N-channel MOSFET. The LT8210-1 includes a precision current sense amplifier that can accurately monitor and limit output or input average current.", "applications": ["Industrial", "Telecom", "Avionics Systems", "Automotive Qualification in Progress"], "ordering_information": [{"part_number": "LT8210-1", "order_device": "LT8210AUJ-1#PBF", "package_type": "QFN", "package_drawing_code": "05-08-1728 <PERSON>", "output_voltage": "不适用", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT8210-1", "order_device": "LT8210AUJ-1#TRPBF", "package_type": "QFN", "package_drawing_code": "05-08-1728 <PERSON>", "output_voltage": "不适用", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LT8210-1", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "SYNC/SPRD", "pin_description": "External Clock Synchronization Input. For external sync, apply a clock signal between 80kHz and 400kHz to this pin. An internal PLL will synchronize the oscillator to the external clock signal. Connect this pin to the VDD pin to enable spread-spectrum operation on the RT set switching frequency; otherwise, connect to ground."}, {"pin_number": "2", "pin_name": "PWGD", "pin_description": "Power Good Indicator. Open-drain logic output, which is pulled to ground when the output voltage is outside ±10% of its programmed value or the IMON pin voltage is greater than 1.20V. This pin can be connected to any voltage rail up to 40V through a pull-up resistor."}, {"pin_number": "3", "pin_name": "RT", "pin_description": "Frequency Set Pin. Place a resistor from this pin to GND to set the switching frequency. The range of frequency adjustment is between 80kHz and 400kHz."}, {"pin_number": "5", "pin_name": "VDD", "pin_description": "Internally Regulated 3.3V Supply Rail. Bypass this pin to ground with a minimum of a 2.2µF ceramic capacitor. VDD can be used for tying MODE1, MODE2, and SYNC/SPRD pins logic high."}, {"pin_number": "6", "pin_name": "SS", "pin_description": "Soft-Start Input. A capacitor to ground at this pin sets the ramp rate of the inductor current at start-up via internal clamping of the VC1 and VC2 voltages. The SS pin sources 5µA once switching is enabled and is held at ground while switching is disabled."}, {"pin_number": "7", "pin_name": "IMON", "pin_description": "Average Current Summing and Sharing Output. Outputs a current proportional to the SNSP2 and SNSN2 differential voltages. Connect this pin to a 11kΩ resistor to ground and to the IMON pins of multiple LT8210-1 for current sharing."}, {"pin_number": "8", "pin_name": "VC1", "pin_description": "Error Amplifier Output and Switching Regulator Compensation Point for CCM and DCM Operations. In pass-thru mode, this pin is the compensation point for the boost regulator loop. The current mode comparator trip point increases with this control voltage."}, {"pin_number": "9", "pin_name": "VC2", "pin_description": "Error Amplifier Output and Switching Regulator Compensation Point for Buck Loop When in pass-thru mode. The current mode comparator trip point increases with this control voltage. If pass-thru mode is not used, leave VC2 floating."}, {"pin_number": "10", "pin_name": "FB2", "pin_description": "Error Amplifier Feedback Input for Buck Regulation Loop When in pass-thru mode. Receives the feedback voltage for the buck controller from an external resistive divider across the output. If pass-thru mode is not used, leave this pin floating."}, {"pin_number": "11", "pin_name": "FB1", "pin_description": "Error Amplifier Feedback Input for CCM and DCM Operation Modes. Feedback input for the boost regulation loop in pass-thru mode. Receives the feedback voltage from an external resistive divider across the output."}, {"pin_number": "13", "pin_name": "VOUT", "pin_description": "Output Voltage Sense. This pin must have a Kelvin connection to the drain of switch D. Use a small RC low-pass filter for improved jitter performance when VOUT ripple is large."}, {"pin_number": "15, 16", "pin_name": "SNSN2, SNSP2", "pin_description": "Positive (+) and Negative (-) Inputs for the Average Current Sense Monitor. SNSP2, SNSN2 should connect to the positive and negative terminals of a sense resistor placed in series with the input, output or load."}, {"pin_number": "18", "pin_name": "TG2", "pin_description": "Top Gate Drive for Boost Regulator. Drives the top N-channel MOSFET with a voltage swing equal to GATEVCC superimposed onto the SW2 node voltage."}, {"pin_number": "19", "pin_name": "SW2", "pin_description": "Boost Regulator Switch Node. The (-) terminal of the bootstrap capacitor connects here."}, {"pin_number": "20", "pin_name": "BST2", "pin_description": "Boosted Floating Driver Supply for Boost Regulator. The (+) terminal of the bootstrap capacitor connects here."}, {"pin_number": "21", "pin_name": "BG2", "pin_description": "Bottom Gate Drive for Boost Regulator. Drives bottom N-channel MOSFET with a voltage swing between GATEVCC and PGND."}, {"pin_number": "22", "pin_name": "PGND", "pin_description": "Driver Power Ground. Connect to CIN, COUT, and sources of MOSFETs, MC, and MD."}, {"pin_number": "23", "pin_name": "BG1", "pin_description": "Bottom Gate Drive for Buck Regulator. Drives bottom N-channel MOSFET with a voltage swing between GATEVCC and PGND."}, {"pin_number": "24", "pin_name": "GATEVCC", "pin_description": "Power Supply for Gate Drivers. Internally regulated to 10.6V. Bypass this pin to ground with a minimum 4.7µF ceramic capacitor."}, {"pin_number": "25", "pin_name": "EXTVCC", "pin_description": "External Power Supply Input for the GATEVCC Regulator. GATEVCC will be linearly regulated from EXTVCC if its voltage is higher than 8V and is simultaneously lower than VINP. May be driven with voltages up to 40V."}, {"pin_number": "27", "pin_name": "BST1", "pin_description": "Boosted Floating Driver Supply for Buck Regulator. The (+) terminal of the bootstrap capacitor connects here."}, {"pin_number": "28", "pin_name": "SW1", "pin_description": "Buck Regulator Switch Node. The (-) terminal of the bootstrap capacitor connects here."}, {"pin_number": "29", "pin_name": "TG1", "pin_description": "Top Gate Drive for Buck Regulator. Drives the top N-channel MOSFET with a voltage swing equal to GATEVCC superimposed onto the SW1 node voltage."}, {"pin_number": "30, 31", "pin_name": "SNSN1, SNSP1", "pin_description": "Positive (+) and Negative (-) Inputs for the Inductor Current Sense Amplifier. Place an appropriately valued shunt resistor in series with the inductor on the SW1 side and connect to SNSP1 and SNSN1. The SNSP1 – SNSN1 voltage is used for current mode control and reverse current detection."}, {"pin_number": "33", "pin_name": "VINP", "pin_description": "Protected Main Input Supply. This pin must connect to the drain terminal of switch A. When reverse input protection is implemented, connect this pin to the drain of the DG MOSFET; otherwise, connect to VIN."}, {"pin_number": "34", "pin_name": "DG", "pin_description": "Reverse Input Protection Gate Drive Output. When VIN is pulled below –1.2V, this pin is clamped internally to VIN with a low resistance switch, forcing an external MOSFET between the VIN and VINP pins into cutoff."}, {"pin_number": "35", "pin_name": "VIN", "pin_description": "Input Voltage Pin. This pin is used for powering the start-up circuitry and the internal charge pump. VIN can withstand negative voltages down to –40V without damaging the regulator or drawing large currents."}, {"pin_number": "37", "pin_name": "EN/UVLO", "pin_description": "Precision Enable Input. The part is enabled when this pin is pulled above 1.45V. A voltage below 1.35V causes the LT8210-1 to reside in a low-power shutdown mode."}, {"pin_number": "39", "pin_name": "MODE2", "pin_description": "Operating Mode Selection Input #2. Used in conjunction with the MODE1 pin to select between continuous conduction switching (CCM), discontinuous switching (DCM), and pass-thru operating modes."}, {"pin_number": "40", "pin_name": "MODE1", "pin_description": "Operating Mode Selection Input #1. Used in conjunction with MODE2 pin to select between continuous conduction switching (CCM), discontinuous switching (DCM), and pass-thru operating modes."}, {"pin_number": "41/Exposed Pad", "pin_name": "GND", "pin_description": "Signal Ground. All small-signal components and compensation components should connect to this ground, which in turn connects to the PCB ground at one location away from high currents and switching noise. The exposed pad must be soldered to the PCB and connected to the GND pin using top layer metal."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT8210-1 Rev. A", "family_comparison": "包含LTC3779, LTC3777, LT8705A等多个相关型号的比较信息。", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 4, "channel_count": 1, "max_input_voltage": "100V", "min_input_voltage": "2.8V", "max_output_voltage": "100V", "min_output_voltage": "1V", "max_output_current": "外部可调", "max_switch_frequency": "400kHz", "quiescent_current": "18µA", "high_side_mosfet_resistance": "不适用(外部器件)", "low_side_mosfet_resistance": "不适用(外部器件)", "over_current_protection_threshold": "可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "DCM, Pulse Skipping", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Fold Back", "over_temperature_protection": "Latch", "pass_through_mode": "True", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "1V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.5", "length": "6", "width": "1.35", "type": "QFN", "pin_count": "11", "height": "99.9"}]}