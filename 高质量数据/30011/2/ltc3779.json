{"part_number": "LTC3779", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "150V VIN and VOUT Synchronous 4-Switch Buck-Boost Controller", "features": ["4-Switch Current Mode Single Inductor Architecture Allows VIN Above, Below or Equal to VOUT", "Wide VIN Range: 4.5V to 150V", "Wide Output Voltage Range: 1.2V ≤ VOUT ≤ 150V", "Synchronous Rectification: Up to 99% Efficiency", "±1% 1.2V Voltage Reference", "Input or Output Average Current Limit", "Onboard LDO or External NMOS LDO for DRVCC", "36V EXTVCC LDO Powers Drivers", "Programmable 6V to 10V DRVCC Optimizes Efficiency", "No Top FET Refresh Noise in Boost or Buck Mode", "VOUT Disconnected from VIN During Shutdown", "Phase-Lockable Fixed Frequency (50kHz to 600kHz)", "No Reverse Current During Start-Up", "Power Good Output Voltage Monitor", "150V Rated RUN Pin with Accurate Turn-On Threshold", "Programmable Input Overvoltage Lockout", "Thermally Enhanced FE38 TSSOP Package Modified for High Voltage Operation"], "description": "The LTC3779 is a high performance buck-boost switching regulator controller that operates from input voltages above, below or equal to the output voltage. The constant frequency current mode architecture allows a phase-lockable frequency of up to 600kHz, while an input/output constant-current loop provides support for battery charging. With a wide 4.5V to 150V input and output range and seamless transfers between operating regions, the LTC3779 is ideal for automotive, telecom and battery-powered systems. The LTC3779 features a precision 1.2V reference and power good output indicator. The MODE pin can select between pulse-skipping mode or forced continuous mode of operation. Pulse-skipping mode offers high efficiency at light load while forced continuous mode operates at a constant frequency for noise sensitive applications. The PLLIN pin allows the IC to be synchronized to an external clock. The SS pin ramps the output voltage during start-up. Current foldback limits MOSFET heat dissipation during short-circuit conditions.", "applications": ["Industrial", "Automotive", "Medical", "Military", "Avionics"], "ordering_information": [{"part_number": "LTC3779", "order_device": "LTC3779EFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE38", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "LEAD FREE FINISH"}, {"part_number": "LTC3779", "order_device": "LTC3779EFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE38", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "TAPE AND REEL"}, {"part_number": "LTC3779", "order_device": "LTC3779IFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE38", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "LEAD FREE FINISH"}, {"part_number": "LTC3779", "order_device": "LTC3779IFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE38", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "TAPE AND REEL"}, {"part_number": "LTC3779", "order_device": "LTC3779HFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE38", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150", "carrier_description": "LEAD FREE FINISH"}, {"part_number": "LTC3779", "order_device": "LTC3779HFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE38", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150", "carrier_description": "TAPE AND REEL"}], "pin_function": [{"product_part_number": "LTC3779", "package_type": "TSSOP-38", "pins": [{"pin_number": "1, 19", "pin_name": "BG1/BG2", "pin_description": "Bottom Gate Driver Outputs. This pin drives the gate(s) of the bottom N-Channel MOSFET between PGND to DRVCC."}, {"pin_number": "2", "pin_name": "VINOV", "pin_description": "Connect to the input supply through a resistor divider to set the over-voltage lockout level."}, {"pin_number": "3", "pin_name": "DRVSET", "pin_description": "Sets the regulated output voltage of the DRVCC linear regulator from 6V to 10V in 1V increments."}, {"pin_number": "4, 14", "pin_name": "SGND", "pin_description": "Signal ground. All feedback and soft-start connections should return to SGND."}, {"pin_number": "5", "pin_name": "EXTVCC", "pin_description": "External Power Input to an Internal LDO Connected to DRVCC."}, {"pin_number": "6", "pin_name": "NDRV", "pin_description": "Drive Output for External Pass Device of the LDO Regulator connected to DRVCC."}, {"pin_number": "7", "pin_name": "DRVCC", "pin_description": "Output of the Internal or External Low Dropout Regulator. The gate drivers are powered from this voltage source."}, {"pin_number": "8", "pin_name": "V5", "pin_description": "Output of the Internal 5.5V Low Dropout Regulator. The control circuits are powered from this voltage."}, {"pin_number": "9", "pin_name": "SS", "pin_description": "Soft-Start Input. A capacitor to ground at this pin sets the ramp time to final regulated output voltage."}, {"pin_number": "10", "pin_name": "VFB", "pin_description": "Error Amplifier Input. The FB pin should be connected through a resistive divider network to VOUT to set the output voltage."}, {"pin_number": "11", "pin_name": "SENSEP", "pin_description": "The positive input to the differential current comparator."}, {"pin_number": "12", "pin_name": "SENSEN", "pin_description": "The negative input to the differential current sense comparator."}, {"pin_number": "13", "pin_name": "ITH", "pin_description": "Error Amplifier Output. The current comparator trip threshold increases with the ITH control voltage."}, {"pin_number": "15", "pin_name": "MODE", "pin_description": "Mode Selection pin. Tying this pin to SGND enables forced continuous mode. Tying it to V5 enables pulse-skipping mode."}, {"pin_number": "16", "pin_name": "PLLIN", "pin_description": "External Synchronization Input to Phase Detector."}, {"pin_number": "17", "pin_name": "FREQ", "pin_description": "The frequency control pin for the internal VCO. Programmed by using a resistor to SGND."}, {"pin_number": "18", "pin_name": "PGOOD", "pin_description": "Fault indicator Output. Open-drain output that pulls to ground when VFB is not within ±10% of its set point."}, {"pin_number": "20, 38", "pin_name": "SW2, SW1", "pin_description": "Switch Node Connections to the Inductors."}, {"pin_number": "21, 37", "pin_name": "TG2, TG1", "pin_description": "High Current Gate Drives for Top N-Channel MOSFETs."}, {"pin_number": "22, 36", "pin_name": "BOOST2, BOOST1", "pin_description": "Boosted Floating Driver Supplies."}, {"pin_number": "24", "pin_name": "RUN", "pin_description": "Enable Control Input. A voltage above 1.2V turns on the IC."}, {"pin_number": "26", "pin_name": "IAVGSNSP", "pin_description": "The positive input to the Input / Output Average Current Sense Amplifier."}, {"pin_number": "28", "pin_name": "IAVGSNSN", "pin_description": "The negative input to the Input / Output Average Current Sense Amplifier."}, {"pin_number": "30", "pin_name": "VOUTSNS", "pin_description": "VOUT Sense Input to the Buck-Boost Transition comparator."}, {"pin_number": "32", "pin_name": "VINSNS", "pin_description": "VIN Sense Input to the Buck-Boost Transition comparator."}, {"pin_number": "34", "pin_name": "VIN", "pin_description": "Main Supply Pin."}, {"pin_number": "39", "pin_name": "PGND (Exposed Pad)", "pin_description": "Driver Power Ground. The exposed pad must be soldered to PCB ground."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3779 Rev.A", "family_comparison": "The LTC3779 is a high-voltage (150V) synchronous 4-switch buck-boost controller. Related parts include the LT8705A (80V buck-boost), LTC7813 (60V boost+buck), and LTC3895 (150V step-down controller).", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "150V", "min_input_voltage": "4.5V", "max_output_voltage": "150V", "min_output_voltage": "1.2V", "max_output_current": "10A", "max_switch_frequency": "600kHz", "quiescent_current": "40µA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "140mV", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Pulse Skipping, Forced Continuous", "power_good_indicator": "Yes", "soft_start": "External", "input_over_voltage_protection": "Latch", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Fold Back", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "1.2V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.50", "height": "1.20", "length": "9.80", "width": "4.50", "type": "Modified", "pin_count": "150"}]}