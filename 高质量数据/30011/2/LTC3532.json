{"part_number": "LTC3532", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "Micropower Synchronous Buck-Boost DC/DC Converter", "features": ["Single Inductor", "Regulated Output with Input Voltages Above, Below or Equal to the Output", "Wide VIN Range: 2.4V to 5.5V", "VOUT Range: 2.4V to 5.25V", "Up to 500mA Peak Output Current", "Synchronous Rectification: Up to 95% Efficiency", "Manual or Programmable Automatic Burst Mode® Operation", "Output Disconnect in Shutdown", "Programmable Oscillator: 300kHz to 2MHz", "Pin Compatible with LTC3440", "Small Thermally Enhanced 10-Lead (3mm × 3mm) DFN and 10-Lead MSOP Packages"], "description": "The LTC®3532 is a high efficiency, fixed frequency, buck-boost DC/DC converter that operates from input voltages above, below or equal to the output voltage. The topology incorporated in the IC provides a continuous transfer function through all operating modes, making the product ideal for single lithium-ion, multicell alkaline or NiMH applications where the output voltage is within the battery voltage range.\nThe device includes two 0.36Ω N-channel MOSFET switches and two 0.42Ω P-channel switches. Switching frequencies up to 2MHz are programmed with an external resistor. Quiescent current is only 35μA in Burst Mode operation, maximizing battery life in portable applications. Automatic Burst Mode operation allows the user to program the load current for Burst Mode operation or to control it manually.\nOther features include a 1μA shutdown, soft-start control, thermal shutdown, and peak current limit. The LTC3532 is available in a low profile (0.75mm) 10-lead (3mm × 3mm) DFN and 10-lead MSOP packages.", "applications": ["Miniature Hard Disk Drive Power Supply", "MP3 Players", "Handheld Instruments", "Digital Cameras", "Handheld Terminals"], "ordering_information": [{"part_number": "LTC3532", "order_device": "LTC3532EDD#PBF", "package_type": "DFN", "package_drawing_code": "10-Lead Plastic DFN", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3532", "order_device": "LTC3532EDD#TRPBF", "package_type": "DFN", "package_drawing_code": "10-Lead Plastic DFN", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3532", "order_device": "LTC3532EMS#PBF", "package_type": "MSOP", "package_drawing_code": "10-Lead Plastic MSOP", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3532", "order_device": "LTC3532EMS#TRPBF", "package_type": "MSOP", "package_drawing_code": "10-Lead Plastic MSOP", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "LTC3532", "package_type": "DFN", "pins": [{"pin_number": "1", "pin_name": "RT", "pin_description": "Timing Resistor to Program the Oscillator Frequency. The programming range is 300kHz to 2MHz."}, {"pin_number": "2", "pin_name": "BURST", "pin_description": "Used to Set the Automatic Burst Mode Operation Threshold. Place a resistor and capacitor in parallel from this pin to ground. For manual control, ground the pin to force Burst Mode operation, connect to VOUT to force fixed frequency mode."}, {"pin_number": "3", "pin_name": "SW1", "pin_description": "Switch Pin Where the Internal Switches A and B are Connected. Connect inductor from SW1 to SW2. An optional Schottky diode can be connected from SW1 to ground."}, {"pin_number": "4", "pin_name": "SW2", "pin_description": "Switch Pin Where the Internal Switches C and D are Connected. For applications with output voltages over 4.3V, a Schottky diode is required from SW2 to VOUT."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Signal and Power Ground for the IC."}, {"pin_number": "6", "pin_name": "VOUT", "pin_description": "Output of the Synchronous Rectifier. A filter capacitor is placed from VOUT to GND."}, {"pin_number": "7", "pin_name": "VIN", "pin_description": "Input Supply Pin. Supplies current to the inductor through SW1 and supplies internal VCC for the IC. A ceramic bypass capacitor as close to the VIN pin and GND (Pin 5) is required."}, {"pin_number": "8", "pin_name": "SHDN/SS", "pin_description": "Combined Soft-Start and Shutdown. Grounding this pin shuts down the IC. Tie to >1.5V to enable the IC. An RC from the shutdown command signal to this pin will provide a soft-start function."}, {"pin_number": "9", "pin_name": "FB", "pin_description": "Feedback Pin. Connect resistor divider tap here. The output voltage can be adjusted from 2.4V to 5.25V. The feedback reference is typically 1.22V."}, {"pin_number": "10", "pin_name": "VC", "pin_description": "Error Amp Output: A frequency compensation network is connected from this pin to the FB pin to compensate the loop."}, {"pin_number": "11", "pin_name": "Exposed Pad", "pin_description": "The exposed pad (DFN Package) must be soldered to PCB ground for electrical contact and rated thermal performance."}]}, {"product_part_number": "LTC3532", "package_type": "MSOP", "pins": [{"pin_number": "1", "pin_name": "RT", "pin_description": "Timing Resistor to Program the Oscillator Frequency. The programming range is 300kHz to 2MHz."}, {"pin_number": "2", "pin_name": "BURST", "pin_description": "Used to Set the Automatic Burst Mode Operation Threshold. Place a resistor and capacitor in parallel from this pin to ground. For manual control, ground the pin to force Burst Mode operation, connect to VOUT to force fixed frequency mode."}, {"pin_number": "3", "pin_name": "SW1", "pin_description": "Switch Pin Where the Internal Switches A and B are Connected. Connect inductor from SW1 to SW2. An optional Schottky diode can be connected from SW1 to ground."}, {"pin_number": "4", "pin_name": "SW2", "pin_description": "Switch Pin Where the Internal Switches C and D are Connected. For applications with output voltages over 4.3V, a Schottky diode is required from SW2 to VOUT."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Signal and Power Ground for the IC."}, {"pin_number": "6", "pin_name": "VOUT", "pin_description": "Output of the Synchronous Rectifier. A filter capacitor is placed from VOUT to GND."}, {"pin_number": "7", "pin_name": "VIN", "pin_description": "Input Supply Pin. Supplies current to the inductor through SW1 and supplies internal VCC for the IC. A ceramic bypass capacitor as close to the VIN pin and GND (Pin 5) is required."}, {"pin_number": "8", "pin_name": "SHDN/SS", "pin_description": "Combined Soft-Start and Shutdown. Grounding this pin shuts down the IC. Tie to >1.5V to enable the IC. An RC from the shutdown command signal to this pin will provide a soft-start function."}, {"pin_number": "9", "pin_name": "FB", "pin_description": "Feedback Pin. Connect resistor divider tap here. The output voltage can be adjusted from 2.4V to 5.25V. The feedback reference is typically 1.22V."}, {"pin_number": "10", "pin_name": "VC", "pin_description": "Error Amp Output: A frequency compensation network is connected from this pin to the FB pin to compensate the loop."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3532.pdf (Rev C, 2006)", "family_comparison": "Pin compatible with LTC3440. Part of the LTC344x/LTC353x family, differing in output current (IOUT), switching frequency, and quiescent current (IQ). See related parts table for LTC3440, LTC3441, LTC3442, LTC3443, LTC3444, and LTC3531.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.4V", "max_output_voltage": "5.25V", "min_output_voltage": "2.4V", "max_output_current": "0.5A", "max_switch_frequency": "2MHz", "quiescent_current": "35µA", "high_side_mosfet_resistance": "420mΩ", "low_side_mosfet_resistance": "360mΩ", "over_current_protection_threshold": "1.1A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Fold Back", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.46%", "output_reference_voltage": "1.22V", "loop_control_mode": "Voltage Mode"}, "package": [{"pitch": "0.5", "height": "0.75", "length": "3", "width": "2.", "type": "DESCRIPTION", "pin_count": "5"}]}