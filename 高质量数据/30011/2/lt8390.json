{"part_number": "LT8390", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "LT8390: 60V Synchronous 4-Switch Buck-Boost Controller with Spread Spectrum", "features": ["4-Switch Single Inductor Architecture Allows VIN Above, Below or Equal to VOUT", "Synchronous Switching: Up to 98% Efficiency", "Proprietary Peak-Buck <PERSON>-Boost Current Mode", "Wide VIN Range: 4V to 60V", "±1.5% Output Voltage Accuracy: 1V ≤ VOUT ≤ 60V", "±3% Input or Output Current Accuracy with Monitor", "Spread Spectrum Frequency Modulation for Low EMI", "High Side PMOS Load Switch Driver", "Integrated Bootstrap Diodes", "No Top MOSFET Refresh Noise in Buck or Boost", "Adjustable and Synchronizable: 150kHz to 650kHz", "VOUT Disconnected from VIN During Shutdown", "Available in 28-Lead TSSOP with Exposed Pad and 28-Lead QFN (4mm × 5mm)", "AEC-Q100 Qualified for Automotive Applications"], "description": "The LT®8390 is a synchronous 4-switch buck-boost DC/DC controller that regulates output voltage, input or output current from an input voltage above, below, or equal to the output voltage. The proprietary peak-buck/peak-boost current mode control scheme allows adjustable and synchronizable 150kHz to 650kHz fixed frequency operation, or internal ±15% triangle spread spectrum frequency modulation for low EMI. With a 4V to 60V input voltage range, 0V to 60V output voltage capability, and seamless low noise transitions between operation regions, the LT8390 is ideal for voltage regulator, battery and supercapacitor charger applications in automotive, industrial, telecom, and even battery-powered systems.\nThe LT8390 provides input or output current monitor and power good flag. Fault protection is also provided to detect output short-circuit condition, during which the LT8390 retries, latches off, or keeps running.", "applications": ["Automotive, Industrial, Telecom Systems", "High Power Battery-Powered System"], "ordering_information": [{"part_number": "LT8390", "order_device": "LT8390EFE#PBF", "package_type": "28-Lead Plastic TSSOP", "pin_count": "28", "min_operation_temp": "-40", "max_operation_temp": "125", "marking": "LT8390FE", "carrier_description": "LEAD FREE FINISH", "application_grade": "Industry"}, {"part_number": "LT8390", "order_device": "LT8390EFE#TRPBF", "package_type": "28-Lead Plastic TSSOP", "pin_count": "28", "min_operation_temp": "-40", "max_operation_temp": "125", "marking": "LT8390FE", "carrier_description": "TAPE AND REEL", "application_grade": "Industry"}, {"part_number": "LT8390", "order_device": "LT8390IFE#PBF", "package_type": "28-Lead Plastic TSSOP", "pin_count": "28", "min_operation_temp": "-40", "max_operation_temp": "125", "marking": "LT8390FE", "carrier_description": "LEAD FREE FINISH", "application_grade": "Industry"}, {"part_number": "LT8390", "order_device": "LT8390IFE#TRPBF", "package_type": "28-Lead Plastic TSSOP", "pin_count": "28", "min_operation_temp": "-40", "max_operation_temp": "125", "marking": "LT8390FE", "carrier_description": "TAPE AND REEL", "application_grade": "Industry"}, {"part_number": "LT8390", "order_device": "LT8390JFE#PBF", "package_type": "28-Lead Plastic TSSOP", "pin_count": "28", "min_operation_temp": "-40", "max_operation_temp": "150", "marking": "LT8390FE", "carrier_description": "LEAD FREE FINISH", "application_grade": "Auto"}, {"part_number": "LT8390", "order_device": "LT8390JFE#TRPBF", "package_type": "28-Lead Plastic TSSOP", "pin_count": "28", "min_operation_temp": "-40", "max_operation_temp": "150", "marking": "LT8390FE", "carrier_description": "TAPE AND REEL", "application_grade": "Auto"}, {"part_number": "LT8390", "order_device": "LT8390HFE#PBF", "package_type": "28-Lead Plastic TSSOP", "pin_count": "28", "min_operation_temp": "-40", "max_operation_temp": "150", "marking": "LT8390FE", "carrier_description": "LEAD FREE FINISH", "application_grade": "Auto"}, {"part_number": "LT8390", "order_device": "LT8390HFE#TRPBF", "package_type": "28-Lead Plastic TSSOP", "pin_count": "28", "min_operation_temp": "-40", "max_operation_temp": "150", "marking": "LT8390FE", "carrier_description": "TAPE AND REEL", "application_grade": "Auto"}, {"part_number": "LT8390", "order_device": "LT8390EUFD#PBF", "package_type": "28-Lead (4mm x 5mm) Plastic QFN", "pin_count": "28", "min_operation_temp": "-40", "max_operation_temp": "125", "marking": "8390", "carrier_description": "LEAD FREE FINISH", "application_grade": "Industry"}, {"part_number": "LT8390", "order_device": "LT8390EUFD#TRPBF", "package_type": "28-Lead (4mm x 5mm) Plastic QFN", "pin_count": "28", "min_operation_temp": "-40", "max_operation_temp": "125", "marking": "8390", "carrier_description": "TAPE AND REEL", "application_grade": "Industry"}, {"part_number": "LT8390", "order_device": "LT8390IUFD#PBF", "package_type": "28-Lead (4mm x 5mm) Plastic QFN", "pin_count": "28", "min_operation_temp": "-40", "max_operation_temp": "125", "marking": "8390", "carrier_description": "LEAD FREE FINISH", "application_grade": "Industry"}, {"part_number": "LT8390", "order_device": "LT8390IUFD#TRPBF", "package_type": "28-Lead (4mm x 5mm) Plastic QFN", "pin_count": "28", "min_operation_temp": "-40", "max_operation_temp": "125", "marking": "8390", "carrier_description": "TAPE AND REEL", "application_grade": "Industry"}, {"part_number": "LT8390", "order_device": "LT8390JUFD#PBF", "package_type": "28-Lead (4mm x 5mm) Plastic QFN", "pin_count": "28", "min_operation_temp": "-40", "max_operation_temp": "150", "marking": "8390", "carrier_description": "LEAD FREE FINISH", "application_grade": "Auto"}, {"part_number": "LT8390", "order_device": "LT8390JUFD#TRPBF", "package_type": "28-Lead (4mm x 5mm) Plastic QFN", "pin_count": "28", "min_operation_temp": "-40", "max_operation_temp": "150", "marking": "8390", "carrier_description": "TAPE AND REEL", "application_grade": "Auto"}, {"part_number": "LT8390", "order_device": "LT8390HUFD#PBF", "package_type": "28-Lead (4mm x 5mm) Plastic QFN", "pin_count": "28", "min_operation_temp": "-40", "max_operation_temp": "150", "marking": "8390", "carrier_description": "LEAD FREE FINISH", "application_grade": "Auto"}, {"part_number": "LT8390", "order_device": "LT8390HUFD#TRPBF", "package_type": "28-Lead (4mm x 5mm) Plastic QFN", "pin_count": "28", "min_operation_temp": "-40", "max_operation_temp": "150", "marking": "8390", "carrier_description": "TAPE AND REEL", "application_grade": "Auto"}], "pin_function": [{"product_part_number": "LT8390", "package_type": "TSSOP", "pins": [{"pin_number": "1", "pin_name": "BG1", "pin_description": "Buck Side Bottom Gate Drive. Drives the gate of buck side bottom N-channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "2", "pin_name": "BST1", "pin_description": "Buck Side Bootstrap Floating Driver Supply. The BST1 pin has an integrated bootstrap Sc<PERSON><PERSON>ky diode from the INTVCC pin and requires an external bootstrap capacitor to the SW1 pin. The BST1 pin swings from a diode voltage drop below INTVCC to (VIN + INTVCC)."}, {"pin_number": "3", "pin_name": "SW1", "pin_description": "Buck Side Switch Node. The SW1 pin swings from a Schottky diode voltage drop below ground up to VIN."}, {"pin_number": "4", "pin_name": "TG1", "pin_description": "Buck Side Top Gate Drive. Drives the gate of buck side top N-channel MOSFET with a voltage swing from SW1 to BST1."}, {"pin_number": "5", "pin_name": "LSP", "pin_description": "Positive Terminal of the Buck Side Inductor Current Sense Resistor (RSENSE). Ensure accurate current sense with <PERSON><PERSON> connection."}, {"pin_number": "6", "pin_name": "LSN", "pin_description": "Negative Terminal of the Buck Side Inductor Current Sense Resistor (RSENSE). Ensure accurate current sense with <PERSON><PERSON> connection."}, {"pin_number": "7", "pin_name": "VIN", "pin_description": "Input Supply. The VIN pin must be tied to the power input to determine the buck, buck-boost, or boost operation regions. Locally bypass this pin to ground with a minimum 1μF ceramic capacitor."}, {"pin_number": "8", "pin_name": "INTVCC", "pin_description": "Internal 5V Linear Regulator Output. The INTVCC linear regulator is supplied from the VIN pin, and powers the internal control circuitry and gate drivers. Locally bypass this pin to ground with a minimum 4.7μF ceramic capacitor."}, {"pin_number": "9", "pin_name": "EN/UVLO", "pin_description": "Enable and Undervoltage Lockout. Force the pin below 0.3V to shut down the part and reduce VIN quiescent current below 2μA. Force the pin above 1.233V for normal operation. The accurate 1.220V falling threshold can be used to program an undervoltage lockout (UVLO) threshold with a resistor divider from VIN to ground. An accurate 2.5μA pull-down current allows the programming of VIN UVLO hysteresis. If neither function is used, tie this pin directly to VIN."}, {"pin_number": "10", "pin_name": "TEST", "pin_description": "Factory Test. This pin is used for testing purpose only and must be directly connected to ground for the part to operate properly."}, {"pin_number": "11", "pin_name": "LOADEN", "pin_description": "Load Switch Enable Input. The LOADEN pin is used to control the ON/OFF of the high side PMOS load switch. If the load switch control is not used, tie this pin to VREF or INTVCC. Forcing the pin low turns off TG1 and TG2, turns on BG1 and BG2, disconnects the VC pin from all internal loads, and turns off LOADTG."}, {"pin_number": "12", "pin_name": "VREF", "pin_description": "Voltage Reference Output. The VREF pin provides an accurate 2V reference capable of supplying 1mA current. Locally bypass this pin to ground with a 0.47μF ceramic capacitor."}, {"pin_number": "13", "pin_name": "CTRL", "pin_description": "Control Input for ISP/ISN Current Sense Threshold. The CTRL pin is used to program the ISP/ISN current limit."}, {"pin_number": "14", "pin_name": "ISP", "pin_description": "Positive Terminal of the ISP/ISN Current Sense Resistor (RIS). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "15", "pin_name": "ISN", "pin_description": "Negative Terminal of the ISP/ISN Current Sense Resistor (RIS). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "16", "pin_name": "ISMON", "pin_description": "ISP/ISN Current Sense Monitor Output. The ISMON pin generates a voltage that is equal to ten times V(ISP-ISN) plus 0.25V offset voltage. For parallel applications, tie the master LT8390 ISMON pin to the slave LT8390 CTRL pin."}, {"pin_number": "17", "pin_name": "PGOOD", "pin_description": "Power Good Open Drain Output. The PGOOD pin is pulled low when the FB pin is within ±10% of the final regulation voltage. To function, the pin requires an external pull-up resistor."}, {"pin_number": "18", "pin_name": "SS", "pin_description": "Soft-Start Timer Setting. The SS pin is used to set soft-start timer by connecting a capacitor to ground. An internal 12.5μA pull-up current charging the external SS capacitor gradually ramps up FB regulation voltage. A 0.1μF capacitor is recommended on this pin. Using a single resistor from SS to VREF, the LT8390 can be set in three different fault protection modes during output short-circuit condition: hiccup (no resistor), latch-off (499kΩ), and keep-running (100kΩ)."}, {"pin_number": "19", "pin_name": "FB", "pin_description": "Voltage Loop Feedback Input. The FB pin is used for constant-voltage regulation and output fault protection. The internal error amplifier with its output VC regulates VFB to 1.00V through the DC/DC converter."}, {"pin_number": "20", "pin_name": "VC", "pin_description": "Error Amplifier Output to Set Inductor Current Comparator Threshold. The VC pin is used to compensate the control loop with an external RC network. During LOADEN low state, the VC pin is disconnected from all internal loads to store its voltage information."}, {"pin_number": "21", "pin_name": "RT", "pin_description": "Switching Frequency Setting. Connect a resistor from this pin to ground to set the internal oscillator frequency from 150kHz to 650kHz."}, {"pin_number": "22", "pin_name": "SYNC/SPRD", "pin_description": "Switching Frequency Synchronization or Spread Spectrum. Ground this pin for switching at internal oscillator frequency. Apply a clock signal for external frequency synchronization. Tie to INTVCC for ±15% triangle spread spectrum around internal oscillator frequency."}, {"pin_number": "23", "pin_name": "LOADTG", "pin_description": "High Side PMOS Load Switch Top Gate Drive. A buffered and inverted version of the LOADEN input signal, the LOADTG pin drives an external high side PMOS load switch with a voltage swing from the higher voltage of (VOUT-5V) and 1.2V to VOUT. Leave this pin unconnected if not used."}, {"pin_number": "24", "pin_name": "VOUT", "pin_description": "Output Supply. The VOUT pin must be tied to the power output to determine the buck, buck-boost, or boost operation regions. The VOUT pin also serves as positive rail for the LOADTG drive. Locally bypass this pin to ground with a minimum 1μF ceramic capacitor."}, {"pin_number": "25", "pin_name": "TG2", "pin_description": "Boost Side Top Gate Drive. Drives the gate of boost side top N-Channel MOSFET with a voltage swing from SW2 to BST2."}, {"pin_number": "26", "pin_name": "SW2", "pin_description": "Boost Side Switch Node. The SW2 pin swings from a <PERSON>hottky diode voltage drop below ground to VOUT."}, {"pin_number": "27", "pin_name": "BST2", "pin_description": "Boost Side Bootstrap Floating Driver Supply. The BST2 pin has an integrated bootstrap <PERSON><PERSON><PERSON>ky diode from the INTVCC pin and requires an external bootstrap capacitor to the SW2 pin. The BST2 pin swings from a diode voltage drop below INTVCC to (VOUT + INTVCC)."}, {"pin_number": "28", "pin_name": "BG2", "pin_description": "Boost Side Bottom Gate Drive. Drives the gate of boost side bottom N-channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "29", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Solder the exposed pad directly to the ground plane."}]}, {"product_part_number": "LT8390", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "TG1", "pin_description": "Buck Side Top Gate Drive. Drives the gate of buck side top N-channel MOSFET with a voltage swing from SW1 to BST1."}, {"pin_number": "2", "pin_name": "LSP", "pin_description": "Positive Terminal of the Buck Side Inductor Current Sense Resistor (RSENSE). Ensure accurate current sense with <PERSON><PERSON> connection."}, {"pin_number": "3", "pin_name": "LSN", "pin_description": "Negative Terminal of the Buck Side Inductor Current Sense Resistor (RSENSE). Ensure accurate current sense with <PERSON><PERSON> connection."}, {"pin_number": "4", "pin_name": "VIN", "pin_description": "Input Supply. The VIN pin must be tied to the power input to determine the buck, buck-boost, or boost operation regions. Locally bypass this pin to ground with a minimum 1μF ceramic capacitor."}, {"pin_number": "5", "pin_name": "INTVCC", "pin_description": "Internal 5V Linear Regulator Output. The INTVCC linear regulator is supplied from the VIN pin, and powers the internal control circuitry and gate drivers. Locally bypass this pin to ground with a minimum 4.7μF ceramic capacitor."}, {"pin_number": "6", "pin_name": "EN/UVLO", "pin_description": "Enable and Undervoltage Lockout. Force the pin below 0.3V to shut down the part and reduce VIN quiescent current below 2μA. Force the pin above 1.233V for normal operation. The accurate 1.220V falling threshold can be used to program an undervoltage lockout (UVLO) threshold with a resistor divider from VIN to ground. An accurate 2.5μA pull-down current allows the programming of VIN UVLO hysteresis. If neither function is used, tie this pin directly to VIN."}, {"pin_number": "7", "pin_name": "TEST", "pin_description": "Factory Test. This pin is used for testing purpose only and must be directly connected to ground for the part to operate properly."}, {"pin_number": "8", "pin_name": "LOADEN", "pin_description": "Load Switch Enable Input. The LOADEN pin is used to control the ON/OFF of the high side PMOS load switch. If the load switch control is not used, tie this pin to VREF or INTVCC. Forcing the pin low turns off TG1 and TG2, turns on BG1 and BG2, disconnects the VC pin from all internal loads, and turns off LOADTG."}, {"pin_number": "9", "pin_name": "VREF", "pin_description": "Voltage Reference Output. The VREF pin provides an accurate 2V reference capable of supplying 1mA current. Locally bypass this pin to ground with a 0.47μF ceramic capacitor."}, {"pin_number": "10", "pin_name": "CTRL", "pin_description": "Control Input for ISP/ISN Current Sense Threshold. The CTRL pin is used to program the ISP/ISN current limit."}, {"pin_number": "11", "pin_name": "ISP", "pin_description": "Positive Terminal of the ISP/ISN Current Sense Resistor (RIS). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "12", "pin_name": "ISN", "pin_description": "Negative Terminal of the ISP/ISN Current Sense Resistor (RIS). Ensure accurate current sense with <PERSON>lvin connection."}, {"pin_number": "13", "pin_name": "ISMON", "pin_description": "ISP/ISN Current Sense Monitor Output. The ISMON pin generates a voltage that is equal to ten times V(ISP-ISN) plus 0.25V offset voltage. For parallel applications, tie the master LT8390 ISMON pin to the slave LT8390 CTRL pin."}, {"pin_number": "14", "pin_name": "PGOOD", "pin_description": "Power Good Open Drain Output. The PGOOD pin is pulled low when the FB pin is within ±10% of the final regulation voltage. To function, the pin requires an external pull-up resistor."}, {"pin_number": "15", "pin_name": "SS", "pin_description": "Soft-Start Timer Setting. The SS pin is used to set soft-start timer by connecting a capacitor to ground. An internal 12.5μA pull-up current charging the external SS capacitor gradually ramps up FB regulation voltage. A 0.1μF capacitor is recommended on this pin. Using a single resistor from SS to VREF, the LT8390 can be set in three different fault protection modes during output short-circuit condition: hiccup (no resistor), latch-off (499kΩ), and keep-running (100kΩ)."}, {"pin_number": "16", "pin_name": "FB", "pin_description": "Voltage Loop Feedback Input. The FB pin is used for constant-voltage regulation and output fault protection. The internal error amplifier with its output VC regulates VFB to 1.00V through the DC/DC converter."}, {"pin_number": "17", "pin_name": "VC", "pin_description": "Error Amplifier Output to Set Inductor Current Comparator Threshold. The VC pin is used to compensate the control loop with an external RC network. During LOADEN low state, the VC pin is disconnected from all internal loads to store its voltage information."}, {"pin_number": "18", "pin_name": "RT", "pin_description": "Switching Frequency Setting. Connect a resistor from this pin to ground to set the internal oscillator frequency from 150kHz to 650kHz."}, {"pin_number": "19", "pin_name": "SYNC/SPRD", "pin_description": "Switching Frequency Synchronization or Spread Spectrum. Ground this pin for switching at internal oscillator frequency. Apply a clock signal for external frequency synchronization. Tie to INTVCC for ±15% triangle spread spectrum around internal oscillator frequency."}, {"pin_number": "20", "pin_name": "LOADTG", "pin_description": "High Side PMOS Load Switch Top Gate Drive. A buffered and inverted version of the LOADEN input signal, the LOADTG pin drives an external high side PMOS load switch with a voltage swing from the higher voltage of (VOUT-5V) and 1.2V to VOUT. Leave this pin unconnected if not used."}, {"pin_number": "21", "pin_name": "VOUT", "pin_description": "Output Supply. The VOUT pin must be tied to the power output to determine the buck, buck-boost, or boost operation regions. The VOUT pin also serves as positive rail for the LOADTG drive. Locally bypass this pin to ground with a minimum 1μF ceramic capacitor."}, {"pin_number": "22", "pin_name": "TG2", "pin_description": "Boost Side Top Gate Drive. Drives the gate of boost side top N-Channel MOSFET with a voltage swing from SW2 to BST2."}, {"pin_number": "23", "pin_name": "SW2", "pin_description": "Boost Side Switch Node. The SW2 pin swings from a <PERSON>hottky diode voltage drop below ground to VOUT."}, {"pin_number": "24", "pin_name": "BST2", "pin_description": "Boost Side Bootstrap Floating Driver Supply. The BST2 pin has an integrated bootstrap <PERSON><PERSON><PERSON>ky diode from the INTVCC pin and requires an external bootstrap capacitor to the SW2 pin. The BST2 pin swings from a diode voltage drop below INTVCC to (VOUT + INTVCC)."}, {"pin_number": "25", "pin_name": "BG2", "pin_description": "Boost Side Bottom Gate Drive. Drives the gate of boost side bottom N-channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "26", "pin_name": "BG1", "pin_description": "Buck Side Bottom Gate Drive. Drives the gate of buck side bottom N-channel MOSFET with a voltage swing from ground to INTVCC."}, {"pin_number": "27", "pin_name": "BST1", "pin_description": "Buck Side Bootstrap Floating Driver Supply. The BST1 pin has an integrated bootstrap Sc<PERSON><PERSON>ky diode from the INTVCC pin and requires an external bootstrap capacitor to the SW1 pin. The BST1 pin swings from a diode voltage drop below INTVCC to (VIN + INTVCC)."}, {"pin_number": "28", "pin_name": "SW1", "pin_description": "Buck Side Switch Node. The SW1 pin swings from a Schottky diode voltage drop below ground up to VIN."}, {"pin_number": "29", "pin_name": "GND (Exposed Pad)", "pin_description": "Ground. Solder the exposed pad directly to the ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT8390", "family_comparison": [{"part_number": "LT8390A", "description": "60V 2MHz Synchronous 4-Switch Buck-Boost Controller with Spread Spectrum", "comments": "VIN: 4V to 60V, VOUT: 0V to 60V, ±1.5% Voltage Accuracy, ±3% Current Accuracy, TSSOP-28 and 4mm × 5mm QFN-28"}, {"part_number": "LT8391", "description": "60V Synchronous 4-Switch Buck-Boost LED Controller with Spread Spectrum", "comments": "VIN: 4V to 60V, VOUT: 0V to 60V, ±3% Current Accuracy, Internal and External PWM Dimming, TSSOP-28 and 4mm × 5mm QFN-28"}, {"part_number": "LT3790", "description": "60V Synchronous 4-<PERSON><PERSON> <PERSON>-<PERSON>ost Controller", "comments": "VIN: 4.7V to 60V, VOUT: 1.2V to 60V, Regulates VOUT, IOUT, IIN, TSSOP-38"}, {"part_number": "LT8705", "description": "80V VIN and VOUT Synchronous 4-Switch Buck-Boost DC/DC Controller", "comments": "VIN: 2.8V to 80V, VOUT: 1.3V to 80V, Regulates VOUT, IOUT, VIN, IIN, 5mm × 7mm QFN-38 and Modified TSSOP-38 for High Voltage"}, {"part_number": "LTC®3789", "description": "High Efficiency Synchronous 4-<PERSON><PERSON> <PERSON>-Boost Controller", "comments": "VIN: 4V to 38V, VOUT: 0.8V to 38V, Regulates VOUT, IOUT or IIN, 5mm × 5mm QFN-32 and SSOP-24"}, {"part_number": "LTC3780", "description": "High Efficiency Synchronous 4-<PERSON><PERSON> <PERSON>-Boost Controller", "comments": "VIN: 4V to 36V, VOUT: 0.8V to 30V, Regulates VOUT, 4mm × 5mm QFN-28 and SSOP-28"}], "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "60V", "min_input_voltage": "4V", "max_output_voltage": "60V", "min_output_voltage": "1V", "max_output_current": "依赖外部元件", "max_switch_frequency": "0.65MHz", "quiescent_current": "2100μA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "可编程", "operation_mode": "同步", "output_voltage_config_method": "外部电阻分压", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Pulse Skipping", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "1V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.65", "height": "1.2", "length": "9.8", "width": "4.4", "type": "TSSOP", "pin_count": "1"}]}