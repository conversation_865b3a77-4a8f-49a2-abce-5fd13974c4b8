{"part_number": "LTC3106", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "300mA Low Voltage Buck-Boost Converter with PowerPath and 1.6µA Quiescent Current", "features": ["Dual Input Buck-Boost with Integrated PowerPath™ Manager", "Ultralow Start-Up Voltages: 850mV Start with No Backup Source, 300mV with a Backup Source", "Compatible with Primary or Rechargeable Backup Batteries", "Digitally Selectable VOUT and VSTORE", "Maximum Power Point Control", "Ultralow Quiescent Current: 1.6μA", "Regulated Output with VIN or VSTORE Above, Below or Equal to the Output", "Optional Backup Battery Trickle Charger", "Shelf Mode Disconnect Function to Preserve Battery Shelf Life", "Burst Mode® Operation", "Accurate RUN Pin Threshold", "Power Good Output Voltage Indicator", "Selectable Peak Current Limit: 90mA/650mA", "Available in Thermally Enhanced 3mm × 4mm 16-Pin QFN and 20-Pin TSSOP Packages"], "description": "The LTC®3106 is a highly integrated, ultralow voltage buck-boost DC/DC converter with automatic PowerPath management optimized for multisource, low power systems. At no load, the LTC3106 draws only 1.6µA while creating an output voltage up to 5V from either input source. If the primary power source is unavailable, the LTC3106 seamlessly switches to the backup power source. The LTC3106 is compatible with either rechargeable or primary cell batteries and can trickle charge a backup battery whenever there is an energy surplus available. Optional maximum power point control ensures power transfer is optimized between power source and load. The output voltage and backup voltage, VSTORE, are programmed digitally, reducing the required number of external components. Zero power Shelf Mode ensures that the backup battery will remain charged if left connected to the LTC3106 for an extended time. Additional features include an accurate turn-on voltage, a power good indicator for VOUT, a user selectable 100mA peak current limit setting for lower power applications, thermal shutdown as well as user selectable backup power and output voltages.", "applications": ["Wireless Sensor Networks", "Home or Office Building Automation", "Energy Harvesting", "Remote Sensors"], "ordering_information": [{"part_number": "LTC3106", "order_device": "LTC3106EUDC#PBF", "package_type": "QFN", "package_drawing_code": "UDC", "carrier_description": "<PERSON><PERSON>", "marking": "LGQH", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "可编程"}, {"part_number": "LTC3106", "order_device": "LTC3106EUDC#TRPBF", "package_type": "QFN", "package_drawing_code": "UDC", "carrier_description": "Ta<PERSON> and Reel", "marking": "LGQH", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "可编程"}, {"part_number": "LTC3106", "order_device": "LTC3106IUDC#PBF", "package_type": "QFN", "package_drawing_code": "UDC", "carrier_description": "<PERSON><PERSON>", "marking": "LGQH", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "可编程"}, {"part_number": "LTC3106", "order_device": "LTC3106IUDC#TRPBF", "package_type": "QFN", "package_drawing_code": "UDC", "carrier_description": "Ta<PERSON> and Reel", "marking": "LGQH", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "可编程"}, {"part_number": "LTC3106", "order_device": "LTC3106EFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE", "carrier_description": "<PERSON><PERSON>", "marking": "LTC3106FE", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "可编程"}, {"part_number": "LTC3106", "order_device": "LTC3106EFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE", "carrier_description": "Ta<PERSON> and Reel", "marking": "LTC3106FE", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "可编程"}, {"part_number": "LTC3106", "order_device": "LTC3106IFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE", "carrier_description": "<PERSON><PERSON>", "marking": "LTC3106FE", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "可编程"}, {"part_number": "LTC3106", "order_device": "LTC3106IFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE", "carrier_description": "Ta<PERSON> and Reel", "marking": "LTC3106FE", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "可编程"}], "pin_function": [{"product_part_number": "LTC3106", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "NC", "pin_description": "No Connect. Not electrically connected internally. May be connected to PCB ground or left floating."}, {"pin_number": "2", "pin_name": "VOUT", "pin_description": "Programmable Output Voltage. Connect at least a 22µF low ESR capacitor to GND as close to the part as possible. Capacitor size may increase depending on output voltage ripple and load current requirements."}, {"pin_number": "3", "pin_name": "VAUX", "pin_description": "Auxiliary Voltage. This pin is a generated voltage rail used to power internal circuitry only. Connect a 2.2µF minimum ceramic capacitor to GND as close to the part as possible."}, {"pin_number": "4", "pin_name": "VCC", "pin_description": "Internal Supply Rail. Do not load. Used for powering internal circuitry and biasing the programming inputs only. Decouple with a 0.1µF ceramic capacitor placed as close to the part as possible."}, {"pin_number": "5", "pin_name": "OS1", "pin_description": "VOUT Select Programming Inputs. Connect the pins to ground or VCC to program the output voltage according to Table 1."}, {"pin_number": "6", "pin_name": "OS2", "pin_description": "VOUT Select Programming Inputs. Connect the pins to ground or VCC to program the output voltage according to Table 1."}, {"pin_number": "7", "pin_name": "PGOOD", "pin_description": "Power Good Indicator. Open-drain output that is pulled to ground if VOUT falls 8% below its programmed voltage."}, {"pin_number": "8", "pin_name": "MPP", "pin_description": "Set Point Input for Maximum Power Point Control. Connect a resistor from MPP to GND to program the activation point for the MPP comparator."}, {"pin_number": "9", "pin_name": "SS2", "pin_description": "VSTORE Select Programming Inputs. Connect the pins to ground or VCC to program the VSTORE voltage range according to Table 2. Only valid if PRI is low."}, {"pin_number": "10", "pin_name": "SS1", "pin_description": "VSTORE Select Programming Inputs. Connect the pins to ground or VCC to program the VSTORE voltage range according to Table 2. Only valid if PRI is low."}, {"pin_number": "11", "pin_name": "PRI", "pin_description": "Primary Battery Enable Input. Tie to VCC to enable the use of a non-rechargeable primary battery and to disable VSTORE pin charge capability."}, {"pin_number": "12", "pin_name": "ILIMSEL", "pin_description": "Current Limit Input Select. Tie to GND to disable the automatic power adjust feature and operate at the lowest peak current or tie to VCC to enable the power adjust feature for operation at higher peak inductor currents."}, {"pin_number": "13", "pin_name": "RUN", "pin_description": "Input to enable the IC and to set custom VIN undervoltage thresholds. The accurate RUN threshold is set at 600mV and enables VIN as an input."}, {"pin_number": "14", "pin_name": "ENVSTR", "pin_description": "Enable VSTORE Input. Tie to VSTORE to enable VSTORE as a backup input. Grounding this pin disables the use of VSTORE as a backup input source."}, {"pin_number": "15", "pin_name": "GND", "pin_description": "Connect to PCB ground for internal electrical ground connection and for rated thermal performance."}, {"pin_number": "16", "pin_name": "VIN", "pin_description": "Main Supply Input. Decouple with minimum 10µF capacitor."}, {"pin_number": "17", "pin_name": "SW2", "pin_description": "Buck-Boost Converter Switch Pins. Connect inductor between SW1 and SW2 pins."}, {"pin_number": "18", "pin_name": "SW1", "pin_description": "Buck-Boost Converter Switch Pins. Connect inductor between SW1 and SW2 pins."}, {"pin_number": "19", "pin_name": "VSTORE", "pin_description": "Secondary Supply Input. A primary or secondary rechargeable battery may be connected from this pin to GND to power the system in the event the input voltage is lost."}, {"pin_number": "20", "pin_name": "VCAP", "pin_description": "VSTORE Isolation Pin. Isolates VSTORE from the decoupling capacitor for low capacity backup batteries."}, {"pin_number": "21", "pin_name": "Exposed Pad", "pin_description": "Connect to PCB ground for internal electrical ground connection and for rated thermal performance."}]}, {"product_part_number": "LTC3106", "package_type": "TSSOP", "pins": [{"pin_number": "1", "pin_name": "VSTORE", "pin_description": "Secondary Supply Input. A primary or secondary rechargeable battery may be connected from this pin to GND to power the system in the event the input voltage is lost."}, {"pin_number": "2", "pin_name": "VCAP", "pin_description": "VSTORE Isolation Pin. Isolates VSTORE from the decoupling capacitor for low capacity backup batteries."}, {"pin_number": "3", "pin_name": "VOUT", "pin_description": "Programmable Output Voltage. Connect at least a 22µF low ESR capacitor to GND as close to the part as possible."}, {"pin_number": "4", "pin_name": "NC", "pin_description": "No Connect. Not electrically connected internally. May be connected to PCB ground or left floating."}, {"pin_number": "5", "pin_name": "VAUX", "pin_description": "Auxiliary Voltage. This pin is a generated voltage rail used to power internal circuitry only. Connect a 2.2µF minimum ceramic capacitor to GND as close to the part as possible."}, {"pin_number": "6", "pin_name": "VCC", "pin_description": "Internal Supply Rail. Do not load. Used for powering internal circuitry and biasing the programming inputs only. Decouple with a 0.1µF ceramic capacitor placed as close to the part as possible."}, {"pin_number": "7", "pin_name": "OS1", "pin_description": "VOUT Select Programming Inputs. Connect the pins to ground or VCC to program the output voltage according to Table 1."}, {"pin_number": "8", "pin_name": "OS2", "pin_description": "VOUT Select Programming Inputs. Connect the pins to ground or VCC to program the output voltage according to Table 1."}, {"pin_number": "9", "pin_name": "PGOOD", "pin_description": "Power Good Indicator. Open-drain output that is pulled to ground if VOUT falls 8% below its programmed voltage."}, {"pin_number": "10", "pin_name": "MPP", "pin_description": "Set Point Input for Maximum Power Point Control. Connect a resistor from MPP to GND to program the activation point for the MPP comparator."}, {"pin_number": "11", "pin_name": "SS2", "pin_description": "VSTORE Select Programming Inputs. Connect the pins to ground or VCC to program the VSTORE voltage range according to Table 2. Only valid if PRI is low."}, {"pin_number": "12", "pin_name": "SS1", "pin_description": "VSTORE Select Programming Inputs. Connect the pins to ground or VCC to program the VSTORE voltage range according to Table 2. Only valid if PRI is low."}, {"pin_number": "13", "pin_name": "PRI", "pin_description": "Primary Battery Enable Input. Tie to VCC to enable the use of a non-rechargeable primary battery and to disable VSTORE pin charge capability."}, {"pin_number": "14", "pin_name": "ILIMSEL", "pin_description": "Current Limit Input Select. Tie to GND to disable the automatic power adjust feature and operate at the lowest peak current or tie to VCC to enable the power adjust feature for operation at higher peak inductor currents."}, {"pin_number": "15", "pin_name": "RUN", "pin_description": "Input to enable the IC and to set custom VIN undervoltage thresholds. The accurate RUN threshold is set at 600mV and enables VIN as an input."}, {"pin_number": "16", "pin_name": "ENVSTR", "pin_description": "Enable VSTORE Input. Tie to VSTORE to enable VSTORE as a backup input. Grounding this pin disables the use of VSTORE as a backup input source."}, {"pin_number": "17", "pin_name": "GND", "pin_description": "Connect to PCB ground for internal electrical ground connection and for rated thermal performance."}, {"pin_number": "18", "pin_name": "VIN", "pin_description": "Main Supply Input. Decouple with minimum 10µF capacitor."}, {"pin_number": "19", "pin_name": "SW2", "pin_description": "Buck-Boost Converter Switch Pins. Connect inductor between SW1 and SW2 pins."}, {"pin_number": "20", "pin_name": "SW1", "pin_description": "Buck-Boost Converter Switch Pins. Connect inductor between SW1 and SW2 pins."}, {"pin_number": "21", "pin_name": "Exposed Pad", "pin_description": "Connect to PCB ground for internal electrical ground connection and for rated thermal performance."}]}], "datasheet_cn": "未找到", "datasheet_en": "3106f", "family_comparison": "PART NUMBER | DESCRIPTION | COMMENTS\n---|---|---\nLTC3103 | 15V, 300mA Synchronous Step-Down DC/DC Converter with Ultralow Quiescent Current | VIN: 2.5V to 15V, VOUT(MIN) = 0.6V, IQ = 1.8µA, ISD = 1µA 3mm × 3mm DFN-10, MSOP-10\nLTC3105 | 400mA Step-Up DC/DC Converter with Maximum Power Point Control and 250mV Start-Up | VIN: 0.225V to 5V, VOUT(MIN) Adj. 1.5V to 5V, IQ = 24µA, ISD < 1µA, 3mm × 3mm DFN-12, MSOP-12\nLTC3107 | Ultralow Voltage Energy Harvester and Primary Battery Life Extender | VIN = 0.02V to 1V, VOUT Tracks VBAT, VBAT = 2V to 4V, IQ = 80nA, VLDO = 2.2V, 3mm × 3mm DFN-10\nLTC3108/LTC3108-1 | Ultralow Voltage Step-Up Converter and Power Managers | VIN: 0.02V to 1V, VOUT(MIN) Fixed 2.35V to 5V, IQ = 6µA, ISD < 1µA, 3mm × 4mm DFN-12, SSOP-16\nLTC3109 | Auto-Polarity, Ultralow Voltage Step-Up Converter and Power Manager | VIN: 0.03V to 1V, VOUT(MIN) Fixed 2.35V to 5V, IQ = 7µA, ISD < 1µA, 4mm × 4mm QFN-20, SSOP-20\nLTC4070 | Li-Ion/Polymer Shunt Battery Charger System | 450nA IQ, 1% Float Voltage Accuracy, 50mA Shunt Current 4.0V/4.1V/4.2V\nLTC4071 | Li-Ion/Polymer Shunt Battery Charger System with Low Battery Disconnect | 550nA IQ, 1% Float Voltage Accuracy, <10nA Low Battery Disconnect, 4.0V/4.1V/4.2V, 8-Lead 2mm × 3mm DFN and MSOP Packages\nLTC3129/LTC3129-1 | Micropower 200mA Synchronous Buck-Boost DC/DC Converter | VIN: 2.42V to 15V, VOUT: 1.4V to 15V, IQ = 1.3µA, ISD = 10nA, MSOP-16E, 3mm × 3mm QFN-16 Packages\nLTC3330/LTC3331 | Nanopower Buck-Boost DC/DC with Energy Harvesting Battery Life Extender | VIN: 2.7V to 20V, VOUT: 1.2V to 5.0V, Enable and Standby Pins, IQ = 750nA, 5mm × 5mm QFN-32 Package\nLTC3388-1/LTC3388-3 | 20V High Efficiency Nanopower Step-Down Regulator | VIN: 2.7V to 20V, VOUT: 1.2V to 5.0V, Enable and Standby Pins, IQ = 720nA, ISD = 400nA, 3mm × 3mm DFN-10, MSOP-10\nLTC3588-1 | Nanopower Energy Harvesting Power Supply | 950nA IQ in Sleep, VOUT: 1.8V, 2.5V, 3.3V, 3.6V, Integrated Bridge Rectifier, MSE-10 and 3mm × 3mm QFN-10 Packages\nLTC3588-2 | Nanopower Energy Harvesting Power Supply | <1µA IQ in Regulation, UVLO Rising = 16V, UVLO Falling = 14V, VOUT = 3.45V, 4.1V, 4.5V 5.0, MSE-10 and 3mm × 3mm QFN-10 Packages\nLTC5800-IPMA | IP Wireless Mote-On-Chip | Ultralow Power Mote, 72-Lead, 10mm × 10mm QFN", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.1V", "min_input_voltage": "0.25V", "max_output_voltage": "5V", "min_output_voltage": "1.8V", "max_output_current": "0.3A", "max_switch_frequency": "未找到", "quiescent_current": "1.6µA", "high_side_mosfet_resistance": "500mΩ", "low_side_mosfet_resistance": "500mΩ", "over_current_protection_threshold": "0.65A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±3%", "output_reference_voltage": "0.6V", "loop_control_mode": "Hysteresis Mode Control"}, "package": [{"pitch": "0.5", "length": "3.5", "width": "4.4", "type": "20-Lead", "pin_count": "2", "height": "0.5"}]}