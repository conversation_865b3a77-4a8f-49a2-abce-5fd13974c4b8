{"part_number": "LM5176-Q1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "升降压控制器", "part_number_title": "LM5176-Q1 55V宽输入电压同步4开关降压/升压控制器", "features": ["符合面向汽车应用的 AEC-Q100 标准 - 温度等级 1：-40℃ 至 125℃ TA", "提供功能安全 - 可帮助进行功能安全系统设计的文档", "单电感降压/升压控制器，用于升压/降压直流/直流转换", "宽 VIN：4.2V (2.5V 偏置) 至 55V (60V 最大输入电压)", "灵活的 VOUT：0.8V 至 55V", "输出电压短路保护", "高效降压/升压转换", "可调开关频率", "可选频率同步和抖动", "集成 2A MOSFET 栅极驱动器", "逐周期电流限制和可选断续模式", "可选输入或输出平均电流限制", "可编程输入 UVLO 和软启动", "电源正常和输出过压保护", "采用 HTSSOP-28 封装", "使用 LM5176-Q1 并借助 WEBENCH Power Designer 创建定制设计方案"], "description": "LM5176-Q1 是一款同步四开关降压/升压直流/直流控制器，能够将输出电压稳定在等于、高于或低于输入电压的某一电压值上。LM5176-Q1 在 4.2V 至 55V (最大绝对值为 60V) 的宽输入电压范围内工作，可支持各种不同的应用。LM5176-Q1 在降压和升压工作模式下均采用电流模式控制，以提供出色的负载和线路调节性能。开关频率可通过外部电阻进行编程，并且可与外部时钟信号同步。该器件还具有可编程的软启动功能，并且提供诸如逐周期电流限制、输入欠压闭锁 (UVLO)、输出过压保护 (OVP) 和热关断等各类保护特性。此外，LM5176-Q1 具有平均输入或输出电流限制、用于减少峰值 EMI 的展频以及持续过载情况下的断续模式保护等选项。", "applications": ["汽车起停系统", "备用电池和超级电容充电", "USB 电力输送", "电池供电型系统", "LED 照明"], "ordering_information": [{"part_number": "LM5176-Q1", "order_device": "LM5176QPWPRQ1", "status": "Active", "package_type": "HTSSOP", "package_drawing_code": "PWP0028V", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5176-Q1", "order_device": "LM5176QPWPRQ1.B", "status": "Active", "package_type": "HTSSOP", "package_drawing_code": "PWP0028V", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5176-Q1", "order_device": "LM5176QPWPTQ1", "status": "Active", "package_type": "HTSSOP", "package_drawing_code": "PWP0028V", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5176-Q1", "order_device": "LM5176QPWPTQ1.B", "status": "Active", "package_type": "HTSSOP", "package_drawing_code": "PWP0028V", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LM5176-Q1", "package_type": "HTSSOP", "pins": [{"pin_number": "1", "pin_name": "EN/UVLO", "pin_description": "使能引脚。对于 EN/UVLO < 0.4V，LM5176-Q1 处于低电流关断模式。对于 EN/UVLO > 1.22V，如果 VCC 超过 VCC UV 阈值，则启用 PWM 功能。"}, {"pin_number": "2", "pin_name": "VIN", "pin_description": "IC 的输入电源引脚。将 VIN 连接到 4.2V 和 55V 之间的电源电压。"}, {"pin_number": "3", "pin_name": "VISNS", "pin_description": "VIN 检测输入。连接到功率级输入轨。"}, {"pin_number": "4", "pin_name": "MODE", "pin_description": "1.38 V < MODE < 2.22 V : CCM, Hiccup Enabled (Set RMODE resistor to AGND = 93.1 kΩ)\n2.6 V < MODE < VCC: CCM, Hiccup Disabled (Set RMODE resistor to AGND = 200 kΩ or connect to VCC)"}, {"pin_number": "5", "pin_name": "DITH", "pin_description": "连接在 DITH 引脚和 AGND 之间的电容器由电流源充电和放电。随着 DITH 引脚上的电压上升和下降，振荡器频率由 RT 电阻器设置的标称频率的 10% 进行调制。将 DITH 引脚接地将禁用抖动功能。在外部同步模式下，DITH 引脚电压被忽略。"}, {"pin_number": "6", "pin_name": "RT/SYNC", "pin_description": "开关频率编程引脚。外部电阻器连接到 RT/SYNC 引脚和 AGND 以设置开关频率。此引脚还可用于将 PWM 控制器与外部时钟同步。"}, {"pin_number": "7", "pin_name": "SLOPE", "pin_description": "连接在 SLOPE 引脚和 AGND 之间的电容器为降压和升压模式下的稳定电流模式操作提供斜率补偿斜坡。"}, {"pin_number": "8", "pin_name": "SS", "pin_description": "软启动编程引脚。SS 引脚和 AGND 引脚之间的电容器编程软启动时间。"}, {"pin_number": "9", "pin_name": "COMP", "pin_description": "误差放大器的输出。连接在 COMP 和 AGND 之间的外部 RC 网络补偿调节器反馈环路。"}, {"pin_number": "10", "pin_name": "AGND", "pin_description": "IC 的模拟地。"}, {"pin_number": "11", "pin_name": "FB", "pin_description": "用于输出电压调节的反馈引脚。将转换器输出的电阻分压器网络连接到 FB 引脚。"}, {"pin_number": "12", "pin_name": "VOSNS", "pin_description": "VOUT 检测输入。连接到功率级输出轨。"}, {"pin_number": "13", "pin_name": "ISNS(-)", "pin_description": "输入或输出电流检测放大器输入。可选的电流检测电阻器连接在 ISNS(+) 和 ISNS(-) 之间，可以位于转换器的输入侧或输出侧。如果 ISNS(+) 和 ISNS(-) 引脚之间的检测电压达到 50 mV，则慢速恒流 (CC) 控制环路变为活动状态，并开始对软启动电容器放电，以将 ISNS(+) 和 ISNS(-) 之间的压降调节到 50 mV。将 ISNS(+) 和 ISNS(-) 短接在一起以禁用此功能。"}, {"pin_number": "14", "pin_name": "ISNS(+)", "pin_description": "输入或输出电流检测放大器输入。可选的电流检测电阻器连接在 ISNS(+) 和 ISNS(-) 之间，可以位于转换器的输入侧或输出侧。如果 ISNS(+) 和 ISNS(-) 引脚之间的检测电压达到 50 mV，则慢速恒流 (CC) 控制环路变为活动状态，并开始对软启动电容器放电，以将 ISNS(+) 和 ISNS(-) 之间的压降调节到 50 mV。将 ISNS(+) 和 ISNS(-) 短接在一起以禁用此功能。"}, {"pin_number": "15", "pin_name": "CSG", "pin_description": "PWM 电流检测放大器的负或接地输入。直接连接到电流检测电阻器的低侧（接地）。"}, {"pin_number": "16", "pin_name": "CS", "pin_description": "PWM 电流检测放大器的正输入。"}, {"pin_number": "17", "pin_name": "PGOOD", "pin_description": "电源良好开漏输出。当 FB 在 0.8V VREF 的 -9%/+10% 调节窗口之外时，PGOOD 被拉低。"}, {"pin_number": "18", "pin_name": "SW2", "pin_description": "升压和降压侧开关节点。"}, {"pin_number": "19", "pin_name": "HDRV2", "pin_description": "高侧栅极驱动器的输出。直接连接到高侧 MOSFET 的栅极。"}, {"pin_number": "20", "pin_name": "BOOT2", "pin_description": "在 BOOT1、BOOT2 引脚和 SW1、SW2 引脚之间需要一个外部电容器，分别为高侧 MOSFET 栅极驱动器提供偏置。"}, {"pin_number": "21", "pin_name": "LDRV2", "pin_description": "低侧栅极驱动器的输出。直接连接到低侧 MOSFET 的栅极。"}, {"pin_number": "22", "pin_name": "PGND", "pin_description": "IC 的功率地。低侧栅极驱动器的高电流接地连接。"}, {"pin_number": "23", "pin_name": "VCC", "pin_description": "VCC 偏置调节器的输出。将电容器连接到地。"}, {"pin_number": "24", "pin_name": "BIAS", "pin_description": "VCC 偏置调节器的可选输入。从外部电源而不是 VIN 为 VCC 供电可以减少高 VIN 时的功率损耗。对于 VBIAS > 8V，VCC 调节器从 BIAS 引脚获取功率。"}, {"pin_number": "25", "pin_name": "LDRV1", "pin_description": "低侧栅极驱动器的输出。直接连接到低侧 MOSFET 的栅极。"}, {"pin_number": "26", "pin_name": "BOOT1", "pin_description": "在 BOOT1、BOOT2 引脚和 SW1、SW2 引脚之间需要一个外部电容器，分别为高侧 MOSFET 栅极驱动器提供偏置。"}, {"pin_number": "27", "pin_name": "HDRV1", "pin_description": "高侧栅极驱动器的输出。直接连接到高侧 MOSFET 的栅极。"}, {"pin_number": "28", "pin_name": "SW1", "pin_description": "升压和降压侧开关节点。"}, {"pin_number": "PowerPAD", "pin_name": "PowerPAD", "pin_description": "The PowerPAD should be soldered to the analog ground for improved power dissipation."}]}], "datasheet_cn": "ZHCSIQ6B", "datasheet_en": "SNVSB46", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "55V", "min_input_voltage": "4.2V", "max_output_voltage": "55V", "min_output_voltage": "1V", "max_output_current": "取决于外部元件", "max_switch_frequency": "600kHz", "quiescent_current": "2.6µA", "high_side_mosfet_resistance": "不适用(外部MOSFET)", "low_side_mosfet_resistance": "不适用(外部MOSFET)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "CCM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "No", "output_discharge": "无", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值/谷值电流模式"}, "package": [{"type": "OPTION", "pitch": "0.65", "height": "1.2", "length": "9.7", "width": "4.4", "pin_count": "1"}]}