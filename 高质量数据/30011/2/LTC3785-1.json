{"part_number": "LTC3785-1", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压-升压(<PERSON><PERSON><PERSON><PERSON>)控制器", "part_number_title": "10V, High Efficiency, Buck-Boost Controller with Power Good", "features": ["Single Inductor Architecture Allows VIN Above, Below or Equal to VOUT", "Power Good Output Indicator", "2.7V to 10V Input and Output Range", "Up to 96% Efficiency", "Up to 10A of Output Current", "All N-channel MOSFETs, No RSENSE™", "True Output Disconnect During Shutdown", "Programmable Current Limit and Soft-Start", "Optional Short-Circuit Shutdown Timer", "Output Overvoltage and Undervoltage Protection", "Programmable Frequency: 100kHz to 1MHz", "Selectable Burst Mode® Operation", "Available in 24-Lead (4mm × 4mm) Exposed Pad QFN Package"], "description": "The LTC®3785-1 is a high power synchronous buck-boost controller that drives all N-channel power MOSFETs from input voltages above, below and equal to the output voltage. With an input range of 2.7V to 10V, the LTC3785-1 is well suited for a wide variety of single or dual cell Li-Ion or multicell alkaline/NiMH applications.\nThe operating frequency can be programmed from 100kHz to 1MHz. The soft-start time and current limit are also programmable. The soft-start capacitor doubles as the fault timer which can program the IC to latch off or recycle after a determined off time. Burst Mode operation is user controlled and can be enabled by driving the mode pin high. The LTC3785-1 includes a power good output that indicates when the output voltage is within 7.5% of its designed setpoint.\nProtection features include foldback current limit, short-circuit and overvoltage protection.", "applications": ["Palmtop Computers", "Handheld Instruments", "Wireless Modems", "Cellular Telephones"], "ordering_information": [{"part_number": "LTC3785-1", "order_device": "LTC3785EUF-1#PBF", "package_type": "QFN", "package_drawing_code": "MO-220 VARIATION (WGGD-X)", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85", "carrier_description": "LEAD FREE FINISH"}, {"part_number": "LTC3785-1", "order_device": "LTC3785EUF-1#TRPBF", "package_type": "QFN", "package_drawing_code": "MO-220 VARIATION (WGGD-X)", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85", "carrier_description": "TAPE AND REEL"}], "pin_function": [{"product_part_number": "LTC3785-1", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "RUN/SS", "pin_description": "Run Control and Soft-Start Input. An internal 1µA charges the soft-start capacitor and will charge to approximately 2.5V. During a current limit fault, the soft-start capacitor will incrementally discharge. Once the pin drops below 1.225V the IC will enter fault mode, turning off the outputs for 32 times the soft-start time. If >5µA (at RUN/SS = 1.225V) is applied externally, the part will latch off after a fault is detected. If >40µA (at RUN/SS = 1.225V) is applied externally, current limit faults will not discharge the SS capacitor."}, {"pin_number": "2", "pin_name": "Vc", "pin_description": "Error Amp Output. A frequency compensation network is connected from this pin to the FB pin to compensate the loop."}, {"pin_number": "3", "pin_name": "FB", "pin_description": "Feedback Pin. Connect resistor divider tap here. The feedback reference voltage is typically 1.225V. The output voltage can be adjusted from 2.7V to 10V."}, {"pin_number": "4", "pin_name": "VSENSE", "pin_description": "Overvoltage and Undervoltage Sense. The overvoltage threshold is internally set 7.5% above the regulated FB voltage and the undervoltage threshold is internally set 7.5% below the FB regulated voltage. This pin can be tied to FB but to optimize the response time it is recommended that a separate voltage divider from VOUT be applied."}, {"pin_number": "5", "pin_name": "ILSET", "pin_description": "Current Limit Set. A resistor from this pin to ground sets the current limit threshold from the ISVIN and ISSW1 pins."}, {"pin_number": "6", "pin_name": "CCM", "pin_description": "Continuous Conduction Mode Control Pin. When set low, the inductor current is allowed to go slightly negative (–15mV referenced to the ISVOUT – ISSW2 pins). When driven high, the reverse current limit is set to the similar value of the forward current limit set by the ILSET pin."}, {"pin_number": "7", "pin_name": "RT", "pin_description": "Oscillator Programming Pin. A resistor from this pin to GND sets the free-running frequency of the IC. fOSC = 2.5e10/RT."}, {"pin_number": "8", "pin_name": "MODE", "pin_description": "Burst Mode Control Pin. MODE = High: Enable Burst Mode Operation. MODE = Low: Disable Burst Mode operation and maintain low noise, constant frequency operation."}, {"pin_number": "9", "pin_name": "PGOOD", "pin_description": "Open Drain Output. PGOOD is pulled to ground when the voltage on VSENSE is not within ±7.5% of its setpoint. PGOOD will also be pulled low when the part is in shutdown or input UVLO."}, {"pin_number": "10", "pin_name": "ISVOUT", "pin_description": "Reverse Current Limit Comparator Non-inverting Input. This pin is normally connected to the drain of the N-channel MOSFET D (TG2 driven)."}, {"pin_number": "11", "pin_name": "VBST2", "pin_description": "Boosted Floating Driver Supply for Boost Switch D. This pin will swing from a diode below VCC up to VOUT + VCC – VDIODE."}, {"pin_number": "12", "pin_name": "TG2", "pin_description": "Top gate drive pins drive the top N-channel MOSFET switches A and D with a voltage swing equal to VCC – VDIODE superimposed on the SW1 and SW2 nodes respectively."}, {"pin_number": "13", "pin_name": "SW2", "pin_description": "Ground Reference for Driver D. Gate drive from TG2 will reference to the common point of output switches C and D."}, {"pin_number": "14", "pin_name": "ISSW2", "pin_description": "Reverse Current Limit Comparator Inverting Input. This pin is normally connected to the source of the N-channel MOSFET D (TG2 driven)."}, {"pin_number": "15", "pin_name": "BG2", "pin_description": "Bottom gate driver pins drive the ground referenced N-channel MOSFET switches B and C."}, {"pin_number": "16", "pin_name": "VDRV", "pin_description": "Driver Supply for Ground Referenced Switches. Connect this pin to VCC potential."}, {"pin_number": "17", "pin_name": "BG1", "pin_description": "Bottom gate driver pins drive the ground referenced N-channel MOSFET switches B and C."}, {"pin_number": "18", "pin_name": "ISSW1", "pin_description": "Forward Current Limit Comparator Non-inverting Input. This pin is normally connected to the source of the N-channel MOSFET A (TG1 driven)."}, {"pin_number": "19", "pin_name": "SW1", "pin_description": "Ground Reference for Driver A. Gate drive from TG1 will reference to the common point of output switches A and B."}, {"pin_number": "20", "pin_name": "TG1", "pin_description": "Top gate drive pins drive the top N-channel MOSFET switches A and D with a voltage swing equal to VCC – VDIODE superimposed on the SW1 and SW2 nodes respectively."}, {"pin_number": "21", "pin_name": "VBST1", "pin_description": "Boosted Floating Driver Supply for the Buck Switch A. This pin will swing from a diode below VCC up to VIN + VCC – VDIODE."}, {"pin_number": "22", "pin_name": "ISVIN", "pin_description": "Forward Current Limit Comparator Inverting Input. This pin is normally connected to the drain of N-channel MOSFET A (TG1 driven)."}, {"pin_number": "23", "pin_name": "VCC", "pin_description": "Internal 4.35V LDO Regulator Output. The driver and control circuits are powered from this voltage to limit the maximum VGS drive voltage. Decouple this pin to power ground with at least a 4.7µF ceramic capacitor. For low VIN applications, VCC can be bootstrapped from VOUT through a <PERSON>hottky diode."}, {"pin_number": "24", "pin_name": "VIN", "pin_description": "Input Supply Pin for the VCC Regulator. A ceramic capacitor of at least 10µF is recommended close to the VIN and GND pins."}, {"pin_number": "25", "pin_name": "Exposed Pad", "pin_description": "The GND and PGND pins are connected to the Exposed Pad which must be connected to the PCB ground for electrical contact and rated thermal performance."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3785-1 Rev <PERSON> (2009-04-01)", "family_comparison": "The 'RELATED PARTS' table on page 20 provides a comparison of the LTC3785 with other parts in the Linear Technology portfolio, highlighting differences in output current, frequency, and features.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "10V", "min_input_voltage": "2.7V", "max_output_voltage": "10V", "min_output_voltage": "2.7V", "max_output_current": "10A", "max_switch_frequency": "1MHz", "quiescent_current": "800µA", "high_side_mosfet_resistance": "不适用(外部控制器)", "low_side_mosfet_resistance": "不适用(外部控制器)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "外部可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.04%", "output_reference_voltage": "1.225V", "loop_control_mode": "电压模式"}, "package": [{"type": "DESCRIPTION", "pitch": "0.5", "length": "4.35", "width": "4", "pin_count": "5", "height": "0.75"}]}