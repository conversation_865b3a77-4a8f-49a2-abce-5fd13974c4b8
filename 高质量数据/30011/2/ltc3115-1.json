{"part_number": "LTC3115-1", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "40V, 2A Synchronous Buck-Boost DC/DC Converter", "features": ["Wide VIN Range: 2.7V to 40V", "Wide Vout Range: 2.7V to 40V", "1A Output Current for VIN ≥ 3.6V, VOUT = 5V", "2A Output Current in Step-Down Operation for VIN ≥ 6V", "Programmable Frequency: 100kHz to 2MHz", "Synchronizable Up to 2MHz with an External Clock", "Up to 95% Efficiency", "30μA No-Load Quiescent Current in Burst Mode® Operation", "Ultralow Noise Buck-Boost PWM", "Internal Soft-Start", "3μA Supply Current in Shutdown", "Programmable Input Undervoltage Lockout", "Small 4mm × 5mm × 0.75mm DFN Package", "Thermally Enhanced 20-Lead TSSOP Package", "AEC-Q100 Qualified for Automotive Applications"], "description": "The LTC®3115-1 is a high voltage monolithic synchronous buck-boost DC/DC converter. Its wide 2.7V to 40V input and output voltage ranges make it well suited to a wide variety of automotive and industrial applications. A proprietary low noise switching algorithm optimizes efficiency with input voltages that are above, below or even equal to the output voltage and ensures seamless transitions between operational modes. Programmable frequency PWM mode operation provides low noise, high efficiency operation and the ability to synchronize switching to an external clock. Switching frequencies up to 2MHz are supported to allow use of small value inductors for miniaturization of the application circuit. Pin selectable Burst Mode operation reduces standby current and improves light load efficiency which, combined with a 3μA shutdown current, make the LTC3115-1 ideally suited for battery-powered applications. Additional features include output disconnect in shutdown, short-circuit protection and internal soft-start. The LTC3115-1 is available in thermally enhanced 16-lead 4mm x 5mm × 0.75mm DFN and 20-lead TSSOP packages.", "applications": ["24V/28V Industrial Applications", "Automotive Power Systems", "Telecom, Servers and Networking Equipment", "FireWire Regulator", "Multiple Power Source Supplies"], "ordering_information": [{"part_number": "LTC3115-1", "order_device": "LTC3115EDHD-1#PBF", "package_type": "DFN", "package_drawing_code": "05-08-1707 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-1", "order_device": "LTC3115EDHD-1#TRPBF", "package_type": "DFN", "package_drawing_code": "05-08-1707 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-1", "order_device": "LTC3115IDHD-1#PBF", "package_type": "DFN", "package_drawing_code": "05-08-1707 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-1", "order_device": "LTC3115IDHD-1#TRPBF", "package_type": "DFN", "package_drawing_code": "05-08-1707 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-1", "order_device": "LTC3115EFE-1#PBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-1", "order_device": "LTC3115EFE-1#TRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-1", "order_device": "LTC3115IFE-1#PBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-1", "order_device": "LTC3115IFE-1#TRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-1", "order_device": "LTC3115HFE-1#PBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3115-1", "order_device": "LTC3115HFE-1#TRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LTC3115-1", "order_device": "LTC3115MPFE-1#PBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}, {"part_number": "LTC3115-1", "order_device": "LTC3115MPFE-1#TRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}, {"part_number": "LTC3115-1", "order_device": "LTC3115EDHD-1#WPBF", "package_type": "DFN", "package_drawing_code": "05-08-1707 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-1", "order_device": "LTC3115EDHD-1#WTRPBF", "package_type": "DFN", "package_drawing_code": "05-08-1707 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-1", "order_device": "LTC3115IDHD-1#WPBF", "package_type": "DFN", "package_drawing_code": "05-08-1707 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-1", "order_device": "LTC3115IDHD-1#WTRPBF", "package_type": "DFN", "package_drawing_code": "05-08-1707 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-1", "order_device": "LTC3115EFE-1#WPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-1", "order_device": "LTC3115EFE-1#WTRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-1", "order_device": "LTC3115IFE-1#WPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3115-1", "order_device": "LTC3115IFE-1#WTRPBF", "package_type": "TSSOP", "package_drawing_code": "05-08-1663 Rev L", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LTC3115-1", "package_type": "DFN", "pins": [{"pin_number": "1", "pin_name": "RUN", "pin_description": "Input to Enable and Disable the IC and Set Custom Input UVLO Thresholds. The RUN pin can be driven by an external logic signal to enable and disable the IC. In addition, the voltage on this pin can be set by a resistor divider connected to the input voltage in order to provide an accurate undervoltage lockout threshold. The IC is enabled if RUN exceeds 1.21V nominally. Once enabled, a 0.5μA current is sourced by the RUN pin to provide hysteresis. To continuously enable the IC, this pin can be tied directly to the input voltage. The RUN pin cannot be forced more than 0.3V above VIN under any condition."}, {"pin_number": "2", "pin_name": "SW2", "pin_description": "Buck-Boost Converter Power Switch Pin. This pin should be connected to one side of the buck-boost inductor."}, {"pin_number": "3", "pin_name": "PVOUT", "pin_description": "Buck-Boost Converter Power Output. This pin should be connected to a low ESR capacitor with a value of at least 10μF. The capacitor should be placed as close to the IC as possible and should have a short return path to ground. In applications with VOUT > 20V that are subject to output overload or short-circuit conditions, it is recommended that a Schottky diode be installed from SW2 (anode) to PVOUT (cathode). In applications subject to output short circuits through an inductive load, it is recommended that a Schottky diode be installed from ground (anode) to PVOUT (cathode) to limit the extent that PVOUT is driven below ground during the short-circuit transient."}, {"pin_number": "4, 5", "pin_name": "GND", "pin_description": "Signal Ground. These pins are the ground connections for the control circuitry of the IC and must be tied to ground in the application."}, {"pin_number": "6", "pin_name": "VC", "pin_description": "Error Amplifier Output. A frequency compensation network must be connected between this pin and FB to stabilize the voltage control loop."}, {"pin_number": "7", "pin_name": "FB", "pin_description": "Feedback Voltage Input. A resistor divider connected to this pin sets the output voltage for the buck-boost converter. The nominal FB voltage is 1000mV. Care should be taken in the routing of connections to this pin in order to minimize stray coupling to the switch pin traces."}, {"pin_number": "8", "pin_name": "RT", "pin_description": "Oscillator Frequency Programming Pin. A resistor placed between this pin and ground sets the switching frequency of the buck-boost converter."}, {"pin_number": "9", "pin_name": "VCC", "pin_description": "Low Voltage Supply Input for IC Control Circuitry. This pin powers internal IC control circuitry and must be connected to the PVCC pin in the application. A 4.7μF or larger bypass capacitor should be connected between this pin and ground. The VCC and PVCC pins must be connected together in the application."}, {"pin_number": "10", "pin_name": "VIN", "pin_description": "Power Supply Connection for Internal Circuitry and the VCC Regulator. This pin provides power to the internal VCC regulator and is the input voltage sense connection for the VIN divider. A 0.1μF bypass capacitor should be connected between this pin and ground. The bypass capacitor should be located as close to the IC as possible and should have a short return path to ground."}, {"pin_number": "11", "pin_name": "PVCC", "pin_description": "Internal VCC Regulator Output. This pin is the output pin of the internal linear regulator that generates the VCC rail from VIN. The PVCC pin is also the supply connection for the power switch gate drivers. If the trace connecting PVCC to VCC cannot be made short in length, an additional bypass capacitor should be connected between this pin and ground. The VCC and PVCC pins must be connected together in the application."}, {"pin_number": "12", "pin_name": "BST2", "pin_description": "Flying Capacitor Pin for SW2. This pin must be connected to SW2 through a 0.1μF capacitor. This pin is used to generate the gate drive rail for power switch D."}, {"pin_number": "13", "pin_name": "BST1", "pin_description": "Flying Capacitor Pin for SW1. This pin must be connected to SW1 through a 0.1μF capacitor. This pin is used to generate the gate drive rail for power switch A."}, {"pin_number": "14", "pin_name": "PVIN", "pin_description": "Power Input for the Buck-Boost Converter. A 4.7μF or larger bypass capacitor should be connected between this pin and ground. The bypass capacitor should be located as close to the IC as possible and should via directly down to the ground plane. When powered through long leads or from a high ESR power source, a larger bulk input capacitor (typically 47μF to 100μF) may be required."}, {"pin_number": "15", "pin_name": "SW1", "pin_description": "Buck-Boost Converter Power Switch Pin. This pin should be connected to one side of the buck-boost inductor."}, {"pin_number": "16", "pin_name": "PWM/SYNC", "pin_description": "Burst Mode/PWM Mode Control Pin and Synchronization Input. Forcing this pin high causes the IC to operate in fixed frequency PWM mode at all loads using the internal oscillator at the frequency set by the RT Pin. Forcing this pin low places the IC into Burst Mode operation for improved efficiency at light load and reduced standby current. If an external clock signal is connected to this pin, the buck-boost converter will synchronize its switching with the external clock using fixed frequency PWM mode operation. The pulse width (negative or positive) of the applied clock should be at least 100ns. The maximum operating voltage for the PWM/SYNC pin is 5.5V. The PWM/SYNC pin can be connected to VCC to force it high continuously."}, {"pin_number": "17", "pin_name": "PGND", "pin_description": "Power Ground Connections. These pins should be connected to the power ground in the application. The exposed pad is the power ground connection. It must be soldered to the PCB and electrically connected to ground through the shortest and lowest impedance connection possible and to the PCB ground plane for rated thermal performance."}]}, {"product_part_number": "LTC3115-1", "package_type": "TSSOP", "pins": [{"pin_number": "1, 10, 11, 20, 21 (Exposed Pad)", "pin_name": "PGND", "pin_description": "Power Ground Connections. These pins should be connected to the power ground in the application. The exposed pad is the power ground connection. It must be soldered to the PCB and electrically connected to ground through the shortest and lowest impedance connection possible and to the PCB ground plane for rated thermal performance."}, {"pin_number": "2", "pin_name": "RUN", "pin_description": "Input to Enable and Disable the IC and Set Custom Input UVLO Thresholds. The RUN pin can be driven by an external logic signal to enable and disable the IC. In addition, the voltage on this pin can be set by a resistor divider connected to the input voltage in order to provide an accurate undervoltage lockout threshold. The IC is enabled if RUN exceeds 1.21V nominally. Once enabled, a 0.5μA current is sourced by the RUN pin to provide hysteresis. To continuously enable the IC, this pin can be tied directly to the input voltage. The RUN pin cannot be forced more than 0.3V above VIN under any condition."}, {"pin_number": "3", "pin_name": "SW2", "pin_description": "Buck-Boost Converter Power Switch Pin. This pin should be connected to one side of the buck-boost inductor."}, {"pin_number": "4", "pin_name": "PVOUT", "pin_description": "Buck-Boost Converter Power Output. This pin should be connected to a low ESR capacitor with a value of at least 10μF. The capacitor should be placed as close to the IC as possible and should have a short return path to ground. In applications with VOUT > 20V that are subject to output overload or short-circuit conditions, it is recommended that a Schottky diode be installed from SW2 (anode) to PVOUT (cathode). In applications subject to output short circuits through an inductive load, it is recommended that a Schottky diode be installed from ground (anode) to PVOUT (cathode) to limit the extent that PVOUT is driven below ground during the short-circuit transient."}, {"pin_number": "5, 6", "pin_name": "GND", "pin_description": "Signal Ground. These pins are the ground connections for the control circuitry of the IC and must be tied to ground in the application."}, {"pin_number": "7", "pin_name": "VC", "pin_description": "Error Amplifier Output. A frequency compensation network must be connected between this pin and FB to stabilize the voltage control loop."}, {"pin_number": "8", "pin_name": "FB", "pin_description": "Feedback Voltage Input. A resistor divider connected to this pin sets the output voltage for the buck-boost converter. The nominal FB voltage is 1000mV. Care should be taken in the routing of connections to this pin in order to minimize stray coupling to the switch pin traces."}, {"pin_number": "9", "pin_name": "RT", "pin_description": "Oscillator Frequency Programming Pin. A resistor placed between this pin and ground sets the switching frequency of the buck-boost converter."}, {"pin_number": "12", "pin_name": "VCC", "pin_description": "Low Voltage Supply Input for IC Control Circuitry. This pin powers internal IC control circuitry and must be connected to the PVCC pin in the application. A 4.7μF or larger bypass capacitor should be connected between this pin and ground. The VCC and PVCC pins must be connected together in the application."}, {"pin_number": "13", "pin_name": "VIN", "pin_description": "Power Supply Connection for Internal Circuitry and the VCC Regulator. This pin provides power to the internal VCC regulator and is the input voltage sense connection for the VIN divider. A 0.1μF bypass capacitor should be connected between this pin and ground. The bypass capacitor should be located as close to the IC as possible and should have a short return path to ground."}, {"pin_number": "14", "pin_name": "PVCC", "pin_description": "Internal VCC Regulator Output. This pin is the output pin of the internal linear regulator that generates the VCC rail from VIN. The PVCC pin is also the supply connection for the power switch gate drivers. If the trace connecting PVCC to VCC cannot be made short in length, an additional bypass capacitor should be connected between this pin and ground. The VCC and PVCC pins must be connected together in the application."}, {"pin_number": "15", "pin_name": "BST2", "pin_description": "Flying Capacitor Pin for SW2. This pin must be connected to SW2 through a 0.1μF capacitor. This pin is used to generate the gate drive rail for power switch D."}, {"pin_number": "16", "pin_name": "BST1", "pin_description": "Flying Capacitor Pin for SW1. This pin must be connected to SW1 through a 0.1μF capacitor. This pin is used to generate the gate drive rail for power switch A."}, {"pin_number": "17", "pin_name": "PVIN", "pin_description": "Power Input for the Buck-Boost Converter. A 4.7μF or larger bypass capacitor should be connected between this pin and ground. The bypass capacitor should be located as close to the IC as possible and should via directly down to the ground plane. When powered through long leads or from a high ESR power source, a larger bulk input capacitor (typically 47μF to 100μF) may be required."}, {"pin_number": "18", "pin_name": "SW1", "pin_description": "Buck-Boost Converter Power Switch Pin. This pin should be connected to one side of the buck-boost inductor."}, {"pin_number": "19", "pin_name": "PWM/SYNC", "pin_description": "Burst Mode/PWM Mode Control Pin and Synchronization Input. Forcing this pin high causes the IC to operate in fixed frequency PWM mode at all loads using the internal oscillator at the frequency set by the RT Pin. Forcing this pin low places the IC into Burst Mode operation for improved efficiency at light load and reduced standby current. If an external clock signal is connected to this pin, the buck-boost converter will synchronize its switching with the external clock using fixed frequency PWM mode operation. The pulse width (negative or positive) of the applied clock should be at least 100ns. The maximum operating voltage for the PWM/SYNC pin is 5.5V. The PWM/SYNC pin can be connected to VCC to force it high continuously."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3115-1.pdf", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "40V", "min_input_voltage": "2.7V", "max_output_voltage": "40V", "min_output_voltage": "2.7V", "max_output_current": "2A", "max_switch_frequency": "2MHz", "quiescent_current": "30µA", "high_side_mosfet_resistance": "150mΩ", "low_side_mosfet_resistance": "150mΩ", "over_current_protection_threshold": "3A", "operation_mode": "Synchronous", "output_voltage_config_method": "Adjustable", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "Internal", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.7%", "output_reference_voltage": "1V", "loop_control_mode": "Voltage Mode"}, "package": [{"pitch": "0.5", "height": "0.75", "length": "6.5", "width": "4.4", "type": "Plastic", "pin_count": "20"}]}