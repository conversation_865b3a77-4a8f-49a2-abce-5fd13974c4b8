{"part_number": "LTC3113", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "3A Low Noise Buck-Boost DC/DC Converter", "features": ["Regulated Output with Input Voltage Above, Below or Equal to the Output Voltage", "1.8V to 5.5V Input and Output Voltage Range", "3A Continuous Output Current VIN > 3.0V, VOUT = 3.8V", "1.5A Continuous Output Current for VIN ≥ 1.8V, VOUT = 3.3V", "Single Inductor", "Low Noise Buck-Boost Architecture", "Up to 96% Efficiency", "Programmable Frequency from 300kHz to 2MHz", "Selectable Burst Mode® Operation", "Output Disconnect in Shutdown", "Shutdown Current: <1µA", "Internal Soft-Start", "Small, Thermally Enhanced 16-Lead (4mm x 5mm × 0.75mm) DFN Package and 20-Lead TSSOP Package"], "description": "The LTC®3113 is a wide VIN range, highly efficient, fixed frequency, buck-boost DC/DC converter that operates from input voltages above, below or equal to the output voltage. The topology incorporated in the IC provides low noise operation, making it well suited for RF and precision measurement applications.\nThe LTC3113 can deliver up to 3A of continuous output current to satisfy the most demanding applications. Higher output current is possible in stepdown (buck) mode. Integrated low RDS(ON) power MOSFETs and a programmable switching frequency up to 2MHz result in a compact solution footprint. Selectable Burst Mode operation improves efficiency at light loads.\nOther features include <1µA shutdown current, integrated soft-start, short-circuit protection, current limit and thermal overload protection. The LTC3113 is housed in the thermally enhanced 16-lead (4mm × 5mm × 0.75mm) DFN and 20-lead TSSOP packages.", "applications": ["Wireless Modems", "Backup Power Systems", "Portable Inventory Terminals", "Portable Barcode Readers", "Portable Instrumentation"], "ordering_information": [{"part_number": "LTC3113", "order_device": "LTC3113EDHD#PBF", "package_type": "DFN", "package_drawing_code": "DHD", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3113", "order_device": "LTC3113EDHD#TRPBF", "package_type": "DFN", "package_drawing_code": "DHD", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3113", "order_device": "LTC3113IDHD#PBF", "package_type": "DFN", "package_drawing_code": "DHD", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3113", "order_device": "LTC3113IDHD#TRPBF", "package_type": "DFN", "package_drawing_code": "DHD", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3113", "order_device": "LTC3113EFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3113", "order_device": "LTC3113EFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3113", "order_device": "LTC3113IFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LTC3113", "order_device": "LTC3113IFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LTC3113", "package_type": "DFN", "pins": [{"pin_number": "1, 2", "pin_name": "VOUT", "pin_description": "Buck-Boost Output Voltage. A low ESR capacitor should be placed from VOUT to PGND. The capacitor should be placed as close to the IC as possible and have a short return path to ground."}, {"pin_number": "3, 4, 5", "pin_name": "VIN", "pin_description": "Power Input for the Converter. A 47μF or larger bypass capacitor should be connected between VIN and PGND. The bypass capacitor should located as close to VIN and PGND as possible and should via directly to the ground plane."}, {"pin_number": "6", "pin_name": "SGND", "pin_description": "Signal Ground. Terminate the frequency setting resistor and output voltage divider to SGND."}, {"pin_number": "7", "pin_name": "BURST", "pin_description": "Pulse Width Modulation/Burst Mode Selection Input. Forcing this pin low causes the switching converter to operate in low noise fixed frequency PWM mode. Forcing this pin high enables constant Burst Mode operation for the converter. During Burst Mode operation, the converter can only support a reduced maximum load current."}, {"pin_number": "8", "pin_name": "RT", "pin_description": "Programs the Frequency of the Internal Oscillator. Connect a resistor from RT to ground (SGND)."}, {"pin_number": "9", "pin_name": "VC", "pin_description": "Error Amp Output. An R-C network is connected from this pin to FB for loop compensation."}, {"pin_number": "10", "pin_name": "FB", "pin_description": "Feedback Voltage for the Buck-Boost Converter Derived from a Resistor Divider on the Buck-Boost Output Voltage."}, {"pin_number": "11", "pin_name": "RUN", "pin_description": "Active High Converter Enable Input. Applying a voltage <0.3V to this pin shuts down the LTC3113. Applying a voltage >1.2V to this pin enables the LTC3113."}, {"pin_number": "12, 13, 14", "pin_name": "SW1", "pin_description": "Switch Pin Where Internal Switches A and B are Connected. Connect the inductor from SW1 to SW2. Minimize trace length to reduce EMI."}, {"pin_number": "15, 16", "pin_name": "SW2", "pin_description": "Switch Pin Where Internal Switches C and D are Connected. Connect the inductor from SW1 to SW2. Minimize trace length to reduce EMI."}, {"pin_number": "17", "pin_name": "PGND (Exposed Pad)", "pin_description": "The exposed pad must be soldered to the PCB and electrically connected to ground through the shortest and lowest impedance connection possible."}]}, {"product_part_number": "LTC3113", "package_type": "TSSOP", "pins": [{"pin_number": "2, 3", "pin_name": "VOUT", "pin_description": "Buck-Boost Output Voltage. A low ESR capacitor should be placed from VOUT to PGND. The capacitor should be placed as close to the IC as possible and have a short return path to ground."}, {"pin_number": "4, 5, 6", "pin_name": "VIN", "pin_description": "Power Input for the Converter. A 47μF or larger bypass capacitor should be connected between VIN and PGND. The bypass capacitor should located as close to VIN and PGND as possible and should via directly to the ground plane."}, {"pin_number": "7", "pin_name": "SGND", "pin_description": "Signal Ground. Terminate the frequency setting resistor and output voltage divider to SGND."}, {"pin_number": "8", "pin_name": "BURST", "pin_description": "Pulse Width Modulation/Burst Mode Selection Input. Forcing this pin low causes the switching converter to operate in low noise fixed frequency PWM mode. Forcing this pin high enables constant Burst Mode operation for the converter. During Burst Mode operation, the converter can only support a reduced maximum load current."}, {"pin_number": "9", "pin_name": "RT", "pin_description": "Programs the Frequency of the Internal Oscillator. Connect a resistor from RT to ground (SGND)."}, {"pin_number": "12", "pin_name": "VC", "pin_description": "Error Amp Output. An R-C network is connected from this pin to FB for loop compensation."}, {"pin_number": "13", "pin_name": "FB", "pin_description": "Feedback Voltage for the Buck-Boost Converter Derived from a Resistor Divider on the Buck-Boost Output Voltage."}, {"pin_number": "14", "pin_name": "RUN", "pin_description": "Active High Converter Enable Input. Applying a voltage <0.3V to this pin shuts down the LTC3113. Applying a voltage >1.2V to this pin enables the LTC3113."}, {"pin_number": "15, 16, 17", "pin_name": "SW1", "pin_description": "Switch Pin Where Internal Switches A and B are Connected. Connect the inductor from SW1 to SW2. Minimize trace length to reduce EMI."}, {"pin_number": "18, 19", "pin_name": "SW2", "pin_description": "Switch Pin Where Internal Switches C and D are Connected. Connect the inductor from SW1 to SW2. Minimize trace length to reduce EMI."}, {"pin_number": "1, 10, 11, 20", "pin_name": "PGND", "pin_description": "The exposed pad must be soldered to the PCB and electrically connected to ground through the shortest and lowest impedance connection possible."}, {"pin_number": "21", "pin_name": "PGND (Exposed Pad)", "pin_description": "The exposed pad must be soldered to the PCB and electrically connected to ground through the shortest and lowest impedance connection possible."}]}], "datasheet_cn": "未找到", "datasheet_en": "3113f.pdf", "family_comparison": "Yes", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "1.8V", "max_output_voltage": "5.5V", "min_output_voltage": "1.8V", "max_output_current": "3A", "max_switch_frequency": "2MHz", "quiescent_current": "300µA", "high_side_mosfet_resistance": "30mΩ", "low_side_mosfet_resistance": "25mΩ", "over_current_protection_threshold": "7.8A", "operation_mode": "同步", "pass_through_mode": "False", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "电流限制", "over_temperature_protection": "自动重启", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.6V", "loop_control_mode": "电压模式"}, "package": [{"pitch": "0.5", "height": "0.75", "length": "6.6", "width": "4.4", "type": "and", "pin_count": "17"}]}