{"part_number": "LM5176", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "LM5176 55V 宽输入电压同步4开关降压/升压控制器", "features": ["提供功能安全 - 可帮助进行功能安全系统设计的文档", "单电感降压/升压控制器，用于升压/降压直流/直流转换", "宽 VIN：4.2V (2.5V 偏置) 至 55V (60V 最大输入电压)", "灵活的 VOUT 0.8 V 至 55 V", "输出电压短路保护", "高效降压/升压转换", "可调开关频率", "可选频率同步和抖动", "集成 2A MOSFET 栅极驱动器", "逐周期电流限制和可选断续模式", "可选输入或输出平均电流限制", "可编程输入 UVLO 和软启动", "电源正常和输出过压保护", "采用 HTSSOP-28 和 QFN-28 封装", "使用 LM5176 并借助 WEBENCH Power Designer 创建定制设计方案"], "description": "LM5176 是一款同步 4 开关降压/升压直流/直流控制器，能够将输出电压稳定在等于、高于或低于输入电压的某一电压值上。LM5176 在 4.2V 至 55V（最大绝对值为 60V）的宽输入电压范围内工作，可支持各种不同的 LM5176 在降压和升压运行模式下均采用电流控制模式，以提供出色的负载和线路调节性能。开关频率可通过外部电阻进行编程，并且可与外部时钟信号同步。该器件还具有可编程的软启动功能，并且提供诸如逐周期电流限制、输入欠压闭锁 (UVLO)、输出过压保护 (OVP) 和热关断等各类保护特性。可选的平均输入或输出电流限制、用于减少峰值 EMI 的可选扩频，以及持续过载情况下的可选间断模式保护。", "applications": ["工业 PC 电源", "USB 电力输送", "电池供电型系统", "LED 照明"], "ordering_information": [{"part_number": "LM5176", "order_device": "LM5176PWPR", "package_type": "HTSSOP", "package_drawing_code": "PWP0028V", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5176", "order_device": "LM5176PWPR.B", "package_type": "HTSSOP", "package_drawing_code": "PWP0028V", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5176", "order_device": "LM5176PWPT", "package_type": "HTSSOP", "package_drawing_code": "PWP0028V", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5176", "order_device": "LM5176PWPT.B", "package_type": "HTSSOP", "package_drawing_code": "PWP0028V", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5176", "order_device": "LM5176PWPTG4.B", "package_type": "HTSSOP", "package_drawing_code": "PWP0028V", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5176", "order_device": "LM5176RHFR", "package_type": "VQFN", "package_drawing_code": "RHF0028A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5176", "order_device": "LM5176RHFR.B", "package_type": "VQFN", "package_drawing_code": "RHF0028A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5176", "order_device": "LM5176RHFT", "package_type": "VQFN", "package_drawing_code": "RHF0028A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5176", "order_device": "LM5176RHFT.B", "package_type": "VQFN", "package_drawing_code": "RHF0028A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LM5176", "order_device": "LM5176RHFTG4.B", "package_type": "VQFN", "package_drawing_code": "RHF0028A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "LM5176", "package_type": "HTSSOP-28", "pins": [{"pin_number": "1", "pin_name": "EN/UVLO", "pin_description": "Enable pin. For EN/UVLO < 0.4 V, the LM5176 is in a low current shutdown mode. For EN/UVLO > 1.22 V, the PWM function is enabled, provided VCC exceeds the VCC UV threshold."}, {"pin_number": "2", "pin_name": "VIN", "pin_description": "The input supply pin to the IC. Connect VIN to a supply voltage between 4.2 V and 55 V."}, {"pin_number": "3", "pin_name": "VISNS", "pin_description": "VIN sense input. Connect to power stage input rail."}, {"pin_number": "4", "pin_name": "MODE", "pin_description": "1.38 V < MODE < 2.22 V: CCM, hiccup enabled (set RMODE resistor to AGND = 93.1 kΩ)\n2.6 V < MODE < VCC: CCM, hiccup disabled (set RMODE resistor to AGND = 200 kΩ or connect to VCC)"}, {"pin_number": "5", "pin_name": "DITH", "pin_description": "A capacitor connected between the DITH pin and AGND is charged and discharged with a current source. As the voltage on the DITH pin ramps up and down the oscillator frequency is modulated by 10% of the nominal frequency set by the RT resistor. Grounding the DITH pin will disable the dithering feature. In the external Sync mode, the DITH pin voltage is ignored."}, {"pin_number": "6", "pin_name": "RT/SYNC", "pin_description": "Switching frequency programming pin. An external resistor is connected to the RT/SYNC pin and AGND to set the switching frequency. This pin can also be used to synchronize the PWM controller to an external clock."}, {"pin_number": "7", "pin_name": "SLOPE", "pin_description": "A capacitor connected between the SLOPE pin and AGND provides the slope compensation ramp for stable current mode operation in both buck and boost mode."}, {"pin_number": "8", "pin_name": "SS", "pin_description": "Soft-start programming pin. A capacitor between the SS pin and AGND pin programs soft-start time."}, {"pin_number": "9", "pin_name": "COMP", "pin_description": "Output of the error amplifier. An external RC network connected between COMP and AGND compensates the regulator feedback loop."}, {"pin_number": "10", "pin_name": "AGND", "pin_description": "Analog ground of the IC"}, {"pin_number": "11", "pin_name": "FB", "pin_description": "Feedback pin for output voltage regulation. Connect a resistor divider network from the output of the converter to the FB pin."}, {"pin_number": "12", "pin_name": "VOSNS", "pin_description": "VOUT sense input. Connect to the power stage output rail."}, {"pin_number": "13", "pin_name": "ISNS(-)", "pin_description": "Input or output current sense amplifier inputs. An optional current sense resistor connected between ISNS(+) and ISNS(-) can be located either on the input side or on the output side of the converter. If the sensed voltage across the ISNS(+) and ISNS(-) pins reaches 50 mV, a slow constant current (CC) control loop becomes active and starts discharging the soft-start capacitor to regulate the drop across ISNS(+) and ISNS( - ) to 50 mV. Short ISNS(+) and ISNS(-) together to disable this feature."}, {"pin_number": "14", "pin_name": "ISNS(+)", "pin_description": "Input or output current sense amplifier inputs. An optional current sense resistor connected between ISNS(+) and ISNS(-) can be located either on the input side or on the output side of the converter. If the sensed voltage across the ISNS(+) and ISNS(-) pins reaches 50 mV, a slow constant current (CC) control loop becomes active and starts discharging the soft-start capacitor to regulate the drop across ISNS(+) and ISNS( - ) to 50 mV. Short ISNS(+) and ISNS(-) together to disable this feature."}, {"pin_number": "15", "pin_name": "CSG", "pin_description": "The negative or ground input to the PWM current sense amplifier. Connect directly to the low-side (ground) of the current sense resistor."}, {"pin_number": "16", "pin_name": "CS", "pin_description": "The positive input to the PWM current sense amplifier"}, {"pin_number": "17", "pin_name": "PGOOD", "pin_description": "Power-Good open drain output. PGOOD is pulled low when FB is outside a -9%/+10% regulation window around the 0.8-V VREF."}, {"pin_number": "18", "pin_name": "SW2", "pin_description": "The boost and the buck side switching nodes, respectively."}, {"pin_number": "19", "pin_name": "HDRV2", "pin_description": "Output of the high-side gate drivers. Connect directly to the gates of the high-side MOSFETs."}, {"pin_number": "20", "pin_name": "BOOT2", "pin_description": "An external capacitor is required between the BOOT1, BOOT2 pins and the SW1, SW2 pins respectively to provide bias to the high-side MOSFET gate drivers."}, {"pin_number": "21", "pin_name": "LDRV2", "pin_description": "Output of the low-side gate drivers. Connect directly to the gates of the low-side MOSFETs."}, {"pin_number": "22", "pin_name": "PGND", "pin_description": "Power ground of the IC. The high current ground connection to the low-side gate drivers"}, {"pin_number": "23", "pin_name": "VCC", "pin_description": "Output of the VCC bias regulator. Connect capacitor to ground."}, {"pin_number": "24", "pin_name": "BIAS", "pin_description": "Optional input to the VCC bias regulator. Powering VCC from an external supply instead of VIN can reduce power loss at high VIN. For VBIAS > 8 V, the VCC regulator draws power from the BIAS pin."}, {"pin_number": "25", "pin_name": "LDRV1", "pin_description": "Output of the low-side gate drivers. Connect directly to the gates of the low-side MOSFETs."}, {"pin_number": "26", "pin_name": "BOOT1", "pin_description": "An external capacitor is required between the BOOT1, BOOT2 pins and the SW1, SW2 pins respectively to provide bias to the high-side MOSFET gate drivers."}, {"pin_number": "27", "pin_name": "HDRV1", "pin_description": "Output of the high-side gate drivers. Connect directly to the gates of the high-side MOSFETs."}, {"pin_number": "28", "pin_name": "SW1", "pin_description": "The boost and the buck side switching nodes, respectively."}, {"pin_number": "PowerPAD", "pin_name": "PowerPAD", "pin_description": "Solder the PowerPAD to the analog ground. If possible, use thermal vias to connect to a PCB ground plane for improved power dissipation."}]}, {"product_part_number": "LM5176", "package_type": "QFN-28", "pins": [{"pin_number": "1", "pin_name": "MODE", "pin_description": "1.38 V < MODE < 2.22 V: CCM, hiccup enabled (set RMODE resistor to AGND = 93.1 kΩ)\n2.6 V < MODE < VCC: CCM, hiccup disabled (set RMODE resistor to AGND = 200 kΩ or connect to VCC)"}, {"pin_number": "2", "pin_name": "DITH", "pin_description": "A capacitor connected between the DITH pin and AGND is charged and discharged with a current source. As the voltage on the DITH pin ramps up and down the oscillator frequency is modulated by 10% of the nominal frequency set by the RT resistor. Grounding the DITH pin will disable the dithering feature. In the external Sync mode, the DITH pin voltage is ignored."}, {"pin_number": "3", "pin_name": "RT/SYNC", "pin_description": "Switching frequency programming pin. An external resistor is connected to the RT/SYNC pin and AGND to set the switching frequency. This pin can also be used to synchronize the PWM controller to an external clock."}, {"pin_number": "4", "pin_name": "SLOPE", "pin_description": "A capacitor connected between the SLOPE pin and AGND provides the slope compensation ramp for stable current mode operation in both buck and boost mode."}, {"pin_number": "5", "pin_name": "SS", "pin_description": "Soft-start programming pin. A capacitor between the SS pin and AGND pin programs soft-start time."}, {"pin_number": "6", "pin_name": "COMP", "pin_description": "Output of the error amplifier. An external RC network connected between COMP and AGND compensates the regulator feedback loop."}, {"pin_number": "7", "pin_name": "AGND", "pin_description": "Analog ground of the IC"}, {"pin_number": "8", "pin_name": "FB", "pin_description": "Feedback pin for output voltage regulation. Connect a resistor divider network from the output of the converter to the FB pin."}, {"pin_number": "9", "pin_name": "VOSNS", "pin_description": "VOUT sense input. Connect to the power stage output rail."}, {"pin_number": "10", "pin_name": "ISNS(-)", "pin_description": "Input or output current sense amplifier inputs. An optional current sense resistor connected between ISNS(+) and ISNS(-) can be located either on the input side or on the output side of the converter. If the sensed voltage across the ISNS(+) and ISNS(-) pins reaches 50 mV, a slow constant current (CC) control loop becomes active and starts discharging the soft-start capacitor to regulate the drop across ISNS(+) and ISNS( - ) to 50 mV. Short ISNS(+) and ISNS(-) together to disable this feature."}, {"pin_number": "11", "pin_name": "ISNS(+)", "pin_description": "Input or output current sense amplifier inputs. An optional current sense resistor connected between ISNS(+) and ISNS(-) can be located either on the input side or on the output side of the converter. If the sensed voltage across the ISNS(+) and ISNS(-) pins reaches 50 mV, a slow constant current (CC) control loop becomes active and starts discharging the soft-start capacitor to regulate the drop across ISNS(+) and ISNS( - ) to 50 mV. Short ISNS(+) and ISNS(-) together to disable this feature."}, {"pin_number": "12", "pin_name": "CSG", "pin_description": "The negative or ground input to the PWM current sense amplifier. Connect directly to the low-side (ground) of the current sense resistor."}, {"pin_number": "13", "pin_name": "CS", "pin_description": "The positive input to the PWM current sense amplifier"}, {"pin_number": "14", "pin_name": "PGOOD", "pin_description": "Power-Good open drain output. PGOOD is pulled low when FB is outside a -9%/+10% regulation window around the 0.8-V VREF."}, {"pin_number": "15", "pin_name": "SW2", "pin_description": "The boost and the buck side switching nodes, respectively."}, {"pin_number": "16", "pin_name": "HDRV2", "pin_description": "Output of the high-side gate drivers. Connect directly to the gates of the high-side MOSFETs."}, {"pin_number": "17", "pin_name": "BOOT2", "pin_description": "An external capacitor is required between the BOOT1, BOOT2 pins and the SW1, SW2 pins respectively to provide bias to the high-side MOSFET gate drivers."}, {"pin_number": "18", "pin_name": "LDRV2", "pin_description": "Output of the low-side gate drivers. Connect directly to the gates of the low-side MOSFETs."}, {"pin_number": "19", "pin_name": "PGND", "pin_description": "Power ground of the IC. The high current ground connection to the low-side gate drivers"}, {"pin_number": "20", "pin_name": "VCC", "pin_description": "Output of the VCC bias regulator. Connect capacitor to ground."}, {"pin_number": "21", "pin_name": "BIAS", "pin_description": "Optional input to the VCC bias regulator. Powering VCC from an external supply instead of VIN can reduce power loss at high VIN. For VBIAS > 8 V, the VCC regulator draws power from the BIAS pin."}, {"pin_number": "22", "pin_name": "LDRV1", "pin_description": "Output of the low-side gate drivers. Connect directly to the gates of the low-side MOSFETs."}, {"pin_number": "23", "pin_name": "BOOT1", "pin_description": "An external capacitor is required between the BOOT1, BOOT2 pins and the SW1, SW2 pins respectively to provide bias to the high-side MOSFET gate drivers."}, {"pin_number": "24", "pin_name": "HDRV1", "pin_description": "Output of the high-side gate drivers. Connect directly to the gates of the high-side MOSFETs."}, {"pin_number": "25", "pin_name": "SW1", "pin_description": "The boost and the buck side switching nodes, respectively."}, {"pin_number": "26", "pin_name": "EN/UVLO", "pin_description": "Enable pin. For EN/UVLO < 0.4 V, the LM5176 is in a low current shutdown mode. For EN/UVLO > 1.22 V, the PWM function is enabled, provided VCC exceeds the VCC UV threshold."}, {"pin_number": "27", "pin_name": "VIN", "pin_description": "The input supply pin to the IC. Connect VIN to a supply voltage between 4.2 V and 55 V."}, {"pin_number": "28", "pin_name": "VISNS", "pin_description": "VIN sense input. Connect to power stage input rail."}, {"pin_number": "PowerPAD", "pin_name": "PowerPAD", "pin_description": "Solder the PowerPAD to the analog ground. If possible, use thermal vias to connect to a PCB ground plane for improved power dissipation."}]}], "datasheet_cn": "LM5176 55V 宽输入电压同步4开关降压/升压控制器", "datasheet_en": "LM5176 55V Wide Input Voltage Synchronous 4-Switch Buck-Boost Controller", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "55V", "min_input_voltage": "4.2V", "max_output_voltage": "55V", "min_output_voltage": "1V", "max_output_current": "可配置", "max_switch_frequency": "600kHz", "quiescent_current": "2.6uA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "Yes", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值/谷值电流模式"}, "package": [{"pitch": "0.65", "height": "1.2", "length": "9.7", "width": "4.4", "type": "28-<PERSON><PERSON>", "pin_count": "2"}]}