{"part_number": "LTC3538", "manufacturer": "Linear Technology", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "LTC3538 800mA Synchronous Buck-Boost DC/DC Converter", "features": ["Regulated Output with Input Voltages Above, Below, or Equal to the Output", "800mA Continuous Output Current from a Single Lithium-Ion/Polymer Cell", "Single Inductor", "1.8V to 5.25V Vout Range", "2.4V to 5.5V VIN Range", "1MHz Fixed Frequency Operation", "Output Disconnect in Shutdown", "35µA Quiescent Current in Burst Mode Operation", "<5µA Shutdown Current", "Internal Soft-Start", "Small, Thermally Enhanced 8-Lead (2mm x 3mm) DFN package"], "description": "The LTC®3538 is a highly efficient, low noise, buck-boost DC/DC converter that operates from input voltages above, below, and equal to the output voltage. The topology incorporated in the IC provides a continuous transfer function through all operating modes, making the product ideal for single Lithium Ion or multicell Alkaline or NiMH applications where the output voltage is within the battery voltage range.\nThe LTC3538 is suited for use in Micro Hard Disk Drive (μHDD) applications with its 800mA current capability. Burst Mode® operation provides high efficiency at light loads.\nThe LTC3538 includes two 0.17Ω N-channel and two 0.2Ω P-channel MOSFET switches. Operating frequency is internally set to 1MHz to minimize solution footprint while maximizing efficiency.\nOther features include <5μA shutdown current, internal soft-start, short circuit protection and thermal shutdown. The LTC3538 is available in a low profile (0.75mm), thermally enhanced 8-lead (2mm × 3mm) DFN package.", "applications": ["Miniature Hard Disk Drives", "MP3 Players", "Digital Cameras", "Cellular Handsets", "PDAs, Handheld PC", "GPS Receivers"], "ordering_information": [{"part_number": "LTC3538", "order_device": "LTC3538EDCB#PBF", "package_type": "DFN", "package_drawing_code": "05-08-1718 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3538", "order_device": "LTC3538EDCB#TRPBF", "package_type": "DFN", "package_drawing_code": "05-08-1718 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3538", "order_device": "LTC3538EDCB", "package_type": "DFN", "package_drawing_code": "05-08-1718 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "LTC3538", "order_device": "LTC3538EDCB#TR", "package_type": "DFN", "package_drawing_code": "05-08-1718 Rev A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "LTC3538", "package_type": "DFN", "pins": [{"pin_number": "1", "pin_name": "FB", "pin_description": "Feedback Input to Error Amplifier. Connect resistive divider tap from VOUT to this pin to set the output voltage. The output voltage can be adjusted from 1.8V to 5.25V. Referring to the Block Diagram the output voltage is given by: VOUT = 1V • (1 + R1/R2)"}, {"pin_number": "2", "pin_name": "VC", "pin_description": "Error Amplifier Output. A frequency compensation network should be connected between this pin and FB to compensate the loop. See Closing the Feedback Loop section of the datasheet for further information. Pulling Vc below 0.25V disables the LTC3538."}, {"pin_number": "3", "pin_name": "GND", "pin_description": "Ground."}, {"pin_number": "4", "pin_name": "BURST", "pin_description": "Burst Mode Select Input. BURST = Low for fixed frequency PWM operation. BURST = High for Burst Mode operation."}, {"pin_number": "5", "pin_name": "VOUT", "pin_description": "Power Supply Output. This pin should be connected to a low ESR output capacitor. The capacitor should be placed as close to the IC as possible and should have a short return to GND."}, {"pin_number": "6", "pin_name": "SW2", "pin_description": "Switch Pin where the Internal Switches C and D are Connected. An optional Schottky diode can be connected from SW2 to VOUT for a moderate efficiency improvement. Keep the trace length as short as possible to minimize EMI."}, {"pin_number": "7", "pin_name": "SW1", "pin_description": "Switch Pin where the Internal Switches A and B are Connected. Connect an inductor from SW1 to SW2. An optional Schottky diode can be connected from SW1 to ground for a moderate efficiency improvement. Keep the trace length as short as possible to minimize EMI."}, {"pin_number": "8", "pin_name": "VIN", "pin_description": "Input Supply. This input provides power to the IC and also supplies current to switch A. A ceramic bypass capacitor (4.7μF or larger) is recommended as close to VIN and GND as possible."}, {"pin_number": "9", "pin_name": "Exposed Pad", "pin_description": "GND. The exposed pad must be electrically connected to the board ground for proper electrical and thermal performance."}]}], "datasheet_cn": "未找到", "datasheet_en": "LTC3538.pdf", "family_comparison": "PART NUMBER | DESCRIPTION | COMMENTS\n---|---|---\nLTC3407 | 600mA (Ιουτ), 1.5MHz Dual Synchronous Step-Up DC/DC Converter | VIN: 2.5V to 5.5V, VOUT(MIN) = 0.8V, IQ = 40μA, ISD ≤1µA, SC70 Package\nLTC3410 | 300mA (Isw), 2.25MHz Synchronous Step-Down DC/DC Converter in SC70 | VIN: 2.5V to 5.5V, VOUT(MIN) = 0.8V, IQ = 26μA, ISD ≤1µA, MS Package\nLTC3440 | 600mA (Ιουτ), 2MHz Synchronous Buck-Boost DC/DC Converter | VIN: 2.5V to 5.5V, Vουτ: 2.5V to 5.5V, IQ = 25μΑ, Isp <1µA, MS, DFN Package\nLTC3531 | 200mA (lout) Synchronous Buck-Boost DC/DC Converter | VIN: 1.8V to 5.5V, Vουτ: 2V to 5V, IQ = 16μΑ, Isp <1μΑ, DFN, ThinSOT Packages", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.4V", "max_output_voltage": "5.25V", "min_output_voltage": "1.8V", "max_output_current": "0.8A", "max_switch_frequency": "1.2MHz", "quiescent_current": "35µA", "high_side_mosfet_resistance": "200mΩ", "low_side_mosfet_resistance": "170mΩ", "over_current_protection_threshold": "1.4A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "1V", "loop_control_mode": "电压模式"}, "package": [{"type": "DESCRIPTION", "length": "3", "width": "0.25", "pin_count": "8", "pitch": "0.45", "height": "0.75"}]}