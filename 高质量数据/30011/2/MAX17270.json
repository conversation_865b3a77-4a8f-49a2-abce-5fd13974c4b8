[{"part_number": "MAX17270", "manufacturer": "Maxim Integrated", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "nanoPower Triple-Output, Single-Inductor, Multiple-Output (SIMO) Buck-Boost Regulator", "features": ["3-Output, Single-Inductor, Multiple-Output (SIMO) Buck-Boost Regulator", "2.7V to 5.5V Input Voltage Range", "Low-Power and Long Battery Life: 1.3μA Operating Current (3 SIMO Channels), 330nA Shutdown Current, 85% Efficiency at 3.3V Output", "Flexible and Configurable: Programmable Output Voltage: 0.8V to 5.175V, Programmable Peak Current Limit", "Robust: Soft-Start, Overload Protection, Thermal Protection", "Small Size: 1.77mm x 1.77mm x 0.50mm, 16-Bump 0.4mm-Pitch WLP Package; 3mm x 3mm x 0.75mm, 16-Pin TQFN Package; Small Total Solution Size"], "description": "The MAX17270/MAX17271 are 3-output switching regulators designed for applications requiring efficient regulation of multiple supplies in a very small space, such as wearable electronic devices. The parts use a buck-boost architecture that regulates three outputs using a single, small 2.2µH inductor at efficiencies up to 85%. This results in smaller board space while delivering better total system efficiency than equivalent power solutions using one buck and linear regulators. The supply current is 0.85µA when only one output is enabled, plus 0.2µA for each additional output enabled. This SIMO (Single-Input Multiple-Output) regulator utilizes the entire battery voltage range due to its ability to create output voltages that are above, below, or equal to the input voltage. Peak inductor current for each output is programmable to optimize the balance between efficiency, output ripple, EMI, PCB design, and load capability. Two versions are available. The MAX17270 has 3 enable inputs and 3 output voltage programming inputs. The MAX17271 includes an I2C interface with interrupt, a push-button turn on/off, and a power-good indication. All versions are offered in either a 4 x 4, 0.4mm wafer-level package (WLP) or a 16-pin TQFN package.", "applications": ["Bluetooth Headsets", "Fitness Bands", "Watches", "Hearables", "Wearables", "Internet of Things (IoT)", "Health Monitors"], "ordering_information": [{"part_number": "MAX17270", "order_device": "MAX17270ETE+", "package_type": "TQFN", "package_drawing_code": "21-0136", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "MAX17270", "order_device": "MAX17270ENE+", "package_type": "WLP", "package_drawing_code": "21-100190", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "MAX17270", "order_device": "MAX17270AENE+", "package_type": "WLP", "package_drawing_code": "21-100190", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX17270", "package_type": "WLP", "pins": [{"pin_number": "A1", "pin_name": "OUT1", "pin_description": "Regulator Output 1. Connect a 10µF (min) capacitor from this pin to ground."}, {"pin_number": "B1", "pin_name": "PGND", "pin_description": "Buck-Boost Power Ground. Connect to the ground plane through a low impedance."}, {"pin_number": "C1", "pin_name": "LXA", "pin_description": "Buck-Boost Input-Side Inductor Connection. Connect a 2.2µH inductor between LXA and LXB."}, {"pin_number": "D1", "pin_name": "VPWR", "pin_description": "Buck-Boost Input Power Supply Pin. Connect a 10µF(min) capacitor from this pin to ground."}, {"pin_number": "A2", "pin_name": "OUT2", "pin_description": "Regulator Output 2. Connect a 10µF (min) capacitor from this pin to ground."}, {"pin_number": "B2", "pin_name": "LXB", "pin_description": "Buck-Boost Output-Side Inductor Connection. Connect a 2.2µH inductor between LXA and LXB."}, {"pin_number": "C2", "pin_name": "BST", "pin_description": "Bootstrap pin for high-side output FET drivers. Connect a 3.3nF capacitor between BST and LXB."}, {"pin_number": "D2", "pin_name": "VSUP", "pin_description": "Analog Input Supply. Connect to VPWR."}, {"pin_number": "A3", "pin_name": "OUT3", "pin_description": "Regulator Output 3. Connect a 10µF (min) capacitor from this pin to ground."}, {"pin_number": "B3", "pin_name": "RSEL3", "pin_description": "Select Resistor Pin 3. Connect a resistor from this pin to GND, using the values from Table 1 to configure the output voltage of OUT3."}, {"pin_number": "C3", "pin_name": "RSEL2", "pin_description": "Select Resistor Pin 2. Connect a resistor from this pin to GND, using the values from Table 1 to configure the output voltage of OUT2."}, {"pin_number": "D3", "pin_name": "RSEL1", "pin_description": "Select Resistor Pin 1. Connect a resistor from this pin to GND, using the values from Table 1 to configure the output voltage of OUT1."}, {"pin_number": "A4", "pin_name": "GND", "pin_description": "Analog Ground."}, {"pin_number": "B4", "pin_name": "EN3", "pin_description": "Enable Input for OUT3. Hold high to enable output regulation. Hold low to disable the output."}, {"pin_number": "C4", "pin_name": "EN2", "pin_description": "Enable Input for OUT2. Hold high to enable output regulation. Hold low to disable the output."}, {"pin_number": "D4", "pin_name": "EN1", "pin_description": "Enable Input for OUT1. Hold high to enable output regulation. Hold low to disable the output."}]}, {"product_part_number": "MAX17270", "package_type": "TQFN", "pins": [{"pin_number": "1", "pin_name": "LXA", "pin_description": "Buck-Boost Input-Side Inductor Connection. Connect a 2.2µH inductor between LXA and LXB."}, {"pin_number": "2", "pin_name": "BST", "pin_description": "Bootstrap pin for high-side output FET drivers. Connect a 3.3nF capacitor between BST and LXB."}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Buck-Boost Power Ground. Connect to the ground plane through a low impedance."}, {"pin_number": "4", "pin_name": "LXB", "pin_description": "Buck-Boost Output-Side Inductor Connection. Connect a 2.2µH inductor between LXA and LXB."}, {"pin_number": "5", "pin_name": "OUT1", "pin_description": "Regulator Output 1. Connect a 10µF (min) capacitor from this pin to ground."}, {"pin_number": "6", "pin_name": "OUT2", "pin_description": "Regulator Output 2. Connect a 10µF (min) capacitor from this pin to ground."}, {"pin_number": "7", "pin_name": "OUT3", "pin_description": "Regulator Output 3. Connect a 10µF (min) capacitor from this pin to ground."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Analog Ground."}, {"pin_number": "9", "pin_name": "RSEL3", "pin_description": "Select Resistor Pin 3. Connect a resistor from this pin to GND, using the values from Table 1 to configure the output voltage of OUT3."}, {"pin_number": "10", "pin_name": "EN3", "pin_description": "Enable Input for OUT3. Hold high to enable output regulation. Hold low to disable the output."}, {"pin_number": "11", "pin_name": "EN2", "pin_description": "Enable Input for OUT2. Hold high to enable output regulation. Hold low to disable the output."}, {"pin_number": "12", "pin_name": "RSEL2", "pin_description": "Select Resistor Pin 2. Connect a resistor from this pin to GND, using the values from Table 1 to configure the output voltage of OUT2."}, {"pin_number": "13", "pin_name": "EN1", "pin_description": "Enable Input for OUT1. Hold high to enable output regulation. Hold low to disable the output."}, {"pin_number": "14", "pin_name": "RSEL1", "pin_description": "Select Resistor Pin 1. Connect a resistor from this pin to GND, using the values from Table 1 to configure the output voltage of OUT1."}, {"pin_number": "15", "pin_name": "VSUP", "pin_description": "Analog Input Supply. Connect to VPWR."}, {"pin_number": "16", "pin_name": "VPWR", "pin_description": "Buck-Boost Input Power Supply Pin. Connect a 10µF (min) capacitor from this pin to ground."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX17270/MAX17271.pdf", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 3, "max_input_voltage": "5.5V", "min_input_voltage": "2.7V", "max_output_voltage": "4.6V", "min_output_voltage": "1V", "max_output_current": "0.15A", "max_switch_frequency": "未找到", "quiescent_current": "1.3µA", "high_side_mosfet_resistance": "70mΩ", "low_side_mosfet_resistance": "55mΩ", "over_current_protection_threshold": "1.1A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}, "package": [{"height": "0.75", "length": "1.77", "width": "1.77", "type": "TQFN", "pin_count": "3", "pitch": "0.4"}]}, {"part_number": "MAX17271", "manufacturer": "Maxim Integrated", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "nanoPower Triple-Output, Single-Inductor, Multiple-Output (SIMO) Buck-Boost Regulator", "features": ["3-Output, Single-Inductor, Multiple-Output (SIMO) Buck-Boost Regulator", "2.7V to 5.5V Input Voltage Range", "Low-Power and Long Battery Life: 1.3μA Operating Current (3 SIMO Channels), 330nA Shutdown Current, 85% Efficiency at 3.3V Output", "Flexible and Configurable: I2C-Compatible Interface, Programmable Output Voltage: 0.8V to 5.175V, Programmable Peak Current Limit", "Robust: Soft-Start, Overload Protection, Thermal Protection", "Small Size: 1.77mm x 1.77mm x 0.50mm, 16-Bump 0.4mm-Pitch WLP Package; 3mm x 3mm x 0.75mm, 16-Pin TQFN Package; Small Total Solution Size"], "description": "The MAX17270/MAX17271 are 3-output switching regulators designed for applications requiring efficient regulation of multiple supplies in a very small space, such as wearable electronic devices. The parts use a buck-boost architecture that regulates three outputs using a single, small 2.2µH inductor at efficiencies up to 85%. This results in smaller board space while delivering better total system efficiency than equivalent power solutions using one buck and linear regulators. The supply current is 0.85µA when only one output is enabled, plus 0.2µA for each additional output enabled. This SIMO (Single-Input Multiple-Output) regulator utilizes the entire battery voltage range due to its ability to create output voltages that are above, below, or equal to the input voltage. Peak inductor current for each output is programmable to optimize the balance between efficiency, output ripple, EMI, PCB design, and load capability. Two versions are available. The MAX17270 has 3 enable inputs and 3 output voltage programming inputs. The MAX17271 includes an I2C interface with interrupt, a push-button turn on/off, and a power-good indication. All versions are offered in either a 4 x 4, 0.4mm wafer-level package (WLP) or a 16-pin TQFN package.", "applications": ["Bluetooth Headsets", "Fitness Bands", "Watches", "Hearables", "Wearables", "Internet of Things (IoT)", "Health Monitors"], "ordering_information": [{"part_number": "MAX17271", "order_device": "MAX17271ETE+", "package_type": "TQFN", "package_drawing_code": "21-0136", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "MAX17271", "order_device": "MAX17271ENE+", "package_type": "WLP", "package_drawing_code": "21-100190", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "MAX17271", "package_type": "WLP", "pins": [{"pin_number": "A1", "pin_name": "OUT1", "pin_description": "Regulator Output 1. Connect a 10µF (min) capacitor from this pin to ground."}, {"pin_number": "B1", "pin_name": "PGND", "pin_description": "Buck-Boost Power Ground. Connect to the ground plane through a low impedance."}, {"pin_number": "C1", "pin_name": "LXA", "pin_description": "Buck-Boost Input-Side Inductor Connection. Connect a 2.2µH inductor between LXA and LXB."}, {"pin_number": "D1", "pin_name": "VPWR", "pin_description": "Buck-Boost Input Power Supply Pin. Connect a 10µF(min) capacitor from this pin to ground."}, {"pin_number": "A2", "pin_name": "OUT2", "pin_description": "Regulator Output 2. Connect a 10µF (min) capacitor from this pin to ground."}, {"pin_number": "B2", "pin_name": "LXB", "pin_description": "Buck-Boost Output-Side Inductor Connection. Connect a 2.2µH inductor between LXA and LXB."}, {"pin_number": "C2", "pin_name": "BST", "pin_description": "Bootstrap pin for high-side output FET drivers. Connect a 3.3nF capacitor between BST and LXB."}, {"pin_number": "D2", "pin_name": "VSUP", "pin_description": "Analog Input Supply. Connect to VPWR."}, {"pin_number": "A3", "pin_name": "OUT3", "pin_description": "Regulator Output 3. Connect a 10µF (min) capacitor from this pin to ground."}, {"pin_number": "B3", "pin_name": "ON", "pin_description": "Push-Button Controller Input. Connect a 100kΩ resistor from ON to GND and momentary switch between ON and TTL Level Supply. Used to initiate power-up and power-down sequencing."}, {"pin_number": "C3", "pin_name": "RSTB", "pin_description": "Open-Drain Output to Indicate All Outputs are Active. Connect a pullup resistor between this pin and an external supply. Goes to logic-high only when all outputs are active."}, {"pin_number": "D3", "pin_name": "VIO", "pin_description": "Supply Voltage for the I2C Inputs. Determines the SDA and SCL thresholds. Connect to I2C supply rail."}, {"pin_number": "A4", "pin_name": "GND", "pin_description": "Analog Ground."}, {"pin_number": "B4", "pin_name": "IRQB", "pin_description": "I2C Interrupt Output. Connect a pullup resistor between this pin and an external supply."}, {"pin_number": "C4", "pin_name": "SDA", "pin_description": "I2C Data Input. Used to communicate with the part through the I2C interface."}, {"pin_number": "D4", "pin_name": "SCL", "pin_description": "I2C Clock Input. Used to communicate with the part through the I2C interface."}]}, {"product_part_number": "MAX17271", "package_type": "TQFN", "pins": [{"pin_number": "1", "pin_name": "LXA", "pin_description": "Buck-Boost Input-Side Inductor Connection. Connect a 2.2µH inductor between LXA and LXB."}, {"pin_number": "2", "pin_name": "BST", "pin_description": "Bootstrap pin for high-side output FET drivers. Connect a 3.3nF capacitor between BST and LXB."}, {"pin_number": "3", "pin_name": "PGND", "pin_description": "Buck-Boost Power Ground. Connect to the ground plane through a low impedance."}, {"pin_number": "4", "pin_name": "LXB", "pin_description": "Buck-Boost Output-Side Inductor Connection. Connect a 2.2µH inductor between LXA and LXB."}, {"pin_number": "5", "pin_name": "OUT1", "pin_description": "Regulator Output 1. Connect a 10µF (min) capacitor from this pin to ground."}, {"pin_number": "6", "pin_name": "OUT2", "pin_description": "Regulator Output 2. Connect a 10µF (min) capacitor from this pin to ground."}, {"pin_number": "7", "pin_name": "OUT3", "pin_description": "Regulator Output 3. Connect a 10µF (min) capacitor from this pin to ground."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Analog Ground."}, {"pin_number": "9", "pin_name": "ON", "pin_description": "Push-Button Controller Input. Connect a 100kΩ resistor from ON to GND and momentary switch between ON and TTL Level Supply. Used to initiate power-up and power-down sequencing."}, {"pin_number": "10", "pin_name": "IRQB", "pin_description": "I2C Interrupt Output. Connect a pullup resistor between this pin and an external supply."}, {"pin_number": "11", "pin_name": "SDA", "pin_description": "I2C Data Input. Used to communicate with the part through the I2C interface."}, {"pin_number": "12", "pin_name": "RSTB", "pin_description": "Open-Drain Output to Indicate All Outputs are Active. Connect a pullup resistor between this pin and an external supply. Goes to logic-high only when all outputs are active."}, {"pin_number": "13", "pin_name": "SCL", "pin_description": "I2C Clock Input. Used to communicate with the part through the I2C interface."}, {"pin_number": "14", "pin_name": "VIO", "pin_description": "Supply Voltage for the I2C Inputs. Determines the SDA and SCL thresholds. Connect to I2C supply rail."}, {"pin_number": "15", "pin_name": "VSUP", "pin_description": "Analog Input Supply. Connect to VPWR."}, {"pin_number": "16", "pin_name": "VPWR", "pin_description": "Buck-Boost Input Power Supply Pin. Connect a 10µF (min) capacitor from this pin to ground."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX17270/MAX17271.pdf", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 3, "max_input_voltage": "5.5V", "min_input_voltage": "2.7V", "max_output_voltage": "5.175V", "min_output_voltage": "1V", "max_output_current": "0.15A", "max_switch_frequency": "未找到", "quiescent_current": "1.3µA", "high_side_mosfet_resistance": "70mΩ", "low_side_mosfet_resistance": "55mΩ", "over_current_protection_threshold": "1.1A", "operation_mode": "同步", "output_voltage_config_method": "I2C可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}, "package": [{"height": "0.75", "length": "1.77", "width": "1.77", "type": "TQFN", "pin_count": "3", "pitch": "0.4"}]}]