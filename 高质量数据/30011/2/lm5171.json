{"part_number": "LM5171", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "LM5171 双通道双向控制器", "features": ["功能安全型", "高压 (HV) 端口和低压 (LV) 端口的最高额定电压分别为 85V 和 80V", "双向电流调节的典型精度为 1%", "通道电流监测的典型精度为 1%", "I2C 接口用于监测和诊断", "内置 3.5V 1% 基准电压", "集成 5V 10mA 辅助电源", "5A 峰值半桥栅极驱动器", "可编程或自适应死区时间控制", "可选择与外部时钟同步的可编程振荡器，频率高达 1MHz", "独立通道使能控制输入", "集成电流和电压环路控制", "可编程逐周期峰值电流限制", "过热关断", "HV 和 LV 端口过压保护", "动态可选二极管仿真和强制 PWM 工作模式", "可编程软启动计时器", "支持多相和独立通道运行", "支持紧急关断锁存"], "description": "LM5171 控制器可为双通道双向转换器提供高压和精密元件。相关示例包括双电池系统。LM5171 支持多相并行运行，每个相位均具有平衡的电流共享。LM5171 还可以支持独立通道双向运行，因此可用作多相降压/升压或独立降压/升压的独立控制器。双通道差分电流检测放大器和专用通道电流监测计可实现 1% 的典型精度。稳健的 5A 半桥栅极驱动器能够驱动并联的 MOSFET，从而在每个通道调节更高的功率。控制器可以进行动态编程，从而在二极管仿真模式 (DEM) 或强制 PWM (FPWM) 模式下运行。通用保护特性包括逐周期电流限制、HV 和 LV 端口过压保护，以及过热保护和紧急关断锁存。", "applications": ["电池测试系统 (BTS)", "超级电容或备用电池电源转换器", "可堆叠高功率降压或升压应用"], "ordering_information": [{"part_number": "LM5171", "order_device": "LM5171PHPR", "package_type": "HTQFP", "package_drawing_code": "PHP0048E", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LM5171", "order_device": "LM5171PHPR.A", "package_type": "HTQFP", "package_drawing_code": "PHP0048E", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "150"}], "pin_function": [{"product_part_number": "LM5171", "package_type": "TQFP-48", "pins": [{"pin_number": "1", "pin_name": "VREF", "pin_description": "内置 3.5V +/- 1% 基准电压输出。"}, {"pin_number": "2", "pin_name": "FBLV", "pin_description": "降压误差电压放大器的反相输入引脚。"}, {"pin_number": "3", "pin_name": "ERRLV", "pin_description": "降压误差电压放大器的输出引脚。"}, {"pin_number": "4", "pin_name": "IMON2", "pin_description": "CH-2 电流监测器引脚。"}, {"pin_number": "5", "pin_name": "CSA2", "pin_description": "CH-2 差分电流检测输入。"}, {"pin_number": "6", "pin_name": "CSB2", "pin_description": "CH-2 差分电流检测输入。"}, {"pin_number": "7", "pin_name": "ISET2", "pin_description": "CH-2 模拟电流编程引脚。"}, {"pin_number": "8", "pin_name": "COMP2", "pin_description": "CH-2 跨导 (gm) 误差放大器的输出和 CH-2 PWM 比较器的反相输入。"}, {"pin_number": "9", "pin_name": "SS/DEM2", "pin_description": "CH-2 控制器的软启动编程引脚。还会将 CH-2 设置为 DEM 或 FPWM 模式。"}, {"pin_number": "10", "pin_name": "EN2", "pin_description": "CH-2 使能引脚。"}, {"pin_number": "11", "pin_name": "DIR2", "pin_description": "CH-2 方向命令输入。"}, {"pin_number": "12", "pin_name": "VDD", "pin_description": "5V 内部 LDO 的输出。"}, {"pin_number": "13", "pin_name": "HV2", "pin_description": "连接到 CH-2 控制器 HV 端口线路电压的输入引脚。"}, {"pin_number": "14", "pin_name": "HB2", "pin_description": "CH-2 高侧栅极驱动器自举电源输入。"}, {"pin_number": "15", "pin_name": "HO2", "pin_description": "CH-2 高侧栅极驱动器输出。"}, {"pin_number": "16", "pin_name": "SW2", "pin_description": "CH-2 开关节点。"}, {"pin_number": "17", "pin_name": "LO2", "pin_description": "CH-2 低侧栅极驱动器输出。"}, {"pin_number": "18", "pin_name": "PGND", "pin_description": "电源接地引脚。"}, {"pin_number": "19", "pin_name": "VCC", "pin_description": "VCC 辅助电源引脚。"}, {"pin_number": "20", "pin_name": "LO1", "pin_description": "CH-1 低侧栅极驱动器输出。"}, {"pin_number": "21", "pin_name": "SW1", "pin_description": "CH-1 开关节点。"}, {"pin_number": "22", "pin_name": "HO1", "pin_description": "CH-1 高侧栅极驱动器输出。"}, {"pin_number": "23", "pin_name": "HB1", "pin_description": "CH-1 高侧栅极驱动器自举电源输入。"}, {"pin_number": "24", "pin_name": "HV1", "pin_description": "连接到 CH-1 控制器 HV 端口线路电压的输入引脚。"}, {"pin_number": "25", "pin_name": "LDODRV", "pin_description": "外部 VCC LDO MOSFET 的控制引脚。"}, {"pin_number": "26", "pin_name": "DIR1", "pin_description": "CH-1 方向命令输入。"}, {"pin_number": "27", "pin_name": "EN1", "pin_description": "CH-1 使能引脚。"}, {"pin_number": "28", "pin_name": "SS/DEM1", "pin_description": "CH-1 控制器的软启动编程引脚。该引脚还可将 CH-1 设置为 DEM 或 FPWM 模式。"}, {"pin_number": "29", "pin_name": "COMP1", "pin_description": "CH-1 跨导 (gm) 误差放大器的输出和 CH-1 PWM 比较器的反相输入。"}, {"pin_number": "30", "pin_name": "ISET1", "pin_description": "CH-1 模拟电流编程引脚。"}, {"pin_number": "31", "pin_name": "CSB1", "pin_description": "CH-1 差分电流检测输入。"}, {"pin_number": "32", "pin_name": "CSA1", "pin_description": "CH-1 差分电流检测输入。"}, {"pin_number": "33", "pin_name": "IMON1", "pin_description": "CH-1 电流监测器引脚。"}, {"pin_number": "34", "pin_name": "ERRHV", "pin_description": "升压误差电压放大器的输出引脚。"}, {"pin_number": "35", "pin_name": "FBHV", "pin_description": "升压误差电压放大器的反相输入引脚。"}, {"pin_number": "36", "pin_name": "OVP", "pin_description": "内置过压比较器的输入。"}, {"pin_number": "37", "pin_name": "SDA", "pin_description": "I2C 接口的数据。"}, {"pin_number": "38", "pin_name": "SCL", "pin_description": "I2C 接口的时钟。"}, {"pin_number": "39", "pin_name": "SYNCO", "pin_description": "时钟同步输出引脚。"}, {"pin_number": "40", "pin_name": "SYNCI", "pin_description": "时钟同步输入引脚。"}, {"pin_number": "41", "pin_name": "OPT", "pin_description": "多相配置引脚。"}, {"pin_number": "42", "pin_name": "OSC", "pin_description": "内部振荡器频率编程引脚。"}, {"pin_number": "43", "pin_name": "AGND", "pin_description": "模拟接地基准。"}, {"pin_number": "44", "pin_name": "CFG", "pin_description": "I2C 地址设置和电流监测器模式选择引脚。"}, {"pin_number": "45", "pin_name": "UVLO", "pin_description": "UVLO 引脚，也用作控制器-外设使能引脚。"}, {"pin_number": "46", "pin_name": "DT/SD", "pin_description": "死区时间编程和紧急锁存关断引脚。"}, {"pin_number": "47", "pin_name": "IPK", "pin_description": "峰值电流限制编程引脚。"}, {"pin_number": "48", "pin_name": "VSET", "pin_description": "电压误差放大器基准输入引脚。"}, {"pin_number": "EP", "pin_name": "EP", "pin_description": "封装的裸露焊盘。"}]}], "datasheet_cn": "LM5171 双通道双向控制器 (ZHCSWL4)", "datasheet_en": "SNVSCM3", "family_comparison": "未找到", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 2, "channel_count": 2, "max_input_voltage": "80V", "min_input_voltage": "1V", "max_output_voltage": "80V", "min_output_voltage": "1V", "max_output_current": "30A", "max_switch_frequency": "1MHz", "quiescent_current": "10mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "DEM", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "自动重启", "input_under_voltage_protection": "欠压锁定", "output_over_voltage_protection": "自动重启", "output_under_voltage_protection": "无", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "间歇式", "over_temperature_protection": "热关断", "pass_through_mode": "False", "output_discharge": "无", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "3.5V", "loop_control_mode": "平均电流模式"}, "package": [{"pitch": "0.5", "height": "1.2", "length": "7.2", "width": "7.2", "type": "OPTION", "pin_count": "2"}]}