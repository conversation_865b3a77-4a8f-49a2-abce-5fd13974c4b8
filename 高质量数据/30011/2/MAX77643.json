[{"part_number": "MAX77642", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PMIC", "category_lv3": "多通道电源", "part_number_title": "Ultra Configurable PMIC Featuring 93% Peak Efficiency Single-Inductor, 3-Output Buck-Boost, 1-LDO for Long Battery Life", "features": ["Highly Integrated: 3x Output, Single-Inductor Multiple-Output (SIMO) Buck-Boost Regulator (Supports Wide Output Voltage Range from 0.5V to 5.5V for all SIMO Channels), 1x 150mA LDO (100mA in LSW mode)", "Ultra Low-Power SIMO: 5μA Operating Current (3x SIMO Channels + 1x LDOs), 1μA Operating Current per SIMO Channel, 0.3μA Shutdown Current, 93% Peak Efficiency in Boost-Only Mode, 91% Peak Efficiency in Buck-Only Mode, Less Than 20mVpp Output Ripple at VOUT = 1.8V, Automatic Low-Power Mode to Normal-Power Mode Transition", "Flexible and Configurable: Ultra-Configurable Resistor Programmable Output Voltages (MAX77642)", "Small Size: 4.24mm2 Wafer-Level Package (WLP), 25-Bump, 0.4mm Pitch, 5 x 5 Array"], "description": "The MAX77642 provides a power supply solution for low-power applications where size and efficiency are critical. The IC features a SIMO buck-boost regulator that provides three independently programmable power rails from a single inductor to minimize total solution size. A 150mA LDO provides ripple rejection for audio and other noise-sensitive applications. The MAX77642's SIMO and LDO output voltages are individually programmable through resistors. A peak current limit input is used to set the inductor's peak current limit with a single resistor. Individual enable pins combined with the flexible resistor programmability allows the device to be tailored for many applications.", "applications": ["Next Generation Hearables", "Fitness, Health, and Activity Monitors", "Safety and Security Monitors", "Portable Consumer Devices"], "ordering_information": [{"part_number": "MAX77642", "order_device": "MAX77642ANA+T", "package_type": "WLP", "package_drawing_code": "21-100480", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MAX77642", "package_type": "25 WLP", "pins": [{"pin_number": "A1, B1", "pin_name": "SYSA", "pin_description": "Analog Input Supply. Connect to VIN_SBB."}, {"pin_number": "A2", "pin_name": "RSET_SBB2", "pin_description": "Select Resistor Pin SBB2. Connect a resistor from this pin to GND, using the value to configure the output voltage of SBB2."}, {"pin_number": "A3", "pin_name": "RSET_SBB1", "pin_description": "Select Resistor Pin SBB1. Connect a resistor from this pin to GND, using the value to configure the output voltage of SBB1."}, {"pin_number": "A4", "pin_name": "LDO", "pin_description": "Linear Regulator Output. Bypass with a 1.0µF ceramic capacitor to GND. If not used, disable LDO and connect this pin to ground or leave unconnected."}, {"pin_number": "A5", "pin_name": "IN_LDO", "pin_description": "Linear Regulator Input. If connected to a SIMO output with a short trace, IN_LDO can share the output's capacitor. Otherwise, bypass with a 2.2µF ceramic capacitor to ground. If not used, connect to ground or leave unconnected."}, {"pin_number": "B2", "pin_name": "RSET_LDO", "pin_description": "Select Resistor Pin LDO. Connect a resistor from this pin to GND, using the value to configure the output voltage of LDO0."}, {"pin_number": "B3", "pin_name": "RSET_SBB0", "pin_description": "Select Resistor Pin SBB0. Connect a resistor from this pin to GND, using the value to configure the output voltage of SBB0."}, {"pin_number": "B4, B5, E2", "pin_name": "GND", "pin_description": "Quiet Ground. Connect GND to PGND, and the low-impedance ground plane of the PCB."}, {"pin_number": "C1", "pin_name": "ENLDO", "pin_description": "Enable Input for LDO. Hold high to enable output regulation. Hold low to disable the output."}, {"pin_number": "C2", "pin_name": "RSET_IPK", "pin_description": "Select Resistor Pin IPK. Connect a resistor from this pin to GND, using the value to configure the peak inductor current."}, {"pin_number": "C3", "pin_name": "SBB1", "pin_description": "SIMO Buck-Boost Output 1. SBB1 is the power output for channel 1 of the SIMO buck-boost. Bypass SBB1 to PGND with a 10µF ceramic capacitor."}, {"pin_number": "C4, D4", "pin_name": "LXB", "pin_description": "Switching Node B. Connect a 1.5µH inductor between LXA and LXB."}, {"pin_number": "C5", "pin_name": "SBB0", "pin_description": "SIMO Buck-Boost Output 0. SBB0 is the power output for channel 0 of the SIMO buck-boost. Bypass SBB0 to PGND with a 10µF ceramic capacitor."}, {"pin_number": "D1", "pin_name": "EN1", "pin_description": "Enable Input for SBB1. Hold high to enable output regulation. Hold low to disable the output."}, {"pin_number": "D2", "pin_name": "EN0", "pin_description": "Enable Input for SBB0. Hold high to enable output regulation. Hold low to disable the output."}, {"pin_number": "D3", "pin_name": "SBB2", "pin_description": "SIMO Buck-Boost Output 2. SBB2 is the power output for channel 2 of the SIMO buck-boost. Bypass SBB2 to PGND with a 10µF ceramic capacitor."}, {"pin_number": "D5", "pin_name": "BST", "pin_description": "SIMO Power Input for the High-Side Output NMOS Drivers. Connect a 10nF ceramic capacitor between BST and LXB."}, {"pin_number": "E1", "pin_name": "EN2", "pin_description": "Enable Input for SBB2. Hold high to enable output regulation. Hold low to disable the output."}, {"pin_number": "E3", "pin_name": "IN_SBB", "pin_description": "SIMO Power Input. Connect IN_SBB to SYSA and bypass to PGND with a minimum of 10µF ceramic capacitor."}, {"pin_number": "E4", "pin_name": "LXA", "pin_description": "Switching Node A. Connect a 1.5µH inductor between LXA and LXB."}, {"pin_number": "E5", "pin_name": "PGND", "pin_description": "Power Ground for the SIMO Low-Side FETs. Connect PGND to GND, and the low-impedance ground plane of the PCB."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77642/MAX77643.pdf", "family_comparison": "MAX77642 is the resistor-programmable version, featuring resistor-programmable output voltages and peak current, with individual hardware enable pins for each regulator. It does not support I2C communication, a watchdog timer, or a flexible power sequencer.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "5.5V", "min_input_voltage": "2.7V", "max_output_voltage": "5.5V", "min_output_voltage": "1V", "max_output_current": "0.5A", "max_switch_frequency": "未找到", "quiescent_current": "5μA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "1A", "operation_mode": "同步", "output_voltage_config_method": "外部可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "Latch", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.5", "length": "2.06", "width": "2.06", "type": "WLP", "pin_count": "28"}]}, {"part_number": "MAX77643", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PMIC", "category_lv3": "多通道电源", "part_number_title": "Ultra Configurable PMIC Featuring 93% Peak Efficiency Single-Inductor, 3-Output Buck-Boost, 1-LDO for Long Battery Life", "features": ["Highly Integrated: 3x Output, Single-Inductor Multiple-Output (SIMO) Buck-Boost Regulator (Supports Wide Output Voltage Range from 0.5V to 5.5V for all SIMO Channels), 1x 150mA LDO (100mA in LSW mode), 2x GPIOs (MAX77643), Watchdog Timer (MAX77643)", "Ultra Low-Power SIMO: 5μA Operating Current (3x SIMO Channels + 1x LDOs), 1μA Operating Current per SIMO Channel, 0.3μA Shutdown Current, 93% Peak Efficiency in Boost-Only Mode, 91% Peak Efficiency in Buck-Only Mode, Less Than 20mVpp Output Ripple at VOUT = 1.8V, Automatic Low-Power Mode to Normal-Power Mode Transition", "Flexible and Configurable: I2C-Programmable Output Voltages (MAX77643)", "Small Size: 4.24mm2 Wafer-Level Package (WLP), 25-Bump, 0.4mm Pitch, 5 x 5 Array"], "description": "The MAX77643 provides a power supply solution for low-power applications where size and efficiency are critical. The IC features a SIMO buck-boost regulator that provides three independently programmable power rails from a single inductor to minimize total solution size. A 150mA LDO provides ripple rejection for audio and other noise-sensitive applications. The MAX77643's SIMO and LDO output voltages are individually programmable through I2C and it also includes two GPIOs with alternate modes for scalability. A bidirectional I2C serial interface allows for configuring and checking the status of the devices. An internal on/off controller provides a controlled startup sequence for the regulators and provides supervisory functionality while they are on.", "applications": ["Next Generation Hearables", "Fitness, Health, and Activity Monitors", "Safety and Security Monitors", "Portable Consumer Devices"], "ordering_information": [{"part_number": "MAX77643", "order_device": "MAX77643ANA+T*", "package_type": "WLP", "package_drawing_code": "21-100480", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77643", "order_device": "MAX77643AANA+T", "package_type": "WLP", "package_drawing_code": "21-100480", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77643", "order_device": "MAX77643SANA+T", "package_type": "WLP", "package_drawing_code": "21-100480", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77643", "order_device": "MAX77643BANA+T", "package_type": "WLP", "package_drawing_code": "21-100480", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77643", "order_device": "MAX77643CANA+T", "package_type": "WLP", "package_drawing_code": "21-100480", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77643", "order_device": "MAX77643EANA+T", "package_type": "WLP", "package_drawing_code": "21-100480", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "MAX77643", "order_device": "MAX77643DANA+T", "package_type": "WLP", "package_drawing_code": "21-100480", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "MAX77643", "package_type": "25 WLP", "pins": [{"pin_number": "A1", "pin_name": "Vio", "pin_description": "I2C Interface and GPIO Driver Power"}, {"pin_number": "A2", "pin_name": "SCL", "pin_description": "I2C Clock"}, {"pin_number": "A3", "pin_name": "SDA", "pin_description": "I2C Data"}, {"pin_number": "A4", "pin_name": "LDO", "pin_description": "Linear Regulator Output. Bypass with a 1.0µF ceramic capacitor to GND. If not used, disable LDO and connect this pin to ground or leave unconnected."}, {"pin_number": "A5", "pin_name": "IN_LDO", "pin_description": "Linear Regulator Input. If connected to a SIMO output with a short trace, IN_LDO can share the output's capacitor. Otherwise, bypass with a 2.2µF ceramic capacitor to ground. If not used, connect to ground or leave unconnected."}, {"pin_number": "B1", "pin_name": "SYSA", "pin_description": "Analog Input Supply. Connect to VIN_SBB."}, {"pin_number": "B2", "pin_name": "GPIO0", "pin_description": "General Purpose Input/Output. The GPIO I/O stage is internally biased with Vio."}, {"pin_number": "B3, C1", "pin_name": "NC", "pin_description": "Not Connected"}, {"pin_number": "B4, B5, E2", "pin_name": "GND", "pin_description": "Quiet Ground. Connect GND to PGND, and the low-impedance ground plane of the PCB."}, {"pin_number": "C2", "pin_name": "GPIO1", "pin_description": "General Purpose Input/Output. The GPIO I/O stage is internally biased with Vio."}, {"pin_number": "C3", "pin_name": "SBB1", "pin_description": "SIMO Buck-Boost Output 1. SBB1 is the power output for channel 1 of the SIMO buck-boost. Bypass SBB1 to PGND with a 10µF ceramic capacitor."}, {"pin_number": "C4, D4", "pin_name": "LXB", "pin_description": "Switching Node B. Connect a 1.5µH inductor between LXA and LXB."}, {"pin_number": "C5", "pin_name": "SBB0", "pin_description": "SIMO Buck-Boost Output 0. SBB0 is the power output for channel 0 of the SIMO buck-boost. Bypass SBB0 to PGND with a 10µF ceramic capacitor."}, {"pin_number": "D1", "pin_name": "nIRQ", "pin_description": "Active-Low, Open-Drain Interrupt Output. Connect a 100kΩ pullup resistor between IRQ and a voltage equal to or less than VSYSA."}, {"pin_number": "D2", "pin_name": "nEN", "pin_description": "Active-Low Enable Input. EN supports push-button or slide-switch configurations."}, {"pin_number": "D3", "pin_name": "SBB2", "pin_description": "SIMO Buck-Boost Output 2. SBB2 is the power output for channel 2 of the SIMO buck-boost. Bypass SBB2 to PGND with a 10µF ceramic capacitor."}, {"pin_number": "D5", "pin_name": "BST", "pin_description": "SIMO Power Input for the High-Side Output NMOS Drivers. Connect a 10nF ceramic capacitor between BST and LXB."}, {"pin_number": "E1", "pin_name": "nRST", "pin_description": "Active-Low, Open-Drain Reset Output. Connect a 100kΩ pullup resistor between nRST and a voltage equal to or less than VSYSA."}, {"pin_number": "E3", "pin_name": "IN_SBB", "pin_description": "SIMO Power Input. Connect IN_SBB to SYSA and bypass to PGND with a minimum of 10µF ceramic capacitor."}, {"pin_number": "E4", "pin_name": "LXA", "pin_description": "Switching Node A. Connect a 1.5µH inductor between LXA and LXB."}, {"pin_number": "E5", "pin_name": "PGND", "pin_description": "Power Ground for the SIMO Low-Side FETs. Connect PGND to GND, and the low-impedance ground plane of the PCB."}]}], "datasheet_cn": "未找到", "datasheet_en": "MAX77642/MAX77643.pdf", "family_comparison": "MAX77643 is the I2C-programmable version, featuring I2C-programmable output voltages, two GPIOs with alternate modes, a watchdog timer, and a programmable flexible power sequencer. It does not use resistors for configuration.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "5.5V", "min_input_voltage": "2.7V", "max_output_voltage": "5.5V", "min_output_voltage": "1V", "max_output_current": "0.5A", "max_switch_frequency": "未找到", "quiescent_current": "5μA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "1A", "operation_mode": "同步", "output_voltage_config_method": "通信接口可调", "communication_interface": "I2C", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "Latch", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2%", "output_reference_voltage": "不适用", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.4", "height": "0.5", "length": "2.06", "width": "2.06", "type": "WLP", "pin_count": "28"}]}]