{"part_number": "LT3790", "manufacturer": "Analog Devices", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "降压升压型稳压器", "part_number_title": "60V Synchronous 4-<PERSON><PERSON> <PERSON>-<PERSON>ost Controller", "features": ["4-Switch Single Inductor Architecture Allows VIN Above, Below or Equal to VOUT", "Synchronous Switching: Up to 98.5% Efficiency", "Wide VIN Range: 4.7V to 60V", "2% Output Voltage Accuracy: 1.2V ≤ VOUT < 60V", "6% Output Current Accuracy: 0V ≤ VOUT < 60V", "Input and Output Current Regulation with Current Monitor Outputs", "No Top FET Refresh in Buck or Boost", "VOUT Disconnected from VIN During Shutdown", "C/10 Charge Termination and Output Shorted Flags", "Capable of 100W or Greater per IC", "Easy Parallel Capability to Extend Output Power", "38-Lead TSSOP with Exposed Pad"], "description": "The LT®3790 is a synchronous 4-switch buck-boost voltage/current regulator controller. The LT3790 can regulate output voltage, output current, or input current with input voltages above, below, or equal to the output voltage. The constant-frequency, current mode architecture allows its frequency to be adjusted or synchronized from 200kHz to 700kHz. No top FET refresh switching cycle is needed in buck or boost operation. With 60V input, 60V output capability and seamless transitions between operating regions, the LT3790 is ideal for voltage regulator, battery/super-capacitor charger applications in automotive, industrial, telecom, and even battery-powered systems.\nThe LT3790 provides input current monitor, output current monitor, and various status flags, such as C/10 charge termination and shorted output flag.", "applications": ["Automotive, Telecom, Industrial Systems", "High Power Battery-Powered System"], "ordering_information": [{"part_number": "LT3790", "order_device": "LT3790EFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE38", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT3790", "order_device": "LT3790EFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE38", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT3790", "order_device": "LT3790IFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE38", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT3790", "order_device": "LT3790IFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE38", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "LT3790", "order_device": "LT3790HFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE38", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT3790", "order_device": "LT3790HFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE38", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "150"}, {"part_number": "LT3790", "order_device": "LT3790MPFE#PBF", "package_type": "TSSOP", "package_drawing_code": "FE38", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}, {"part_number": "LT3790", "order_device": "LT3790MPFE#TRPBF", "package_type": "TSSOP", "package_drawing_code": "FE38", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "150"}], "pin_function": [{"product_part_number": "LT3790", "package_type": "TSSOP-38", "pins": [{"pin_number": "1", "pin_name": "CTRL", "pin_description": "Output Current Sense Threshold Adjustment Pin. Regulating threshold V(ISP-ISN) is 1/20th of VCTRL. CTRL linear range is from 0V to 1.1V. For VCTRL > 1.3V, the current sense threshold is constant at the full-scale value of 60mV. Connect CTRL to VREF for the 60mV default threshold. Force less than 50mV (typical) to stop switching. Do not leave this pin open."}, {"pin_number": "2", "pin_name": "SS", "pin_description": "Soft-start reduces the input power sources surge current by gradually increasing the controller’s current limit. A minimum value of 22nF is recommended on this pin. A 100k resistor must be placed between SS and VREF for the LT3790."}, {"pin_number": "3", "pin_name": "PWM", "pin_description": "A signal low turns off switches, idles switching and disconnects the VC pin from all external loads. The PWMOUT pin follows the PWM pin. PWM has an internal 90k pull-down resistor. If not used, connect to INTVCC."}, {"pin_number": "4", "pin_name": "C/10", "pin_description": "C/10 Charge Termination Pin. An open-drain pull-down on C/10 asserts if V(ISP-ISN) is less than 5mV (typical). To function, the pin requires an external pull-up resistor."}, {"pin_number": "5", "pin_name": "SHORT", "pin_description": "Output Shorted Pin. An open-drain pull-down on SHORT asserts if FB is less than 400mV (typical) and V(ISP-ISN) is larger than 5mV (typical). To function, the pin requires an external pull-up resistor."}, {"pin_number": "6", "pin_name": "VREF", "pin_description": "Voltage Reference Output Pin, Typically 2V. This pin drives a resistor divider for the CTRL pin. Can supply up to 200μA of current."}, {"pin_number": "7", "pin_name": "ISMON", "pin_description": "Monitor pin that produces a voltage that is twenty times the voltage V(ISP-ISN). ISMON will equal 1.2V when V(ISP-ISN) = 60mV. For parallel applications, tie master LT3790 ISMON pin to slave LT3790 CTRL pin."}, {"pin_number": "8", "pin_name": "IVINMON", "pin_description": "Monitor pin that produces a voltage that is twenty times the voltage V(IVINP-IVINN). IVINMON will equal 1V when V(IVINP-IVINN) = 50mV."}, {"pin_number": "9", "pin_name": "EN/UVLO", "pin_description": "Enable Control Pin. Forcing an accurate 1.2V falling threshold with an externally programmable hysteresis. Tie to 0.3V, or less, to disable the device and reduce VIN quiescent current below 1μA."}, {"pin_number": "10", "pin_name": "IVINP", "pin_description": "Positive Input for the Input Current Limit and Monitor. Input bias current for this pin is typically 90μA."}, {"pin_number": "11", "pin_name": "IVINN", "pin_description": "Negative Input for the Input Current Limit and Monitor. The input bias current for this pin is typically 20μA."}, {"pin_number": "12", "pin_name": "VIN", "pin_description": "Main Input Supply. Bypass this pin to PGND with a capacitor."}, {"pin_number": "13", "pin_name": "INTVCC", "pin_description": "Internal 5V Regulator Output. The driver and control circuits are powered from this voltage. Bypass this pin to PGND with a minimum 4.7μF ceramic capacitor."}, {"pin_number": "14", "pin_name": "TG1", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFET with a voltage equal to INTVCC superimposed on the switch node voltage SW1."}, {"pin_number": "15", "pin_name": "BST1", "pin_description": "Bootstrapped Driver Supply. The BST1 pin swings from a diode voltage below INTVCC up to a diode voltage below VIN + INTVCC."}, {"pin_number": "16", "pin_name": "SW1", "pin_description": "Switch Node. SW1 pin swings from a diode voltage drop below ground up to VIN."}, {"pin_number": "17, 20", "pin_name": "PGND", "pin_description": "Power Ground. Connect these pins closely to the source of the bottom N-channel MOSFET."}, {"pin_number": "18", "pin_name": "BG1", "pin_description": "Bottom Gate Drive. Drives the gate of the bottom N-channel MOSFET between ground and INTVCC."}, {"pin_number": "19", "pin_name": "BG2", "pin_description": "Bottom Gate Drive. Drives the gate of the bottom N-channel MOSFET between ground and INTVCC."}, {"pin_number": "21", "pin_name": "SW2", "pin_description": "Switch Node. SW2 pin swings from a diode voltage drop below ground up to VOUT."}, {"pin_number": "22", "pin_name": "BST2", "pin_description": "Bootstrapped Driver Supply. The BST2 pin swings from a diode voltage below INTVCC up to a diode voltage below VOUT + INTVCC."}, {"pin_number": "23", "pin_name": "NC", "pin_description": "No Connect Pin. Leave this pin floating."}, {"pin_number": "24", "pin_name": "TG2", "pin_description": "Top Gate Drive. Drives the top N-channel MOSFET with a voltage equal to INTVCC superimposed on the switch node voltage SW2."}, {"pin_number": "25", "pin_name": "ISP", "pin_description": "Connection Point for the Positive Terminal of the Output Current Feedback Resistor."}, {"pin_number": "26", "pin_name": "ISN", "pin_description": "Connection Point for the Negative Terminal of the Output Current Feedback Resistor."}, {"pin_number": "27", "pin_name": "SNSP", "pin_description": "The Positive Input to the Current Sense Comparator. The VC pin voltage and controlled offsets between the SNSP and SNSN pins, in conjunction with a resistor, set the current trip threshold."}, {"pin_number": "28", "pin_name": "SNSN", "pin_description": "The Negative Input to the Current Sense Comparator."}, {"pin_number": "29", "pin_name": "TEST1", "pin_description": "This pin is used for testing purposes only and must be connected to SGND for the part to operate properly."}, {"pin_number": "30, 39", "pin_name": "SGND", "pin_description": "Signal Ground. All small-signal components and compensation should connect to this ground, which should be connected to PGND at a single point. Solder the exposed pad directly to the ground plane."}, {"pin_number": "31", "pin_name": "PWMOUT", "pin_description": "Buffered Version of PWM Signal for Driving Output Load Disconnect N-Channel MOSFET. The PWMOUT pin is driven from INTVCC. Use of a MOSFET with a gate cutoff voltage higher than 1V is recommended."}, {"pin_number": "32", "pin_name": "CCM", "pin_description": "Continuous Conduction Mode Pin. When the pin voltage is higher than 1.5V, the part runs in fixed frequency forced continuous conduction mode. When the pin voltage is less than 0.3V, the part runs in discontinuous conduction mode."}, {"pin_number": "33", "pin_name": "CLKOUT", "pin_description": "Clock Output Pin. A 180° out-of-phase clock is provided at the oscillator frequency to allow for paralleling two devices for extending output power capability."}, {"pin_number": "34", "pin_name": "SYNC", "pin_description": "External Synchronization Input Pin. This pin is internally terminated to GND with a 90k resistor. The internal buck clock is synchronized to the rising edge of the SYNC signal while the internal boost clock is 180° phase shifted."}, {"pin_number": "35", "pin_name": "RT", "pin_description": "Frequency Set Pin. Place a resistor to GND to set the internal frequency. The range of oscillation is 200kHz to 700kHz."}, {"pin_number": "36", "pin_name": "VC", "pin_description": "Current Control Threshold and Error Amplifier Compensation Point. The current comparator threshold increases with this control voltage. The voltage ranges from 0.7V to 1.9V."}, {"pin_number": "37", "pin_name": "FB", "pin_description": "Voltage Loop Feedback Pin. FB is intended for constant-voltage regulation. The internal transconductance amplifier with output VC will regulate FB to 1.2V (typical) through the DC/DC converter."}, {"pin_number": "38", "pin_name": "OVLO", "pin_description": "Overvoltage Input Pin. This pin is used for OVLO, if OVLO > 3V then SS is pulled low, the part stops switching and resets. Do not leave this pin open."}]}], "datasheet_cn": "未找到", "datasheet_en": "LT3790 Datasheet Rev. B", "family_comparison": "The LT3790 is an improved version of the LT3791-1, recommended for new designs. Key differences include: 1. Output current sense voltage (60mV vs 100mV for LT3791-1), allowing lower power sense resistors. 2. CTRL pin linear range (0V-1.1V vs 0.2V-1.1V), making paralleling easier. 3. C/10 pin pull-low condition is simplified.", "attributes": {"power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "60V", "min_input_voltage": "4.7V", "max_output_voltage": "60V", "min_output_voltage": "1.2V", "max_output_current": "Dependent on external components", "max_switch_frequency": "0.7MHz", "quiescent_current": "3000µA", "high_side_mosfet_resistance": "不适用(外部)", "low_side_mosfet_resistance": "不适用(外部)", "over_current_protection_threshold": "Adjustable", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "DCM", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "Latch", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Latch", "pass_through_mode": "False", "output_discharge": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "1.2V", "loop_control_mode": "峰值电流模式"}, "package": [{"pitch": "0.5", "height": "1.2", "length": "9.7", "width": "4.4", "type": "DESCRIPTION", "pin_count": "1"}]}