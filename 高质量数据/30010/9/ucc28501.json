{"response": "\n[\n  {\n    \"part_number\": \"UCC28500\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Industry\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"AC/DC转换器\",\n    \"category_lv3\": \"PFC/PWM组合控制器\",\n    \"part_number_title\": \"BICMOS PFC/PWM COMBINATION CONTROLLER\",\n    \"features\": [\n      \"Combines PFC and Downstream Converter Controls\",\n      \"Controls Boost Preregulator to Near-Unity Power Factor\",\n      \"Accurate Power Limiting\",\n      \"Improved Feedforward Line Regulation\",\n      \"Peak Current-Mode Control in Second Stage\",\n      \"Programmable Oscillator\",\n      \"Leading-Edge/Trailing-Edge Modulation for Reduced Output Ripple\",\n      \"Low Start-up Supply Current\",\n      \"Synchronized Second Stage Start-Up, with Programmable Soft-start\",\n      \"Programmable Second Stage Shutdown\"\n    ],\n    \"description\": \"The UCC2850x family provides all of the control functions necessary for an active power-factor-corrected preregulator and a second-stage dc-to-dc converter. The controller achieves near-unity power factor by shaping the ac input line current waveform to correspond to the ac input-line voltage using average current-mode control. The dc-to-dc converter uses peak current-mode control. This model, UCC28500, has a wide VCC UVLO threshold (16.5V/10V) and a narrow PWM stage UVLO threshold (operation down to 75% of nominal bulk voltage).\",\n    \"applications\": [\n      \"Active power-factor-corrected preregulator\",\n      \"Second-stage dc-to-dc converter\",\n      \"Off-line power converters\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UCC28500\",\n        \"order_device\": \"UCC28500DW\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"DW\",\n        \"carrier_description\": \"TUBE\",\n        \"min_operation_temp\": \"-40\",\n        \"max_operation_temp\": \"85\"\n      },\n      {\n        \"part_number\": \"UCC28500\",\n        \"order_device\": \"UCC28500DWTR\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"DW\",\n        \"carrier_description\": \"TAPE AND REEL\",\n        \"min_operation_temp\": \"-40\",\n        \"max_operation_temp\": \"85\"\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"VAOUT\",\n        \"description\": \"(voltage amplifier output) This is the output of the operational amplifier that regulates output voltage. The voltage amplifier output is internally limited to approximately 5.5 V to prevent overshoot.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"RT\",\n        \"description\": \"(oscillator charging current) A resistor from RT to GND is used to program oscillator charging current. A resistor between 10 kΩ and 100 kΩ is recommended. Nominal voltage on this pin is 3 V.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VSENSE\",\n        \"description\": \"(voltage amplifier inverting input) This is normally connected to a compensation network and to the boost converter output through a divider network.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OVP/ENBL\",\n        \"description\": \"(over-voltage/enable) A window comparator input which disables the PFC output driver if the boost output is 6.67% above nominal or disables both the PFC and second stage output drivers and reset SS2 if pulled below 1.9 V. This input is also used to determine the active range of the second stage PWM.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"CT\",\n        \"description\": \"(oscillator timing capacitor) A capacitor from CT to GND sets the oscillator frequency.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"GND\",\n        \"description\": \"(ground) All voltages measured with respect to ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"VERR\",\n        \"description\": \"(voltage amp error signal for the second stage) The error signal is generated by an external amplifier which drives this pin. This pin has an internal 4.5-V voltage clamp that limits GT2 to less than 50% duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"ISENSE2\",\n        \"description\": \"(current sense) A resistor from the source of the lower FET to ground generates the input signal for the peak limit control of the second stage.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"VCC\",\n        \"description\": \"(positive supply voltage) Connect to a stable source of at least 20 mA between 12 V and 17 V for normal operation.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"GT2\",\n        \"description\": \"(gate drive) Same as output GT1 for the second stage output drive. Limited to 50% maximum duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"PWRGND\",\n        \"description\": \"Ground for totem pole output drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"GT1\",\n        \"description\": \"(gate drive) The output drive for the PFC stage is a totem pole MOSFET gate driver on GT1.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"SS2\",\n        \"description\": \"(soft-start for PWM) SS2 is at ground for either enable low or OVP/ENBL below the UVLO2 threshold conditions. When enabled, SS2 charges an external capacitor with a current source.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"PKLMT\",\n        \"description\": \"(PFC peak current limit) The threshold for peak limit is 0 V. Use a resistor divider from the negative side of the current sense resistor to VREF to level-shift this signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"CAOUT\",\n        \"description\": \"(current amplifier output) This is the output of a wide bandwidth operational amplifier that senses line current and commands the PFC pulse width modulator (PWM) to force the correct duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"ISENSE1\",\n        \"description\": \"(current sense) This is the non-inverting input to the current amplifier.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"17\",\n        \"pin_name\": \"MOUT\",\n        \"description\": \"(multiplier output and current sense amplifier inverting input) The output of the analog multiplier and the inverting input of the current amplifier are connected together at MOUT.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"18\",\n        \"pin_name\": \"IAC\",\n        \"description\": \"(input ac current) This input to the analog multiplier is a current. Recommended maximum IAC is 500 μA.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"19\",\n        \"pin_name\": \"VFF\",\n        \"description\": \"(RMS feed forward signal) VFF signal is generated at this pin by mirroring one-half of IAC into a single pole external filter.\"\n      },\n      {\n        \"product_part_number\": \"UCC28500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"20\",\n        \"pin_name\": \"VREF\",\n        \"description\": \"(voltage reference output) VREF is the output of an accurate 7.5-V voltage reference.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS419C\",\n    \"family_comparison\": \"The UCC2850x family differs in UVLO thresholds and operating temperature ranges. UCC28500/UCC28502 have a wide VCC UVLO threshold (16.5 V/10 V). UCC28501/UCC28503 have a narrow VCC UVLO range (10.5 V/10 V). UCC28500/UCC28501 have a narrow PWM stage UVLO threshold (operation down to 75% of nominal bulk voltage). UCC28502/UCC38503 have a wider operation range for the PWM stage (down to 50% of bulk nominal voltage). UCC2850x series operates from -40°C to 85°C, while UCC3850x series operates from 0°C to 70°C.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 2,\n      \"max_input_voltage\": \"18V\",\n      \"min_input_voltage\": \"16V\",\n      \"max_output_voltage\": \"可调\",\n      \"min_output_voltage\": \"可调\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"4mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"1.3V\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"无\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"Latch\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"Latch\",\n      \"output_under_voltage_protection\": \"75% of nominal\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Current Limit\",\n      \"over_temperature_protection\": \"无\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"峰值电流模式, 平均电流模式\",\n      \"output_voltage_accuracy\": \"±2%\",\n      \"output_reference_voltage\": \"7.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC28501\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Military\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"AC/DC转换器\",\n    \"category_lv3\": \"PFC/PWM组合控制器\",\n    \"part_number_title\": \"BICMOS PFC/PWM COMBINATION CONTROLLER\",\n    \"features\": [\n      \"Combines PFC and Downstream Converter Controls\",\n      \"Controls Boost Preregulator to Near-Unity Power Factor\",\n      \"Accurate Power Limiting\",\n      \"Improved Feedforward Line Regulation\",\n      \"Peak Current-Mode Control in Second Stage\",\n      \"Programmable Oscillator\",\n      \"Leading-Edge/Trailing-Edge Modulation for Reduced Output Ripple\",\n      \"Low Start-up Supply Current\",\n      \"Synchronized Second Stage Start-Up, with Programmable Soft-start\",\n      \"Programmable Second Stage Shutdown\"\n    ],\n    \"description\": \"The UCC2850x family provides all of the control functions necessary for an active power-factor-corrected preregulator and a second-stage dc-to-dc converter. The controller achieves near-unity power factor by shaping the ac input line current waveform to correspond to the ac input-line voltage using average current-mode control. The dc-to-dc converter uses peak current-mode control. This model, UCC28501, has a narrow VCC UVLO range (10.5V/10V) and a narrow PWM stage UVLO threshold (operation down to 75% of nominal bulk voltage).\",\n    \"applications\": [\n      \"Active power-factor-corrected preregulator\",\n      \"Second-stage dc-to-dc converter\",\n      \"Off-line power converters\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UCC28501\",\n        \"order_device\": \"UCC28501DW\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"DW\",\n        \"carrier_description\": \"TUBE\",\n        \"min_operation_temp\": \"-55\",\n        \"max_operation_temp\": \"125\"\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"VAOUT\",\n        \"description\": \"(voltage amplifier output) This is the output of the operational amplifier that regulates output voltage. The voltage amplifier output is internally limited to approximately 5.5 V to prevent overshoot.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"RT\",\n        \"description\": \"(oscillator charging current) A resistor from RT to GND is used to program oscillator charging current. A resistor between 10 kΩ and 100 kΩ is recommended. Nominal voltage on this pin is 3 V.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VSENSE\",\n        \"description\": \"(voltage amplifier inverting input) This is normally connected to a compensation network and to the boost converter output through a divider network.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OVP/ENBL\",\n        \"description\": \"(over-voltage/enable) A window comparator input which disables the PFC output driver if the boost output is 6.67% above nominal or disables both the PFC and second stage output drivers and reset SS2 if pulled below 1.9 V. This input is also used to determine the active range of the second stage PWM.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"CT\",\n        \"description\": \"(oscillator timing capacitor) A capacitor from CT to GND sets the oscillator frequency.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"GND\",\n        \"description\": \"(ground) All voltages measured with respect to ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"VERR\",\n        \"description\": \"(voltage amp error signal for the second stage) The error signal is generated by an external amplifier which drives this pin. This pin has an internal 4.5-V voltage clamp that limits GT2 to less than 50% duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"ISENSE2\",\n        \"description\": \"(current sense) A resistor from the source of the lower FET to ground generates the input signal for the peak limit control of the second stage.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"VCC\",\n        \"description\": \"(positive supply voltage) Connect to a stable source of at least 20 mA between 12 V and 17 V for normal operation.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"GT2\",\n        \"description\": \"(gate drive) Same as output GT1 for the second stage output drive. Limited to 50% maximum duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"PWRGND\",\n        \"description\": \"Ground for totem pole output drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"GT1\",\n        \"description\": \"(gate drive) The output drive for the PFC stage is a totem pole MOSFET gate driver on GT1.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"SS2\",\n        \"description\": \"(soft-start for PWM) SS2 is at ground for either enable low or OVP/ENBL below the UVLO2 threshold conditions. When enabled, SS2 charges an external capacitor with a current source.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"PKLMT\",\n        \"description\": \"(PFC peak current limit) The threshold for peak limit is 0 V. Use a resistor divider from the negative side of the current sense resistor to VREF to level-shift this signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"CAOUT\",\n        \"description\": \"(current amplifier output) This is the output of a wide bandwidth operational amplifier that senses line current and commands the PFC pulse width modulator (PWM) to force the correct duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"ISENSE1\",\n        \"description\": \"(current sense) This is the non-inverting input to the current amplifier.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"17\",\n        \"pin_name\": \"MOUT\",\n        \"description\": \"(multiplier output and current sense amplifier inverting input) The output of the analog multiplier and the inverting input of the current amplifier are connected together at MOUT.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"18\",\n        \"pin_name\": \"IAC\",\n        \"description\": \"(input ac current) This input to the analog multiplier is a current. Recommended maximum IAC is 500 μA.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"19\",\n        \"pin_name\": \"VFF\",\n        \"description\": \"(RMS feed forward signal) VFF signal is generated at this pin by mirroring one-half of IAC into a single pole external filter.\"\n      },\n      {\n        \"product_part_number\": \"UCC28501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"20\",\n        \"pin_name\": \"VREF\",\n        \"description\": \"(voltage reference output) VREF is the output of an accurate 7.5-V voltage reference.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS419C\",\n    \"family_comparison\": \"The UCC2850x family differs in UVLO thresholds and operating temperature ranges. UCC28500/UCC28502 have a wide VCC UVLO threshold (16.5 V/10 V). UCC28501/UCC28503 have a narrow VCC UVLO range (10.5 V/10 V). UCC28500/UCC28501 have a narrow PWM stage UVLO threshold (operation down to 75% of nominal bulk voltage). UCC28502/UCC38503 have a wider operation range for the PWM stage (down to 50% of bulk nominal voltage). UCC2850x series operates from -40°C to 85°C, while UCC3850x series operates from 0°C to 70°C.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 2,\n      \"max_input_voltage\": \"18V\",\n      \"min_input_voltage\": \"10.2V\",\n      \"max_output_voltage\": \"可调\",\n      \"min_output_voltage\": \"可调\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"4mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"1.3V\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"无\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"Latch\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"Latch\",\n      \"output_under_voltage_protection\": \"75% of nominal\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Current Limit\",\n      \"over_temperature_protection\": \"无\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"峰值电流模式, 平均电流模式\",\n      \"output_voltage_accuracy\": \"±2%\",\n      \"output_reference_voltage\": \"7.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC28502\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Industry\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"AC/DC转换器\",\n    \"category_lv3\": \"PFC/PWM组合控制器\",\n    \"part_number_title\": \"BICMOS PFC/PWM COMBINATION CONTROLLER\",\n    \"features\": [\n      \"Combines PFC and Downstream Converter Controls\",\n      \"Controls Boost Preregulator to Near-Unity Power Factor\",\n      \"Accurate Power Limiting\",\n      \"Improved Feedforward Line Regulation\",\n      \"Peak Current-Mode Control in Second Stage\",\n      \"Programmable Oscillator\",\n      \"Leading-Edge/Trailing-Edge Modulation for Reduced Output Ripple\",\n      \"Low Start-up Supply Current\",\n      \"Synchronized Second Stage Start-Up, with Programmable Soft-start\",\n      \"Programmable Second Stage Shutdown\"\n    ],\n    \"description\": \"The UCC2850x family provides all of the control functions necessary for an active power-factor-corrected preregulator and a second-stage dc-to-dc converter. The controller achieves near-unity power factor by shaping the ac input line current waveform to correspond to the ac input-line voltage using average current-mode control. The dc-to-dc converter uses peak current-mode control. This model, UCC28502, has a wide VCC UVLO threshold (16.5V/10V) and a wider operation range for the PWM stage (down to 50% of bulk nominal voltage).\",\n    \"applications\": [\n      \"Active power-factor-corrected preregulator\",\n      \"Second-stage dc-to-dc converter\",\n      \"Off-line power converters\"\n    ],\n    \"ordering_information\": [],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"VAOUT\",\n        \"description\": \"(voltage amplifier output) This is the output of the operational amplifier that regulates output voltage. The voltage amplifier output is internally limited to approximately 5.5 V to prevent overshoot.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"RT\",\n        \"description\": \"(oscillator charging current) A resistor from RT to GND is used to program oscillator charging current. A resistor between 10 kΩ and 100 kΩ is recommended. Nominal voltage on this pin is 3 V.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VSENSE\",\n        \"description\": \"(voltage amplifier inverting input) This is normally connected to a compensation network and to the boost converter output through a divider network.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OVP/ENBL\",\n        \"description\": \"(over-voltage/enable) A window comparator input which disables the PFC output driver if the boost output is 6.67% above nominal or disables both the PFC and second stage output drivers and reset SS2 if pulled below 1.9 V. This input is also used to determine the active range of the second stage PWM.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"CT\",\n        \"description\": \"(oscillator timing capacitor) A capacitor from CT to GND sets the oscillator frequency.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"GND\",\n        \"description\": \"(ground) All voltages measured with respect to ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"VERR\",\n        \"description\": \"(voltage amp error signal for the second stage) The error signal is generated by an external amplifier which drives this pin. This pin has an internal 4.5-V voltage clamp that limits GT2 to less than 50% duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"ISENSE2\",\n        \"description\": \"(current sense) A resistor from the source of the lower FET to ground generates the input signal for the peak limit control of the second stage.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"VCC\",\n        \"description\": \"(positive supply voltage) Connect to a stable source of at least 20 mA between 12 V and 17 V for normal operation.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"GT2\",\n        \"description\": \"(gate drive) Same as output GT1 for the second stage output drive. Limited to 50% maximum duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"PWRGND\",\n        \"description\": \"Ground for totem pole output drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"GT1\",\n        \"description\": \"(gate drive) The output drive for the PFC stage is a totem pole MOSFET gate driver on GT1.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"SS2\",\n        \"description\": \"(soft-start for PWM) SS2 is at ground for either enable low or OVP/ENBL below the UVLO2 threshold conditions. When enabled, SS2 charges an external capacitor with a current source.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"PKLMT\",\n        \"description\": \"(PFC peak current limit) The threshold for peak limit is 0 V. Use a resistor divider from the negative side of the current sense resistor to VREF to level-shift this signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"CAOUT\",\n        \"description\": \"(current amplifier output) This is the output of a wide bandwidth operational amplifier that senses line current and commands the PFC pulse width modulator (PWM) to force the correct duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"ISENSE1\",\n        \"description\": \"(current sense) This is the non-inverting input to the current amplifier.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"17\",\n        \"pin_name\": \"MOUT\",\n        \"description\": \"(multiplier output and current sense amplifier inverting input) The output of the analog multiplier and the inverting input of the current amplifier are connected together at MOUT.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"18\",\n        \"pin_name\": \"IAC\",\n        \"description\": \"(input ac current) This input to the analog multiplier is a current. Recommended maximum IAC is 500 μA.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"19\",\n        \"pin_name\": \"VFF\",\n        \"description\": \"(RMS feed forward signal) VFF signal is generated at this pin by mirroring one-half of IAC into a single pole external filter.\"\n      },\n      {\n        \"product_part_number\": \"UCC28502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"20\",\n        \"pin_name\": \"VREF\",\n        \"description\": \"(voltage reference output) VREF is the output of an accurate 7.5-V voltage reference.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS419C\",\n    \"family_comparison\": \"The UCC2850x family differs in UVLO thresholds and operating temperature ranges. UCC28500/UCC28502 have a wide VCC UVLO threshold (16.5 V/10 V). UCC28501/UCC28503 have a narrow VCC UVLO range (10.5 V/10 V). UCC28500/UCC28501 have a narrow PWM stage UVLO threshold (operation down to 75% of nominal bulk voltage). UCC28502/UCC38503 have a wider operation range for the PWM stage (down to 50% of bulk nominal voltage). UCC2850x series operates from -40°C to 85°C, while UCC3850x series operates from 0°C to 70°C.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 2,\n      \"max_input_voltage\": \"18V\",\n      \"min_input_voltage\": \"16V\",\n      \"max_output_voltage\": \"可调\",\n      \"min_output_voltage\": \"可调\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"4mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"1.3V\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"无\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"Latch\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"Latch\",\n      \"output_under_voltage_protection\": \"50% of nominal\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Current Limit\",\n      \"over_temperature_protection\": \"无\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"峰值电流模式, 平均电流模式\",\n      \"output_voltage_accuracy\": \"±2%\",\n      \"output_reference_voltage\": \"7.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC28503\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Industry\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"AC/DC转换器\",\n    \"category_lv3\": \"PFC/PWM组合控制器\",\n    \"part_number_title\": \"BICMOS PFC/PWM COMBINATION CONTROLLER\",\n    \"features\": [\n      \"Combines PFC and Downstream Converter Controls\",\n      \"Controls Boost Preregulator to Near-Unity Power Factor\",\n      \"Accurate Power Limiting\",\n      \"Improved Feedforward Line Regulation\",\n      \"Peak Current-Mode Control in Second Stage\",\n      \"Programmable Oscillator\",\n      \"Leading-Edge/Trailing-Edge Modulation for Reduced Output Ripple\",\n      \"Low Start-up Supply Current\",\n      \"Synchronized Second Stage Start-Up, with Programmable Soft-start\",\n      \"Programmable Second Stage Shutdown\"\n    ],\n    \"description\": \"The UCC2850x family provides all of the control functions necessary for an active power-factor-corrected preregulator and a second-stage dc-to-dc converter. The controller achieves near-unity power factor by shaping the ac input line current waveform to correspond to the ac input-line voltage using average current-mode control. The dc-to-dc converter uses peak current-mode control. This model, UCC28503, has a narrow VCC UVLO range (10.5V/10V) and a wider operation range for the PWM stage (down to 50% of bulk nominal voltage).\",\n    \"applications\": [\n      \"Active power-factor-corrected preregulator\",\n      \"Second-stage dc-to-dc converter\",\n      \"Off-line power converters\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UCC28503\",\n        \"order_device\": \"UCC28503DW\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"DW\",\n        \"carrier_description\": \"TUBE\",\n        \"min_operation_temp\": \"-40\",\n        \"max_operation_temp\": \"85\"\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"VAOUT\",\n        \"description\": \"(voltage amplifier output) This is the output of the operational amplifier that regulates output voltage. The voltage amplifier output is internally limited to approximately 5.5 V to prevent overshoot.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"RT\",\n        \"description\": \"(oscillator charging current) A resistor from RT to GND is used to program oscillator charging current. A resistor between 10 kΩ and 100 kΩ is recommended. Nominal voltage on this pin is 3 V.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VSENSE\",\n        \"description\": \"(voltage amplifier inverting input) This is normally connected to a compensation network and to the boost converter output through a divider network.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OVP/ENBL\",\n        \"description\": \"(over-voltage/enable) A window comparator input which disables the PFC output driver if the boost output is 6.67% above nominal or disables both the PFC and second stage output drivers and reset SS2 if pulled below 1.9 V. This input is also used to determine the active range of the second stage PWM.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"CT\",\n        \"description\": \"(oscillator timing capacitor) A capacitor from CT to GND sets the oscillator frequency.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"GND\",\n        \"description\": \"(ground) All voltages measured with respect to ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"VERR\",\n        \"description\": \"(voltage amp error signal for the second stage) The error signal is generated by an external amplifier which drives this pin. This pin has an internal 4.5-V voltage clamp that limits GT2 to less than 50% duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"ISENSE2\",\n        \"description\": \"(current sense) A resistor from the source of the lower FET to ground generates the input signal for the peak limit control of the second stage.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"VCC\",\n        \"description\": \"(positive supply voltage) Connect to a stable source of at least 20 mA between 12 V and 17 V for normal operation.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"GT2\",\n        \"description\": \"(gate drive) Same as output GT1 for the second stage output drive. Limited to 50% maximum duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"PWRGND\",\n        \"description\": \"Ground for totem pole output drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"GT1\",\n        \"description\": \"(gate drive) The output drive for the PFC stage is a totem pole MOSFET gate driver on GT1.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"SS2\",\n        \"description\": \"(soft-start for PWM) SS2 is at ground for either enable low or OVP/ENBL below the UVLO2 threshold conditions. When enabled, SS2 charges an external capacitor with a current source.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"PKLMT\",\n        \"description\": \"(PFC peak current limit) The threshold for peak limit is 0 V. Use a resistor divider from the negative side of the current sense resistor to VREF to level-shift this signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"CAOUT\",\n        \"description\": \"(current amplifier output) This is the output of a wide bandwidth operational amplifier that senses line current and commands the PFC pulse width modulator (PWM) to force the correct duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"ISENSE1\",\n        \"description\": \"(current sense) This is the non-inverting input to the current amplifier.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"17\",\n        \"pin_name\": \"MOUT\",\n        \"description\": \"(multiplier output and current sense amplifier inverting input) The output of the analog multiplier and the inverting input of the current amplifier are connected together at MOUT.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"18\",\n        \"pin_name\": \"IAC\",\n        \"description\": \"(input ac current) This input to the analog multiplier is a current. Recommended maximum IAC is 500 μA.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"19\",\n        \"pin_name\": \"VFF\",\n        \"description\": \"(RMS feed forward signal) VFF signal is generated at this pin by mirroring one-half of IAC into a single pole external filter.\"\n      },\n      {\n        \"product_part_number\": \"UCC28503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"20\",\n        \"pin_name\": \"VREF\",\n        \"description\": \"(voltage reference output) VREF is the output of an accurate 7.5-V voltage reference.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS419C\",\n    \"family_comparison\": \"The UCC2850x family differs in UVLO thresholds and operating temperature ranges. UCC28500/UCC28502 have a wide VCC UVLO threshold (16.5 V/10 V). UCC28501/UCC28503 have a narrow VCC UVLO range (10.5 V/10 V). UCC28500/UCC28501 have a narrow PWM stage UVLO threshold (operation down to 75% of nominal bulk voltage). UCC28502/UCC38503 have a wider operation range for the PWM stage (down to 50% of bulk nominal voltage). UCC2850x series operates from -40°C to 85°C, while UCC3850x series operates from 0°C to 70°C.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 2,\n      \"max_input_voltage\": \"18V\",\n      \"min_input_voltage\": \"10.2V\",\n      \"max_output_voltage\": \"可调\",\n      \"min_output_voltage\": \"可调\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"4mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"1.3V\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"无\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"Latch\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"Latch\",\n      \"output_under_voltage_protection\": \"50% of nominal\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Current Limit\",\n      \"over_temperature_protection\": \"无\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"峰值电流模式, 平均电流模式\",\n      \"output_voltage_accuracy\": \"±2%\",\n      \"output_reference_voltage\": \"7.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC38500\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Consumer\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"AC/DC转换器\",\n    \"category_lv3\": \"PFC/PWM组合控制器\",\n    \"part_number_title\": \"BICMOS PFC/PWM COMBINATION CONTROLLER\",\n    \"features\": [\n      \"Combines PFC and Downstream Converter Controls\",\n      \"Controls Boost Preregulator to Near-Unity Power Factor\",\n      \"Accurate Power Limiting\",\n      \"Improved Feedforward Line Regulation\",\n      \"Peak Current-Mode Control in Second Stage\",\n      \"Programmable Oscillator\",\n      \"Leading-Edge/Trailing-Edge Modulation for Reduced Output Ripple\",\n      \"Low Start-up Supply Current\",\n      \"Synchronized Second Stage Start-Up, with Programmable Soft-start\",\n      \"Programmable Second Stage Shutdown\"\n    ],\n    \"description\": \"The UCC3850x family provides all of the control functions necessary for an active power-factor-corrected preregulator and a second-stage dc-to-dc converter. The controller achieves near-unity power factor by shaping the ac input line current waveform to correspond to the ac input-line voltage using average current-mode control. The dc-to-dc converter uses peak current-mode control. This model, UCC38500, has a wide VCC UVLO threshold (16.5V/10V) and a narrow PWM stage UVLO threshold (operation down to 75% of nominal bulk voltage).\",\n    \"applications\": [\n      \"Active power-factor-corrected preregulator\",\n      \"Second-stage dc-to-dc converter\",\n      \"Off-line power converters\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UCC38500\",\n        \"order_device\": \"UCC38500DW\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"DW\",\n        \"carrier_description\": \"TUBE\",\n        \"min_operation_temp\": \"0\",\n        \"max_operation_temp\": \"70\"\n      },\n      {\n        \"part_number\": \"UCC38500\",\n        \"order_device\": \"UCC38500N\",\n        \"package_type\": \"PDIP\",\n        \"package_drawing_code\": \"N\",\n        \"carrier_description\": \"TUBE\",\n        \"min_operation_temp\": \"0\",\n        \"max_operation_temp\": \"70\"\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"VAOUT\",\n        \"description\": \"(voltage amplifier output) This is the output of the operational amplifier that regulates output voltage. The voltage amplifier output is internally limited to approximately 5.5 V to prevent overshoot.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"RT\",\n        \"description\": \"(oscillator charging current) A resistor from RT to GND is used to program oscillator charging current. A resistor between 10 kΩ and 100 kΩ is recommended. Nominal voltage on this pin is 3 V.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VSENSE\",\n        \"description\": \"(voltage amplifier inverting input) This is normally connected to a compensation network and to the boost converter output through a divider network.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OVP/ENBL\",\n        \"description\": \"(over-voltage/enable) A window comparator input which disables the PFC output driver if the boost output is 6.67% above nominal or disables both the PFC and second stage output drivers and reset SS2 if pulled below 1.9 V. This input is also used to determine the active range of the second stage PWM.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"CT\",\n        \"description\": \"(oscillator timing capacitor) A capacitor from CT to GND sets the oscillator frequency.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"GND\",\n        \"description\": \"(ground) All voltages measured with respect to ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"VERR\",\n        \"description\": \"(voltage amp error signal for the second stage) The error signal is generated by an external amplifier which drives this pin. This pin has an internal 4.5-V voltage clamp that limits GT2 to less than 50% duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"ISENSE2\",\n        \"description\": \"(current sense) A resistor from the source of the lower FET to ground generates the input signal for the peak limit control of the second stage.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"VCC\",\n        \"description\": \"(positive supply voltage) Connect to a stable source of at least 20 mA between 12 V and 17 V for normal operation.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"GT2\",\n        \"description\": \"(gate drive) Same as output GT1 for the second stage output drive. Limited to 50% maximum duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"PWRGND\",\n        \"description\": \"Ground for totem pole output drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"GT1\",\n        \"description\": \"(gate drive) The output drive for the PFC stage is a totem pole MOSFET gate driver on GT1.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"SS2\",\n        \"description\": \"(soft-start for PWM) SS2 is at ground for either enable low or OVP/ENBL below the UVLO2 threshold conditions. When enabled, SS2 charges an external capacitor with a current source.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"PKLMT\",\n        \"description\": \"(PFC peak current limit) The threshold for peak limit is 0 V. Use a resistor divider from the negative side of the current sense resistor to VREF to level-shift this signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"CAOUT\",\n        \"description\": \"(current amplifier output) This is the output of a wide bandwidth operational amplifier that senses line current and commands the PFC pulse width modulator (PWM) to force the correct duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"ISENSE1\",\n        \"description\": \"(current sense) This is the non-inverting input to the current amplifier.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"17\",\n        \"pin_name\": \"MOUT\",\n        \"description\": \"(multiplier output and current sense amplifier inverting input) The output of the analog multiplier and the inverting input of the current amplifier are connected together at MOUT.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"18\",\n        \"pin_name\": \"IAC\",\n        \"description\": \"(input ac current) This input to the analog multiplier is a current. Recommended maximum IAC is 500 μA.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"19\",\n        \"pin_name\": \"VFF\",\n        \"description\": \"(RMS feed forward signal) VFF signal is generated at this pin by mirroring one-half of IAC into a single pole external filter.\"\n      },\n      {\n        \"product_part_number\": \"UCC38500\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"20\",\n        \"pin_name\": \"VREF\",\n        \"description\": \"(voltage reference output) VREF is the output of an accurate 7.5-V voltage reference.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS419C\",\n    \"family_comparison\": \"The UCC2850x family differs in UVLO thresholds and operating temperature ranges. UCC28500/UCC28502 have a wide VCC UVLO threshold (16.5 V/10 V). UCC28501/UCC28503 have a narrow VCC UVLO range (10.5 V/10 V). UCC28500/UCC28501 have a narrow PWM stage UVLO threshold (operation down to 75% of nominal bulk voltage). UCC28502/UCC38503 have a wider operation range for the PWM stage (down to 50% of bulk nominal voltage). UCC2850x series operates from -40°C to 85°C, while UCC3850x series operates from 0°C to 70°C.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 2,\n      \"max_input_voltage\": \"18V\",\n      \"min_input_voltage\": \"16V\",\n      \"max_output_voltage\": \"可调\",\n      \"min_output_voltage\": \"可调\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"4mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"1.3V\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"无\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"Latch\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"Latch\",\n      \"output_under_voltage_protection\": \"75% of nominal\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Current Limit\",\n      \"over_temperature_protection\": \"无\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"峰值电流模式, 平均电流模式\",\n      \"output_voltage_accuracy\": \"±2%\",\n      \"output_reference_voltage\": \"7.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC38501\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Consumer\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"AC/DC转换器\",\n    \"category_lv3\": \"PFC/PWM组合控制器\",\n    \"part_number_title\": \"BICMOS PFC/PWM COMBINATION CONTROLLER\",\n    \"features\": [\n      \"Combines PFC and Downstream Converter Controls\",\n      \"Controls Boost Preregulator to Near-Unity Power Factor\",\n      \"Accurate Power Limiting\",\n      \"Improved Feedforward Line Regulation\",\n      \"Peak Current-Mode Control in Second Stage\",\n      \"Programmable Oscillator\",\n      \"Leading-Edge/Trailing-Edge Modulation for Reduced Output Ripple\",\n      \"Low Start-up Supply Current\",\n      \"Synchronized Second Stage Start-Up, with Programmable Soft-start\",\n      \"Programmable Second Stage Shutdown\"\n    ],\n    \"description\": \"The UCC3850x family provides all of the control functions necessary for an active power-factor-corrected preregulator and a second-stage dc-to-dc converter. The controller achieves near-unity power factor by shaping the ac input line current waveform to correspond to the ac input-line voltage using average current-mode control. The dc-to-dc converter uses peak current-mode control. This model, UCC38501, has a narrow VCC UVLO range (10.5V/10V) and a narrow PWM stage UVLO threshold (operation down to 75% of nominal bulk voltage).\",\n    \"applications\": [\n      \"Active power-factor-corrected preregulator\",\n      \"Second-stage dc-to-dc converter\",\n      \"Off-line power converters\"\n    ],\n    \"ordering_information\": [],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"VAOUT\",\n        \"description\": \"(voltage amplifier output) This is the output of the operational amplifier that regulates output voltage. The voltage amplifier output is internally limited to approximately 5.5 V to prevent overshoot.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"RT\",\n        \"description\": \"(oscillator charging current) A resistor from RT to GND is used to program oscillator charging current. A resistor between 10 kΩ and 100 kΩ is recommended. Nominal voltage on this pin is 3 V.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VSENSE\",\n        \"description\": \"(voltage amplifier inverting input) This is normally connected to a compensation network and to the boost converter output through a divider network.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OVP/ENBL\",\n        \"description\": \"(over-voltage/enable) A window comparator input which disables the PFC output driver if the boost output is 6.67% above nominal or disables both the PFC and second stage output drivers and reset SS2 if pulled below 1.9 V. This input is also used to determine the active range of the second stage PWM.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"CT\",\n        \"description\": \"(oscillator timing capacitor) A capacitor from CT to GND sets the oscillator frequency.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"GND\",\n        \"description\": \"(ground) All voltages measured with respect to ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"VERR\",\n        \"description\": \"(voltage amp error signal for the second stage) The error signal is generated by an external amplifier which drives this pin. This pin has an internal 4.5-V voltage clamp that limits GT2 to less than 50% duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"ISENSE2\",\n        \"description\": \"(current sense) A resistor from the source of the lower FET to ground generates the input signal for the peak limit control of the second stage.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"VCC\",\n        \"description\": \"(positive supply voltage) Connect to a stable source of at least 20 mA between 12 V and 17 V for normal operation.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"GT2\",\n        \"description\": \"(gate drive) Same as output GT1 for the second stage output drive. Limited to 50% maximum duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"PWRGND\",\n        \"description\": \"Ground for totem pole output drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"GT1\",\n        \"description\": \"(gate drive) The output drive for the PFC stage is a totem pole MOSFET gate driver on GT1.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"SS2\",\n        \"description\": \"(soft-start for PWM) SS2 is at ground for either enable low or OVP/ENBL below the UVLO2 threshold conditions. When enabled, SS2 charges an external capacitor with a current source.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"PKLMT\",\n        \"description\": \"(PFC peak current limit) The threshold for peak limit is 0 V. Use a resistor divider from the negative side of the current sense resistor to VREF to level-shift this signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"CAOUT\",\n        \"description\": \"(current amplifier output) This is the output of a wide bandwidth operational amplifier that senses line current and commands the PFC pulse width modulator (PWM) to force the correct duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"ISENSE1\",\n        \"description\": \"(current sense) This is the non-inverting input to the current amplifier.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"17\",\n        \"pin_name\": \"MOUT\",\n        \"description\": \"(multiplier output and current sense amplifier inverting input) The output of the analog multiplier and the inverting input of the current amplifier are connected together at MOUT.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"18\",\n        \"pin_name\": \"IAC\",\n        \"description\": \"(input ac current) This input to the analog multiplier is a current. Recommended maximum IAC is 500 μA.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"19\",\n        \"pin_name\": \"VFF\",\n        \"description\": \"(RMS feed forward signal) VFF signal is generated at this pin by mirroring one-half of IAC into a single pole external filter.\"\n      },\n      {\n        \"product_part_number\": \"UCC38501\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"20\",\n        \"pin_name\": \"VREF\",\n        \"description\": \"(voltage reference output) VREF is the output of an accurate 7.5-V voltage reference.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS419C\",\n    \"family_comparison\": \"The UCC2850x family differs in UVLO thresholds and operating temperature ranges. UCC28500/UCC28502 have a wide VCC UVLO threshold (16.5 V/10 V). UCC28501/UCC28503 have a narrow VCC UVLO range (10.5 V/10 V). UCC28500/UCC28501 have a narrow PWM stage UVLO threshold (operation down to 75% of nominal bulk voltage). UCC28502/UCC38503 have a wider operation range for the PWM stage (down to 50% of bulk nominal voltage). UCC2850x series operates from -40°C to 85°C, while UCC3850x series operates from 0°C to 70°C.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 2,\n      \"max_input_voltage\": \"18V\",\n      \"min_input_voltage\": \"10.2V\",\n      \"max_output_voltage\": \"可调\",\n      \"min_output_voltage\": \"可调\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"4mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"1.3V\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"无\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"Latch\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"Latch\",\n      \"output_under_voltage_protection\": \"75% of nominal\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Current Limit\",\n      \"over_temperature_protection\": \"无\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"峰值电流模式, 平均电流模式\",\n      \"output_voltage_accuracy\": \"±2%\",\n      \"output_reference_voltage\": \"7.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC38502\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Consumer\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"AC/DC转换器\",\n    \"category_lv3\": \"PFC/PWM组合控制器\",\n    \"part_number_title\": \"BICMOS PFC/PWM COMBINATION CONTROLLER\",\n    \"features\": [\n      \"Combines PFC and Downstream Converter Controls\",\n      \"Controls Boost Preregulator to Near-Unity Power Factor\",\n      \"Accurate Power Limiting\",\n      \"Improved Feedforward Line Regulation\",\n      \"Peak Current-Mode Control in Second Stage\",\n      \"Programmable Oscillator\",\n      \"Leading-Edge/Trailing-Edge Modulation for Reduced Output Ripple\",\n      \"Low Start-up Supply Current\",\n      \"Synchronized Second Stage Start-Up, with Programmable Soft-start\",\n      \"Programmable Second Stage Shutdown\"\n    ],\n    \"description\": \"The UCC3850x family provides all of the control functions necessary for an active power-factor-corrected preregulator and a second-stage dc-to-dc converter. The controller achieves near-unity power factor by shaping the ac input line current waveform to correspond to the ac input-line voltage using average current-mode control. The dc-to-dc converter uses peak current-mode control. This model, UCC38502, has a wide VCC UVLO threshold (16.5V/10V) and a wider operation range for the PWM stage (down to 50% of bulk nominal voltage).\",\n    \"applications\": [\n      \"Active power-factor-corrected preregulator\",\n      \"Second-stage dc-to-dc converter\",\n      \"Off-line power converters\"\n    ],\n    \"ordering_information\": [],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"VAOUT\",\n        \"description\": \"(voltage amplifier output) This is the output of the operational amplifier that regulates output voltage. The voltage amplifier output is internally limited to approximately 5.5 V to prevent overshoot.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"RT\",\n        \"description\": \"(oscillator charging current) A resistor from RT to GND is used to program oscillator charging current. A resistor between 10 kΩ and 100 kΩ is recommended. Nominal voltage on this pin is 3 V.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VSENSE\",\n        \"description\": \"(voltage amplifier inverting input) This is normally connected to a compensation network and to the boost converter output through a divider network.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OVP/ENBL\",\n        \"description\": \"(over-voltage/enable) A window comparator input which disables the PFC output driver if the boost output is 6.67% above nominal or disables both the PFC and second stage output drivers and reset SS2 if pulled below 1.9 V. This input is also used to determine the active range of the second stage PWM.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"CT\",\n        \"description\": \"(oscillator timing capacitor) A capacitor from CT to GND sets the oscillator frequency.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"GND\",\n        \"description\": \"(ground) All voltages measured with respect to ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"VERR\",\n        \"description\": \"(voltage amp error signal for the second stage) The error signal is generated by an external amplifier which drives this pin. This pin has an internal 4.5-V voltage clamp that limits GT2 to less than 50% duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"ISENSE2\",\n        \"description\": \"(current sense) A resistor from the source of the lower FET to ground generates the input signal for the peak limit control of the second stage.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"VCC\",\n        \"description\": \"(positive supply voltage) Connect to a stable source of at least 20 mA between 12 V and 17 V for normal operation.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"GT2\",\n        \"description\": \"(gate drive) Same as output GT1 for the second stage output drive. Limited to 50% maximum duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"PWRGND\",\n        \"description\": \"Ground for totem pole output drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"GT1\",\n        \"description\": \"(gate drive) The output drive for the PFC stage is a totem pole MOSFET gate driver on GT1.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"SS2\",\n        \"description\": \"(soft-start for PWM) SS2 is at ground for either enable low or OVP/ENBL below the UVLO2 threshold conditions. When enabled, SS2 charges an external capacitor with a current source.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"PKLMT\",\n        \"description\": \"(PFC peak current limit) The threshold for peak limit is 0 V. Use a resistor divider from the negative side of the current sense resistor to VREF to level-shift this signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"CAOUT\",\n        \"description\": \"(current amplifier output) This is the output of a wide bandwidth operational amplifier that senses line current and commands the PFC pulse width modulator (PWM) to force the correct duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"ISENSE1\",\n        \"description\": \"(current sense) This is the non-inverting input to the current amplifier.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"17\",\n        \"pin_name\": \"MOUT\",\n        \"description\": \"(multiplier output and current sense amplifier inverting input) The output of the analog multiplier and the inverting input of the current amplifier are connected together at MOUT.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"18\",\n        \"pin_name\": \"IAC\",\n        \"description\": \"(input ac current) This input to the analog multiplier is a current. Recommended maximum IAC is 500 μA.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"19\",\n        \"pin_name\": \"VFF\",\n        \"description\": \"(RMS feed forward signal) VFF signal is generated at this pin by mirroring one-half of IAC into a single pole external filter.\"\n      },\n      {\n        \"product_part_number\": \"UCC38502\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"20\",\n        \"pin_name\": \"VREF\",\n        \"description\": \"(voltage reference output) VREF is the output of an accurate 7.5-V voltage reference.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS419C\",\n    \"family_comparison\": \"The UCC2850x family differs in UVLO thresholds and operating temperature ranges. UCC28500/UCC28502 have a wide VCC UVLO threshold (16.5 V/10 V). UCC28501/UCC28503 have a narrow VCC UVLO range (10.5 V/10 V). UCC28500/UCC28501 have a narrow PWM stage UVLO threshold (operation down to 75% of nominal bulk voltage). UCC28502/UCC38503 have a wider operation range for the PWM stage (down to 50% of bulk nominal voltage). UCC2850x series operates from -40°C to 85°C, while UCC3850x series operates from 0°C to 70°C.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 2,\n      \"max_input_voltage\": \"18V\",\n      \"min_input_voltage\": \"16V\",\n      \"max_output_voltage\": \"可调\",\n      \"min_output_voltage\": \"可调\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"4mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"1.3V\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"无\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"Latch\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"Latch\",\n      \"output_under_voltage_protection\": \"50% of nominal\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Current Limit\",\n      \"over_temperature_protection\": \"无\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"峰值电流模式, 平均电流模式\",\n      \"output_voltage_accuracy\": \"±2%\",\n      \"output_reference_voltage\": \"7.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC38503\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Consumer\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"AC/DC转换器\",\n    \"category_lv3\": \"PFC/PWM组合控制器\",\n    \"part_number_title\": \"BICMOS PFC/PWM COMBINATION CONTROLLER\",\n    \"features\": [\n      \"Combines PFC and Downstream Converter Controls\",\n      \"Controls Boost Preregulator to Near-Unity Power Factor\",\n      \"Accurate Power Limiting\",\n      \"Improved Feedforward Line Regulation\",\n      \"Peak Current-Mode Control in Second Stage\",\n      \"Programmable Oscillator\",\n      \"Leading-Edge/Trailing-Edge Modulation for Reduced Output Ripple\",\n      \"Low Start-up Supply Current\",\n      \"Synchronized Second Stage Start-Up, with Programmable Soft-start\",\n      \"Programmable Second Stage Shutdown\"\n    ],\n    \"description\": \"The UCC3850x family provides all of the control functions necessary for an active power-factor-corrected preregulator and a second-stage dc-to-dc converter. The controller achieves near-unity power factor by shaping the ac input line current waveform to correspond to the ac input-line voltage using average current-mode control. The dc-to-dc converter uses peak current-mode control. This model, UCC38503, has a narrow VCC UVLO range (10.5V/10V) and a wider operation range for the PWM stage (down to 50% of bulk nominal voltage).\",\n    \"applications\": [\n      \"Active power-factor-corrected preregulator\",\n      \"Second-stage dc-to-dc converter\",\n      \"Off-line power converters\"\n    ],\n    \"ordering_information\": [],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"VAOUT\",\n        \"description\": \"(voltage amplifier output) This is the output of the operational amplifier that regulates output voltage. The voltage amplifier output is internally limited to approximately 5.5 V to prevent overshoot.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"RT\",\n        \"description\": \"(oscillator charging current) A resistor from RT to GND is used to program oscillator charging current. A resistor between 10 kΩ and 100 kΩ is recommended. Nominal voltage on this pin is 3 V.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VSENSE\",\n        \"description\": \"(voltage amplifier inverting input) This is normally connected to a compensation network and to the boost converter output through a divider network.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OVP/ENBL\",\n        \"description\": \"(over-voltage/enable) A window comparator input which disables the PFC output driver if the boost output is 6.67% above nominal or disables both the PFC and second stage output drivers and reset SS2 if pulled below 1.9 V. This input is also used to determine the active range of the second stage PWM.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"CT\",\n        \"description\": \"(oscillator timing capacitor) A capacitor from CT to GND sets the oscillator frequency.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"GND\",\n        \"description\": \"(ground) All voltages measured with respect to ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"VERR\",\n        \"description\": \"(voltage amp error signal for the second stage) The error signal is generated by an external amplifier which drives this pin. This pin has an internal 4.5-V voltage clamp that limits GT2 to less than 50% duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"ISENSE2\",\n        \"description\": \"(current sense) A resistor from the source of the lower FET to ground generates the input signal for the peak limit control of the second stage.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"VCC\",\n        \"description\": \"(positive supply voltage) Connect to a stable source of at least 20 mA between 12 V and 17 V for normal operation.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"GT2\",\n        \"description\": \"(gate drive) Same as output GT1 for the second stage output drive. Limited to 50% maximum duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"PWRGND\",\n        \"description\": \"Ground for totem pole output drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"GT1\",\n        \"description\": \"(gate drive) The output drive for the PFC stage is a totem pole MOSFET gate driver on GT1.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"SS2\",\n        \"description\": \"(soft-start for PWM) SS2 is at ground for either enable low or OVP/ENBL below the UVLO2 threshold conditions. When enabled, SS2 charges an external capacitor with a current source.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"PKLMT\",\n        \"description\": \"(PFC peak current limit) The threshold for peak limit is 0 V. Use a resistor divider from the negative side of the current sense resistor to VREF to level-shift this signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"CAOUT\",\n        \"description\": \"(current amplifier output) This is the output of a wide bandwidth operational amplifier that senses line current and commands the PFC pulse width modulator (PWM) to force the correct duty cycle.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"ISENSE1\",\n        \"description\": \"(current sense) This is the non-inverting input to the current amplifier.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"17\",\n        \"pin_name\": \"MOUT\",\n        \"description\": \"(multiplier output and current sense amplifier inverting input) The output of the analog multiplier and the inverting input of the current amplifier are connected together at MOUT.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"18\",\n        \"pin_name\": \"IAC\",\n        \"description\": \"(input ac current) This input to the analog multiplier is a current. Recommended maximum IAC is 500 μA.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\": \"N/DW\",\n        \"pin_number\": \"19\",\n        \"pin_name\": \"VFF\",\n        \"description\": \"(RMS feed forward signal) VFF signal is generated at this pin by mirroring one-half of IAC into a single pole external filter.\"\n      },\n      {\n        \"product_part_number\": \"UCC38503\",\n        \"package_type\":", "package": [{"type": "and", "pin_count": "1", "length": "12.8", "pitch": "1.27", "height": "2.65", "width": "7.5"}]}