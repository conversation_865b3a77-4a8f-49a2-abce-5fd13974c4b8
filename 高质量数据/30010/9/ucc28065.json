{"part_number": "UCC28065", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "功率因数校正(PFC)控制器", "category_lv3": "交错式PFC控制器", "part_number_title": "UCC28065 Natural Interleaving™ Transition-Mode PFC Controller with High Light-Load Efficiency Supporting High-Frequency Switching up-to 800 kHz", "features": ["Input filter and output capacitor ripple-current reduction", "Reduced current ripple for higher system reliability and smaller bulk capacitor", "Reduced EMI filter size", "Higher switching frequency support", "Up-to 800-kHz switching frequency, reducing boost inductor size to at least one-half in size", "Improved input-current THD", "High light-load efficiency", "User adjustable phase management with input voltage compensation", "Burst mode operation with adjustable burst threshold", "Helps enable compliance to EUP Lot6 tier II, CoC tier II and DOE Level VI standards", "Sensorless current-shaping simplifies board layout and improves efficiency", "Input line feed-forward for fast line transient response", "Inrush-safe current limiting: Prevents MOSFET conduction during inrush, Eliminates CCM operation and reverse recovery events in output rectifier"], "description": "The UCC28065 interleaved PFC controller enables transition mode PFC at higher power ratings than previously possible. The device uses a Natural Interleaving™ technique to maintain a 180-degree phase shift. Both channels operate as masters (there is no slave channel) synchronized to the same frequency. This approach enables faster response time, accurate phase shift, and transition mode operation for each channel. The device has a burst mode function to get high light-load efficiency. Burst mode eliminates the need to turn off the PFC during light load operation to meet standby power targets, eliminating the need for an auxiliary Flyback when paired with UCC25640x LLC controller and the UCC24612 or UCC24624 synchronous rectifier controllers. The increased frequency clamping doubles the switching frequency capability compared with previous generation devices. The increased switching frequency range also allows the design to fully utilize the benefits of GaN MOSFETs such as LMG3410 and SiC MOSFETs.", "applications": ["Slim AC/DC for LED and OLED TVs", "All-in-one PC", "High-density AC/DC and gaming adapters", "Home audio systems", "Server, telecom, and DIN rail power supplies"], "ordering_information": [{"part_number": "UCC28065", "order_device": "UCC28065DR", "package_type": "SOIC", "package_drawing_code": "D", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28065", "order_device": "UCC28065DR.A", "package_type": "SOIC", "package_drawing_code": "D", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28065", "order_device": "UCC28065DR.B", "package_type": "SOIC", "package_drawing_code": "D", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28065", "order_device": "UCC28065DT", "package_type": "SOIC", "package_drawing_code": "D", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28065", "order_device": "UCC28065DT.A", "package_type": "SOIC", "package_drawing_code": "D", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28065", "order_device": "UCC28065DT.B", "package_type": "SOIC", "package_drawing_code": "D", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC28065", "package_type": "SOIC", "pin_number": "1", "pin_name": "ZCD_B", "pin_description": "Phase B zero current detection input"}, {"product_part_number": "UCC28065", "package_type": "SOIC", "pin_number": "2", "pin_name": "VSENSE", "pin_description": "Error amplifier input"}, {"product_part_number": "UCC28065", "package_type": "SOIC", "pin_number": "3", "pin_name": "TSET", "pin_description": "Timing set"}, {"product_part_number": "UCC28065", "package_type": "SOIC", "pin_number": "4", "pin_name": "PHB", "pin_description": "Phase B enable disable threshold input"}, {"product_part_number": "UCC28065", "package_type": "SOIC", "pin_number": "5", "pin_name": "COMP", "pin_description": "Error amplifier output"}, {"product_part_number": "UCC28065", "package_type": "SOIC", "pin_number": "6", "pin_name": "AGND", "pin_description": "Analog ground"}, {"product_part_number": "UCC28065", "package_type": "SOIC", "pin_number": "7", "pin_name": "VINAC", "pin_description": "Input AC voltage sense"}, {"product_part_number": "UCC28065", "package_type": "SOIC", "pin_number": "8", "pin_name": "HVSEN", "pin_description": "High voltage output sense"}, {"product_part_number": "UCC28065", "package_type": "SOIC", "pin_number": "9", "pin_name": "BRST", "pin_description": "Burst mode threshold input"}, {"product_part_number": "UCC28065", "package_type": "SOIC", "pin_number": "10", "pin_name": "CS", "pin_description": "Current sense input"}, {"product_part_number": "UCC28065", "package_type": "SOIC", "pin_number": "11", "pin_name": "GDB", "pin_description": "Phase B gate driver output"}, {"product_part_number": "UCC28065", "package_type": "SOIC", "pin_number": "12", "pin_name": "VCC", "pin_description": "Bias supply input"}, {"product_part_number": "UCC28065", "package_type": "SOIC", "pin_number": "13", "pin_name": "PGND", "pin_description": "Power ground"}, {"product_part_number": "UCC28065", "package_type": "SOIC", "pin_number": "14", "pin_name": "GDA", "pin_description": "Phase A gate driver output"}, {"product_part_number": "UCC28065", "package_type": "SOIC", "pin_number": "15", "pin_name": "VREF", "pin_description": "Voltage reference output"}, {"product_part_number": "UCC28065", "package_type": "SOIC", "pin_number": "16", "pin_name": "ZCD_A", "pin_description": "Phase A zero current detection input"}], "datasheet_cn": "未找到", "datasheet_en": "https://www.ti.com/lit/ds/symlink/ucc28065.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 2, "channel_count": 1, "max_input_voltage": "265V", "min_input_voltage": "85V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用(控制器)", "max_switch_frequency": "800kHz", "quiescent_current": "150µA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "-200mV", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "output_discharge": "No", "integrated_ldo": "Yes", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "Transition Mode", "output_voltage_accuracy": "±3%", "output_reference_voltage": "6V"}, "package": [{"type": "OPTION", "pin_count": "6", "pitch": "1.27", "height": "2.1", "width": "6.5", "length": "10.3"}]}