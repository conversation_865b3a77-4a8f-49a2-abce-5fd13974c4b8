[{"part_number": "UC1879", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "移相谐振控制器", "part_number_title": "PHASE SHIFT RESONANT CONTROLLER", "features": ["Programmable Output Turn On Delay; Zero Delay Available", "Compatible with Voltage Mode or Current Mode Topologies", "Practical Operation at Switching Frequencies to 300 kHz", "10-MHz Error Amplifier", "Pin Programmable Undervoltage Lockout", "Low Startup Current – 150 μA", "Soft Start Control", "Outputs Active Low During UVLO"], "description": "The UC3879 controls a bridge power stage by phase shifting the switching of one half-bridge with respect to the other. This allows constant frequency pulse width modulation in combination with resonant, zero-voltage switching for high efficiency performance. The UC3879 can be configured to provide control in either voltage mode or current mode operation, with overcurrent shutdown for fast fault protection. Independently programmable time delays provide dead-time at the turn-on of each output stage, allowing time for each resonant switching interval.", "applications": [], "ordering_information": [], "pin_function": [{"product_part_number": "UC1879", "package_type": "DIL-20, SOIC-20, PLCC-20, CLCC-28", "pins": [{"pin_number": "17/11/9", "pin_name": "CLKSYNC", "pin_description": "Bi-directional Clock and Synchronization: Used as an output, provides a clock signal. As an input, provides a synchronization point. Multiple UC3879s can be connected together to synchronize to the fastest oscillator. Can also be synchronized to an external clock."}, {"pin_number": "2/18/21", "pin_name": "COMP", "pin_description": "Error Amplifier Output: Output of the gain stage for overall feedback control. Voltage levels below 0.9 V force zero phase shift."}, {"pin_number": "14/10/6", "pin_name": "CT", "pin_description": "Oscillator Frequency Set: A timing capacitor connected between CT and GND sets the oscillator frequency."}, {"pin_number": "4/20/23", "pin_name": "CS", "pin_description": "Current Sense: Non-inverting input to two current fault comparators with internal references of 2 V (cycle-by-cycle limit) and 2.5 V (fault latch shutdown)."}, {"pin_number": "15/13/5", "pin_name": "DELSETA-B", "pin_description": "Output Delay Control: User programmed current from this pin to GND sets the turn-on delay for the A-B output pair."}, {"pin_number": "5/1/24", "pin_name": "DELSETC-D", "pin_description": "Output Delay Control: User programmed current from this pin to GND sets the turn-on delay for the C-D output pair."}, {"pin_number": "3/19/22", "pin_name": "EA-", "pin_description": "Error Amplifier Inverting Input: Normally connected to the voltage divider for sensing the output voltage level."}, {"pin_number": "20/16/14", "pin_name": "GND", "pin_description": "Signal Ground: All voltages are measured with respect to GND."}, {"pin_number": "13/9", "pin_name": "OUTA", "pin_description": "Output A: 100-mA totem pole output driver. Part of the A-B pair."}, {"pin_number": "12/8/3", "pin_name": "OUTB", "pin_description": "Output B: 100-mA totem pole output driver. Part of the A-B pair."}, {"pin_number": "8/4/27", "pin_name": "OUTC", "pin_description": "Output C: 100-mA totem pole output driver. Part of the C-D pair."}, {"pin_number": "7/3/26", "pin_name": "OUTD", "pin_description": "Output D: 100-mA totem pole output driver. Part of the C-D pair."}, {"pin_number": "11/7/2", "pin_name": "PWRGND", "pin_description": "Power Ground: Ground connection for the output drivers."}, {"pin_number": "19/15/13", "pin_name": "RAMP", "pin_description": "Voltage Ramp: Input to the PWM comparator. Used for voltage or current mode control."}, {"pin_number": "18/14/11", "pin_name": "RT", "pin_description": "Clock/Sync Duty Cycle Set Pin: A resistor from RT to GND sets the oscillator charge current and thus the frequency and maximum duty cycle."}, {"pin_number": "6/2/25", "pin_name": "SS", "pin_description": "Soft Start: A capacitor to GND sets the soft start time."}, {"pin_number": "16/12/8", "pin_name": "UVSEL", "pin_description": "Undervoltage Lockout Select: Connecting to VIN or leaving open programs different UVLO thresholds."}, {"pin_number": "9/5/28", "pin_name": "VC", "pin_description": "Output Switch Supply Voltage: Supplies power to the output drivers."}, {"pin_number": "10/6/1", "pin_name": "VIN", "pin_description": "Primary Chip Supply Voltage: Supplies power to the logic and analog circuitry."}, {"pin_number": "1/17/17", "pin_name": "VREF", "pin_description": "Voltage Reference: Provides an accurate 5 V voltage reference."}]}], "datasheet_cn": "未找到", "datasheet_en": "slus230b.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "不集成(控制器)", "power_device_type": "不适用(控制器)", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "20V", "min_input_voltage": "10.75V", "max_output_voltage": "不适用(控制器)", "min_output_voltage": "不适用(控制器)", "max_output_current": "不适用(控制器)", "max_switch_frequency": "300kHz", "quiescent_current": "23mA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "2.5V", "operation_mode": "移相全桥", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "No", "output_discharge": "未找到", "integrated_ldo": "未找到", "dynamic_voltage_setting": "未找到", "pass_through_mode": "未找到", "load_disconnect": "未找到", "loop_control_mode": "电压/电流模式", "output_voltage_accuracy": "±2.5%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "2.65", "width": "7.6", "length": "26.92", "pin_count": "10"}]}, {"part_number": "UC2879", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "移相谐振控制器", "part_number_title": "PHASE SHIFT RESONANT CONTROLLER", "features": ["Programmable Output Turn On Delay; Zero Delay Available", "Compatible with Voltage Mode or Current Mode Topologies", "Practical Operation at Switching Frequencies to 300 kHz", "10-MHz Error Amplifier", "Pin Programmable Undervoltage Lockout", "Low Startup Current – 150 μA", "Soft Start Control", "Outputs Active Low During UVLO"], "description": "The UC3879 controls a bridge power stage by phase shifting the switching of one half-bridge with respect to the other. This allows constant frequency pulse width modulation in combination with resonant, zero-voltage switching for high efficiency performance. The UC3879 can be configured to provide control in either voltage mode or current mode operation, with overcurrent shutdown for fast fault protection. Independently programmable time delays provide dead-time at the turn-on of each output stage, allowing time for each resonant switching interval.", "applications": [], "ordering_information": [{"part_number": "UC2879", "order_device": "UC2879DW", "package_type": "SOIC", "package_drawing_code": "DW", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UC2879", "order_device": "UC2879DWTR", "package_type": "SOIC", "package_drawing_code": "DW", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UC2879", "order_device": "UC2879N", "package_type": "PDIP", "package_drawing_code": "N", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "UC2879", "package_type": "DIL-20, SOIC-20, PLCC-20, CLCC-28", "pins": [{"pin_number": "17/11/9", "pin_name": "CLKSYNC", "pin_description": "Bi-directional Clock and Synchronization: Used as an output, provides a clock signal. As an input, provides a synchronization point. Multiple UC3879s can be connected together to synchronize to the fastest oscillator. Can also be synchronized to an external clock."}, {"pin_number": "2/18/21", "pin_name": "COMP", "pin_description": "Error Amplifier Output: Output of the gain stage for overall feedback control. Voltage levels below 0.9 V force zero phase shift."}, {"pin_number": "14/10/6", "pin_name": "CT", "pin_description": "Oscillator Frequency Set: A timing capacitor connected between CT and GND sets the oscillator frequency."}, {"pin_number": "4/20/23", "pin_name": "CS", "pin_description": "Current Sense: Non-inverting input to two current fault comparators with internal references of 2 V (cycle-by-cycle limit) and 2.5 V (fault latch shutdown)."}, {"pin_number": "15/13/5", "pin_name": "DELSETA-B", "pin_description": "Output Delay Control: User programmed current from this pin to GND sets the turn-on delay for the A-B output pair."}, {"pin_number": "5/1/24", "pin_name": "DELSETC-D", "pin_description": "Output Delay Control: User programmed current from this pin to GND sets the turn-on delay for the C-D output pair."}, {"pin_number": "3/19/22", "pin_name": "EA-", "pin_description": "Error Amplifier Inverting Input: Normally connected to the voltage divider for sensing the output voltage level."}, {"pin_number": "20/16/14", "pin_name": "GND", "pin_description": "Signal Ground: All voltages are measured with respect to GND."}, {"pin_number": "13/9", "pin_name": "OUTA", "pin_description": "Output A: 100-mA totem pole output driver. Part of the A-B pair."}, {"pin_number": "12/8/3", "pin_name": "OUTB", "pin_description": "Output B: 100-mA totem pole output driver. Part of the A-B pair."}, {"pin_number": "8/4/27", "pin_name": "OUTC", "pin_description": "Output C: 100-mA totem pole output driver. Part of the C-D pair."}, {"pin_number": "7/3/26", "pin_name": "OUTD", "pin_description": "Output D: 100-mA totem pole output driver. Part of the C-D pair."}, {"pin_number": "11/7/2", "pin_name": "PWRGND", "pin_description": "Power Ground: Ground connection for the output drivers."}, {"pin_number": "19/15/13", "pin_name": "RAMP", "pin_description": "Voltage Ramp: Input to the PWM comparator. Used for voltage or current mode control."}, {"pin_number": "18/14/11", "pin_name": "RT", "pin_description": "Clock/Sync Duty Cycle Set Pin: A resistor from RT to GND sets the oscillator charge current and thus the frequency and maximum duty cycle."}, {"pin_number": "6/2/25", "pin_name": "SS", "pin_description": "Soft Start: A capacitor to GND sets the soft start time."}, {"pin_number": "16/12/8", "pin_name": "UVSEL", "pin_description": "Undervoltage Lockout Select: Connecting to VIN or leaving open programs different UVLO thresholds."}, {"pin_number": "9/5/28", "pin_name": "VC", "pin_description": "Output Switch Supply Voltage: Supplies power to the output drivers."}, {"pin_number": "10/6/1", "pin_name": "VIN", "pin_description": "Primary Chip Supply Voltage: Supplies power to the logic and analog circuitry."}, {"pin_number": "1/17/17", "pin_name": "VREF", "pin_description": "Voltage Reference: Provides an accurate 5 V voltage reference."}]}], "datasheet_cn": "未找到", "datasheet_en": "slus230b.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "不集成(控制器)", "power_device_type": "不适用(控制器)", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "20V", "min_input_voltage": "10.75V", "max_output_voltage": "不适用(控制器)", "min_output_voltage": "不适用(控制器)", "max_output_current": "不适用(控制器)", "max_switch_frequency": "300kHz", "quiescent_current": "23mA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "2.5V", "operation_mode": "移相全桥", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "No", "output_discharge": "未找到", "integrated_ldo": "未找到", "dynamic_voltage_setting": "未找到", "pass_through_mode": "未找到", "load_disconnect": "未找到", "loop_control_mode": "电压/电流模式", "output_voltage_accuracy": "±2.5%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "2.65", "width": "7.6", "length": "26.92", "pin_count": "10"}]}, {"part_number": "UC3879", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "移相谐振控制器", "part_number_title": "PHASE SHIFT RESONANT CONTROLLER", "features": ["Programmable Output Turn On Delay; Zero Delay Available", "Compatible with Voltage Mode or Current Mode Topologies", "Practical Operation at Switching Frequencies to 300 kHz", "10-MHz Error Amplifier", "Pin Programmable Undervoltage Lockout", "Low Startup Current – 150 μA", "Soft Start Control", "Outputs Active Low During UVLO"], "description": "The UC3879 controls a bridge power stage by phase shifting the switching of one half-bridge with respect to the other. This allows constant frequency pulse width modulation in combination with resonant, zero-voltage switching for high efficiency performance. The UC3879 can be configured to provide control in either voltage mode or current mode operation, with overcurrent shutdown for fast fault protection. Independently programmable time delays provide dead-time at the turn-on of each output stage, allowing time for each resonant switching interval.", "applications": [], "ordering_information": [{"part_number": "UC3879", "order_device": "UC3879DW", "package_type": "SOIC", "package_drawing_code": "DW", "output_voltage": "未找到", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3879", "order_device": "UC3879DWTR", "package_type": "SOIC", "package_drawing_code": "DW", "output_voltage": "未找到", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3879", "order_device": "UC3879N", "package_type": "PDIP", "package_drawing_code": "N", "output_voltage": "未找到", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "UC3879", "package_type": "DIL-20, SOIC-20, PLCC-20, CLCC-28", "pins": [{"pin_number": "17/11/9", "pin_name": "CLKSYNC", "pin_description": "Bi-directional Clock and Synchronization: Used as an output, provides a clock signal. As an input, provides a synchronization point. Multiple UC3879s can be connected together to synchronize to the fastest oscillator. Can also be synchronized to an external clock."}, {"pin_number": "2/18/21", "pin_name": "COMP", "pin_description": "Error Amplifier Output: Output of the gain stage for overall feedback control. Voltage levels below 0.9 V force zero phase shift."}, {"pin_number": "14/10/6", "pin_name": "CT", "pin_description": "Oscillator Frequency Set: A timing capacitor connected between CT and GND sets the oscillator frequency."}, {"pin_number": "4/20/23", "pin_name": "CS", "pin_description": "Current Sense: Non-inverting input to two current fault comparators with internal references of 2 V (cycle-by-cycle limit) and 2.5 V (fault latch shutdown)."}, {"pin_number": "15/13/5", "pin_name": "DELSETA-B", "pin_description": "Output Delay Control: User programmed current from this pin to GND sets the turn-on delay for the A-B output pair."}, {"pin_number": "5/1/24", "pin_name": "DELSETC-D", "pin_description": "Output Delay Control: User programmed current from this pin to GND sets the turn-on delay for the C-D output pair."}, {"pin_number": "3/19/22", "pin_name": "EA-", "pin_description": "Error Amplifier Inverting Input: Normally connected to the voltage divider for sensing the output voltage level."}, {"pin_number": "20/16/14", "pin_name": "GND", "pin_description": "Signal Ground: All voltages are measured with respect to GND."}, {"pin_number": "13/9", "pin_name": "OUTA", "pin_description": "Output A: 100-mA totem pole output driver. Part of the A-B pair."}, {"pin_number": "12/8/3", "pin_name": "OUTB", "pin_description": "Output B: 100-mA totem pole output driver. Part of the A-B pair."}, {"pin_number": "8/4/27", "pin_name": "OUTC", "pin_description": "Output C: 100-mA totem pole output driver. Part of the C-D pair."}, {"pin_number": "7/3/26", "pin_name": "OUTD", "pin_description": "Output D: 100-mA totem pole output driver. Part of the C-D pair."}, {"pin_number": "11/7/2", "pin_name": "PWRGND", "pin_description": "Power Ground: Ground connection for the output drivers."}, {"pin_number": "19/15/13", "pin_name": "RAMP", "pin_description": "Voltage Ramp: Input to the PWM comparator. Used for voltage or current mode control."}, {"pin_number": "18/14/11", "pin_name": "RT", "pin_description": "Clock/Sync Duty Cycle Set Pin: A resistor from RT to GND sets the oscillator charge current and thus the frequency and maximum duty cycle."}, {"pin_number": "6/2/25", "pin_name": "SS", "pin_description": "Soft Start: A capacitor to GND sets the soft start time."}, {"pin_number": "16/12/8", "pin_name": "UVSEL", "pin_description": "Undervoltage Lockout Select: Connecting to VIN or leaving open programs different UVLO thresholds."}, {"pin_number": "9/5/28", "pin_name": "VC", "pin_description": "Output Switch Supply Voltage: Supplies power to the output drivers."}, {"pin_number": "10/6/1", "pin_name": "VIN", "pin_description": "Primary Chip Supply Voltage: Supplies power to the logic and analog circuitry."}, {"pin_number": "1/17/17", "pin_name": "VREF", "pin_description": "Voltage Reference: Provides an accurate 5 V voltage reference."}]}], "datasheet_cn": "未找到", "datasheet_en": "slus230b.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "不集成(控制器)", "power_device_type": "不适用(控制器)", "power_phase_number": 1, "channel_count": 4, "max_input_voltage": "20V", "min_input_voltage": "10.75V", "max_output_voltage": "不适用(控制器)", "min_output_voltage": "不适用(控制器)", "max_output_current": "不适用(控制器)", "max_switch_frequency": "300kHz", "quiescent_current": "23mA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "2.5V", "operation_mode": "移相全桥", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "No", "output_discharge": "未找到", "integrated_ldo": "未找到", "dynamic_voltage_setting": "未找到", "pass_through_mode": "未找到", "load_disconnect": "未找到", "loop_control_mode": "电压/电流模式", "output_voltage_accuracy": "±2.5%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "2.65", "width": "7.6", "length": "26.92", "pin_count": "10"}]}]