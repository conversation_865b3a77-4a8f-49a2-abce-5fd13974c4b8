[{"part_number": "UCC1581", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电压模式PWM控制器", "part_number_title": "Micropower Voltage Mode PWM", "features": ["Low 85µA Startup Current", "Low 300μA Operating Current", "Automatically Disabled Startup Preregulator", "Programmable Minimum Duty Cycle with Cycle Skipping", "Programmable Maximum Duty Cycle", "Output Current 1A Peak Source and Sink", "Programmable Soft Start", "Programmable Oscillator Frequency", "External Oscillator Synchronization Capability"], "description": "The UCC3581 voltage mode pulse width modulator is designed to control low power isolated DC - DC converters in applications such as Subscriber Line Power (ISDN 1.430). Primarily used for single switch forward and flyback converters, the UCC3581 features BICMOS circuitry for low startup and operating current, while maintaining the ability to drive power MOSFETs at frequencies up to 100kHz. The UCC3581 oscillator allows the flexibility to program both the frequency and the maximum duty cycle with two resistors and a capacitor. A TTL level input is also provided to allow synchronization to an external frequency source. The UCC3581 includes programmable soft start circuitry, overcurrent detection, a 7.5V linear preregulator to control chip VDD during startup, and an on-board 4.0V logic supply. The UCC3581 provides functions to maximize light load efficiency that are not normally found in PWM controllers. A linear preregulator driver in conjunction with an external depletion mode N-MOSFET provides initial controller power. Once the bootstrap supply is functional, the preregulator is shut down to conserve power. During light load, power is saved by providing a programmable minimum duty cycle clamp. When a duty cycle below the minimum is called for, the modulator skips cycles to provide the correct average duty cycle required for output regulation. This effectively reduces the switching frequency, saving significant gate drive and power stage losses.", "applications": ["Subscriber Line Power (ISDN 1.430)", "Single switch forward converters", "Flyback converters", "Low Power Isolated DC-DC Converters"], "ordering_information": [{"part_number": "UCC1581", "order_device": "UCC1581J", "package_type": "CDIP", "package_drawing_code": "J", "output_voltage": "Adjustable", "min_operation_temp": "-55", "max_operation_temp": "125"}], "pin_function": [{"pin_number": "1", "pin_name": "CT", "pin_description": "Oscillator timing capacitor pin. Minimum value is 100pF."}, {"pin_number": "2", "pin_name": "GT", "pin_description": "Pin for controlling the gate of an external depletion mode N-MOSFET for the startup supply. The external N-MOSFET regulates VDD to 7.5V until the bootstrap supply comes up, then GT goes low."}, {"pin_number": "3", "pin_name": "VDD", "pin_description": "Chip input power with an 15V internal clamp. VDD is regulated by startup FET to 7.5V until the bootstrap voltage comes up. VDD should be bypassed at the chip with a 0.1µF minimum capacitor."}, {"pin_number": "4", "pin_name": "OUT", "pin_description": "Gate drive output to external N-MOSFET."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Circuit ground."}, {"pin_number": "6", "pin_name": "REF", "pin_description": "4.0V reference output. A minimum value bypass capacitor of 1.0µF is required for stability."}, {"pin_number": "7", "pin_name": "ISEN", "pin_description": "Input for overcurrent comparator. This function can be used for pulse-by-pulse current limiting. The threshold is 0.5V nominal."}, {"pin_number": "8", "pin_name": "VC", "pin_description": "Control voltage input to PWM comparator. The nominal control range of VC is 1.0V to 2.5V."}, {"pin_number": "9", "pin_name": "DCMIN", "pin_description": "Input for programming minimum duty cycle where pulse skipping begins. This pin can be grounded to disable minimum duty cycle feature and pulse skipping."}, {"pin_number": "10", "pin_name": "SS", "pin_description": "Soft start capacitor pin. The charging current out of SS is 3.75X the current in RT1."}, {"pin_number": "11", "pin_name": "EN", "pin_description": "Enable input. This pin has an internal 10µA pull-up. A logic low input inhibits the PWM output and causes the soft start capacitor to be discharged."}, {"pin_number": "12", "pin_name": "RT1", "pin_description": "Resistor pin to program oscillator charging current. The value of RT1 should be between 220k and 1MΩ."}, {"pin_number": "13", "pin_name": "SYNC", "pin_description": "Oscillator synchronization pin. Rising edge triggered CMOS/TTL compatible input with a 2.1V threshold. SYNC should be grounded if not used. The minimum pulse width of the SYNC signal is 100ns."}, {"pin_number": "14", "pin_name": "RT2", "pin_description": "Resistor pin to program oscillator discharge time. The minimum value of RT2 is 10kΩ."}], "datasheet_cn": "未找到", "datasheet_en": "UCC1581_UCC2581_UCC3581_SLUS295B.pdf", "family_comparison": "The UCCx581 family consists of three variants with different operating temperature ranges: UCC1581 (Military, -55°C to +125°C), UCC2581 (Industrial, -40°C to +85°C), and UCC3581 (Commercial, 0°C to +70°C).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "15V", "min_input_voltage": "6.8V", "max_output_voltage": "Adjustable", "min_output_voltage": "Adjustable", "max_output_current": "不适用", "max_switch_frequency": "100kHz", "quiescent_current": "300µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "0.5V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "Pulse Skipping", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "Yes", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "Voltage Mode", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "4V"}, "package": [{"type": "OPTION", "pin_count": "300", "pitch": "1.27", "height": "1.75", "width": "1.430", "length": "8.75"}]}, {"part_number": "UCC2581", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电压模式PWM控制器", "part_number_title": "Micropower Voltage Mode PWM", "features": ["Low 85µA Startup Current", "Low 300μA Operating Current", "Automatically Disabled Startup Preregulator", "Programmable Minimum Duty Cycle with Cycle Skipping", "Programmable Maximum Duty Cycle", "Output Current 1A Peak Source and Sink", "Programmable Soft Start", "Programmable Oscillator Frequency", "External Oscillator Synchronization Capability"], "description": "The UCC3581 voltage mode pulse width modulator is designed to control low power isolated DC - DC converters in applications such as Subscriber Line Power (ISDN 1.430). Primarily used for single switch forward and flyback converters, the UCC3581 features BICMOS circuitry for low startup and operating current, while maintaining the ability to drive power MOSFETs at frequencies up to 100kHz. The UCC3581 oscillator allows the flexibility to program both the frequency and the maximum duty cycle with two resistors and a capacitor. A TTL level input is also provided to allow synchronization to an external frequency source. The UCC3581 includes programmable soft start circuitry, overcurrent detection, a 7.5V linear preregulator to control chip VDD during startup, and an on-board 4.0V logic supply. The UCC3581 provides functions to maximize light load efficiency that are not normally found in PWM controllers. A linear preregulator driver in conjunction with an external depletion mode N-MOSFET provides initial controller power. Once the bootstrap supply is functional, the preregulator is shut down to conserve power. During light load, power is saved by providing a programmable minimum duty cycle clamp. When a duty cycle below the minimum is called for, the modulator skips cycles to provide the correct average duty cycle required for output regulation. This effectively reduces the switching frequency, saving significant gate drive and power stage losses.", "applications": ["Subscriber Line Power (ISDN 1.430)", "Single switch forward converters", "Flyback converters", "Low Power Isolated DC-DC Converters"], "ordering_information": [{"part_number": "UCC2581", "order_device": "UCC2581D", "package_type": "SOIC", "package_drawing_code": "D0014A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC2581", "order_device": "UCC2581N", "package_type": "PDIP", "package_drawing_code": "未找到", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC2581", "order_device": "UCC2581DTR", "package_type": "SOIC", "package_drawing_code": "D0014A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"pin_number": "1", "pin_name": "CT", "pin_description": "Oscillator timing capacitor pin. Minimum value is 100pF."}, {"pin_number": "2", "pin_name": "GT", "pin_description": "Pin for controlling the gate of an external depletion mode N-MOSFET for the startup supply. The external N-MOSFET regulates VDD to 7.5V until the bootstrap supply comes up, then GT goes low."}, {"pin_number": "3", "pin_name": "VDD", "pin_description": "Chip input power with an 15V internal clamp. VDD is regulated by startup FET to 7.5V until the bootstrap voltage comes up. VDD should be bypassed at the chip with a 0.1µF minimum capacitor."}, {"pin_number": "4", "pin_name": "OUT", "pin_description": "Gate drive output to external N-MOSFET."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Circuit ground."}, {"pin_number": "6", "pin_name": "REF", "pin_description": "4.0V reference output. A minimum value bypass capacitor of 1.0µF is required for stability."}, {"pin_number": "7", "pin_name": "ISEN", "pin_description": "Input for overcurrent comparator. This function can be used for pulse-by-pulse current limiting. The threshold is 0.5V nominal."}, {"pin_number": "8", "pin_name": "VC", "pin_description": "Control voltage input to PWM comparator. The nominal control range of VC is 1.0V to 2.5V."}, {"pin_number": "9", "pin_name": "DCMIN", "pin_description": "Input for programming minimum duty cycle where pulse skipping begins. This pin can be grounded to disable minimum duty cycle feature and pulse skipping."}, {"pin_number": "10", "pin_name": "SS", "pin_description": "Soft start capacitor pin. The charging current out of SS is 3.75X the current in RT1."}, {"pin_number": "11", "pin_name": "EN", "pin_description": "Enable input. This pin has an internal 10µA pull-up. A logic low input inhibits the PWM output and causes the soft start capacitor to be discharged."}, {"pin_number": "12", "pin_name": "RT1", "pin_description": "Resistor pin to program oscillator charging current. The value of RT1 should be between 220k and 1MΩ."}, {"pin_number": "13", "pin_name": "SYNC", "pin_description": "Oscillator synchronization pin. Rising edge triggered CMOS/TTL compatible input with a 2.1V threshold. SYNC should be grounded if not used. The minimum pulse width of the SYNC signal is 100ns."}, {"pin_number": "14", "pin_name": "RT2", "pin_description": "Resistor pin to program oscillator discharge time. The minimum value of RT2 is 10kΩ."}], "datasheet_cn": "未找到", "datasheet_en": "UCC1581_UCC2581_UCC3581_SLUS295B.pdf", "family_comparison": "The UCCx581 family consists of three variants with different operating temperature ranges: UCC1581 (Military, -55°C to +125°C), UCC2581 (Industrial, -40°C to +85°C), and UCC3581 (Commercial, 0°C to +70°C).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "15V", "min_input_voltage": "6.8V", "max_output_voltage": "Adjustable", "min_output_voltage": "Adjustable", "max_output_current": "不适用", "max_switch_frequency": "100kHz", "quiescent_current": "300µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "0.5V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "Pulse Skipping", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "Yes", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "Voltage Mode", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "4V"}, "package": [{"type": "OPTION", "pin_count": "300", "pitch": "1.27", "height": "1.75", "width": "1.430", "length": "8.75"}]}, {"part_number": "UCC3581", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电压模式PWM控制器", "part_number_title": "Micropower Voltage Mode PWM", "features": ["Low 85µA Startup Current", "Low 300μA Operating Current", "Automatically Disabled Startup Preregulator", "Programmable Minimum Duty Cycle with Cycle Skipping", "Programmable Maximum Duty Cycle", "Output Current 1A Peak Source and Sink", "Programmable Soft Start", "Programmable Oscillator Frequency", "External Oscillator Synchronization Capability"], "description": "The UCC3581 voltage mode pulse width modulator is designed to control low power isolated DC - DC converters in applications such as Subscriber Line Power (ISDN 1.430). Primarily used for single switch forward and flyback converters, the UCC3581 features BICMOS circuitry for low startup and operating current, while maintaining the ability to drive power MOSFETs at frequencies up to 100kHz. The UCC3581 oscillator allows the flexibility to program both the frequency and the maximum duty cycle with two resistors and a capacitor. A TTL level input is also provided to allow synchronization to an external frequency source. The UCC3581 includes programmable soft start circuitry, overcurrent detection, a 7.5V linear preregulator to control chip VDD during startup, and an on-board 4.0V logic supply. The UCC3581 provides functions to maximize light load efficiency that are not normally found in PWM controllers. A linear preregulator driver in conjunction with an external depletion mode N-MOSFET provides initial controller power. Once the bootstrap supply is functional, the preregulator is shut down to conserve power. During light load, power is saved by providing a programmable minimum duty cycle clamp. When a duty cycle below the minimum is called for, the modulator skips cycles to provide the correct average duty cycle required for output regulation. This effectively reduces the switching frequency, saving significant gate drive and power stage losses.", "applications": ["Subscriber Line Power (ISDN 1.430)", "Single switch forward converters", "Flyback converters", "Low Power Isolated DC-DC Converters"], "ordering_information": [{"part_number": "UCC3581", "order_device": "UCC3581D", "package_type": "SOIC", "package_drawing_code": "D0014A", "output_voltage": "Adjustable", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC3581", "order_device": "UCC3581N", "package_type": "PDIP", "package_drawing_code": "未找到", "output_voltage": "Adjustable", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC3581", "order_device": "UCC3581DTR", "package_type": "SOIC", "package_drawing_code": "D0014A", "output_voltage": "Adjustable", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"pin_number": "1", "pin_name": "CT", "pin_description": "Oscillator timing capacitor pin. Minimum value is 100pF."}, {"pin_number": "2", "pin_name": "GT", "pin_description": "Pin for controlling the gate of an external depletion mode N-MOSFET for the startup supply. The external N-MOSFET regulates VDD to 7.5V until the bootstrap supply comes up, then GT goes low."}, {"pin_number": "3", "pin_name": "VDD", "pin_description": "Chip input power with an 15V internal clamp. VDD is regulated by startup FET to 7.5V until the bootstrap voltage comes up. VDD should be bypassed at the chip with a 0.1µF minimum capacitor."}, {"pin_number": "4", "pin_name": "OUT", "pin_description": "Gate drive output to external N-MOSFET."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Circuit ground."}, {"pin_number": "6", "pin_name": "REF", "pin_description": "4.0V reference output. A minimum value bypass capacitor of 1.0µF is required for stability."}, {"pin_number": "7", "pin_name": "ISEN", "pin_description": "Input for overcurrent comparator. This function can be used for pulse-by-pulse current limiting. The threshold is 0.5V nominal."}, {"pin_number": "8", "pin_name": "VC", "pin_description": "Control voltage input to PWM comparator. The nominal control range of VC is 1.0V to 2.5V."}, {"pin_number": "9", "pin_name": "DCMIN", "pin_description": "Input for programming minimum duty cycle where pulse skipping begins. This pin can be grounded to disable minimum duty cycle feature and pulse skipping."}, {"pin_number": "10", "pin_name": "SS", "pin_description": "Soft start capacitor pin. The charging current out of SS is 3.75X the current in RT1."}, {"pin_number": "11", "pin_name": "EN", "pin_description": "Enable input. This pin has an internal 10µA pull-up. A logic low input inhibits the PWM output and causes the soft start capacitor to be discharged."}, {"pin_number": "12", "pin_name": "RT1", "pin_description": "Resistor pin to program oscillator charging current. The value of RT1 should be between 220k and 1MΩ."}, {"pin_number": "13", "pin_name": "SYNC", "pin_description": "Oscillator synchronization pin. Rising edge triggered CMOS/TTL compatible input with a 2.1V threshold. SYNC should be grounded if not used. The minimum pulse width of the SYNC signal is 100ns."}, {"pin_number": "14", "pin_name": "RT2", "pin_description": "Resistor pin to program oscillator discharge time. The minimum value of RT2 is 10kΩ."}], "datasheet_cn": "未找到", "datasheet_en": "UCC1581_UCC2581_UCC3581_SLUS295B.pdf", "family_comparison": "The UCCx581 family consists of three variants with different operating temperature ranges: UCC1581 (Military, -55°C to +125°C), UCC2581 (Industrial, -40°C to +85°C), and UCC3581 (Commercial, 0°C to +70°C).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "15V", "min_input_voltage": "6.8V", "max_output_voltage": "Adjustable", "min_output_voltage": "Adjustable", "max_output_current": "不适用", "max_switch_frequency": "100kHz", "quiescent_current": "300µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "0.5V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "Pulse Skipping", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "Yes", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "Voltage Mode", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "4V"}, "package": [{"type": "OPTION", "pin_count": "300", "pitch": "1.27", "height": "1.75", "width": "1.430", "length": "8.75"}]}]