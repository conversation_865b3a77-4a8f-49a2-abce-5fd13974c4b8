[{"part_number": "UCC28C40-EP", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电流模式PWM控制器", "part_number_title": "UCC28C4x-EP BICMOS LOW-POWER CURRENT-MODE PWM CONTROLLERS", "features": ["Controlled Baseline", "One Assembly/Test Site, One Fabrication Site", "Extended Temperature Performance of –55°C to 125°C", "Enhanced Diminishing Manufacturing Sources (DMS) Support", "Enhanced Product-Change Notification", "Qualification Pedigree", "Enhanced Replacements for UC2842A Family With Pin-to-Pin Compatibility", "1 MHz Operation", "50 μA Standby Current, 100 μA Maximum", "Low Operating Current of 2.3 mA at 52 kHz", "Fast 35 ns Cycle-by-Cycle Overcurrent Limiting", "±1 A Peak Output Current", "Rail-to-Rail Output Swings With 25 ns Rise and 20 ns Fall Times", "±1% Initial Trimmed 2.5 V Error Amplifier Reference", "Trimmed Oscillator Discharge Current", "New Undervoltage Lockout Versions", "MSOP-8 Package Minimizes Board Space"], "description": "The UCC28C4x family are high performance current mode PWM controllers. They are enhanced BICMOS versions with pin-for-pin compatibility to the industry standard UC284xA family and UC284x family of PWM controllers. In addition, lower startup voltage versions of 7 V are offered as UCC28C40 and UCC28C41. Providing necessary features to control fixed frequency, peak current mode power supplies, this family offers several performance advantages. These devices offer high frequency operation up to 1 MHz with low start up and operating currents, thus minimizing start up loss and low operating power consumption for improved efficiency. The devices also feature a fast current sense to output delay time of 35 ns, and a ±1 A peak output current capability with improved rise and fall times for driving large external MOSFETs directly. The UCC28C4x family is offered in 8-pin package SOIC (D).", "applications": ["Switch Mode Power Supplies", "DC-to-DC Converters", "Board Mount Power Modules"], "ordering_information": [{"part_number": "UCC28C40-EP", "order_device": "UCC28C40MDREP", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-55", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC28C4x-EP", "package_type": "SOIC (D)", "pins": [{"pin_number": "1", "pin_name": "COMP", "pin_description": "This pin provides the output of the error amplifier for compensation. In addition, the COMP pin is frequently used as a control port by utilizing a secondary-side error amplifier to send an error signal across the secondary-primary isolation boundary through an opto-isolator."}, {"pin_number": "2", "pin_name": "FB", "pin_description": "This pin is the inverting input to the error amplifier. The noninverting input to the error amplifier is internally trimmed to 2.5 V ± 1%."}, {"pin_number": "3", "pin_name": "CS", "pin_description": "The current-sense pin is the noninverting input to the PWM comparator. This is compared to a signal proportional to the error amplifier output voltage. A voltage ramp can be applied to this pin to run the device with a voltage mode control configuration."}, {"pin_number": "4", "pin_name": "RT/CT", "pin_description": "Timing resistor and timing capacitor. The timing capacitor should be connected to the device ground using minimal trace length."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Ground return pin for the output driver stage and the logic-level controller section."}, {"pin_number": "6", "pin_name": "OUT", "pin_description": "The output of the on-chip drive stage. OUT is intended to directly drive a MOSFET. The OUT pin in the UCC28C40, UCC28C42, and UCC28C43 is the same frequency as the oscillator, and can operate near 100% duty cycle. In the UCC28C41, UCC28C44, and the UCC28C45, the frequency of OUT is one-half that of the oscillator due to an internal T flipflop. This limits the maximum duty cycle to <50%."}, {"pin_number": "7", "pin_name": "VDD", "pin_description": "Power supply pin for the device. This pin should be bypassed with a 0.1 µF capacitor with minimal trace lengths. Additional capacitance may be needed to provide hold up power to the device during startup."}, {"pin_number": "8", "pin_name": "VREF", "pin_description": "5-V reference. For stability, the reference should be bypassed with a 0.1 µF capacitor to ground using the minimal trace length possible."}]}], "datasheet_cn": "未找到", "datasheet_en": "SGLS352B", "family_comparison": "The UCC28C4x family includes versions with different UVLO thresholds (7V, 8.4V, 14.5V) and maximum duty cycles (~100% for UCC28C40/42/43 and <50% for UCC28C41/44/45).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "7V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "1A", "max_switch_frequency": "1MHz", "quiescent_current": "2.3mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "1%", "output_reference_voltage": "2.5V"}, "package": [{"type": "Minimizes", "pitch": "1.27", "height": "1.75", "width": "3.98", "length": "0.1", "pin_count": "2842"}]}, {"part_number": "UCC28C41-EP", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电流模式PWM控制器", "part_number_title": "UCC28C4x-EP BICMOS LOW-POWER CURRENT-MODE PWM CONTROLLERS", "features": ["Controlled Baseline", "One Assembly/Test Site, One Fabrication Site", "Extended Temperature Performance of –55°C to 125°C", "Enhanced Diminishing Manufacturing Sources (DMS) Support", "Enhanced Product-Change Notification", "Qualification Pedigree", "Enhanced Replacements for UC2842A Family With Pin-to-Pin Compatibility", "1 MHz Operation", "50 μA Standby Current, 100 μA Maximum", "Low Operating Current of 2.3 mA at 52 kHz", "Fast 35 ns Cycle-by-Cycle Overcurrent Limiting", "±1 A Peak Output Current", "Rail-to-Rail Output Swings With 25 ns Rise and 20 ns Fall Times", "±1% Initial Trimmed 2.5 V Error Amplifier Reference", "Trimmed Oscillator Discharge Current", "New Undervoltage Lockout Versions", "MSOP-8 Package Minimizes Board Space"], "description": "The UCC28C4x family are high performance current mode PWM controllers. They are enhanced BICMOS versions with pin-for-pin compatibility to the industry standard UC284xA family and UC284x family of PWM controllers. In addition, lower startup voltage versions of 7 V are offered as UCC28C40 and UCC28C41. Providing necessary features to control fixed frequency, peak current mode power supplies, this family offers several performance advantages. These devices offer high frequency operation up to 1 MHz with low start up and operating currents, thus minimizing start up loss and low operating power consumption for improved efficiency. The devices also feature a fast current sense to output delay time of 35 ns, and a ±1 A peak output current capability with improved rise and fall times for driving large external MOSFETs directly. The UCC28C4x family is offered in 8-pin package SOIC (D).", "applications": ["Switch Mode Power Supplies", "DC-to-DC Converters", "Board Mount Power Modules"], "ordering_information": [{"part_number": "UCC28C41-EP", "order_device": "UCC28C41MDREP", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-55", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC28C4x-EP", "package_type": "SOIC (D)", "pins": [{"pin_number": "1", "pin_name": "COMP", "pin_description": "This pin provides the output of the error amplifier for compensation. In addition, the COMP pin is frequently used as a control port by utilizing a secondary-side error amplifier to send an error signal across the secondary-primary isolation boundary through an opto-isolator."}, {"pin_number": "2", "pin_name": "FB", "pin_description": "This pin is the inverting input to the error amplifier. The noninverting input to the error amplifier is internally trimmed to 2.5 V ± 1%."}, {"pin_number": "3", "pin_name": "CS", "pin_description": "The current-sense pin is the noninverting input to the PWM comparator. This is compared to a signal proportional to the error amplifier output voltage. A voltage ramp can be applied to this pin to run the device with a voltage mode control configuration."}, {"pin_number": "4", "pin_name": "RT/CT", "pin_description": "Timing resistor and timing capacitor. The timing capacitor should be connected to the device ground using minimal trace length."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Ground return pin for the output driver stage and the logic-level controller section."}, {"pin_number": "6", "pin_name": "OUT", "pin_description": "The output of the on-chip drive stage. OUT is intended to directly drive a MOSFET. The OUT pin in the UCC28C40, UCC28C42, and UCC28C43 is the same frequency as the oscillator, and can operate near 100% duty cycle. In the UCC28C41, UCC28C44, and the UCC28C45, the frequency of OUT is one-half that of the oscillator due to an internal T flipflop. This limits the maximum duty cycle to <50%."}, {"pin_number": "7", "pin_name": "VDD", "pin_description": "Power supply pin for the device. This pin should be bypassed with a 0.1 µF capacitor with minimal trace lengths. Additional capacitance may be needed to provide hold up power to the device during startup."}, {"pin_number": "8", "pin_name": "VREF", "pin_description": "5-V reference. For stability, the reference should be bypassed with a 0.1 µF capacitor to ground using the minimal trace length possible."}]}], "datasheet_cn": "未找到", "datasheet_en": "SGLS352B", "family_comparison": "The UCC28C4x family includes versions with different UVLO thresholds (7V, 8.4V, 14.5V) and maximum duty cycles (~100% for UCC28C40/42/43 and <50% for UCC28C41/44/45).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "7V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "1A", "max_switch_frequency": "1MHz", "quiescent_current": "2.3mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "1%", "output_reference_voltage": "2.5V"}, "package": [{"type": "Minimizes", "pitch": "1.27", "height": "1.75", "width": "3.98", "length": "0.1", "pin_count": "2842"}]}, {"part_number": "UCC28C42-EP", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电流模式PWM控制器", "part_number_title": "UCC28C4x-EP BICMOS LOW-POWER CURRENT-MODE PWM CONTROLLERS", "features": ["Controlled Baseline", "One Assembly/Test Site, One Fabrication Site", "Extended Temperature Performance of –55°C to 125°C", "Enhanced Diminishing Manufacturing Sources (DMS) Support", "Enhanced Product-Change Notification", "Qualification Pedigree", "Enhanced Replacements for UC2842A Family With Pin-to-Pin Compatibility", "1 MHz Operation", "50 μA Standby Current, 100 μA Maximum", "Low Operating Current of 2.3 mA at 52 kHz", "Fast 35 ns Cycle-by-Cycle Overcurrent Limiting", "±1 A Peak Output Current", "Rail-to-Rail Output Swings With 25 ns Rise and 20 ns Fall Times", "±1% Initial Trimmed 2.5 V Error Amplifier Reference", "Trimmed Oscillator Discharge Current", "New Undervoltage Lockout Versions", "MSOP-8 Package Minimizes Board Space"], "description": "The UCC28C4x family are high performance current mode PWM controllers. They are enhanced BICMOS versions with pin-for-pin compatibility to the industry standard UC284xA family and UC284x family of PWM controllers. In addition, lower startup voltage versions of 7 V are offered as UCC28C40 and UCC28C41. Providing necessary features to control fixed frequency, peak current mode power supplies, this family offers several performance advantages. These devices offer high frequency operation up to 1 MHz with low start up and operating currents, thus minimizing start up loss and low operating power consumption for improved efficiency. The devices also feature a fast current sense to output delay time of 35 ns, and a ±1 A peak output current capability with improved rise and fall times for driving large external MOSFETs directly. The UCC28C4x family is offered in 8-pin package SOIC (D).", "applications": ["Switch Mode Power Supplies", "DC-to-DC Converters", "Board Mount Power Modules"], "ordering_information": [{"part_number": "UCC28C42-EP", "order_device": "UCC28C42MDREP", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-55", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC28C4x-EP", "package_type": "SOIC (D)", "pins": [{"pin_number": "1", "pin_name": "COMP", "pin_description": "This pin provides the output of the error amplifier for compensation. In addition, the COMP pin is frequently used as a control port by utilizing a secondary-side error amplifier to send an error signal across the secondary-primary isolation boundary through an opto-isolator."}, {"pin_number": "2", "pin_name": "FB", "pin_description": "This pin is the inverting input to the error amplifier. The noninverting input to the error amplifier is internally trimmed to 2.5 V ± 1%."}, {"pin_number": "3", "pin_name": "CS", "pin_description": "The current-sense pin is the noninverting input to the PWM comparator. This is compared to a signal proportional to the error amplifier output voltage. A voltage ramp can be applied to this pin to run the device with a voltage mode control configuration."}, {"pin_number": "4", "pin_name": "RT/CT", "pin_description": "Timing resistor and timing capacitor. The timing capacitor should be connected to the device ground using minimal trace length."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Ground return pin for the output driver stage and the logic-level controller section."}, {"pin_number": "6", "pin_name": "OUT", "pin_description": "The output of the on-chip drive stage. OUT is intended to directly drive a MOSFET. The OUT pin in the UCC28C40, UCC28C42, and UCC28C43 is the same frequency as the oscillator, and can operate near 100% duty cycle. In the UCC28C41, UCC28C44, and the UCC28C45, the frequency of OUT is one-half that of the oscillator due to an internal T flipflop. This limits the maximum duty cycle to <50%."}, {"pin_number": "7", "pin_name": "VDD", "pin_description": "Power supply pin for the device. This pin should be bypassed with a 0.1 µF capacitor with minimal trace lengths. Additional capacitance may be needed to provide hold up power to the device during startup."}, {"pin_number": "8", "pin_name": "VREF", "pin_description": "5-V reference. For stability, the reference should be bypassed with a 0.1 µF capacitor to ground using the minimal trace length possible."}]}], "datasheet_cn": "未找到", "datasheet_en": "SGLS352B", "family_comparison": "The UCC28C4x family includes versions with different UVLO thresholds (7V, 8.4V, 14.5V) and maximum duty cycles (~100% for UCC28C40/42/43 and <50% for UCC28C41/44/45).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "14.5V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "1A", "max_switch_frequency": "1MHz", "quiescent_current": "2.3mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "1%", "output_reference_voltage": "2.5V"}, "package": [{"type": "Minimizes", "pitch": "1.27", "height": "1.75", "width": "3.98", "length": "0.1", "pin_count": "2842"}]}, {"part_number": "UCC28C43-EP", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电流模式PWM控制器", "part_number_title": "UCC28C4x-EP BICMOS LOW-POWER CURRENT-MODE PWM CONTROLLERS", "features": ["Controlled Baseline", "One Assembly/Test Site, One Fabrication Site", "Extended Temperature Performance of –55°C to 125°C", "Enhanced Diminishing Manufacturing Sources (DMS) Support", "Enhanced Product-Change Notification", "Qualification Pedigree", "Enhanced Replacements for UC2842A Family With Pin-to-Pin Compatibility", "1 MHz Operation", "50 μA Standby Current, 100 μA Maximum", "Low Operating Current of 2.3 mA at 52 kHz", "Fast 35 ns Cycle-by-Cycle Overcurrent Limiting", "±1 A Peak Output Current", "Rail-to-Rail Output Swings With 25 ns Rise and 20 ns Fall Times", "±1% Initial Trimmed 2.5 V Error Amplifier Reference", "Trimmed Oscillator Discharge Current", "New Undervoltage Lockout Versions", "MSOP-8 Package Minimizes Board Space"], "description": "The UCC28C4x family are high performance current mode PWM controllers. They are enhanced BICMOS versions with pin-for-pin compatibility to the industry standard UC284xA family and UC284x family of PWM controllers. In addition, lower startup voltage versions of 7 V are offered as UCC28C40 and UCC28C41. Providing necessary features to control fixed frequency, peak current mode power supplies, this family offers several performance advantages. These devices offer high frequency operation up to 1 MHz with low start up and operating currents, thus minimizing start up loss and low operating power consumption for improved efficiency. The devices also feature a fast current sense to output delay time of 35 ns, and a ±1 A peak output current capability with improved rise and fall times for driving large external MOSFETs directly. The UCC28C4x family is offered in 8-pin package SOIC (D).", "applications": ["Switch Mode Power Supplies", "DC-to-DC Converters", "Board Mount Power Modules"], "ordering_information": [{"part_number": "UCC28C43-EP", "order_device": "UCC28C43MDREP", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-55", "max_operation_temp": "125"}, {"part_number": "UCC28C43-EP", "order_device": "UCC28C43MDREP.A", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-55", "max_operation_temp": "125"}, {"part_number": "UCC28C43-EP", "order_device": "V62/07615-01XE", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-55", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC28C4x-EP", "package_type": "SOIC (D)", "pins": [{"pin_number": "1", "pin_name": "COMP", "pin_description": "This pin provides the output of the error amplifier for compensation. In addition, the COMP pin is frequently used as a control port by utilizing a secondary-side error amplifier to send an error signal across the secondary-primary isolation boundary through an opto-isolator."}, {"pin_number": "2", "pin_name": "FB", "pin_description": "This pin is the inverting input to the error amplifier. The noninverting input to the error amplifier is internally trimmed to 2.5 V ± 1%."}, {"pin_number": "3", "pin_name": "CS", "pin_description": "The current-sense pin is the noninverting input to the PWM comparator. This is compared to a signal proportional to the error amplifier output voltage. A voltage ramp can be applied to this pin to run the device with a voltage mode control configuration."}, {"pin_number": "4", "pin_name": "RT/CT", "pin_description": "Timing resistor and timing capacitor. The timing capacitor should be connected to the device ground using minimal trace length."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Ground return pin for the output driver stage and the logic-level controller section."}, {"pin_number": "6", "pin_name": "OUT", "pin_description": "The output of the on-chip drive stage. OUT is intended to directly drive a MOSFET. The OUT pin in the UCC28C40, UCC28C42, and UCC28C43 is the same frequency as the oscillator, and can operate near 100% duty cycle. In the UCC28C41, UCC28C44, and the UCC28C45, the frequency of OUT is one-half that of the oscillator due to an internal T flipflop. This limits the maximum duty cycle to <50%."}, {"pin_number": "7", "pin_name": "VDD", "pin_description": "Power supply pin for the device. This pin should be bypassed with a 0.1 µF capacitor with minimal trace lengths. Additional capacitance may be needed to provide hold up power to the device during startup."}, {"pin_number": "8", "pin_name": "VREF", "pin_description": "5-V reference. For stability, the reference should be bypassed with a 0.1 µF capacitor to ground using the minimal trace length possible."}]}], "datasheet_cn": "未找到", "datasheet_en": "SGLS352B", "family_comparison": "The UCC28C4x family includes versions with different UVLO thresholds (7V, 8.4V, 14.5V) and maximum duty cycles (~100% for UCC28C40/42/43 and <50% for UCC28C41/44/45).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "8.4V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "1A", "max_switch_frequency": "1MHz", "quiescent_current": "2.3mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "1%", "output_reference_voltage": "2.5V"}, "package": [{"type": "Minimizes", "pitch": "1.27", "height": "1.75", "width": "3.98", "length": "0.1", "pin_count": "2842"}]}, {"part_number": "UCC28C44-EP", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电流模式PWM控制器", "part_number_title": "UCC28C4x-EP BICMOS LOW-POWER CURRENT-MODE PWM CONTROLLERS", "features": ["Controlled Baseline", "One Assembly/Test Site, One Fabrication Site", "Extended Temperature Performance of –55°C to 125°C", "Enhanced Diminishing Manufacturing Sources (DMS) Support", "Enhanced Product-Change Notification", "Qualification Pedigree", "Enhanced Replacements for UC2842A Family With Pin-to-Pin Compatibility", "1 MHz Operation", "50 μA Standby Current, 100 μA Maximum", "Low Operating Current of 2.3 mA at 52 kHz", "Fast 35 ns Cycle-by-Cycle Overcurrent Limiting", "±1 A Peak Output Current", "Rail-to-Rail Output Swings With 25 ns Rise and 20 ns Fall Times", "±1% Initial Trimmed 2.5 V Error Amplifier Reference", "Trimmed Oscillator Discharge Current", "New Undervoltage Lockout Versions", "MSOP-8 Package Minimizes Board Space"], "description": "The UCC28C4x family are high performance current mode PWM controllers. They are enhanced BICMOS versions with pin-for-pin compatibility to the industry standard UC284xA family and UC284x family of PWM controllers. In addition, lower startup voltage versions of 7 V are offered as UCC28C40 and UCC28C41. Providing necessary features to control fixed frequency, peak current mode power supplies, this family offers several performance advantages. These devices offer high frequency operation up to 1 MHz with low start up and operating currents, thus minimizing start up loss and low operating power consumption for improved efficiency. The devices also feature a fast current sense to output delay time of 35 ns, and a ±1 A peak output current capability with improved rise and fall times for driving large external MOSFETs directly. The UCC28C4x family is offered in 8-pin package SOIC (D).", "applications": ["Switch Mode Power Supplies", "DC-to-DC Converters", "Board Mount Power Modules"], "ordering_information": [{"part_number": "UCC28C44-EP", "order_device": "UCC28C44MDREP", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-55", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC28C4x-EP", "package_type": "SOIC (D)", "pins": [{"pin_number": "1", "pin_name": "COMP", "pin_description": "This pin provides the output of the error amplifier for compensation. In addition, the COMP pin is frequently used as a control port by utilizing a secondary-side error amplifier to send an error signal across the secondary-primary isolation boundary through an opto-isolator."}, {"pin_number": "2", "pin_name": "FB", "pin_description": "This pin is the inverting input to the error amplifier. The noninverting input to the error amplifier is internally trimmed to 2.5 V ± 1%."}, {"pin_number": "3", "pin_name": "CS", "pin_description": "The current-sense pin is the noninverting input to the PWM comparator. This is compared to a signal proportional to the error amplifier output voltage. A voltage ramp can be applied to this pin to run the device with a voltage mode control configuration."}, {"pin_number": "4", "pin_name": "RT/CT", "pin_description": "Timing resistor and timing capacitor. The timing capacitor should be connected to the device ground using minimal trace length."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Ground return pin for the output driver stage and the logic-level controller section."}, {"pin_number": "6", "pin_name": "OUT", "pin_description": "The output of the on-chip drive stage. OUT is intended to directly drive a MOSFET. The OUT pin in the UCC28C40, UCC28C42, and UCC28C43 is the same frequency as the oscillator, and can operate near 100% duty cycle. In the UCC28C41, UCC28C44, and the UCC28C45, the frequency of OUT is one-half that of the oscillator due to an internal T flipflop. This limits the maximum duty cycle to <50%."}, {"pin_number": "7", "pin_name": "VDD", "pin_description": "Power supply pin for the device. This pin should be bypassed with a 0.1 µF capacitor with minimal trace lengths. Additional capacitance may be needed to provide hold up power to the device during startup."}, {"pin_number": "8", "pin_name": "VREF", "pin_description": "5-V reference. For stability, the reference should be bypassed with a 0.1 µF capacitor to ground using the minimal trace length possible."}]}], "datasheet_cn": "未找到", "datasheet_en": "SGLS352B", "family_comparison": "The UCC28C4x family includes versions with different UVLO thresholds (7V, 8.4V, 14.5V) and maximum duty cycles (~100% for UCC28C40/42/43 and <50% for UCC28C41/44/45).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "14.5V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "1A", "max_switch_frequency": "1MHz", "quiescent_current": "2.3mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "1%", "output_reference_voltage": "2.5V"}, "package": [{"type": "Minimizes", "pitch": "1.27", "height": "1.75", "width": "3.98", "length": "0.1", "pin_count": "2842"}]}, {"part_number": "UCC28C45-EP", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电流模式PWM控制器", "part_number_title": "UCC28C4x-EP BICMOS LOW-POWER CURRENT-MODE PWM CONTROLLERS", "features": ["Controlled Baseline", "One Assembly/Test Site, One Fabrication Site", "Extended Temperature Performance of –55°C to 125°C", "Enhanced Diminishing Manufacturing Sources (DMS) Support", "Enhanced Product-Change Notification", "Qualification Pedigree", "Enhanced Replacements for UC2842A Family With Pin-to-Pin Compatibility", "1 MHz Operation", "50 μA Standby Current, 100 μA Maximum", "Low Operating Current of 2.3 mA at 52 kHz", "Fast 35 ns Cycle-by-Cycle Overcurrent Limiting", "±1 A Peak Output Current", "Rail-to-Rail Output Swings With 25 ns Rise and 20 ns Fall Times", "±1% Initial Trimmed 2.5 V Error Amplifier Reference", "Trimmed Oscillator Discharge Current", "New Undervoltage Lockout Versions", "MSOP-8 Package Minimizes Board Space"], "description": "The UCC28C4x family are high performance current mode PWM controllers. They are enhanced BICMOS versions with pin-for-pin compatibility to the industry standard UC284xA family and UC284x family of PWM controllers. In addition, lower startup voltage versions of 7 V are offered as UCC28C40 and UCC28C41. Providing necessary features to control fixed frequency, peak current mode power supplies, this family offers several performance advantages. These devices offer high frequency operation up to 1 MHz with low start up and operating currents, thus minimizing start up loss and low operating power consumption for improved efficiency. The devices also feature a fast current sense to output delay time of 35 ns, and a ±1 A peak output current capability with improved rise and fall times for driving large external MOSFETs directly. The UCC28C4x family is offered in 8-pin package SOIC (D).", "applications": ["Switch Mode Power Supplies", "DC-to-DC Converters", "Board Mount Power Modules"], "ordering_information": [{"part_number": "UCC28C45-EP", "order_device": "UCC28C45MDREP", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-55", "max_operation_temp": "125"}, {"part_number": "UCC28C45-EP", "order_device": "UCC28C45MDREP.A", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-55", "max_operation_temp": "125"}, {"part_number": "UCC28C45-EP", "order_device": "V62/07615-02XE", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-55", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC28C4x-EP", "package_type": "SOIC (D)", "pins": [{"pin_number": "1", "pin_name": "COMP", "pin_description": "This pin provides the output of the error amplifier for compensation. In addition, the COMP pin is frequently used as a control port by utilizing a secondary-side error amplifier to send an error signal across the secondary-primary isolation boundary through an opto-isolator."}, {"pin_number": "2", "pin_name": "FB", "pin_description": "This pin is the inverting input to the error amplifier. The noninverting input to the error amplifier is internally trimmed to 2.5 V ± 1%."}, {"pin_number": "3", "pin_name": "CS", "pin_description": "The current-sense pin is the noninverting input to the PWM comparator. This is compared to a signal proportional to the error amplifier output voltage. A voltage ramp can be applied to this pin to run the device with a voltage mode control configuration."}, {"pin_number": "4", "pin_name": "RT/CT", "pin_description": "Timing resistor and timing capacitor. The timing capacitor should be connected to the device ground using minimal trace length."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Ground return pin for the output driver stage and the logic-level controller section."}, {"pin_number": "6", "pin_name": "OUT", "pin_description": "The output of the on-chip drive stage. OUT is intended to directly drive a MOSFET. The OUT pin in the UCC28C40, UCC28C42, and UCC28C43 is the same frequency as the oscillator, and can operate near 100% duty cycle. In the UCC28C41, UCC28C44, and the UCC28C45, the frequency of OUT is one-half that of the oscillator due to an internal T flipflop. This limits the maximum duty cycle to <50%."}, {"pin_number": "7", "pin_name": "VDD", "pin_description": "Power supply pin for the device. This pin should be bypassed with a 0.1 µF capacitor with minimal trace lengths. Additional capacitance may be needed to provide hold up power to the device during startup."}, {"pin_number": "8", "pin_name": "VREF", "pin_description": "5-V reference. For stability, the reference should be bypassed with a 0.1 µF capacitor to ground using the minimal trace length possible."}]}], "datasheet_cn": "未找到", "datasheet_en": "SGLS352B", "family_comparison": "The UCC28C4x family includes versions with different UVLO thresholds (7V, 8.4V, 14.5V) and maximum duty cycles (~100% for UCC28C40/42/43 and <50% for UCC28C41/44/45).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "8.4V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "1A", "max_switch_frequency": "1MHz", "quiescent_current": "2.3mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "1%", "output_reference_voltage": "2.5V"}, "package": [{"type": "Minimizes", "pitch": "1.27", "height": "1.75", "width": "3.98", "length": "0.1", "pin_count": "2842"}]}]