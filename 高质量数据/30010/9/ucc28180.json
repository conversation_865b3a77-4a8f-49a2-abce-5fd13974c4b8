{"part_number": "UCC28180", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC控制器和稳压器", "category_lv3": "连续导通模式 (CCM) PFC 控制器", "part_number_title": "UCC28180 可编程频率、连续导通模式 (CCM)、升压功率因数校正 (PFC) 控制器", "features": ["8 引脚解决方案 (无需 AC 线路感测)", "宽范围可编程开关频率 (对于基于金属氧化物半导体场效应晶体管 (MOSFET) 和基于绝缘栅双极型晶体管 (IGBT) 的 PFC 控制器为 18kHz 至 250kHz)", "用于降低 iTHD 的经调整电流环路", "电流感测阈值有所降低 (最大限度降低分流电阻功耗)", "平均电流模式控制", "软过流和逐周期峰值电流限制保护", "具有滞后恢复功能的输出过压保护", "可闻噪声最小化电路", "开环检测", "改善输出过压和欠压状态期间的动态响应", "最高占空比为 96% (典型值)", "针对无负载稳压的突发模式", "VCC 欠压锁定 (UVLO)、低附加动态功耗电流 (ICC) 启动 (< 75μA)"], "description": "UCC28180 是一款灵活且易于使用的 8 引脚有源功率因数校正 (PFC) 控制器，该控制器运行在连续导通模式 (CCM) 下，可为交流-直流前端中的升压前置稳压器提供高功率因数、低电流失真和出色的电压稳压。此控制器适用于 100 瓦至几千瓦范围内的通用交流输入系统，开关频率可在 18kHz 至 250kHz 范围内编程，以便轻松支持功率 MOSFET 和 IGBT 开关。集成的1.5A 和 2A (SRC-SNK) 峰值栅极驱动输出在内部钳位为 15.2V（典型值），无需使用缓冲电路即可快速接通、关闭以及轻松管理外部电源开关。\n通过使用平均电流模式控制，在无需输入线路感测的情况下，即可实现输入电流低失真波整形，从而减少了外部组件数量。此外，该控制器的电流感测阈值有所降低，方便使用低值分流电阻来降低功率耗散，这对于高功率系统尤为重要。为了实现低电流失真，此控制器还特有用于消除相关误差的经调整电流环路稳压电路。", "applications": ["100 瓦到几千瓦范围内的通用交流输入、CCM 升压 PFC 转换器", "服务器和台式机电源", "大型家用电器 (空调、冰箱)", "工业电源 (德国标准化学会 (DIN) 电源轨)", "平板 (等离子 (PDP)、液晶 (LCD) 和发光二级管 (LED)) 电视"], "ordering_information": [{"part_number": "UCC28180", "order_device": "UCC28180D", "package_type": "SOIC", "package_drawing_code": "D0008A", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28180", "order_device": "UCC28180DR", "package_type": "SOIC", "package_drawing_code": "D0008A", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"pin_number": "1", "pin_name": "GND", "pin_description": "Ground: device ground reference."}, {"pin_number": "2", "pin_name": "ICOMP", "pin_description": "Current Loop Compensation: Transconductance current amplifier output. A capacitor connected to GND provides compensation and averaging of the current sense signal in the current control loop. The controller is disabled if the voltage on ICOMP is less than 0.2 V, (ICOMPP protection function)."}, {"pin_number": "3", "pin_name": "ISENSE", "pin_description": "Inductor Current Sense: Input for the voltage across the external current sense resistor, which represents the instantaneous current through the PFC boost inductor. This voltage is averaged by the current amplifier to eliminate the effects of ripple and noise. Soft Over Current (SOC) limits the average inductor current. Cycle-by-cycle peak current limit (PCL) immediately shuts off the GATE drive if the peak-limit voltage is exceeded. An internal 2.3-μA current source pulls ISENSE above 0.085 V to shut down PFC operation if this pin becomes open-circuited, (ISOP protection function). Use a 220-Ω resistor between this pin and the current sense resistor to limit inrush-surge currents into this pin."}, {"pin_number": "4", "pin_name": "FREQ", "pin_description": "Switching Frequency Setting: This pin allows the setting of the operating switching frequency by connecting a resistor to ground. The programmable frequency range is from 18 kHz to 250 kHz."}, {"pin_number": "5", "pin_name": "VCOMP", "pin_description": "Voltage Loop Compensation: Transconductance voltage error amplifier output. A resistor-capacitor network connected from this pin to GND provides compensation. VCOMP is held at GND until VCC, and VSENSE exceed their threshold voltages. Once these conditions are satisfied, VCOMP is charged until the VSENSE voltage reaches its nominal regulation level. When Enhanced Dynamic Response (EDR) is engaged, a higher transconductance is applied to VCOMP to reduce the charge or discharge time for faster transient response. Soft Start is programmed by the capacitance on this pin. VCOMP is pulled low when VCC UVLO, OLP/Standby, ICOMPP and ISOP functions are activated."}, {"pin_number": "6", "pin_name": "VSENSE", "pin_description": "Output Voltage Sense: An external resistor-divider network connected from this pin to the PFC output voltage provides feedback sensing for regulation to the internal 5-V reference voltage. A small capacitor from this pin to GND filters high-frequency noise. Standby disables the controller and discharges VCOMP when the voltage at VSENSE drops below the Open-Loop Protection (OLP) threshold of 16.5%VREF (0.82 V). An internal 100-nA current source pulls VSENSE to GND during pin disconnection. Enhanced Dynamic Response (EDR) rapidly returns the output voltage to its normal regulation level when a system line or load step causes VSENSE to rise above 105% or fall below 95% of the reference voltage. Two level Output Over-Voltage Protection (OVP): a 4-kΩ resistor connects VCOMP to ground to rapidly discharge VCOMP when VSENSE exceeds 107% (VOVP_L) of the reference voltage. If VSENSE exceeds 109% (VOVP_H) of the reference voltage, GATE output will be disabled until VSENSE drops below 102% of the reference voltage."}, {"pin_number": "7", "pin_name": "VCC", "pin_description": "Device Supply: External bias supply input. Under-Voltage Lockout (UVLO) disables the controller until VCC exceeds a turn-on threshold of 11.5 V. Operation continues until VCC falls below the turn-off (UVLO) threshold of 9.5 V. A ceramic by-pass capacitor of 0.1 µF minimum value should be connected from VCC to GND as close to the device as possible for high-frequency filtering of the VCC voltage."}, {"pin_number": "8", "pin_name": "GATE", "pin_description": "Gate Drive: Integrated push-pull gate driver for one or more external power MOSFETs. Typical 2.0-A sink and 1.5-A source capability. Output voltage is typically clamped at 15.2 V (typical)."}], "datasheet_cn": "ZHCSBTOD-NOVEMBER 2013-REVISED JULY 2016", "datasheet_en": "SLUSBQ5", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET/IGBT", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "21V", "min_input_voltage": "10.5V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "250kHz", "quiescent_current": "2.4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "-0.4V", "operation_mode": "CCM", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "±2.8%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "width": "3.98", "length": "5.0", "pin_count": "8"}]}