{"part_number": "UCC28061-Q1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "功率因数校正(PFC)控制器", "category_lv3": "转换模式PFC控制器", "part_number_title": "Natural Interleaving™ 具有改进可闻噪声抗扰的转换模式功率因数校正 (PFC) 控制器", "features": ["符合汽车应用要求", "符合 AEC-Q100 标准的下列结果: 器件温度1级: -40℃至+125℃的环境运行温度范围; 器件人体模型 (HBM) 静电放电 (ESD) 分类等级 H2; 器件充电器件模型 (CDM) ESD 分类等级 C3B", "简便相位管理使得符合轻负载高效标准变得更加容易", "双路故障安全过压保护 (OVP) 防止由电压感测故障引起的输出过压情况", "无传感器电流整形简化了电路板设计并提升了效率", "涌入安全电流限制: 涌入期间防止金属氧化物半导体场效应晶体管 (MOSFET) 传导; 消除了输出整流器内的反向恢复事件", "改进的可闻噪声性能", "过压上的软启动", "集成欠压保护", "在传统、单相位持续传导模式 (CCM) 上提升的效率和设计灵活性", "输入滤波器和输出电容器电流撤销: 为了实现更高系统可靠性和更小大容量电容器而减少的电流纹波; 缩小的电磁干扰 (EMI) 滤波器尺寸", "在无需扩展缓冲器电路的情况下可使用低成本二极管", "改进的轻负载效率", "改进的瞬态响应", "完整的系统级保护", "1A源电流 / 1.8A吸收电流栅极驱动器"], "description": "针对对可闻噪声消除有要求的消费类应用，这个解决方案将转换模式的优势--使用低成本组件实现高效率--扩展至比之前的可能值更高的额定功率值。通过使用一个 Natural Interleaving 技术，两个作为主信道运行的信道（也就是说，没有从信道）与同一频率同步。这个方法从内部产生了强匹配、更快速的响应、并确保每个信道都运行在转换模式。完整的系统级保护特有输入欠压保护、输出过压保护、开环路保护、过载保护、软启动、相位故障检测、和热关断功能。附加的故障安全过压保护(OVP) 特性防止到一个中间电压的短路，如果没有检测到此短路的话，有可能导致非常严重的器件故障。", "applications": ["100W至 800W 电源", "电动自行车", "车载应用"], "ordering_information": [{"part_number": "UCC28061-Q1", "order_device": "UCC28061QDRQ1", "package_type": "SOIC", "package_drawing_code": "R-PDSO-G16", "pin_count": 16, "carrier_description": "Ta<PERSON> and Reel", "carrier_quantity": 2500, "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC28061-Q1", "package_type": "SOIC-16", "pin_number": "1", "pin_name": "ZCDB", "pin_description": "零电流检测输入B。当B相电感电流降至零时，此输入应看到一个负向边沿。输入被钳位在0V和3V。信号应通过一个限流电阻耦合。"}, {"product_part_number": "UCC28061-Q1", "package_type": "SOIC-16", "pin_number": "2", "pin_name": "VSENSE", "pin_description": "输出直流电压检测。连接到功率转换器输出的分压器。误差放大器参考电压为6V。可被拉低以禁用输出。"}, {"product_part_number": "UCC28061-Q1", "package_type": "SOIC-16", "pin_number": "3", "pin_name": "TSET", "pin_description": "时序设置。从TSET到AGND连接一个电阻，以设置导通时间与COMP电压的关系以及栅极驱动输出的最小周期。"}, {"product_part_number": "UCC28061-Q1", "package_type": "SOIC-16", "pin_number": "4", "pin_name": "PHB", "pin_description": "B相使能。此引脚开关B相升压转换器。当B相被禁用时，A相的指令导通时间立即加倍。可由外部逻辑驱动以实现自定义相位管理。"}, {"product_part_number": "UCC28061-Q1", "package_type": "SOIC-16", "pin_number": "5", "pin_name": "COMP", "pin_description": "误差放大器输出。这是一个高阻抗电流源。连接电压调节环路补偿元件到此引脚和AGND。栅极驱动输出的导通时间与此引脚电压成正比。"}, {"product_part_number": "UCC28061-Q1", "package_type": "SOIC-16", "pin_number": "6", "pin_name": "AGND", "pin_description": "模拟地。连接模拟信号旁路电容、补偿元件和模拟信号返回到此引脚。"}, {"product_part_number": "UCC28061-Q1", "package_type": "SOIC-16", "pin_number": "7", "pin_name": "VINAC", "pin_description": "输入交流电压检测。连接到整流后输入电源线的分压器，用于实现掉电保护。"}, {"product_part_number": "UCC28061-Q1", "package_type": "SOIC-16", "pin_number": "8", "pin_name": "HVSEN", "pin_description": "高压输出检测。用于故障安全OVP，与VSENSE一起监控输出过压。也可用于使能下游功率转换器。"}, {"product_part_number": "UCC28061-Q1", "package_type": "SOIC-16", "pin_number": "9", "pin_name": "PWMCNTL", "pin_description": "PWM使能逻辑输出。这是一个开漏输出，当HVSEN在正常范围内且ZCDA/ZCDB输入正常切换时（双相模式下），此引脚拉低。"}, {"product_part_number": "UCC28061-Q1", "package_type": "SOIC-16", "pin_number": "10", "pin_name": "CS", "pin_description": "电流检测输入。连接到电流检测电阻和二极管桥的负端。用于逐周期过流保护。"}, {"product_part_number": "UCC28061-Q1", "package_type": "SOIC-16", "pin_number": "11", "pin_name": "GDB", "pin_description": "B相栅极驱动输出。连接到B相功率MOSFET的栅极。"}, {"product_part_number": "UCC28061-Q1", "package_type": "SOIC-16", "pin_number": "12", "pin_name": "VCC", "pin_description": "偏置电源输入。连接到一个14V至21V的受控偏置电源。"}, {"product_part_number": "UCC28061-Q1", "package_type": "SOIC-16", "pin_number": "13", "pin_name": "PGND", "pin_description": "集成电路的功率地。通过单独的短走线连接到AGND，以隔离栅极驱动器噪声。"}, {"product_part_number": "UCC28061-Q1", "package_type": "SOIC-16", "pin_number": "14", "pin_name": "GDA", "pin_description": "A相栅极驱动输出。连接到A相功率MOSFET的栅极。"}, {"product_part_number": "UCC28061-Q1", "package_type": "SOIC-16", "pin_number": "15", "pin_name": "VREF", "pin_description": "电压参考输出。6V直流参考电压，可用于偏置需要小于2mA总供电电流的其他电路。"}, {"product_part_number": "UCC28061-Q1", "package_type": "SOIC-16", "pin_number": "16", "pin_name": "ZCDA", "pin_description": "零电流检测输入A。当A相电感电流降至零时，此输入应看到一个负向边沿。输入被钳位在0V和3V。信号应通过一个限流电阻耦合。"}], "datasheet_cn": "ZHCS841 (2012-03)", "datasheet_en": "SLUSB01", "family_comparison": "UCC28051: PFC controller for low to medium power applications; UCC28019: 8-pin continuous conduction mode (CCM) PFC controller; UCC28060: Natural Interleaving™ Dual-Phase Transition-Mode PFC Controller", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 2, "channel_count": 2, "max_input_voltage": "265V", "min_input_voltage": "85V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "应用决定", "max_switch_frequency": "550kHz", "quiescent_current": "100μA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "-0.2V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Phase Shedding, Cycle Skipping", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Auto Recovery", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "Transition Mode", "output_voltage_accuracy": "±3%", "output_reference_voltage": "6V"}, "package": [{"type": "SOIC", "pin_count": "1", "pitch": "1.27", "height": "1.75", "width": "6.2", "length": "10.0"}]}