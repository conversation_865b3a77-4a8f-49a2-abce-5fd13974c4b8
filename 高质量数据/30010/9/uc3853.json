[{"part_number": "UC1853", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "功率因数校正(PFC)控制器", "category_lv3": "有源功率因数校正控制器", "part_number_title": "High Power Factor Preregulator", "features": ["Complete 8-pin Power Factor Solution", "Reduced External Components", "RMS Line Voltage Compensation", "Precision Multiplier/Squarer/Divider", "Internal 75kHz Synchronizable Oscillator", "Average Current Mode PWM Control", "Overvoltage Protection Comparator", "High Current, Clamped Gate Driver"], "description": "The UC3853 provides simple, yet high performance active power factor correction. Using the same control technique as the UC1854, this 8-pin device exploits a simplified architecture and an internal oscillator to minimize external component count. The UC3853 incorporates a precision multiplier/squarer/divider circuit, voltage and current loop error amplifiers, and a precision voltage reference to implement average current mode control with RMS line voltage compensation. This control technique maintains constant loop gain with changes in input voltage, which minimizes input line current distortion over the worldwide input voltage range. The internal 75kHz oscillator includes an external clock input, allowing synchronization to downstream converters. Additionally, the device features an overvoltage protection comparator, a clamped MOSFET gate driver which self-biases low during undervoltage lockout, and low startup and supply current. These devices are available in 8-pin plastic and ceramic dual in-line (DIP) packages, and 8-lead small outline (SOIC) packages. The UC1853 is specified for operation from −55°C to +125°C, the UC2853 is specified for operation from -25°C to +85°C, and the UC3853 is specified for operation from 0°C to +70°C.", "applications": ["Active Power Factor Correction (PFC)", "Switch-Mode Power Supplies (SMPS)", "Downstream Converters"], "ordering_information": [], "pin_function": [{"product_part_number": "UC1853", "package_type": "DIL-8, SOIC-8", "pins": [{"pin_number": "1", "pin_name": "IAC", "pin_description": "AC Waveform Input. This input provides voltage waveform information to the multiplier. The current loop will try to produce a current waveform with the same shape as the IAC signal. IAC is a low impedance input, nominally at 2V, which accepts a current proportional to the input voltage. Connect a resistor from the rectified input line to IAC which will conduct 500mA at maximum line voltage."}, {"pin_number": "2", "pin_name": "VCC", "pin_description": "Input Supply Voltage. This pin serves two functions. It supplies power to the chip, and an input voltage level signal to the squarer circuit. When this input is connected to a DC voltage proportional to the AC input RMS voltage, the voltage loop gain is reduced by 64/Vcc^2. This configuration maintains constant loop gain. The UC3853 input voltage range extends from 12V to 40V, allowing an AC supply voltage range in excess of 85VAC to 265VAC. Bypass VCC with at least a 0.1µF ceramic capacitor to ensure proper operation."}, {"pin_number": "3", "pin_name": "OUT", "pin_description": "Gate Driver Output. OUT provides high current gate drive for the external power MOSFET. A 15V clamp prevents excessive MOSFET gate-to-source voltage so that the UC3853 can be operated with VCC and high as 40V. A series gate resistor of at least 5 ohms should be used to minimize clamp voltage overshoot. In addition, a Schottky diode such as a 1N5818 connected between OUT and GND may be necessary to prevent parasitic substrate diode conduction."}, {"pin_number": "4", "pin_name": "GND", "pin_description": "Ground. All voltages are measured with respect to GND. The VCC bypass capacitor should be connected to ground as close to the GND pin as possible."}, {"pin_number": "5", "pin_name": "FB", "pin_description": "Voltage Amplifier Inverting Input, Overvoltage Comparator Input, Sync Input. This pin serves three functions. FB accepts a fraction of the power factor corrected output voltage through a voltage divider, and is nominally regulated to 3V. FB voltages 5% greater than nominal will trip the overvoltage comparator, and shut down the output stage until the output voltage drops 5%. The internal oscillator can be synchronized through FB by injecting a 2V clock signal though a capacitor."}, {"pin_number": "6", "pin_name": "VCOMP", "pin_description": "Voltage Loop Error Amplifier Output. The voltage loop error amplifier is a transconductance type operational amplifier. A feedback impedance between VCOMP and FB for loop compensation must be avoided to maintain proper operation of the overvoltage protection comparator. Instead, compensate the voltage loop with an impedance between VCOMP and GND. When VCOMP is below 1.5V, the multiplier output current is zero."}, {"pin_number": "7", "pin_name": "IMO", "pin_description": "Multiplier Output and Current Sense Inverting Input. The output of the multiplier and the inverting input of the current amplifier are connected together at IMO. Avoid bringing this input below –0.5V to prevent the internal protection diode from conducting. The multiplier output is a current, making this a summing node and allowing a differential current error amplifier configuration to reject ground noise. The input resistance at this node should be 3.9k to minimize input bias current induced offset voltage."}, {"pin_number": "8", "pin_name": "ICOMP", "pin_description": "Current Loop Error Amplifier Output. The current loop error amplifier is a conventional operational amplifier with a 150µA current source class A output stage. Compensate the current loop by placing an impedance between ICOMP and IMO. This output can swing above the oscillator peak voltage, allowing zero duty cycle when necessary."}]}], "datasheet_cn": "未找到", "datasheet_en": "UC1853.pdf", "family_comparison": "The three part numbers are differentiated by their operating temperature ranges: UC1853 (-55°C to +125°C), UC2853 (-25°C to +85°C), and UC3853 (0°C to +70°C).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "40V", "min_input_voltage": "12V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "100kHz", "quiescent_current": "10mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "未找到", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "±3.33%", "output_reference_voltage": "3V"}, "package": [{"type": "DIP", "width": "1.5", "height": "55", "length": "75", "pitch": "55", "pin_count": "8"}]}, {"part_number": "UC2853", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "功率因数校正(PFC)控制器", "category_lv3": "有源功率因数校正控制器", "part_number_title": "High Power Factor Preregulator", "features": ["Complete 8-pin Power Factor Solution", "Reduced External Components", "RMS Line Voltage Compensation", "Precision Multiplier/Squarer/Divider", "Internal 75kHz Synchronizable Oscillator", "Average Current Mode PWM Control", "Overvoltage Protection Comparator", "High Current, Clamped Gate Driver"], "description": "The UC3853 provides simple, yet high performance active power factor correction. Using the same control technique as the UC1854, this 8-pin device exploits a simplified architecture and an internal oscillator to minimize external component count. The UC3853 incorporates a precision multiplier/squarer/divider circuit, voltage and current loop error amplifiers, and a precision voltage reference to implement average current mode control with RMS line voltage compensation. This control technique maintains constant loop gain with changes in input voltage, which minimizes input line current distortion over the worldwide input voltage range. The internal 75kHz oscillator includes an external clock input, allowing synchronization to downstream converters. Additionally, the device features an overvoltage protection comparator, a clamped MOSFET gate driver which self-biases low during undervoltage lockout, and low startup and supply current. These devices are available in 8-pin plastic and ceramic dual in-line (DIP) packages, and 8-lead small outline (SOIC) packages. The UC1853 is specified for operation from −55°C to +125°C, the UC2853 is specified for operation from -25°C to +85°C, and the UC3853 is specified for operation from 0°C to +70°C.", "applications": ["Active Power Factor Correction (PFC)", "Switch-Mode Power Supplies (SMPS)", "Downstream Converters"], "ordering_information": [{"part_number": "UC2853", "order_device": "UC2853D", "package_type": "SOIC", "package_drawing_code": "D", "carrier_description": "TUBE", "min_operation_temp": "-25", "max_operation_temp": "85"}, {"part_number": "UC2853", "order_device": "UC2853D.A", "package_type": "SOIC", "package_drawing_code": "D", "carrier_description": "TUBE", "min_operation_temp": "-25", "max_operation_temp": "85"}, {"part_number": "UC2853", "order_device": "UC2853DTR", "package_type": "SOIC", "package_drawing_code": "D", "carrier_description": "LARGE T&R", "min_operation_temp": "-25", "max_operation_temp": "85"}, {"part_number": "UC2853", "order_device": "UC2853DTR.A", "package_type": "SOIC", "package_drawing_code": "D", "carrier_description": "LARGE T&R", "min_operation_temp": "-25", "max_operation_temp": "85"}, {"part_number": "UC2853", "order_device": "UC2853N", "package_type": "PDIP", "package_drawing_code": "P", "carrier_description": "TUBE", "min_operation_temp": "-25", "max_operation_temp": "85"}, {"part_number": "UC2853", "order_device": "UC2853N.A", "package_type": "PDIP", "package_drawing_code": "P", "carrier_description": "TUBE", "min_operation_temp": "-25", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "UC2853", "package_type": "DIL-8, SOIC-8", "pins": [{"pin_number": "1", "pin_name": "IAC", "pin_description": "AC Waveform Input. This input provides voltage waveform information to the multiplier. The current loop will try to produce a current waveform with the same shape as the IAC signal. IAC is a low impedance input, nominally at 2V, which accepts a current proportional to the input voltage. Connect a resistor from the rectified input line to IAC which will conduct 500mA at maximum line voltage."}, {"pin_number": "2", "pin_name": "VCC", "pin_description": "Input Supply Voltage. This pin serves two functions. It supplies power to the chip, and an input voltage level signal to the squarer circuit. When this input is connected to a DC voltage proportional to the AC input RMS voltage, the voltage loop gain is reduced by 64/Vcc^2. This configuration maintains constant loop gain. The UC3853 input voltage range extends from 12V to 40V, allowing an AC supply voltage range in excess of 85VAC to 265VAC. Bypass VCC with at least a 0.1µF ceramic capacitor to ensure proper operation."}, {"pin_number": "3", "pin_name": "OUT", "pin_description": "Gate Driver Output. OUT provides high current gate drive for the external power MOSFET. A 15V clamp prevents excessive MOSFET gate-to-source voltage so that the UC3853 can be operated with VCC and high as 40V. A series gate resistor of at least 5 ohms should be used to minimize clamp voltage overshoot. In addition, a Schottky diode such as a 1N5818 connected between OUT and GND may be necessary to prevent parasitic substrate diode conduction."}, {"pin_number": "4", "pin_name": "GND", "pin_description": "Ground. All voltages are measured with respect to GND. The VCC bypass capacitor should be connected to ground as close to the GND pin as possible."}, {"pin_number": "5", "pin_name": "FB", "pin_description": "Voltage Amplifier Inverting Input, Overvoltage Comparator Input, Sync Input. This pin serves three functions. FB accepts a fraction of the power factor corrected output voltage through a voltage divider, and is nominally regulated to 3V. FB voltages 5% greater than nominal will trip the overvoltage comparator, and shut down the output stage until the output voltage drops 5%. The internal oscillator can be synchronized through FB by injecting a 2V clock signal though a capacitor."}, {"pin_number": "6", "pin_name": "VCOMP", "pin_description": "Voltage Loop Error Amplifier Output. The voltage loop error amplifier is a transconductance type operational amplifier. A feedback impedance between VCOMP and FB for loop compensation must be avoided to maintain proper operation of the overvoltage protection comparator. Instead, compensate the voltage loop with an impedance between VCOMP and GND. When VCOMP is below 1.5V, the multiplier output current is zero."}, {"pin_number": "7", "pin_name": "IMO", "pin_description": "Multiplier Output and Current Sense Inverting Input. The output of the multiplier and the inverting input of the current amplifier are connected together at IMO. Avoid bringing this input below –0.5V to prevent the internal protection diode from conducting. The multiplier output is a current, making this a summing node and allowing a differential current error amplifier configuration to reject ground noise. The input resistance at this node should be 3.9k to minimize input bias current induced offset voltage."}, {"pin_number": "8", "pin_name": "ICOMP", "pin_description": "Current Loop Error Amplifier Output. The current loop error amplifier is a conventional operational amplifier with a 150µA current source class A output stage. Compensate the current loop by placing an impedance between ICOMP and IMO. This output can swing above the oscillator peak voltage, allowing zero duty cycle when necessary."}]}], "datasheet_cn": "未找到", "datasheet_en": "UC1853.pdf", "family_comparison": "The three part numbers are differentiated by their operating temperature ranges: UC1853 (-55°C to +125°C), UC2853 (-25°C to +85°C), and UC3853 (0°C to +70°C).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "40V", "min_input_voltage": "12V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "100kHz", "quiescent_current": "10mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "未找到", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "±3.33%", "output_reference_voltage": "3V"}, "package": [{"type": "DIP", "width": "1.5", "height": "55", "length": "75", "pitch": "55", "pin_count": "8"}]}, {"part_number": "UC3853", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "功率因数校正(PFC)控制器", "category_lv3": "有源功率因数校正控制器", "part_number_title": "High Power Factor Preregulator", "features": ["Complete 8-pin Power Factor Solution", "Reduced External Components", "RMS Line Voltage Compensation", "Precision Multiplier/Squarer/Divider", "Internal 75kHz Synchronizable Oscillator", "Average Current Mode PWM Control", "Overvoltage Protection Comparator", "High Current, Clamped Gate Driver"], "description": "The UC3853 provides simple, yet high performance active power factor correction. Using the same control technique as the UC1854, this 8-pin device exploits a simplified architecture and an internal oscillator to minimize external component count. The UC3853 incorporates a precision multiplier/squarer/divider circuit, voltage and current loop error amplifiers, and a precision voltage reference to implement average current mode control with RMS line voltage compensation. This control technique maintains constant loop gain with changes in input voltage, which minimizes input line current distortion over the worldwide input voltage range. The internal 75kHz oscillator includes an external clock input, allowing synchronization to downstream converters. Additionally, the device features an overvoltage protection comparator, a clamped MOSFET gate driver which self-biases low during undervoltage lockout, and low startup and supply current. These devices are available in 8-pin plastic and ceramic dual in-line (DIP) packages, and 8-lead small outline (SOIC) packages. The UC1853 is specified for operation from −55°C to +125°C, the UC2853 is specified for operation from -25°C to +85°C, and the UC3853 is specified for operation from 0°C to +70°C.", "applications": ["Active Power Factor Correction (PFC)", "Switch-Mode Power Supplies (SMPS)", "Downstream Converters"], "ordering_information": [{"part_number": "UC3853", "order_device": "UC3853D", "package_type": "SOIC", "package_drawing_code": "D", "carrier_description": "TUBE", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3853", "order_device": "UC3853D.A", "package_type": "SOIC", "package_drawing_code": "D", "carrier_description": "TUBE", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3853", "order_device": "UC3853DTR", "package_type": "SOIC", "package_drawing_code": "D", "carrier_description": "LARGE T&R", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3853", "order_device": "UC3853DTR.A", "package_type": "SOIC", "package_drawing_code": "D", "carrier_description": "LARGE T&R", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3853", "order_device": "UC3853N", "package_type": "PDIP", "package_drawing_code": "P", "carrier_description": "TUBE", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3853", "order_device": "UC3853N.A", "package_type": "PDIP", "package_drawing_code": "P", "carrier_description": "TUBE", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3853", "order_device": "UC3853NG4", "package_type": "PDIP", "package_drawing_code": "P", "carrier_description": "TUBE", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "UC3853", "package_type": "DIL-8, SOIC-8", "pins": [{"pin_number": "1", "pin_name": "IAC", "pin_description": "AC Waveform Input. This input provides voltage waveform information to the multiplier. The current loop will try to produce a current waveform with the same shape as the IAC signal. IAC is a low impedance input, nominally at 2V, which accepts a current proportional to the input voltage. Connect a resistor from the rectified input line to IAC which will conduct 500mA at maximum line voltage."}, {"pin_number": "2", "pin_name": "VCC", "pin_description": "Input Supply Voltage. This pin serves two functions. It supplies power to the chip, and an input voltage level signal to the squarer circuit. When this input is connected to a DC voltage proportional to the AC input RMS voltage, the voltage loop gain is reduced by 64/Vcc^2. This configuration maintains constant loop gain. The UC3853 input voltage range extends from 12V to 40V, allowing an AC supply voltage range in excess of 85VAC to 265VAC. Bypass VCC with at least a 0.1µF ceramic capacitor to ensure proper operation."}, {"pin_number": "3", "pin_name": "OUT", "pin_description": "Gate Driver Output. OUT provides high current gate drive for the external power MOSFET. A 15V clamp prevents excessive MOSFET gate-to-source voltage so that the UC3853 can be operated with VCC and high as 40V. A series gate resistor of at least 5 ohms should be used to minimize clamp voltage overshoot. In addition, a Schottky diode such as a 1N5818 connected between OUT and GND may be necessary to prevent parasitic substrate diode conduction."}, {"pin_number": "4", "pin_name": "GND", "pin_description": "Ground. All voltages are measured with respect to GND. The VCC bypass capacitor should be connected to ground as close to the GND pin as possible."}, {"pin_number": "5", "pin_name": "FB", "pin_description": "Voltage Amplifier Inverting Input, Overvoltage Comparator Input, Sync Input. This pin serves three functions. FB accepts a fraction of the power factor corrected output voltage through a voltage divider, and is nominally regulated to 3V. FB voltages 5% greater than nominal will trip the overvoltage comparator, and shut down the output stage until the output voltage drops 5%. The internal oscillator can be synchronized through FB by injecting a 2V clock signal though a capacitor."}, {"pin_number": "6", "pin_name": "VCOMP", "pin_description": "Voltage Loop Error Amplifier Output. The voltage loop error amplifier is a transconductance type operational amplifier. A feedback impedance between VCOMP and FB for loop compensation must be avoided to maintain proper operation of the overvoltage protection comparator. Instead, compensate the voltage loop with an impedance between VCOMP and GND. When VCOMP is below 1.5V, the multiplier output current is zero."}, {"pin_number": "7", "pin_name": "IMO", "pin_description": "Multiplier Output and Current Sense Inverting Input. The output of the multiplier and the inverting input of the current amplifier are connected together at IMO. Avoid bringing this input below –0.5V to prevent the internal protection diode from conducting. The multiplier output is a current, making this a summing node and allowing a differential current error amplifier configuration to reject ground noise. The input resistance at this node should be 3.9k to minimize input bias current induced offset voltage."}, {"pin_number": "8", "pin_name": "ICOMP", "pin_description": "Current Loop Error Amplifier Output. The current loop error amplifier is a conventional operational amplifier with a 150µA current source class A output stage. Compensate the current loop by placing an impedance between ICOMP and IMO. This output can swing above the oscillator peak voltage, allowing zero duty cycle when necessary."}]}], "datasheet_cn": "未找到", "datasheet_en": "UC1853.pdf", "family_comparison": "The three part numbers are differentiated by their operating temperature ranges: UC1853 (-55°C to +125°C), UC2853 (-25°C to +85°C), and UC3853 (0°C to +70°C).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "40V", "min_input_voltage": "12V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "100kHz", "quiescent_current": "10mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "未找到", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "±2.5%", "output_reference_voltage": "3V"}, "package": [{"type": "DIP", "width": "1.5", "height": "55", "length": "75", "pitch": "55", "pin_count": "8"}]}]