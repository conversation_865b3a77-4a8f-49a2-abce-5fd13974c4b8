[{"part_number": "UCC28810", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "PFC控制器", "part_number_title": "LED LIGHTING POWER CONTROLLER", "features": ["Transition Mode Controller for Low Implementation Cost of AC Input LED Lighting Applications", "Implements Single Stage Power Factor Corrected LED Driver", "Enhanced Transient Response With Slew-Rate Comparator", "Interfaces with Traditional Wall Dimmers", "Accurate Internal VREF for Tight Output Regulation", "Two UVLO Options", "Overvoltage Protection (OVP), Open-Feedback Protection and Enable Circuits", "±750-mA Peak Gate Drive Current", "Low Start-Up and Operating Currents", "Lead (Pb)-Free Packages"], "description": "The UCC28810 and UCC28811 are general lighting power controllers for low to medium power lumens applications requiring power factor correction and EMC compliance. It is designed for controlling a flyback, buck or boost converter operating in critical conduction mode. The UCC28810 is suitable for applications such as commercial or residential retrofit luminaires where there is no down-stream PWM conversion and the advantages of smaller VDD capacitor and improved transient response can be realized.", "applications": ["AC Input General Lighting Applications Using HB LEDs", "Industrial, Commercial and Residential Lighting Fixtures", "Outdoor Lighting: Street, Roadway, Parking, Construction and Ornamental LED Lighting Fixtures"], "ordering_information": [{"part_number": "UCC28810", "order_device": "UCC28810D", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "105"}, {"part_number": "UCC28810", "order_device": "UCC28810DR", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "105"}], "pin_function": [{"product_part_number": "UCC28810", "package_type": "SOIC-8", "pins": [{"pin_number": "1", "pin_name": "VSENSE", "pin_description": "This pin is the inverting input to the transconductance amplifier, with a nominal value of 2.5 V, and is also the input to the OVP comparator. Pulling this pin below the ENABLE threshold turns off the output switching, providing the ability to externally disable the converter. This function also provides feedback fault protection, ensuring no runaway if the feedback path is open. When using the internal error amplifier, this pin senses the output voltage through a voltage divider."}, {"pin_number": "2", "pin_name": "EAOUT", "pin_description": "Output of the transconductance error amplifier. The output current capability of this pin is 10 µA under normal conditions, but increases to 1 mA when the voltage on VSENSE rises above 2.5 V. The EAOUT voltage is one of the inputs to the current reference generator, with a dynamic input range of 2.5 V to 4.0 V. During zero energy or overvoltage conditions, this pin goes below 2.5 V, nominal. When it goes below 2.3 V, the zero energy detect comparator is activated which prevents the gate drive from switching. Loop compensation components are connected between this pin and ground, or can be connected directly to the collector of the opto coupler in isolated applications."}, {"pin_number": "3", "pin_name": "VINS", "pin_description": "This pin senses the instantaneous regulator input voltage through an external voltage divider. The VINS voltage acts as one of the inputs to the current reference generator. The recommended operating range is 0 V to 3.8 V at high line."}, {"pin_number": "4", "pin_name": "ISENSE", "pin_description": "This pin senses the instantaneous switch current in the external switch and uses this signal as the internal ramp for the current sense comparator. A small internal noise filter is provided. If additional filtering is needed, an external R-C filter may be added to further suppress noise spikes. An internal clamp on the current reference generator output terminates the switching cycle if VISENSE exceeds 1.7 V. An internal 75-mV offset is added to ISENSE signal to limit the zero crossing distortion."}, {"pin_number": "5", "pin_name": "TZE", "pin_description": "This pin is the input for the transformer zero energy detect comparator. A bias winding can be used to sense the transformer zero energy. The transition is detected when the inductor current falls to zero and the TZE input goes low. Internal active clamps are provided to prevent TZE from going below ground or rising too high. If zero energy is not detected within 400 µs, a restart timer sets the latch and the gate drive high."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "The device reference ground. All bypassing elements are connected to the GND pin with the shortest traces possible."}, {"pin_number": "7", "pin_name": "GDRV", "pin_description": "The gate drive output driving the flyback, buck, or boost switch. This output is capable of delivering up to 750-mA peak currents during turn-on and turn-off. An external gate drive resistor may be needed to limit the peak current depending upon the VDD voltage being used. Below the UVLO threshold, the output is held low."}, {"pin_number": "8", "pin_name": "VDD", "pin_description": "The supply voltage for the device. This pin must be bypassed with a high-frequency capacitor (not less than 0.1 µF) and tied directly to GND with the shortest traces possible. The UCC28810 has a wide UVLO hysteresis, typically 6.3 V, which allows use of a lower value holdup capacitor on VDD, resulting in faster start up."}]}], "datasheet_cn": "未找到", "datasheet_en": "slus865.pdf", "family_comparison": "There are two key parametric differences between UCC28810 and UCC28811: the UVLO turn-on threshold and the gm amplifier source current. The UVLO turn-on threshold of the UCC28810 is 15.8V and for the UCC28811 it is 12.5V. The gm amplifier source current for UCC28810 is typically 1.3mA, and for the UCC28811 it is 300uA.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "20V", "min_input_voltage": "15.8V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "未找到", "max_switch_frequency": "未找到", "quiescent_current": "2µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.7V", "operation_mode": "Transition Mode", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Pulse Skipping", "power_good_indicator": "No", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "Peak Current Mode", "output_voltage_accuracy": "±2%", "output_reference_voltage": "2.5V", "uvlo_hysteresis": "6.3V"}, "package": [{"type": "SOIC", "pin_count": "750", "pitch": "1.27", "height": "1.75", "width": "3.98", "length": "5"}]}, {"part_number": "UCC28811", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "PFC控制器", "part_number_title": "LED LIGHTING POWER CONTROLLER", "features": ["Transition Mode Controller for Low Implementation Cost of AC Input LED Lighting Applications", "Implements Single Stage Power Factor Corrected LED Driver", "Enhanced Transient Response With Slew-Rate Comparator", "Interfaces with Traditional Wall Dimmers", "Accurate Internal VREF for Tight Output Regulation", "Two UVLO Options", "Overvoltage Protection (OVP), Open-Feedback Protection and Enable Circuits", "±750-mA Peak Gate Drive Current", "Low Start-Up and Operating Currents", "Lead (Pb)-Free Packages"], "description": "The UCC28810 and UCC28811 are general lighting power controllers for low to medium power lumens applications requiring power factor correction and EMC compliance. It is designed for controlling a flyback, buck or boost converter operating in critical conduction mode. The UCC28811 is suitable for applications such as street lights and larger area luminaires where a two-stage power conversion is needed.", "applications": ["AC Input General Lighting Applications Using HB LEDs", "Industrial, Commercial and Residential Lighting Fixtures", "Outdoor Lighting: Street, Roadway, Parking, Construction and Ornamental LED Lighting Fixtures"], "ordering_information": [{"part_number": "UCC28811", "order_device": "UCC28811D", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "105"}, {"part_number": "UCC28811", "order_device": "UCC28811DR", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "105"}], "pin_function": [{"product_part_number": "UCC28811", "package_type": "SOIC-8", "pins": [{"pin_number": "1", "pin_name": "VSENSE", "pin_description": "This pin is the inverting input to the transconductance amplifier, with a nominal value of 2.5 V, and is also the input to the OVP comparator. Pulling this pin below the ENABLE threshold turns off the output switching, providing the ability to externally disable the converter. This function also provides feedback fault protection, ensuring no runaway if the feedback path is open. When using the internal error amplifier, this pin senses the output voltage through a voltage divider."}, {"pin_number": "2", "pin_name": "EAOUT", "pin_description": "Output of the transconductance error amplifier. The output current capability of this pin is 10 µA under normal conditions, but increases to 1 mA when the voltage on VSENSE rises above 2.5 V. The EAOUT voltage is one of the inputs to the current reference generator, with a dynamic input range of 2.5 V to 4.0 V. During zero energy or overvoltage conditions, this pin goes below 2.5 V, nominal. When it goes below 2.3 V, the zero energy detect comparator is activated which prevents the gate drive from switching. Loop compensation components are connected between this pin and ground, or can be connected directly to the collector of the opto coupler in isolated applications."}, {"pin_number": "3", "pin_name": "VINS", "pin_description": "This pin senses the instantaneous regulator input voltage through an external voltage divider. The VINS voltage acts as one of the inputs to the current reference generator. The recommended operating range is 0 V to 3.8 V at high line."}, {"pin_number": "4", "pin_name": "ISENSE", "pin_description": "This pin senses the instantaneous switch current in the external switch and uses this signal as the internal ramp for the current sense comparator. A small internal noise filter is provided. If additional filtering is needed, an external R-C filter may be added to further suppress noise spikes. An internal clamp on the current reference generator output terminates the switching cycle if VISENSE exceeds 1.7 V. An internal 75-mV offset is added to ISENSE signal to limit the zero crossing distortion."}, {"pin_number": "5", "pin_name": "TZE", "pin_description": "This pin is the input for the transformer zero energy detect comparator. A bias winding can be used to sense the transformer zero energy. The transition is detected when the inductor current falls to zero and the TZE input goes low. Internal active clamps are provided to prevent TZE from going below ground or rising too high. If zero energy is not detected within 400 µs, a restart timer sets the latch and the gate drive high."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "The device reference ground. All bypassing elements are connected to the GND pin with the shortest traces possible."}, {"pin_number": "7", "pin_name": "GDRV", "pin_description": "The gate drive output driving the flyback, buck, or boost switch. This output is capable of delivering up to 750-mA peak currents during turn-on and turn-off. An external gate drive resistor may be needed to limit the peak current depending upon the VDD voltage being used. Below the UVLO threshold, the output is held low."}, {"pin_number": "8", "pin_name": "VDD", "pin_description": "The supply voltage for the device. This pin must be bypassed with a high-frequency capacitor (not less than 0.1 µF) and tied directly to GND with the shortest traces possible. The UCC28811 has a narrow UVLO hysteresis, typically 2.8 V, and a typical turn-on threshold of 12.5 V for applications where the device needs to be controlled by a downstream PWM controller. This narrower UVLO hysteresis requires a larger value holdup capacitor."}]}], "datasheet_cn": "未找到", "datasheet_en": "slus865.pdf", "family_comparison": "There are two key parametric differences between UCC28810 and UCC28811: the UVLO turn-on threshold and the gm amplifier source current. The UVLO turn-on threshold of the UCC28810 is 15.8V and for the UCC28811 it is 12.5V. The gm amplifier source current for UCC28810 is typically 1.3mA, and for the UCC28811 it is 300uA.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "20V", "min_input_voltage": "12.5V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "未找到", "max_switch_frequency": "未找到", "quiescent_current": "2µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.7V", "operation_mode": "Transition Mode", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Pulse Skipping", "power_good_indicator": "No", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "Peak Current Mode", "output_voltage_accuracy": "±2%", "output_reference_voltage": "2.5V", "uvlo_hysteresis": "2.8V"}, "package": [{"type": "SOIC", "pin_count": "750", "pitch": "1.27", "height": "1.75", "width": "3.98", "length": "5"}]}]