[{"part_number": "UCC2817-EP", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "功率因数校正(PFC)控制器", "part_number_title": "BICMOS POWER-FACTOR PREREGULATOR", "features": ["Controls Boost Preregulator to Near-Unity Power Factor", "Limits Line Distortion", "World-Wide Line Operation", "Overvoltage Protection", "Accurate Power Limiting", "Average Current Mode Control", "Improved Noise Immunity", "Improved Feed-Forward Line Regulation", "Leading Edge Modulation", "150-μA Typical Start-Up Current", "Low-Power BiCMOS Operation", "12-V to 17-V Operation", "Frequency Range 6 kHz to 220 kHz"], "description": "The UCC2817 and UCC2818 provides all the functions necessary for active power-factor-corrected preregulators. The controller achieves near-unity power factor by shaping the ac-input line current waveform to correspond to that of the ac input line voltage. Average current mode control maintains stable, low-distortion sinusoidal line current. Designed with TI's BiCMOS process, the UCC2817 and UCC2818 offers new features, such as lower start-up current, lower power dissipation, overvoltage protection, a shunt UVLO detect circuitry, a leading-edge modulation technique to reduce ripple current in the bulk capacitor, and an improved, low-offset (±2-mV) current amplifier to reduce distortion at light load conditions. The UCC2817 offers an on-chip shunt regulator with low start-up current suitable for applications utilizing a bootstrap supply. The UCC2818 is intended for applications with a fixed supply (Vcc).", "applications": ["DEFENSE", "AEROSPACE", "MEDICAL APPLICATIONS"], "ordering_information": [], "pin_function": [{"product_part_number": "UCC2817-EP", "package_type": "SOIC-16", "pin_number": "1", "pin_name": "GND", "pin_description": "Ground. All voltages measured with respect to ground. Vcc and REF should be bypassed directly to GND with a 0.1-µF or larger ceramic capacitor."}, {"product_part_number": "UCC2817-EP", "package_type": "SOIC-16", "pin_number": "2", "pin_name": "PKLMT", "pin_description": "PFC peak current limit. The threshold for peak limit is 0 V. Use a resistor divider from the negative side of the current sense resistor to VREF to level shift this signal to a voltage level defined by the value of the sense resistor and the peak current limit. Peak current limit is reached when PKLMT voltage falls below 0 V."}, {"product_part_number": "UCC2817-EP", "package_type": "SOIC-16", "pin_number": "3", "pin_name": "CAOUT", "pin_description": "Current amplifier output. This is the output of a wide bandwidth operational amplifier that senses line current and commands the PFC pulse-width modulator (PWM) to force the correct duty cycle. Compensation components are placed between CAOUT and MOUT."}, {"product_part_number": "UCC2817-EP", "package_type": "SOIC-16", "pin_number": "4", "pin_name": "CAI", "pin_description": "Current amplifier noninverting input. Place a resistor between this pin and the GND side of current sense resistor. This input and the inverting input (MOUT) remain functional down to and below GND."}, {"product_part_number": "UCC2817-EP", "package_type": "SOIC-16", "pin_number": "5", "pin_name": "MOUT", "pin_description": "Multiplier output and current amplifier inverting input. The output of the analog multiplier and the inverting input of the current amplifier are connected together at MOUT. As the multiplier output is a current, this is a high-impedance input so the amplifier can be configured as a differential amplifier."}, {"product_part_number": "UCC2817-EP", "package_type": "SOIC-16", "pin_number": "6", "pin_name": "IAC", "pin_description": "Current proportional to input voltage. This input to the analog multiplier is a current proportional to instantaneous line voltage. The multiplier is tailored for very low distortion from this current input (IIAC) to multiplier output. The recommended maximum IIAC is 500 μA."}, {"product_part_number": "UCC2817-EP", "package_type": "SOIC-16", "pin_number": "7", "pin_name": "VAOUT", "pin_description": "Voltage amplifier output. This is the output of the operational amplifier that regulates output voltage. The voltage amplifier output is internally limited to approximately 5.5 V to prevent overshoot."}, {"product_part_number": "UCC2817-EP", "package_type": "SOIC-16", "pin_number": "8", "pin_name": "VFF", "pin_description": "Feed-forward voltage. The RMS voltage signal generated at this pin by mirroring 1/2 of the IIAC into a single pole external filter. At low line, the VFF voltage should be 1.4 V."}, {"product_part_number": "UCC2817-EP", "package_type": "SOIC-16", "pin_number": "9", "pin_name": "VREF", "pin_description": "Voltage reference output. VREF is the output of an accurate 7.5-V voltage reference. This output is capable of delivering 20 mA to peripheral circuitry and is internally short-circuit current limited."}, {"product_part_number": "UCC2817-EP", "package_type": "SOIC-16", "pin_number": "10", "pin_name": "OVP/EN", "pin_description": "Over-voltage/enable. A window comparator input that disables the output driver if the boost output voltage is a programmed level above the nominal, or disables both the PFC output driver and resets SS if pulled below 1.9 V (typ)."}, {"product_part_number": "UCC2817-EP", "package_type": "SOIC-16", "pin_number": "11", "pin_name": "VSENSE", "pin_description": "Voltage amplifier inverting input. This is normally connected to a compensation network and to the boost converter output through a divider network."}, {"product_part_number": "UCC2817-EP", "package_type": "SOIC-16", "pin_number": "12", "pin_name": "RT", "pin_description": "Oscillator charging current. A resistor from RT to GND is used to program oscillator charging current. A resistor between 10 kΩ and 100 kΩ is recommended. Nominal voltage on this pin is 3 V."}, {"product_part_number": "UCC2817-EP", "package_type": "SOIC-16", "pin_number": "13", "pin_name": "SS", "pin_description": "Soft-start. VSS is discharged for Vvcc low conditions. When enabled, SS charges an external capacitor with a current source. This voltage is used as the voltage error signal during start-up, enabling the PWM duty cycle to increase slowly."}, {"product_part_number": "UCC2817-EP", "package_type": "SOIC-16", "pin_number": "14", "pin_name": "CT", "pin_description": "Oscillator timing capacitor. A capacitor from CT to GND sets the PWM oscillator frequency."}, {"product_part_number": "UCC2817-EP", "package_type": "SOIC-16", "pin_number": "15", "pin_name": "VCC", "pin_description": "Positive supply voltage. Connect to a stable source of at least 20 mA between 10 V and 17 V for normal operation."}, {"product_part_number": "UCC2817-EP", "package_type": "SOIC-16", "pin_number": "16", "pin_name": "DRVOUT", "pin_description": "Gate drive. The output drive for the boost switch is a totem-pole MOSFET gate driver on DRVOUT."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS716", "family_comparison": "UCCx817 vs UCCx818: UCCx817具有更高的UVLO开启电压(16V)和更宽的迟滞(6.3V)，适用于自举电路供电；UCCx818具有标准的UVLO开启电压(10.2V)和较窄的迟滞(0.5V)，适用于12V辅助电源供电。\nUCC281x vs UCC381x: UCC281x系列工作温度范围为-40°C至85°C (工业级)；UCC381x系列工作温度范围为0°C至70°C (商业级)。", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "16V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "220kHz", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": false, "integrated_ldo": true, "dynamic_voltage_setting": false, "pass_through_mode": false, "load_disconnect": false, "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "±2%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "10", "pitch": "450", "height": "40", "length": "40", "width": "40"}]}, {"part_number": "UCC2818-EP", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "功率因数校正(PFC)控制器", "part_number_title": "BICMOS POWER-FACTOR PREREGULATOR", "features": ["Controls Boost Preregulator to Near-Unity Power Factor", "Limits Line Distortion", "World-Wide Line Operation", "Overvoltage Protection", "Accurate Power Limiting", "Average Current Mode Control", "Improved Noise Immunity", "Improved Feed-Forward Line Regulation", "Leading Edge Modulation", "150-μA Typical Start-Up Current", "Low-Power BiCMOS Operation", "12-V to 17-V Operation", "Frequency Range 6 kHz to 220 kHz"], "description": "The UCC2817 and UCC2818 provides all the functions necessary for active power-factor-corrected preregulators. The controller achieves near-unity power factor by shaping the ac-input line current waveform to correspond to that of the ac input line voltage. Average current mode control maintains stable, low-distortion sinusoidal line current. Designed with TI's BiCMOS process, the UCC2817 and UCC2818 offers new features, such as lower start-up current, lower power dissipation, overvoltage protection, a shunt UVLO detect circuitry, a leading-edge modulation technique to reduce ripple current in the bulk capacitor, and an improved, low-offset (±2-mV) current amplifier to reduce distortion at light load conditions. The UCC2817 offers an on-chip shunt regulator with low start-up current suitable for applications utilizing a bootstrap supply. The UCC2818 is intended for applications with a fixed supply (Vcc).", "applications": ["DEFENSE", "AEROSPACE", "MEDICAL APPLICATIONS"], "ordering_information": [{"part_number": "UCC2818-EP", "order_device": "UCC2818MDREP", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "min_operation_temp": "-55", "max_operation_temp": "125", "carrier_description": "Tape & Reel"}, {"part_number": "UCC2818-EP", "order_device": "V62/09617-01XE", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "min_operation_temp": "-55", "max_operation_temp": "125", "carrier_description": "Tape & Reel"}], "pin_function": [{"product_part_number": "UCC2818-EP", "package_type": "SOIC-16", "pin_number": "1", "pin_name": "GND", "pin_description": "Ground. All voltages measured with respect to ground. Vcc and REF should be bypassed directly to GND with a 0.1-µF or larger ceramic capacitor."}, {"product_part_number": "UCC2818-EP", "package_type": "SOIC-16", "pin_number": "2", "pin_name": "PKLMT", "pin_description": "PFC peak current limit. The threshold for peak limit is 0 V. Use a resistor divider from the negative side of the current sense resistor to VREF to level shift this signal to a voltage level defined by the value of the sense resistor and the peak current limit. Peak current limit is reached when PKLMT voltage falls below 0 V."}, {"product_part_number": "UCC2818-EP", "package_type": "SOIC-16", "pin_number": "3", "pin_name": "CAOUT", "pin_description": "Current amplifier output. This is the output of a wide bandwidth operational amplifier that senses line current and commands the PFC pulse-width modulator (PWM) to force the correct duty cycle. Compensation components are placed between CAOUT and MOUT."}, {"product_part_number": "UCC2818-EP", "package_type": "SOIC-16", "pin_number": "4", "pin_name": "CAI", "pin_description": "Current amplifier noninverting input. Place a resistor between this pin and the GND side of current sense resistor. This input and the inverting input (MOUT) remain functional down to and below GND."}, {"product_part_number": "UCC2818-EP", "package_type": "SOIC-16", "pin_number": "5", "pin_name": "MOUT", "pin_description": "Multiplier output and current amplifier inverting input. The output of the analog multiplier and the inverting input of the current amplifier are connected together at MOUT. As the multiplier output is a current, this is a high-impedance input so the amplifier can be configured as a differential amplifier."}, {"product_part_number": "UCC2818-EP", "package_type": "SOIC-16", "pin_number": "6", "pin_name": "IAC", "pin_description": "Current proportional to input voltage. This input to the analog multiplier is a current proportional to instantaneous line voltage. The multiplier is tailored for very low distortion from this current input (IIAC) to multiplier output. The recommended maximum IIAC is 500 μA."}, {"product_part_number": "UCC2818-EP", "package_type": "SOIC-16", "pin_number": "7", "pin_name": "VAOUT", "pin_description": "Voltage amplifier output. This is the output of the operational amplifier that regulates output voltage. The voltage amplifier output is internally limited to approximately 5.5 V to prevent overshoot."}, {"product_part_number": "UCC2818-EP", "package_type": "SOIC-16", "pin_number": "8", "pin_name": "VFF", "pin_description": "Feed-forward voltage. The RMS voltage signal generated at this pin by mirroring 1/2 of the IIAC into a single pole external filter. At low line, the VFF voltage should be 1.4 V."}, {"product_part_number": "UCC2818-EP", "package_type": "SOIC-16", "pin_number": "9", "pin_name": "VREF", "pin_description": "Voltage reference output. VREF is the output of an accurate 7.5-V voltage reference. This output is capable of delivering 20 mA to peripheral circuitry and is internally short-circuit current limited."}, {"product_part_number": "UCC2818-EP", "package_type": "SOIC-16", "pin_number": "10", "pin_name": "OVP/EN", "pin_description": "Over-voltage/enable. A window comparator input that disables the output driver if the boost output voltage is a programmed level above the nominal, or disables both the PFC output driver and resets SS if pulled below 1.9 V (typ)."}, {"product_part_number": "UCC2818-EP", "package_type": "SOIC-16", "pin_number": "11", "pin_name": "VSENSE", "pin_description": "Voltage amplifier inverting input. This is normally connected to a compensation network and to the boost converter output through a divider network."}, {"product_part_number": "UCC2818-EP", "package_type": "SOIC-16", "pin_number": "12", "pin_name": "RT", "pin_description": "Oscillator charging current. A resistor from RT to GND is used to program oscillator charging current. A resistor between 10 kΩ and 100 kΩ is recommended. Nominal voltage on this pin is 3 V."}, {"product_part_number": "UCC2818-EP", "package_type": "SOIC-16", "pin_number": "13", "pin_name": "SS", "pin_description": "Soft-start. VSS is discharged for Vvcc low conditions. When enabled, SS charges an external capacitor with a current source. This voltage is used as the voltage error signal during start-up, enabling the PWM duty cycle to increase slowly."}, {"product_part_number": "UCC2818-EP", "package_type": "SOIC-16", "pin_number": "14", "pin_name": "CT", "pin_description": "Oscillator timing capacitor. A capacitor from CT to GND sets the PWM oscillator frequency."}, {"product_part_number": "UCC2818-EP", "package_type": "SOIC-16", "pin_number": "15", "pin_name": "VCC", "pin_description": "Positive supply voltage. Connect to a stable source of at least 20 mA between 10 V and 17 V for normal operation."}, {"product_part_number": "UCC2818-EP", "package_type": "SOIC-16", "pin_number": "16", "pin_name": "DRVOUT", "pin_description": "Gate drive. The output drive for the boost switch is a totem-pole MOSFET gate driver on DRVOUT."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS716", "family_comparison": "UCCx817 vs UCCx818: UCCx817具有更高的UVLO开启电压(16V)和更宽的迟滞(6.3V)，适用于自举电路供电；UCCx818具有标准的UVLO开启电压(10.2V)和较窄的迟滞(0.5V)，适用于12V辅助电源供电。\nUCC281x vs UCC381x: UCC281x系列工作温度范围为-40°C至85°C (工业级)；UCC381x系列工作温度范围为0°C至70°C (商业级)。", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "10.2V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "220kHz", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": false, "integrated_ldo": false, "dynamic_voltage_setting": false, "pass_through_mode": false, "load_disconnect": false, "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "±2%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "10", "pitch": "450", "height": "40", "length": "40", "width": "40"}]}, {"part_number": "UCC2817", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "功率因数校正(PFC)控制器", "part_number_title": "UCC2817, UCC2818, UCC3817 and UCC3818 BiCMOS Power Factor Pregulator", "features": ["Controls Boost Preregulator to Near-Unity Power Factor", "Limits Line Distortion", "World-Wide Line Operation", "Overvoltage Protection", "Accurate Power Limiting", "Average Current Mode Control", "Improved Noise Immunity", "Improved Feed-Forward Line Regulation", "Leading Edge Modulation", "150-μA Typical Start-Up Current", "Low-Power BiCMOS Operation", "12-V to 17-V Operation", "Frequency Range 6 kHz to 220 kHz"], "description": "The UCC2817 and UCC2818 provides all the functions necessary for active power-factor-corrected preregulators. The controller achieves near-unity power factor by shaping the ac-input line current waveform to correspond to that of the ac input line voltage. Average current mode control maintains stable, low-distortion sinusoidal line current. Designed with TI's BiCMOS process, the UCC2817 and UCC2818 offers new features, such as lower start-up current, lower power dissipation, overvoltage protection, a shunt UVLO detect circuitry, a leading-edge modulation technique to reduce ripple current in the bulk capacitor, and an improved, low-offset (±2-mV) current amplifier to reduce distortion at light load conditions. The UCC2817 offers an on-chip shunt regulator with low start-up current suitable for applications utilizing a bootstrap supply. The UCC2818 is intended for applications with a fixed supply (Vcc).", "applications": ["PC Power", "Consumer Electronics", "Lighting", "Industrial Power Supplies", "IEC6100-3-2 Compliant Supplies Less Than 300 W"], "ordering_information": [{"part_number": "UCC2817", "order_device": "UCC2817D", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-40", "max_operation_temp": "85", "carrier_description": "TUBE"}, {"part_number": "UCC2817", "order_device": "UCC2817DTR", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-40", "max_operation_temp": "85", "carrier_description": "LARGE T&R"}, {"part_number": "UCC2817", "order_device": "UCC2817DW", "package_type": "SOIC", "package_drawing_code": "DW", "min_operation_temp": "-40", "max_operation_temp": "85", "carrier_description": "TUBE"}, {"part_number": "UCC2817", "order_device": "UCC2817N", "package_type": "PDIP", "package_drawing_code": "N", "min_operation_temp": "-40", "max_operation_temp": "85", "carrier_description": "TUBE"}], "pin_function": [{"product_part_number": "UCC2817", "package_type": "SOIC-16", "pin_number": "1", "pin_name": "GND", "pin_description": "Ground. All voltages measured with respect to ground. Vcc and REF should be bypassed directly to GND with a 0.1-µF or larger ceramic capacitor."}, {"product_part_number": "UCC2817", "package_type": "SOIC-16", "pin_number": "2", "pin_name": "PKLMT", "pin_description": "PFC peak current limit. The threshold for peak limit is 0 V. Use a resistor divider from the negative side of the current sense resistor to VREF to level shift this signal to a voltage level defined by the value of the sense resistor and the peak current limit. Peak current limit is reached when PKLMT voltage falls below 0 V."}, {"product_part_number": "UCC2817", "package_type": "SOIC-16", "pin_number": "3", "pin_name": "CAOUT", "pin_description": "Current amplifier output. This is the output of a wide bandwidth operational amplifier that senses line current and commands the PFC pulse-width modulator (PWM) to force the correct duty cycle. Compensation components are placed between CAOUT and MOUT."}, {"product_part_number": "UCC2817", "package_type": "SOIC-16", "pin_number": "4", "pin_name": "CAI", "pin_description": "Current amplifier noninverting input. Place a resistor between this pin and the GND side of current sense resistor. This input and the inverting input (MOUT) remain functional down to and below GND."}, {"product_part_number": "UCC2817", "package_type": "SOIC-16", "pin_number": "5", "pin_name": "MOUT", "pin_description": "Multiplier output and current amplifier inverting input. The output of the analog multiplier and the inverting input of the current amplifier are connected together at MOUT. As the multiplier output is a current, this is a high-impedance input so the amplifier can be configured as a differential amplifier."}, {"product_part_number": "UCC2817", "package_type": "SOIC-16", "pin_number": "6", "pin_name": "IAC", "pin_description": "Current proportional to input voltage. This input to the analog multiplier is a current proportional to instantaneous line voltage. The multiplier is tailored for very low distortion from this current input (IIAC) to multiplier output. The recommended maximum IIAC is 500 μA."}, {"product_part_number": "UCC2817", "package_type": "SOIC-16", "pin_number": "7", "pin_name": "VAOUT", "pin_description": "Voltage amplifier output. This is the output of the operational amplifier that regulates output voltage. The voltage amplifier output is internally limited to approximately 5.5 V to prevent overshoot."}, {"product_part_number": "UCC2817", "package_type": "SOIC-16", "pin_number": "8", "pin_name": "VFF", "pin_description": "Feed-forward voltage. The RMS voltage signal generated at this pin by mirroring 1/2 of the IIAC into a single pole external filter. At low line, the VFF voltage should be 1.4 V."}, {"product_part_number": "UCC2817", "package_type": "SOIC-16", "pin_number": "9", "pin_name": "VREF", "pin_description": "Voltage reference output. VREF is the output of an accurate 7.5-V voltage reference. This output is capable of delivering 20 mA to peripheral circuitry and is internally short-circuit current limited."}, {"product_part_number": "UCC2817", "package_type": "SOIC-16", "pin_number": "10", "pin_name": "OVP/EN", "pin_description": "Over-voltage/enable. A window comparator input that disables the output driver if the boost output voltage is a programmed level above the nominal, or disables both the PFC output driver and resets SS if pulled below 1.9 V (typ)."}, {"product_part_number": "UCC2817", "package_type": "SOIC-16", "pin_number": "11", "pin_name": "VSENSE", "pin_description": "Voltage amplifier inverting input. This is normally connected to a compensation network and to the boost converter output through a divider network."}, {"product_part_number": "UCC2817", "package_type": "SOIC-16", "pin_number": "12", "pin_name": "RT", "pin_description": "Oscillator charging current. A resistor from RT to GND is used to program oscillator charging current. A resistor between 10 kΩ and 100 kΩ is recommended. Nominal voltage on this pin is 3 V."}, {"product_part_number": "UCC2817", "package_type": "SOIC-16", "pin_number": "13", "pin_name": "SS", "pin_description": "Soft-start. VSS is discharged for Vvcc low conditions. When enabled, SS charges an external capacitor with a current source. This voltage is used as the voltage error signal during start-up, enabling the PWM duty cycle to increase slowly."}, {"product_part_number": "UCC2817", "package_type": "SOIC-16", "pin_number": "14", "pin_name": "CT", "pin_description": "Oscillator timing capacitor. A capacitor from CT to GND sets the PWM oscillator frequency."}, {"product_part_number": "UCC2817", "package_type": "SOIC-16", "pin_number": "15", "pin_name": "VCC", "pin_description": "Positive supply voltage. Connect to a stable source of at least 20 mA between 10 V and 17 V for normal operation."}, {"product_part_number": "UCC2817", "package_type": "SOIC-16", "pin_number": "16", "pin_name": "DRVOUT", "pin_description": "Gate drive. The output drive for the boost switch is a totem-pole MOSFET gate driver on DRVOUT."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS197K", "family_comparison": "UCCx817 vs UCCx818: UCCx817具有更高的UVLO开启电压(16V)和更宽的迟滞(6.3V)，适用于自举电路供电；UCCx818具有标准的UVLO开启电压(10.2V)和较窄的迟滞(0.5V)，适用于12V辅助电源供电。\nUCC281x vs UCC381x: UCC281x系列工作温度范围为-40°C至85°C (工业级)；UCC381x系列工作温度范围为0°C至70°C (商业级)。", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "16V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "220kHz", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": false, "integrated_ldo": true, "dynamic_voltage_setting": false, "pass_through_mode": false, "load_disconnect": false, "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "±2%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "10", "pitch": "450", "height": "40", "length": "40", "width": "40"}]}, {"part_number": "UCC2818", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "功率因数校正(PFC)控制器", "part_number_title": "UCC2817, UCC2818, UCC3817 and UCC3818 BiCMOS Power Factor Pregulator", "features": ["Controls Boost Preregulator to Near-Unity Power Factor", "Limits Line Distortion", "World-Wide Line Operation", "Overvoltage Protection", "Accurate Power Limiting", "Average Current Mode Control", "Improved Noise Immunity", "Improved Feed-Forward Line Regulation", "Leading Edge Modulation", "150-μA Typical Start-Up Current", "Low-Power BiCMOS Operation", "12-V to 17-V Operation", "Frequency Range 6 kHz to 220 kHz"], "description": "The UCC2817 and UCC2818 provides all the functions necessary for active power-factor-corrected preregulators. The controller achieves near-unity power factor by shaping the ac-input line current waveform to correspond to that of the ac input line voltage. Average current mode control maintains stable, low-distortion sinusoidal line current. Designed with TI's BiCMOS process, the UCC2817 and UCC2818 offers new features, such as lower start-up current, lower power dissipation, overvoltage protection, a shunt UVLO detect circuitry, a leading-edge modulation technique to reduce ripple current in the bulk capacitor, and an improved, low-offset (±2-mV) current amplifier to reduce distortion at light load conditions. The UCC2817 offers an on-chip shunt regulator with low start-up current suitable for applications utilizing a bootstrap supply. The UCC2818 is intended for applications with a fixed supply (Vcc).", "applications": ["PC Power", "Consumer Electronics", "Lighting", "Industrial Power Supplies", "IEC6100-3-2 Compliant Supplies Less Than 300 W"], "ordering_information": [{"part_number": "UCC2818", "order_device": "UCC2818D", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-40", "max_operation_temp": "85", "carrier_description": "TUBE"}, {"part_number": "UCC2818", "order_device": "UCC2818DTR", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-40", "max_operation_temp": "85", "carrier_description": "LARGE T&R"}, {"part_number": "UCC2818", "order_device": "UCC2818DW", "package_type": "SOIC", "package_drawing_code": "DW", "min_operation_temp": "-40", "max_operation_temp": "85", "carrier_description": "TUBE"}, {"part_number": "UCC2818", "order_device": "UCC2818DWTR", "package_type": "SOIC", "package_drawing_code": "DW", "min_operation_temp": "-40", "max_operation_temp": "85", "carrier_description": "LARGE T&R"}, {"part_number": "UCC2818", "order_device": "UCC2818N", "package_type": "PDIP", "package_drawing_code": "N", "min_operation_temp": "-40", "max_operation_temp": "85", "carrier_description": "TUBE"}, {"part_number": "UCC2818", "order_device": "UCC2818PW", "package_type": "TSSOP", "package_drawing_code": "PW", "min_operation_temp": "-40", "max_operation_temp": "85", "carrier_description": "TUBE"}], "pin_function": [{"product_part_number": "UCC2818", "package_type": "SOIC-16", "pin_number": "1", "pin_name": "GND", "pin_description": "Ground. All voltages measured with respect to ground. Vcc and REF should be bypassed directly to GND with a 0.1-µF or larger ceramic capacitor."}, {"product_part_number": "UCC2818", "package_type": "SOIC-16", "pin_number": "2", "pin_name": "PKLMT", "pin_description": "PFC peak current limit. The threshold for peak limit is 0 V. Use a resistor divider from the negative side of the current sense resistor to VREF to level shift this signal to a voltage level defined by the value of the sense resistor and the peak current limit. Peak current limit is reached when PKLMT voltage falls below 0 V."}, {"product_part_number": "UCC2818", "package_type": "SOIC-16", "pin_number": "3", "pin_name": "CAOUT", "pin_description": "Current amplifier output. This is the output of a wide bandwidth operational amplifier that senses line current and commands the PFC pulse-width modulator (PWM) to force the correct duty cycle. Compensation components are placed between CAOUT and MOUT."}, {"product_part_number": "UCC2818", "package_type": "SOIC-16", "pin_number": "4", "pin_name": "CAI", "pin_description": "Current amplifier noninverting input. Place a resistor between this pin and the GND side of current sense resistor. This input and the inverting input (MOUT) remain functional down to and below GND."}, {"product_part_number": "UCC2818", "package_type": "SOIC-16", "pin_number": "5", "pin_name": "MOUT", "pin_description": "Multiplier output and current amplifier inverting input. The output of the analog multiplier and the inverting input of the current amplifier are connected together at MOUT. As the multiplier output is a current, this is a high-impedance input so the amplifier can be configured as a differential amplifier."}, {"product_part_number": "UCC2818", "package_type": "SOIC-16", "pin_number": "6", "pin_name": "IAC", "pin_description": "Current proportional to input voltage. This input to the analog multiplier is a current proportional to instantaneous line voltage. The multiplier is tailored for very low distortion from this current input (IIAC) to multiplier output. The recommended maximum IIAC is 500 μA."}, {"product_part_number": "UCC2818", "package_type": "SOIC-16", "pin_number": "7", "pin_name": "VAOUT", "pin_description": "Voltage amplifier output. This is the output of the operational amplifier that regulates output voltage. The voltage amplifier output is internally limited to approximately 5.5 V to prevent overshoot."}, {"product_part_number": "UCC2818", "package_type": "SOIC-16", "pin_number": "8", "pin_name": "VFF", "pin_description": "Feed-forward voltage. The RMS voltage signal generated at this pin by mirroring 1/2 of the IIAC into a single pole external filter. At low line, the VFF voltage should be 1.4 V."}, {"product_part_number": "UCC2818", "package_type": "SOIC-16", "pin_number": "9", "pin_name": "VREF", "pin_description": "Voltage reference output. VREF is the output of an accurate 7.5-V voltage reference. This output is capable of delivering 20 mA to peripheral circuitry and is internally short-circuit current limited."}, {"product_part_number": "UCC2818", "package_type": "SOIC-16", "pin_number": "10", "pin_name": "OVP/EN", "pin_description": "Over-voltage/enable. A window comparator input that disables the output driver if the boost output voltage is a programmed level above the nominal, or disables both the PFC output driver and resets SS if pulled below 1.9 V (typ)."}, {"product_part_number": "UCC2818", "package_type": "SOIC-16", "pin_number": "11", "pin_name": "VSENSE", "pin_description": "Voltage amplifier inverting input. This is normally connected to a compensation network and to the boost converter output through a divider network."}, {"product_part_number": "UCC2818", "package_type": "SOIC-16", "pin_number": "12", "pin_name": "RT", "pin_description": "Oscillator charging current. A resistor from RT to GND is used to program oscillator charging current. A resistor between 10 kΩ and 100 kΩ is recommended. Nominal voltage on this pin is 3 V."}, {"product_part_number": "UCC2818", "package_type": "SOIC-16", "pin_number": "13", "pin_name": "SS", "pin_description": "Soft-start. VSS is discharged for Vvcc low conditions. When enabled, SS charges an external capacitor with a current source. This voltage is used as the voltage error signal during start-up, enabling the PWM duty cycle to increase slowly."}, {"product_part_number": "UCC2818", "package_type": "SOIC-16", "pin_number": "14", "pin_name": "CT", "pin_description": "Oscillator timing capacitor. A capacitor from CT to GND sets the PWM oscillator frequency."}, {"product_part_number": "UCC2818", "package_type": "SOIC-16", "pin_number": "15", "pin_name": "VCC", "pin_description": "Positive supply voltage. Connect to a stable source of at least 20 mA between 10 V and 17 V for normal operation."}, {"product_part_number": "UCC2818", "package_type": "SOIC-16", "pin_number": "16", "pin_name": "DRVOUT", "pin_description": "Gate drive. The output drive for the boost switch is a totem-pole MOSFET gate driver on DRVOUT."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS197K", "family_comparison": "UCCx817 vs UCCx818: UCCx817具有更高的UVLO开启电压(16V)和更宽的迟滞(6.3V)，适用于自举电路供电；UCCx818具有标准的UVLO开启电压(10.2V)和较窄的迟滞(0.5V)，适用于12V辅助电源供电。\nUCC281x vs UCC381x: UCC281x系列工作温度范围为-40°C至85°C (工业级)；UCC381x系列工作温度范围为0°C至70°C (商业级)。", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "10.2V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "220kHz", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": false, "integrated_ldo": false, "dynamic_voltage_setting": false, "pass_through_mode": false, "load_disconnect": false, "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "±2%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "10", "pitch": "450", "height": "40", "length": "40", "width": "40"}]}, {"part_number": "UCC3817", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "功率因数校正(PFC)控制器", "part_number_title": "UCC2817, UCC2818, UCC3817 and UCC3818 BiCMOS Power Factor Pregulator", "features": ["Controls Boost Preregulator to Near-Unity Power Factor", "Limits Line Distortion", "World-Wide Line Operation", "Overvoltage Protection", "Accurate Power Limiting", "Average Current Mode Control", "Improved Noise Immunity", "Improved Feed-Forward Line Regulation", "Leading Edge Modulation", "150-μA Typical Start-Up Current", "Low-Power BiCMOS Operation", "12-V to 17-V Operation", "Frequency Range 6 kHz to 220 kHz"], "description": "The UCC2817 and UCC2818 provides all the functions necessary for active power-factor-corrected preregulators. The controller achieves near-unity power factor by shaping the ac-input line current waveform to correspond to that of the ac input line voltage. Average current mode control maintains stable, low-distortion sinusoidal line current. Designed with TI's BiCMOS process, the UCC2817 and UCC2818 offers new features, such as lower start-up current, lower power dissipation, overvoltage protection, a shunt UVLO detect circuitry, a leading-edge modulation technique to reduce ripple current in the bulk capacitor, and an improved, low-offset (±2-mV) current amplifier to reduce distortion at light load conditions. The UCC2817 offers an on-chip shunt regulator with low start-up current suitable for applications utilizing a bootstrap supply. The UCC2818 is intended for applications with a fixed supply (Vcc).", "applications": ["PC Power", "Consumer Electronics", "Lighting", "Industrial Power Supplies", "IEC6100-3-2 Compliant Supplies Less Than 300 W"], "ordering_information": [{"part_number": "UCC3817", "order_device": "UCC3817D", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "0", "max_operation_temp": "70", "carrier_description": "TUBE"}, {"part_number": "UCC3817", "order_device": "UCC3817DTR", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "0", "max_operation_temp": "70", "carrier_description": "LARGE T&R"}, {"part_number": "UCC3817", "order_device": "UCC3817DW", "package_type": "SOIC", "package_drawing_code": "DW", "min_operation_temp": "0", "max_operation_temp": "70", "carrier_description": "TUBE"}, {"part_number": "UCC3817", "order_device": "UCC3817DWTR", "package_type": "SOIC", "package_drawing_code": "DW", "min_operation_temp": "0", "max_operation_temp": "70", "carrier_description": "LARGE T&R"}, {"part_number": "UCC3817", "order_device": "UCC3817N", "package_type": "PDIP", "package_drawing_code": "N", "min_operation_temp": "0", "max_operation_temp": "70", "carrier_description": "TUBE"}], "pin_function": [{"product_part_number": "UCC3817", "package_type": "SOIC-16", "pin_number": "1", "pin_name": "GND", "pin_description": "Ground. All voltages measured with respect to ground. Vcc and REF should be bypassed directly to GND with a 0.1-µF or larger ceramic capacitor."}, {"product_part_number": "UCC3817", "package_type": "SOIC-16", "pin_number": "2", "pin_name": "PKLMT", "pin_description": "PFC peak current limit. The threshold for peak limit is 0 V. Use a resistor divider from the negative side of the current sense resistor to VREF to level shift this signal to a voltage level defined by the value of the sense resistor and the peak current limit. Peak current limit is reached when PKLMT voltage falls below 0 V."}, {"product_part_number": "UCC3817", "package_type": "SOIC-16", "pin_number": "3", "pin_name": "CAOUT", "pin_description": "Current amplifier output. This is the output of a wide bandwidth operational amplifier that senses line current and commands the PFC pulse-width modulator (PWM) to force the correct duty cycle. Compensation components are placed between CAOUT and MOUT."}, {"product_part_number": "UCC3817", "package_type": "SOIC-16", "pin_number": "4", "pin_name": "CAI", "pin_description": "Current amplifier noninverting input. Place a resistor between this pin and the GND side of current sense resistor. This input and the inverting input (MOUT) remain functional down to and below GND."}, {"product_part_number": "UCC3817", "package_type": "SOIC-16", "pin_number": "5", "pin_name": "MOUT", "pin_description": "Multiplier output and current amplifier inverting input. The output of the analog multiplier and the inverting input of the current amplifier are connected together at MOUT. As the multiplier output is a current, this is a high-impedance input so the amplifier can be configured as a differential amplifier."}, {"product_part_number": "UCC3817", "package_type": "SOIC-16", "pin_number": "6", "pin_name": "IAC", "pin_description": "Current proportional to input voltage. This input to the analog multiplier is a current proportional to instantaneous line voltage. The multiplier is tailored for very low distortion from this current input (IIAC) to multiplier output. The recommended maximum IIAC is 500 μA."}, {"product_part_number": "UCC3817", "package_type": "SOIC-16", "pin_number": "7", "pin_name": "VAOUT", "pin_description": "Voltage amplifier output. This is the output of the operational amplifier that regulates output voltage. The voltage amplifier output is internally limited to approximately 5.5 V to prevent overshoot."}, {"product_part_number": "UCC3817", "package_type": "SOIC-16", "pin_number": "8", "pin_name": "VFF", "pin_description": "Feed-forward voltage. The RMS voltage signal generated at this pin by mirroring 1/2 of the IIAC into a single pole external filter. At low line, the VFF voltage should be 1.4 V."}, {"product_part_number": "UCC3817", "package_type": "SOIC-16", "pin_number": "9", "pin_name": "VREF", "pin_description": "Voltage reference output. VREF is the output of an accurate 7.5-V voltage reference. This output is capable of delivering 20 mA to peripheral circuitry and is internally short-circuit current limited."}, {"product_part_number": "UCC3817", "package_type": "SOIC-16", "pin_number": "10", "pin_name": "OVP/EN", "pin_description": "Over-voltage/enable. A window comparator input that disables the output driver if the boost output voltage is a programmed level above the nominal, or disables both the PFC output driver and resets SS if pulled below 1.9 V (typ)."}, {"product_part_number": "UCC3817", "package_type": "SOIC-16", "pin_number": "11", "pin_name": "VSENSE", "pin_description": "Voltage amplifier inverting input. This is normally connected to a compensation network and to the boost converter output through a divider network."}, {"product_part_number": "UCC3817", "package_type": "SOIC-16", "pin_number": "12", "pin_name": "RT", "pin_description": "Oscillator charging current. A resistor from RT to GND is used to program oscillator charging current. A resistor between 10 kΩ and 100 kΩ is recommended. Nominal voltage on this pin is 3 V."}, {"product_part_number": "UCC3817", "package_type": "SOIC-16", "pin_number": "13", "pin_name": "SS", "pin_description": "Soft-start. VSS is discharged for Vvcc low conditions. When enabled, SS charges an external capacitor with a current source. This voltage is used as the voltage error signal during start-up, enabling the PWM duty cycle to increase slowly."}, {"product_part_number": "UCC3817", "package_type": "SOIC-16", "pin_number": "14", "pin_name": "CT", "pin_description": "Oscillator timing capacitor. A capacitor from CT to GND sets the PWM oscillator frequency."}, {"product_part_number": "UCC3817", "package_type": "SOIC-16", "pin_number": "15", "pin_name": "VCC", "pin_description": "Positive supply voltage. Connect to a stable source of at least 20 mA between 10 V and 17 V for normal operation."}, {"product_part_number": "UCC3817", "package_type": "SOIC-16", "pin_number": "16", "pin_name": "DRVOUT", "pin_description": "Gate drive. The output drive for the boost switch is a totem-pole MOSFET gate driver on DRVOUT."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS197K", "family_comparison": "UCCx817 vs UCCx818: UCCx817具有更高的UVLO开启电压(16V)和更宽的迟滞(6.3V)，适用于自举电路供电；UCCx818具有标准的UVLO开启电压(10.2V)和较窄的迟滞(0.5V)，适用于12V辅助电源供电。\nUCC281x vs UCC381x: UCC281x系列工作温度范围为-40°C至85°C (工业级)；UCC381x系列工作温度范围为0°C至70°C (商业级)。", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "16V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "220kHz", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": false, "integrated_ldo": true, "dynamic_voltage_setting": false, "pass_through_mode": false, "load_disconnect": false, "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "10", "pitch": "450", "height": "40", "length": "40", "width": "40"}]}, {"part_number": "UCC3818", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "功率因数校正(PFC)控制器", "part_number_title": "UCC2817, UCC2818, UCC3817 and UCC3818 BiCMOS Power Factor Pregulator", "features": ["Controls Boost Preregulator to Near-Unity Power Factor", "Limits Line Distortion", "World-Wide Line Operation", "Overvoltage Protection", "Accurate Power Limiting", "Average Current Mode Control", "Improved Noise Immunity", "Improved Feed-Forward Line Regulation", "Leading Edge Modulation", "150-μA Typical Start-Up Current", "Low-Power BiCMOS Operation", "12-V to 17-V Operation", "Frequency Range 6 kHz to 220 kHz"], "description": "The UCC2817 and UCC2818 provides all the functions necessary for active power-factor-corrected preregulators. The controller achieves near-unity power factor by shaping the ac-input line current waveform to correspond to that of the ac input line voltage. Average current mode control maintains stable, low-distortion sinusoidal line current. Designed with TI's BiCMOS process, the UCC2817 and UCC2818 offers new features, such as lower start-up current, lower power dissipation, overvoltage protection, a shunt UVLO detect circuitry, a leading-edge modulation technique to reduce ripple current in the bulk capacitor, and an improved, low-offset (±2-mV) current amplifier to reduce distortion at light load conditions. The UCC2817 offers an on-chip shunt regulator with low start-up current suitable for applications utilizing a bootstrap supply. The UCC2818 is intended for applications with a fixed supply (Vcc).", "applications": ["PC Power", "Consumer Electronics", "Lighting", "Industrial Power Supplies", "IEC6100-3-2 Compliant Supplies Less Than 300 W"], "ordering_information": [{"part_number": "UCC3818", "order_device": "UCC3818D", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "0", "max_operation_temp": "70", "carrier_description": "TUBE"}, {"part_number": "UCC3818", "order_device": "UCC3818DTR", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "0", "max_operation_temp": "70", "carrier_description": "LARGE T&R"}, {"part_number": "UCC3818", "order_device": "UCC3818DW", "package_type": "SOIC", "package_drawing_code": "DW", "min_operation_temp": "0", "max_operation_temp": "70", "carrier_description": "TUBE"}, {"part_number": "UCC3818", "order_device": "UCC3818DWTR", "package_type": "SOIC", "package_drawing_code": "DW", "min_operation_temp": "0", "max_operation_temp": "70", "carrier_description": "LARGE T&R"}, {"part_number": "UCC3818", "order_device": "UCC3818N", "package_type": "PDIP", "package_drawing_code": "N", "min_operation_temp": "0", "max_operation_temp": "70", "carrier_description": "TUBE"}, {"part_number": "UCC3818", "order_device": "UCC3818PW", "package_type": "TSSOP", "package_drawing_code": "PW", "min_operation_temp": "0", "max_operation_temp": "70", "carrier_description": "TUBE"}], "pin_function": [{"product_part_number": "UCC3818", "package_type": "SOIC-16", "pin_number": "1", "pin_name": "GND", "pin_description": "Ground. All voltages measured with respect to ground. Vcc and REF should be bypassed directly to GND with a 0.1-µF or larger ceramic capacitor."}, {"product_part_number": "UCC3818", "package_type": "SOIC-16", "pin_number": "2", "pin_name": "PKLMT", "pin_description": "PFC peak current limit. The threshold for peak limit is 0 V. Use a resistor divider from the negative side of the current sense resistor to VREF to level shift this signal to a voltage level defined by the value of the sense resistor and the peak current limit. Peak current limit is reached when PKLMT voltage falls below 0 V."}, {"product_part_number": "UCC3818", "package_type": "SOIC-16", "pin_number": "3", "pin_name": "CAOUT", "pin_description": "Current amplifier output. This is the output of a wide bandwidth operational amplifier that senses line current and commands the PFC pulse-width modulator (PWM) to force the correct duty cycle. Compensation components are placed between CAOUT and MOUT."}, {"product_part_number": "UCC3818", "package_type": "SOIC-16", "pin_number": "4", "pin_name": "CAI", "pin_description": "Current amplifier noninverting input. Place a resistor between this pin and the GND side of current sense resistor. This input and the inverting input (MOUT) remain functional down to and below GND."}, {"product_part_number": "UCC3818", "package_type": "SOIC-16", "pin_number": "5", "pin_name": "MOUT", "pin_description": "Multiplier output and current amplifier inverting input. The output of the analog multiplier and the inverting input of the current amplifier are connected together at MOUT. As the multiplier output is a current, this is a high-impedance input so the amplifier can be configured as a differential amplifier."}, {"product_part_number": "UCC3818", "package_type": "SOIC-16", "pin_number": "6", "pin_name": "IAC", "pin_description": "Current proportional to input voltage. This input to the analog multiplier is a current proportional to instantaneous line voltage. The multiplier is tailored for very low distortion from this current input (IIAC) to multiplier output. The recommended maximum IIAC is 500 μA."}, {"product_part_number": "UCC3818", "package_type": "SOIC-16", "pin_number": "7", "pin_name": "VAOUT", "pin_description": "Voltage amplifier output. This is the output of the operational amplifier that regulates output voltage. The voltage amplifier output is internally limited to approximately 5.5 V to prevent overshoot."}, {"product_part_number": "UCC3818", "package_type": "SOIC-16", "pin_number": "8", "pin_name": "VFF", "pin_description": "Feed-forward voltage. The RMS voltage signal generated at this pin by mirroring 1/2 of the IIAC into a single pole external filter. At low line, the VFF voltage should be 1.4 V."}, {"product_part_number": "UCC3818", "package_type": "SOIC-16", "pin_number": "9", "pin_name": "VREF", "pin_description": "Voltage reference output. VREF is the output of an accurate 7.5-V voltage reference. This output is capable of delivering 20 mA to peripheral circuitry and is internally short-circuit current limited."}, {"product_part_number": "UCC3818", "package_type": "SOIC-16", "pin_number": "10", "pin_name": "OVP/EN", "pin_description": "Over-voltage/enable. A window comparator input that disables the output driver if the boost output voltage is a programmed level above the nominal, or disables both the PFC output driver and resets SS if pulled below 1.9 V (typ)."}, {"product_part_number": "UCC3818", "package_type": "SOIC-16", "pin_number": "11", "pin_name": "VSENSE", "pin_description": "Voltage amplifier inverting input. This is normally connected to a compensation network and to the boost converter output through a divider network."}, {"product_part_number": "UCC3818", "package_type": "SOIC-16", "pin_number": "12", "pin_name": "RT", "pin_description": "Oscillator charging current. A resistor from RT to GND is used to program oscillator charging current. A resistor between 10 kΩ and 100 kΩ is recommended. Nominal voltage on this pin is 3 V."}, {"product_part_number": "UCC3818", "package_type": "SOIC-16", "pin_number": "13", "pin_name": "SS", "pin_description": "Soft-start. VSS is discharged for Vvcc low conditions. When enabled, SS charges an external capacitor with a current source. This voltage is used as the voltage error signal during start-up, enabling the PWM duty cycle to increase slowly."}, {"product_part_number": "UCC3818", "package_type": "SOIC-16", "pin_number": "14", "pin_name": "CT", "pin_description": "Oscillator timing capacitor. A capacitor from CT to GND sets the PWM oscillator frequency."}, {"product_part_number": "UCC3818", "package_type": "SOIC-16", "pin_number": "15", "pin_name": "VCC", "pin_description": "Positive supply voltage. Connect to a stable source of at least 20 mA between 10 V and 17 V for normal operation."}, {"product_part_number": "UCC3818", "package_type": "SOIC-16", "pin_number": "16", "pin_name": "DRVOUT", "pin_description": "Gate drive. The output drive for the boost switch is a totem-pole MOSFET gate driver on DRVOUT."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS197K", "family_comparison": "UCCx817 vs UCCx818: UCCx817具有更高的UVLO开启电压(16V)和更宽的迟滞(6.3V)，适用于自举电路供电；UCCx818具有标准的UVLO开启电压(10.2V)和较窄的迟滞(0.5V)，适用于12V辅助电源供电。\nUCC281x vs UCC381x: UCC281x系列工作温度范围为-40°C至85°C (工业级)；UCC381x系列工作温度范围为0°C至70°C (商业级)。", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "10.2V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "220kHz", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": false, "integrated_ldo": false, "dynamic_voltage_setting": false, "pass_through_mode": false, "load_disconnect": false, "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "10", "pitch": "450", "height": "40", "length": "40", "width": "40"}]}]