[{"part_number": "UCC28070", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "交错式连续导通模式PFC控制器", "part_number_title": "Interleaving Continuous Conduction Mode PFC Controller", "features": ["Interleaved Average Current-Mode PWM Control With Inherent Current Matching", "Advanced Current Synthesizer Current Sensing for Superior Efficiency", "Highly-Linear Multiplier Output With Internal Quantized Voltage Feed-Forward Correction for Near-Unity PF", "Programmable Frequency from 30 kHz to 300 kHz", "Programmable Maximum Duty-Cycle Clamp", "Programmable Frequency-Dithering Rate and Magnitude for Enhanced EMI Reduction", "External-Clock Synchronization Capability", "Enhanced Load and Line Transient Response through Voltage Amplifier Output Slew-Rate Correction", "Programmable Peak Current Limiting", "Bias-Supply UVLO, Overvoltage Protection, OpenLoop Detection, and PFC-Enable Monitoring", "External PFC-Disable Interface", "Open-Circuit Protection on VSENSE and VINAC pins", "Programmable Soft-Start"], "description": "The UCC28070 is an advanced power factor correction (PFC) device that integrates two pulsewidth modulators (PWMs) operating 180° out of phase. This interleaved PWM operation generates substantial reduction in the input and output ripple currents, allowing the conducted-EMI filtering to become easier and less expensive. A significantly improved multiplier design provides a shared current reference to two independent current amplifiers that ensures matched average-current mode control in both PWM outputs while maintaining a stable, lowdistortion, sinusoidal input-line current. The UCC28070 device contains multiple innovations including current synthesis and quantized voltage feed-forward to promote performance enhancements in PF, efficiency, THD, and transient response. Features including frequency dithering, clock synchronization, and slew rate enhancement further expand the potential performance enhancements. The UCC28070 device also contains a variety of protection features including output-overvoltage detection, programmable peak-current limit, undervoltage lockout, and open-loop protection.", "applications": ["High-Efficiency Server and Desktop Power Supplies", "Telecom Rectifiers", "White Goods and Industrial Equipment", "Automotive Power Systems"], "ordering_information": [{"part_number": "UCC28070", "order_device": "UCC28070DW", "package_type": "SOIC", "package_drawing_code": "DW", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC28070", "order_device": "UCC28070DWR", "package_type": "SOIC", "package_drawing_code": "DW", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC28070", "order_device": "UCC28070PW", "package_type": "TSSOP", "package_drawing_code": "PW", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28070", "order_device": "UCC28070PWR", "package_type": "TSSOP", "package_drawing_code": "PW", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC28070", "package_type": "TSSOP", "pins": [{"pin_number": "1", "pin_name": "CDR", "pin_description": "Dither Rate Capacitor. An external capacitor to GND programs the rate of oscillator dither. Connect the CDR pin to the VREF pin to disable dithering."}, {"pin_number": "2", "pin_name": "RDM (SYNC)", "pin_description": "Dither Magnitude Resistor. Frequency-dithering magnitude and external synchronization pin. An external resistor to GND programs the magnitude of oscillator frequency dither. When frequency dithering is disabled (CDR > 5 V), the internal master clock will synchronize to positive edges presented on the RDM pin. Connect RDM to GND when dithering is disabled and synchronization is not desired."}, {"pin_number": "3", "pin_name": "VAO", "pin_description": "Voltage Amplifier Output. Output of transconductance voltage error amplifier. Internally connected to Multiplier input and Zero-Power comparator. Connect the voltage regulation loop compensation components between this pin and GND."}, {"pin_number": "4", "pin_name": "VSENSE", "pin_description": "Output Voltage Sense. Internally connected to the inverting input of the transconductance voltage error amplifier in addition to the positive terminal of the Current Synthesis difference amplifier. Also connected to the OVP, PFC Enable, and slew-rate comparators. Connect to PFC output with a resistor-divider network."}, {"pin_number": "5", "pin_name": "VINAC", "pin_description": "Scaled AC Line Input Voltage. Internally connected to the Multiplier and negative terminal of the Current Synthesis difference amplifier. Connect a resistor-divider network between VIN, VINAC, and GND identical to the PFC output divider network connected at VSENSE."}, {"pin_number": "6", "pin_name": "IMO", "pin_description": "Multiplier Current Output. Connect a resistor between this pin and GND to set the multiplier gain."}, {"pin_number": "7", "pin_name": "RSYNTH", "pin_description": "Current Synthesis Down-Slope Programming. Connect a resistor between this pin and GND to set the magnitude of the current synthesizer down-slope. Connecting RSYNTH to VREF will disable current synthesis and connect CSA and CSB directly to their respective current amplifiers."}, {"pin_number": "8", "pin_name": "CSB", "pin_description": "Phase B Current Sense Input. During the on-time of GDB, CSB is internally connected to the inverting input of Phase B's current amplifier through the current synthesis stage."}, {"pin_number": "9", "pin_name": "CSA", "pin_description": "Phase A Current Sense Input. During the on-time of GDA, CSA is internally connected to the inverting input of Phase A's current amplifier through the current synthesis stage."}, {"pin_number": "10", "pin_name": "PKLMT", "pin_description": "Peak Current Limit Programming. Connect a resistor-divider network between VREF and this pin to set the voltage threshold of the cycle-by-cycle peak current limiting comparators. Allows adjustment for desired ΔILB."}, {"pin_number": "11", "pin_name": "CAOB", "pin_description": "Phase B Current Amplifier Output. Output of phase B's transconductance current amplifier. Internally connected to the inverting input of phase B's PWM comparator for trailing-edge modulation. Connect the current regulation loop compensation components between this pin and GND."}, {"pin_number": "12", "pin_name": "CAOA", "pin_description": "Phase A Current Amplifier Output. Output of phase A's transconductance current amplifier. Internally connected to the inverting input of phase A's PWM comparator for trailing-edge modulation. Connect the current regulation loop compensation components between this pin and GND."}, {"pin_number": "13", "pin_name": "VREF", "pin_description": "6-V Reference Voltage and Internal Bias Voltage. Connect a 0.1-µF ceramic bypass capacitor as close as possible to this pin and GND."}, {"pin_number": "14", "pin_name": "GDA", "pin_description": "Phase A's Gate Drive. This limited-current output is intended to connect to a separate gate-drive device suitable for driving the Phase A switching component(s). The output voltage is typically clamped to 13.5 V."}, {"pin_number": "15", "pin_name": "VCC", "pin_description": "Bias Voltage Input. Connect a 0.1-µF ceramic bypass capacitor as close as possible to this pin and GND."}, {"pin_number": "16", "pin_name": "GND", "pin_description": "Device Ground Reference. Connect all compensation and programming resistor and capacitor networks to this pin. Connect this pin to the system through a separate trace for high-current noise isolation."}, {"pin_number": "17", "pin_name": "GDB", "pin_description": "Phase B's Gate Drive. This limited-current output is intended to connect to a separate gate-drive device suitable for driving the Phase B switching component(s). The output voltage is typically clamped to 13.5 V."}, {"pin_number": "18", "pin_name": "SS", "pin_description": "Soft-Start and External Fault Interface. Connect a capacitor to GND on this pin to set the soft-start slew rate based on an internally-fixed 10-µA current source. The regulation reference voltage for VSENSE is clamped to VSS until VSS exceeds 3 V. Upon recovery from certain fault conditions a 1-mA current source is present at the SS pin until the SS voltage equals the VSENSE voltage. Pulling the SS pin below 0.6 V immediately disables both GDA and GDB outputs."}, {"pin_number": "19", "pin_name": "RT", "pin_description": "Timing Resistor. Oscillator frequency programming pin. A resistor to GND sets the running frequency of the internal oscillator."}, {"pin_number": "20", "pin_name": "DMAX", "pin_description": "Maximum Duty-Cycle Resistor. Maximum PWM duty-cycle programming pin. A resistor to GND sets the PWM maximum duty-cycle based on the ratio of RDMX/RRT."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUSA71A-JULY 2010-REVISED JUNE 2011", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET/IGBT", "power_phase_number": 2, "channel_count": 2, "max_input_voltage": "21V", "min_input_voltage": "10.8V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "300kHz", "quiescent_current": "7000uA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Pulse Skipping", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Auto Recovery", "output_discharge": "No", "integrated_ldo": "Yes", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "1%", "output_reference_voltage": "3V"}, "package": [{"type": "OPTION", "pitch": "0.65", "height": "1.2", "length": "6.6", "width": "4.5", "pin_count": "30"}]}, {"part_number": "UCC28070-Q1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "交错式连续导通模式PFC控制器", "part_number_title": "Interleaving Continuous Conduction Mode PFC Controller", "features": ["Qualified for Automotive Applications", "Interleaved Average Current-Mode PWM Control with Inherent Current Matching", "Advanced Current Synthesizer Current Sensing for Superior Efficiency", "Highly-Linear Multiplier Output with Internal Quantized Voltage Feed-Forward Correction for Near-Unity PF", "Programmable Frequency (30 kHz to 300 kHz)", "Programmable Maximum Duty-Cycle Clamp", "Programmable Frequency Dithering Rate and Magnitude for Enhanced EMI Reduction", "External Clock Synchronization Capability", "Enhanced Load and Line Transient Response through Voltage Amplifier Output Slew-Rate Correction", "Programmable Peak Current Limiting", "Bias-Supply UVLO, Over-Voltage Protection, Open-Loop Detection, and PFC-Enable Monitoring", "External PFC-Disable Interface", "Open-Circuit Protection on VSENSE and VINAC pins", "Programmable Soft Start", "20-Lead TSSOP Package"], "description": "The UCC28070 is an advanced power factor correction device that integrates two pulse-width modulators (PWMs) operating 180° out of phase. This interleaved PWM operation generates substantial reduction in the input and output ripple currents, and the conducted-EMI filtering becomes easier and less expensive. A significantly improved multiplier design provides a shared current reference to two independent current amplifiers that ensures matched average current mode control in both PWM outputs while maintaining a stable, low-distortion sinusoidal input line current. The UCC28070 contains multiple innovations including current synthesis and quantized voltage feed-forward to promote performance enhancements in PF, efficiency, THD, and transient response. Features including frequency dithering, clock synchronization, and slew rate enhancement further expand the potential performance enhancements. The UCC28070 also contains a variety of protection features including output over-voltage detection, programmable peak-current limit, under-voltage lockout, and open-loop protection.", "applications": ["Automotive Power Systems", "High-Efficiency Server and Desktop Power Supplies", "Telecom Rectifiers", "White Goods and Industrial Equipment"], "ordering_information": [{"part_number": "UCC28070-Q1", "order_device": "UCC28070QPWRQ1", "package_type": "TSSOP", "package_drawing_code": "PW", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC28070-Q1", "package_type": "TSSOP", "pins": [{"pin_number": "1", "pin_name": "CDR", "pin_description": "Dither Rate Capacitor. An external capacitor to GND programs the rate of oscillator dither. Connect the CDR pin to the VREF pin to disable dithering."}, {"pin_number": "2", "pin_name": "RDM (SYNC)", "pin_description": "Dither Magnitude Resistor. Frequency-dithering magnitude and external synchronization pin. An external resistor to GND programs the magnitude of oscillator frequency dither. When frequency dithering is disabled (CDR > 5 V), the internal master clock will synchronize to positive edges presented on the RDM pin. Connect RDM to GND when dithering is disabled and synchronization is not desired."}, {"pin_number": "3", "pin_name": "VAO", "pin_description": "Voltage Amplifier Output. Output of transconductance voltage error amplifier. Internally connected to Multiplier input and Zero-Power comparator. Connect the voltage regulation loop compensation components between this pin and GND."}, {"pin_number": "4", "pin_name": "VSENSE", "pin_description": "Output Voltage Sense. Internally connected to the inverting input of the transconductance voltage error amplifier in addition to the positive terminal of the Current Synthesis difference amplifier. Also connected to the OVP, PFC Enable, and slew-rate comparators. Connect to PFC output with a resistor-divider network."}, {"pin_number": "5", "pin_name": "VINAC", "pin_description": "Scaled AC Line Input Voltage. Internally connected to the Multiplier and negative terminal of the Current Synthesis difference amplifier. Connect a resistor-divider network between VIN, VINAC, and GND identical to the PFC output divider network connected at VSENSE."}, {"pin_number": "6", "pin_name": "IMO", "pin_description": "Multiplier Current Output. Connect a resistor between this pin and GND to set the multiplier gain."}, {"pin_number": "7", "pin_name": "RSYNTH", "pin_description": "Current Synthesis Down-Slope Programming. Connect a resistor between this pin and GND to set the magnitude of the current synthesizer down-slope. Connecting RSYNTH to VREF will disable current synthesis and connect CSA and CSB directly to their respective current amplifiers."}, {"pin_number": "8", "pin_name": "CSB", "pin_description": "Phase B Current Sense Input. During the on-time of GDB, CSB is internally connected to the inverting input of Phase B's current amplifier through the current synthesis stage."}, {"pin_number": "9", "pin_name": "CSA", "pin_description": "Phase A Current Sense Input. During the on-time of GDA, CSA is internally connected to the inverting input of Phase A's current amplifier through the current synthesis stage."}, {"pin_number": "10", "pin_name": "PKLMT", "pin_description": "Peak Current Limit Programming. Connect a resistor-divider network between VREF and this pin to set the voltage threshold of the cycle-by-cycle peak current limiting comparators. Allows adjustment for desired ΔILB."}, {"pin_number": "11", "pin_name": "CAOB", "pin_description": "Phase B Current Amplifier Output. Output of phase B's transconductance current amplifier. Internally connected to the inverting input of phase B's PWM comparator for trailing-edge modulation. Connect the current regulation loop compensation components between this pin and GND."}, {"pin_number": "12", "pin_name": "CAOA", "pin_description": "Phase A Current Amplifier Output. Output of phase A's transconductance current amplifier. Internally connected to the inverting input of phase A's PWM comparator for trailing-edge modulation. Connect the current regulation loop compensation components between this pin and GND."}, {"pin_number": "13", "pin_name": "VREF", "pin_description": "6-V Reference Voltage and Internal Bias Voltage. Connect a 0.1-µF ceramic bypass capacitor as close as possible to this pin and GND."}, {"pin_number": "14", "pin_name": "GDA", "pin_description": "Phase A's Gate Drive. This limited-current output is intended to connect to a separate gate-drive device suitable for driving the Phase A switching component(s). The output voltage is typically clamped to 13.5 V."}, {"pin_number": "15", "pin_name": "VCC", "pin_description": "Bias Voltage Input. Connect a 0.1-µF ceramic bypass capacitor as close as possible to this pin and GND."}, {"pin_number": "16", "pin_name": "GND", "pin_description": "Device Ground Reference. Connect all compensation and programming resistor and capacitor networks to this pin. Connect this pin to the system through a separate trace for high-current noise isolation."}, {"pin_number": "17", "pin_name": "GDB", "pin_description": "Phase B's Gate Drive. This limited-current output is intended to connect to a separate gate-drive device suitable for driving the Phase B switching component(s). The output voltage is typically clamped to 13.5 V."}, {"pin_number": "18", "pin_name": "SS", "pin_description": "Soft-Start and External Fault Interface. Connect a capacitor to GND on this pin to set the soft-start slew rate based on an internally-fixed 10-µA current source. The regulation reference voltage for VSENSE is clamped to VSS until VSS exceeds 3 V. Upon recovery from certain fault conditions a 1-mA current source is present at the SS pin until the SS voltage equals the VSENSE voltage. Pulling the SS pin below 0.6 V immediately disables both GDA and GDB outputs."}, {"pin_number": "19", "pin_name": "RT", "pin_description": "Timing Resistor. Oscillator frequency programming pin. A resistor to GND sets the running frequency of the internal oscillator."}, {"pin_number": "20", "pin_name": "DMAX", "pin_description": "Maximum Duty-Cycle Resistor. Maximum PWM duty-cycle programming pin. A resistor to GND sets the PWM maximum duty-cycle based on the ratio of RDMX/RRT."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUSA71A-JULY 2010-REVISED JUNE 2011", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET/IGBT", "power_phase_number": 2, "channel_count": 2, "max_input_voltage": "21V", "min_input_voltage": "10.8V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "300kHz", "quiescent_current": "7000uA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Pulse Skipping", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Auto Recovery", "output_discharge": "No", "integrated_ldo": "Yes", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "1%", "output_reference_voltage": "3V"}, "package": [{"type": "OPTION", "pitch": "0.65", "height": "1.2", "length": "6.6", "width": "4.5", "pin_count": "30"}]}]