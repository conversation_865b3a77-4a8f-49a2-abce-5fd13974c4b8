{"response": "\n[\n  {\n    \"part_number\": \"UCC1580-1\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Military\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PWM控制器\",\n    \"category_lv3\": \"有源钳位PWM控制器\",\n    \"part_number_title\": \"Single Ended Active Clamp/Reset PWM\",\n    \"features\": [\n      \"Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive\",\n      \"Programmable deadtime (Turn-on Delay) Between Activation of Each Switch\",\n      \"Voltage Mode Control with Feedforward Operation\",\n      \"Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle\",\n      \"High Current Gate Driver for Both Main and Auxiliary Outputs\",\n      \"Multiple Protection Features with Latched Shutdown and Soft Restart\",\n      \"Low Supply Current (100 μA Startup, 1.5 mA Operation)\"\n    ],\n    \"description\": \"The UCC1580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. This version (UCC1580-1) has a 9V VDD UVLO threshold and a normal polarity auxiliary driver (OUT2). It is specified for the military temperature range of -55°C to 125°C.\",\n    \"applications\": [\n      \"Active clamp/reset switching converter topologies\",\n      \"Synchronous rectifier switching converter topologies\",\n      \"Active clamp forward converter\",\n      \"Off-line active clamp flyback converter\"\n    ],\n    \"ordering_information\": [],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC1580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"DELAY\",\n        \"pin_description\": \"A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"LINE\",\n        \"pin_description\": \"Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VDD\",\n        \"pin_description\": \"Chip power supply pin. VDD should be bypassed to PGND.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OUT1\",\n        \"pin_description\": \"Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"PGND\",\n        \"pin_description\": \"Ground connection for the gate drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"OUT2\",\n        \"pin_description\": \"Gate drive output for the auxiliary switch with 0.3A drive current capability.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"CLK\",\n        \"pin_description\": \"Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"GND\",\n        \"pin_description\": \"Signal Ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"RAMP\",\n        \"pin_description\": \"A resistor (R4) and a capacitor (CR) program the feedforward ramp signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"OSC2\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC2 controls guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"OSC1\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC1 sets maximum on time.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"EAOUT\",\n        \"pin_description\": \"Output of the error amplifier and input to the PWM comparator.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"EAIN\",\n        \"pin_description\": \"Inverting input to the error amplifier. The noninverting input is internally set to 2.5V.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"REF\",\n        \"pin_description\": \"Precision 5.0V reference pin.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"SS\",\n        \"pin_description\": \"A capacitor from SS to ground programs the soft start time.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"SHTDWN\",\n        \"pin_description\": \"Comparator input to stop the chip. The threshold is 0.5V.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS292D\",\n    \"family_comparison\": \"OUT2 is normal (non-inverted). VDD UVLO ON threshold is 9V.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"16V\",\n      \"min_input_voltage\": \"9V\",\n      \"max_output_voltage\": \"未找到\",\n      \"min_output_voltage\": \"未找到\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"430kHz\",\n      \"quiescent_current\": \"1.5mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"未找到\",\n      \"operation_mode\": \"Active Clamp\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"No\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"No\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"No\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"No\",\n      \"output_under_voltage_protection\": \"No\",\n      \"output_over_load_protection\": \"Latch\",\n      \"output_short_circuit_protection\": \"Latch\",\n      \"over_temperature_protection\": \"No\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"电压模式\",\n      \"output_voltage_accuracy\": \"±2.4%\",\n      \"output_reference_voltage\": \"2.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC1580-2\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Military\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PWM控制器\",\n    \"category_lv3\": \"有源钳位PWM控制器\",\n    \"part_number_title\": \"Single Ended Active Clamp/Reset PWM\",\n    \"features\": [\n      \"Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive\",\n      \"Programmable deadtime (Turn-on Delay) Between Activation of Each Switch\",\n      \"Voltage Mode Control with Feedforward Operation\",\n      \"Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle\",\n      \"High Current Gate Driver for Both Main and Auxiliary Outputs\",\n      \"Multiple Protection Features with Latched Shutdown and Soft Restart\",\n      \"Low Supply Current (100 μA Startup, 1.5 mA Operation)\"\n    ],\n    \"description\": \"The UCC1580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. This version (UCC1580-2) has a 15V VDD UVLO threshold and a normal polarity auxiliary driver (OUT2). It is specified for the military temperature range of -55°C to 125°C.\",\n    \"applications\": [\n      \"Active clamp/reset switching converter topologies\",\n      \"Synchronous rectifier switching converter topologies\",\n      \"Active clamp forward converter\",\n      \"Off-line active clamp flyback converter\"\n    ],\n    \"ordering_information\": [],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC1580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"DELAY\",\n        \"pin_description\": \"A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"LINE\",\n        \"pin_description\": \"Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VDD\",\n        \"pin_description\": \"Chip power supply pin. VDD should be bypassed to PGND.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OUT1\",\n        \"pin_description\": \"Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"PGND\",\n        \"pin_description\": \"Ground connection for the gate drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"OUT2\",\n        \"pin_description\": \"Gate drive output for the auxiliary switch with 0.3A drive current capability.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"CLK\",\n        \"pin_description\": \"Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"GND\",\n        \"pin_description\": \"Signal Ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"RAMP\",\n        \"pin_description\": \"A resistor (R4) and a capacitor (CR) program the feedforward ramp signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"OSC2\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC2 controls guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"OSC1\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC1 sets maximum on time.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"EAOUT\",\n        \"pin_description\": \"Output of the error amplifier and input to the PWM comparator.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"EAIN\",\n        \"pin_description\": \"Inverting input to the error amplifier. The noninverting input is internally set to 2.5V.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"REF\",\n        \"pin_description\": \"Precision 5.0V reference pin.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"SS\",\n        \"pin_description\": \"A capacitor from SS to ground programs the soft start time.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"SHTDWN\",\n        \"pin_description\": \"Comparator input to stop the chip. The threshold is 0.5V.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS292D\",\n    \"family_comparison\": \"OUT2 is normal (non-inverted). VDD UVLO ON threshold is 15V.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"16V\",\n      \"min_input_voltage\": \"15V\",\n      \"max_output_voltage\": \"未找到\",\n      \"min_output_voltage\": \"未找到\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"430kHz\",\n      \"quiescent_current\": \"1.5mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"未找到\",\n      \"operation_mode\": \"Active Clamp\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"No\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"No\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"No\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"No\",\n      \"output_under_voltage_protection\": \"No\",\n      \"output_over_load_protection\": \"Latch\",\n      \"output_short_circuit_protection\": \"Latch\",\n      \"over_temperature_protection\": \"No\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"电压模式\",\n      \"output_voltage_accuracy\": \"±2.4%\",\n      \"output_reference_voltage\": \"2.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC1580-3\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Military\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PWM控制器\",\n    \"category_lv3\": \"有源钳位PWM控制器\",\n    \"part_number_title\": \"Single Ended Active Clamp/Reset PWM\",\n    \"features\": [\n      \"Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive\",\n      \"Programmable deadtime (Turn-on Delay) Between Activation of Each Switch\",\n      \"Voltage Mode Control with Feedforward Operation\",\n      \"Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle\",\n      \"High Current Gate Driver for Both Main and Auxiliary Outputs\",\n      \"Multiple Protection Features with Latched Shutdown and Soft Restart\",\n      \"Low Supply Current (100 μA Startup, 1.5 mA Operation)\"\n    ],\n    \"description\": \"The UCC1580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. This version (UCC1580-3) has a 9V VDD UVLO threshold and an inverted polarity auxiliary driver (OUT2). It is specified for the military temperature range of -55°C to 125°C.\",\n    \"applications\": [\n      \"Active clamp/reset switching converter topologies\",\n      \"Synchronous rectifier switching converter topologies\",\n      \"Active clamp forward converter\",\n      \"Off-line active clamp flyback converter\"\n    ],\n    \"ordering_information\": [],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC1580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"DELAY\",\n        \"pin_description\": \"A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"LINE\",\n        \"pin_description\": \"Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VDD\",\n        \"pin_description\": \"Chip power supply pin. VDD should be bypassed to PGND.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OUT1\",\n        \"pin_description\": \"Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"PGND\",\n        \"pin_description\": \"Ground connection for the gate drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"OUT2\",\n        \"pin_description\": \"Gate drive output for the auxiliary switch with 0.3A drive current capability.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"CLK\",\n        \"pin_description\": \"Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"GND\",\n        \"pin_description\": \"Signal Ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"RAMP\",\n        \"pin_description\": \"A resistor (R4) and a capacitor (CR) program the feedforward ramp signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"OSC2\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC2 controls guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"OSC1\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC1 sets maximum on time.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"EAOUT\",\n        \"pin_description\": \"Output of the error amplifier and input to the PWM comparator.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"EAIN\",\n        \"pin_description\": \"Inverting input to the error amplifier. The noninverting input is internally set to 2.5V.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"REF\",\n        \"pin_description\": \"Precision 5.0V reference pin.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"SS\",\n        \"pin_description\": \"A capacitor from SS to ground programs the soft start time.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"SHTDWN\",\n        \"pin_description\": \"Comparator input to stop the chip. The threshold is 0.5V.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS292D\",\n    \"family_comparison\": \"OUT2 is inverted. VDD UVLO ON threshold is 9V.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"16V\",\n      \"min_input_voltage\": \"9V\",\n      \"max_output_voltage\": \"未找到\",\n      \"min_output_voltage\": \"未找到\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"430kHz\",\n      \"quiescent_current\": \"1.5mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"未找到\",\n      \"operation_mode\": \"Active Clamp\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"No\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"No\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"No\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"No\",\n      \"output_under_voltage_protection\": \"No\",\n      \"output_over_load_protection\": \"Latch\",\n      \"output_short_circuit_protection\": \"Latch\",\n      \"over_temperature_protection\": \"No\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"电压模式\",\n      \"output_voltage_accuracy\": \"±2.4%\",\n      \"output_reference_voltage\": \"2.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC1580-4\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Military\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PWM控制器\",\n    \"category_lv3\": \"有源钳位PWM控制器\",\n    \"part_number_title\": \"Single Ended Active Clamp/Reset PWM\",\n    \"features\": [\n      \"Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive\",\n      \"Programmable deadtime (Turn-on Delay) Between Activation of Each Switch\",\n      \"Voltage Mode Control with Feedforward Operation\",\n      \"Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle\",\n      \"High Current Gate Driver for Both Main and Auxiliary Outputs\",\n      \"Multiple Protection Features with Latched Shutdown and Soft Restart\",\n      \"Low Supply Current (100 μA Startup, 1.5 mA Operation)\"\n    ],\n    \"description\": \"The UCC1580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. This version (UCC1580-4) has a 15V VDD UVLO threshold and an inverted polarity auxiliary driver (OUT2). It is specified for the military temperature range of -55°C to 125°C.\",\n    \"applications\": [\n      \"Active clamp/reset switching converter topologies\",\n      \"Synchronous rectifier switching converter topologies\",\n      \"Active clamp forward converter\",\n      \"Off-line active clamp flyback converter\"\n    ],\n    \"ordering_information\": [],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC1580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"DELAY\",\n        \"pin_description\": \"A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"LINE\",\n        \"pin_description\": \"Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VDD\",\n        \"pin_description\": \"Chip power supply pin. VDD should be bypassed to PGND.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OUT1\",\n        \"pin_description\": \"Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"PGND\",\n        \"pin_description\": \"Ground connection for the gate drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"OUT2\",\n        \"pin_description\": \"Gate drive output for the auxiliary switch with 0.3A drive current capability.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"CLK\",\n        \"pin_description\": \"Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"GND\",\n        \"pin_description\": \"Signal Ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"RAMP\",\n        \"pin_description\": \"A resistor (R4) and a capacitor (CR) program the feedforward ramp signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"OSC2\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC2 controls guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"OSC1\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC1 sets maximum on time.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"EAOUT\",\n        \"pin_description\": \"Output of the error amplifier and input to the PWM comparator.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"EAIN\",\n        \"pin_description\": \"Inverting input to the error amplifier. The noninverting input is internally set to 2.5V.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"REF\",\n        \"pin_description\": \"Precision 5.0V reference pin.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"SS\",\n        \"pin_description\": \"A capacitor from SS to ground programs the soft start time.\"\n      },\n      {\n        \"product_part_number\": \"UCC1580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"SHTDWN\",\n        \"pin_description\": \"Comparator input to stop the chip. The threshold is 0.5V.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS292D\",\n    \"family_comparison\": \"OUT2 is inverted. VDD UVLO ON threshold is 15V.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"16V\",\n      \"min_input_voltage\": \"15V\",\n      \"max_output_voltage\": \"未找到\",\n      \"min_output_voltage\": \"未找到\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"430kHz\",\n      \"quiescent_current\": \"1.5mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"未找到\",\n      \"operation_mode\": \"Active Clamp\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"No\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"No\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"No\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"No\",\n      \"output_under_voltage_protection\": \"No\",\n      \"output_over_load_protection\": \"Latch\",\n      \"output_short_circuit_protection\": \"Latch\",\n      \"over_temperature_protection\": \"No\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"电压模式\",\n      \"output_voltage_accuracy\": \"±2.4%\",\n      \"output_reference_voltage\": \"2.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC2580-1\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Industry\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PWM控制器\",\n    \"category_lv3\": \"有源钳位PWM控制器\",\n    \"part_number_title\": \"Single Ended Active Clamp/Reset PWM\",\n    \"features\": [\n      \"Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive\",\n      \"Programmable deadtime (Turn-on Delay) Between Activation of Each Switch\",\n      \"Voltage Mode Control with Feedforward Operation\",\n      \"Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle\",\n      \"High Current Gate Driver for Both Main and Auxiliary Outputs\",\n      \"Multiple Protection Features with Latched Shutdown and Soft Restart\",\n      \"Low Supply Current (100 μA Startup, 1.5 mA Operation)\"\n    ],\n    \"description\": \"The UCC2580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. This version (UCC2580-1) has a 9V VDD UVLO threshold and a normal polarity auxiliary driver (OUT2). It is specified for the industrial temperature range of -40°C to 85°C.\",\n    \"applications\": [\n      \"Active clamp/reset switching converter topologies\",\n      \"Synchronous rectifier switching converter topologies\",\n      \"Active clamp forward converter\",\n      \"Off-line active clamp flyback converter\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UCC2580-1\",\n        \"order_device\": \"UCC2580D-1\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"D\",\n        \"carrier_description\": \"TUBE\",\n        \"carrier_quantity\": 40\n      },\n      {\n        \"part_number\": \"UCC2580-1\",\n        \"order_device\": \"UCC2580DTR-1\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"D\",\n        \"carrier_description\": \"LARGE T&R\",\n        \"carrier_quantity\": 2500\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC2580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"DELAY\",\n        \"pin_description\": \"A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"LINE\",\n        \"pin_description\": \"Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VDD\",\n        \"pin_description\": \"Chip power supply pin. VDD should be bypassed to PGND.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OUT1\",\n        \"pin_description\": \"Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"PGND\",\n        \"pin_description\": \"Ground connection for the gate drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"OUT2\",\n        \"pin_description\": \"Gate drive output for the auxiliary switch with 0.3A drive current capability.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"CLK\",\n        \"pin_description\": \"Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"GND\",\n        \"pin_description\": \"Signal Ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"RAMP\",\n        \"pin_description\": \"A resistor (R4) and a capacitor (CR) program the feedforward ramp signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"OSC2\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC2 controls guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"OSC1\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC1 sets maximum on time.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"EAOUT\",\n        \"pin_description\": \"Output of the error amplifier and input to the PWM comparator.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"EAIN\",\n        \"pin_description\": \"Inverting input to the error amplifier. The noninverting input is internally set to 2.5V.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"REF\",\n        \"pin_description\": \"Precision 5.0V reference pin.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"SS\",\n        \"pin_description\": \"A capacitor from SS to ground programs the soft start time.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"SHTDWN\",\n        \"pin_description\": \"Comparator input to stop the chip. The threshold is 0.5V.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS292D\",\n    \"family_comparison\": \"OUT2 is normal (non-inverted). VDD UVLO ON threshold is 9V.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"16V\",\n      \"min_input_voltage\": \"9V\",\n      \"max_output_voltage\": \"未找到\",\n      \"min_output_voltage\": \"未找到\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"430kHz\",\n      \"quiescent_current\": \"1.5mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"未找到\",\n      \"operation_mode\": \"Active Clamp\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"No\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"No\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"No\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"No\",\n      \"output_under_voltage_protection\": \"No\",\n      \"output_over_load_protection\": \"Latch\",\n      \"output_short_circuit_protection\": \"Latch\",\n      \"over_temperature_protection\": \"No\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"电压模式\",\n      \"output_voltage_accuracy\": \"±2.4%\",\n      \"output_reference_voltage\": \"2.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC2580-2\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Industry\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PWM控制器\",\n    \"category_lv3\": \"有源钳位PWM控制器\",\n    \"part_number_title\": \"Single Ended Active Clamp/Reset PWM\",\n    \"features\": [\n      \"Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive\",\n      \"Programmable deadtime (Turn-on Delay) Between Activation of Each Switch\",\n      \"Voltage Mode Control with Feedforward Operation\",\n      \"Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle\",\n      \"High Current Gate Driver for Both Main and Auxiliary Outputs\",\n      \"Multiple Protection Features with Latched Shutdown and Soft Restart\",\n      \"Low Supply Current (100 μA Startup, 1.5 mA Operation)\"\n    ],\n    \"description\": \"The UCC2580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. This version (UCC2580-2) has a 15V VDD UVLO threshold and a normal polarity auxiliary driver (OUT2). It is specified for the industrial temperature range of -40°C to 85°C.\",\n    \"applications\": [\n      \"Active clamp/reset switching converter topologies\",\n      \"Synchronous rectifier switching converter topologies\",\n      \"Active clamp forward converter\",\n      \"Off-line active clamp flyback converter\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UCC2580-2\",\n        \"order_device\": \"UCC2580DTR-2\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"D\",\n        \"carrier_description\": \"LARGE T&R\",\n        \"carrier_quantity\": 2500\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC2580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"DELAY\",\n        \"pin_description\": \"A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"LINE\",\n        \"pin_description\": \"Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VDD\",\n        \"pin_description\": \"Chip power supply pin. VDD should be bypassed to PGND.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OUT1\",\n        \"pin_description\": \"Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"PGND\",\n        \"pin_description\": \"Ground connection for the gate drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"OUT2\",\n        \"pin_description\": \"Gate drive output for the auxiliary switch with 0.3A drive current capability.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"CLK\",\n        \"pin_description\": \"Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"GND\",\n        \"pin_description\": \"Signal Ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"RAMP\",\n        \"pin_description\": \"A resistor (R4) and a capacitor (CR) program the feedforward ramp signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"OSC2\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC2 controls guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"OSC1\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC1 sets maximum on time.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"EAOUT\",\n        \"pin_description\": \"Output of the error amplifier and input to the PWM comparator.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"EAIN\",\n        \"pin_description\": \"Inverting input to the error amplifier. The noninverting input is internally set to 2.5V.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"REF\",\n        \"pin_description\": \"Precision 5.0V reference pin.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"SS\",\n        \"pin_description\": \"A capacitor from SS to ground programs the soft start time.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"SHTDWN\",\n        \"pin_description\": \"Comparator input to stop the chip. The threshold is 0.5V.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS292D\",\n    \"family_comparison\": \"OUT2 is normal (non-inverted). VDD UVLO ON threshold is 15V.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"16V\",\n      \"min_input_voltage\": \"15V\",\n      \"max_output_voltage\": \"未找到\",\n      \"min_output_voltage\": \"未找到\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"430kHz\",\n      \"quiescent_current\": \"1.5mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"未找到\",\n      \"operation_mode\": \"Active Clamp\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"No\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"No\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"No\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"No\",\n      \"output_under_voltage_protection\": \"No\",\n      \"output_over_load_protection\": \"Latch\",\n      \"output_short_circuit_protection\": \"Latch\",\n      \"over_temperature_protection\": \"No\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"电压模式\",\n      \"output_voltage_accuracy\": \"±2.4%\",\n      \"output_reference_voltage\": \"2.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC2580-3\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Industry\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PWM控制器\",\n    \"category_lv3\": \"有源钳位PWM控制器\",\n    \"part_number_title\": \"Single Ended Active Clamp/Reset PWM\",\n    \"features\": [\n      \"Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive\",\n      \"Programmable deadtime (Turn-on Delay) Between Activation of Each Switch\",\n      \"Voltage Mode Control with Feedforward Operation\",\n      \"Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle\",\n      \"High Current Gate Driver for Both Main and Auxiliary Outputs\",\n      \"Multiple Protection Features with Latched Shutdown and Soft Restart\",\n      \"Low Supply Current (100 μA Startup, 1.5 mA Operation)\"\n    ],\n    \"description\": \"The UCC2580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. This version (UCC2580-3) has a 9V VDD UVLO threshold and an inverted polarity auxiliary driver (OUT2). It is specified for the industrial temperature range of -40°C to 85°C.\",\n    \"applications\": [\n      \"Active clamp/reset switching converter topologies\",\n      \"Synchronous rectifier switching converter topologies\",\n      \"Active clamp forward converter\",\n      \"Off-line active clamp flyback converter\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UCC2580-3\",\n        \"order_device\": \"UCC2580D-3\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"D\",\n        \"carrier_description\": \"TUBE\",\n        \"carrier_quantity\": 40\n      },\n      {\n        \"part_number\": \"UCC2580-3\",\n        \"order_device\": \"UCC2580DTR-3\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"D\",\n        \"carrier_description\": \"LARGE T&R\",\n        \"carrier_quantity\": 2500\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC2580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"DELAY\",\n        \"pin_description\": \"A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"LINE\",\n        \"pin_description\": \"Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VDD\",\n        \"pin_description\": \"Chip power supply pin. VDD should be bypassed to PGND.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OUT1\",\n        \"pin_description\": \"Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"PGND\",\n        \"pin_description\": \"Ground connection for the gate drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"OUT2\",\n        \"pin_description\": \"Gate drive output for the auxiliary switch with 0.3A drive current capability.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"CLK\",\n        \"pin_description\": \"Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"GND\",\n        \"pin_description\": \"Signal Ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"RAMP\",\n        \"pin_description\": \"A resistor (R4) and a capacitor (CR) program the feedforward ramp signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"OSC2\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC2 controls guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"OSC1\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC1 sets maximum on time.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"EAOUT\",\n        \"pin_description\": \"Output of the error amplifier and input to the PWM comparator.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"EAIN\",\n        \"pin_description\": \"Inverting input to the error amplifier. The noninverting input is internally set to 2.5V.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"REF\",\n        \"pin_description\": \"Precision 5.0V reference pin.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"SS\",\n        \"pin_description\": \"A capacitor from SS to ground programs the soft start time.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"SHTDWN\",\n        \"pin_description\": \"Comparator input to stop the chip. The threshold is 0.5V.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS292D\",\n    \"family_comparison\": \"OUT2 is inverted. VDD UVLO ON threshold is 9V.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"16V\",\n      \"min_input_voltage\": \"9V\",\n      \"max_output_voltage\": \"未找到\",\n      \"min_output_voltage\": \"未找到\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"430kHz\",\n      \"quiescent_current\": \"1.5mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"未找到\",\n      \"operation_mode\": \"Active Clamp\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"No\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"No\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"No\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"No\",\n      \"output_under_voltage_protection\": \"No\",\n      \"output_over_load_protection\": \"Latch\",\n      \"output_short_circuit_protection\": \"Latch\",\n      \"over_temperature_protection\": \"No\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"电压模式\",\n      \"output_voltage_accuracy\": \"±2.4%\",\n      \"output_reference_voltage\": \"2.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC2580-4\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Industry\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PWM控制器\",\n    \"category_lv3\": \"有源钳位PWM控制器\",\n    \"part_number_title\": \"Single Ended Active Clamp/Reset PWM\",\n    \"features\": [\n      \"Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive\",\n      \"Programmable deadtime (Turn-on Delay) Between Activation of Each Switch\",\n      \"Voltage Mode Control with Feedforward Operation\",\n      \"Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle\",\n      \"High Current Gate Driver for Both Main and Auxiliary Outputs\",\n      \"Multiple Protection Features with Latched Shutdown and Soft Restart\",\n      \"Low Supply Current (100 μA Startup, 1.5 mA Operation)\"\n    ],\n    \"description\": \"The UCC2580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. This version (UCC2580-4) has a 15V VDD UVLO threshold and an inverted polarity auxiliary driver (OUT2). It is specified for the industrial temperature range of -40°C to 85°C.\",\n    \"applications\": [\n      \"Active clamp/reset switching converter topologies\",\n      \"Synchronous rectifier switching converter topologies\",\n      \"Active clamp forward converter\",\n      \"Off-line active clamp flyback converter\"\n    ],\n    \"ordering_information\": [],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC2580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"DELAY\",\n        \"pin_description\": \"A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"LINE\",\n        \"pin_description\": \"Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VDD\",\n        \"pin_description\": \"Chip power supply pin. VDD should be bypassed to PGND.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OUT1\",\n        \"pin_description\": \"Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"PGND\",\n        \"pin_description\": \"Ground connection for the gate drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"OUT2\",\n        \"pin_description\": \"Gate drive output for the auxiliary switch with 0.3A drive current capability.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"CLK\",\n        \"pin_description\": \"Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"GND\",\n        \"pin_description\": \"Signal Ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"RAMP\",\n        \"pin_description\": \"A resistor (R4) and a capacitor (CR) program the feedforward ramp signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"OSC2\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC2 controls guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"OSC1\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC1 sets maximum on time.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"EAOUT\",\n        \"pin_description\": \"Output of the error amplifier and input to the PWM comparator.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"EAIN\",\n        \"pin_description\": \"Inverting input to the error amplifier. The noninverting input is internally set to 2.5V.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"REF\",\n        \"pin_description\": \"Precision 5.0V reference pin.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"SS\",\n        \"pin_description\": \"A capacitor from SS to ground programs the soft start time.\"\n      },\n      {\n        \"product_part_number\": \"UCC2580-4\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"SHTDWN\",\n        \"pin_description\": \"Comparator input to stop the chip. The threshold is 0.5V.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS292D\",\n    \"family_comparison\": \"OUT2 is inverted. VDD UVLO ON threshold is 15V.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"16V\",\n      \"min_input_voltage\": \"15V\",\n      \"max_output_voltage\": \"未找到\",\n      \"min_output_voltage\": \"未找到\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"430kHz\",\n      \"quiescent_current\": \"1.5mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"未找到\",\n      \"operation_mode\": \"Active Clamp\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"No\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"No\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"No\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"No\",\n      \"output_under_voltage_protection\": \"No\",\n      \"output_over_load_protection\": \"Latch\",\n      \"output_short_circuit_protection\": \"Latch\",\n      \"over_temperature_protection\": \"No\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"电压模式\",\n      \"output_voltage_accuracy\": \"±2.4%\",\n      \"output_reference_voltage\": \"2.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC3580-1\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Consumer\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PWM控制器\",\n    \"category_lv3\": \"有源钳位PWM控制器\",\n    \"part_number_title\": \"Single Ended Active Clamp/Reset PWM\",\n    \"features\": [\n      \"Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive\",\n      \"Programmable deadtime (Turn-on Delay) Between Activation of Each Switch\",\n      \"Voltage Mode Control with Feedforward Operation\",\n      \"Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle\",\n      \"High Current Gate Driver for Both Main and Auxiliary Outputs\",\n      \"Multiple Protection Features with Latched Shutdown and Soft Restart\",\n      \"Low Supply Current (100 μA Startup, 1.5 mA Operation)\"\n    ],\n    \"description\": \"The UCC3580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. This version (UCC3580-1) has a 9V VDD UVLO threshold and a normal polarity auxiliary driver (OUT2). It is specified for the commercial temperature range of 0°C to 70°C.\",\n    \"applications\": [\n      \"Active clamp/reset switching converter topologies\",\n      \"Synchronous rectifier switching converter topologies\",\n      \"Active clamp forward converter\",\n      \"Off-line active clamp flyback converter\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UCC3580-1\",\n        \"order_device\": \"UCC3580DTR-1\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"D\",\n        \"carrier_description\": \"LARGE T&R\",\n        \"carrier_quantity\": 2500\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC3580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"DELAY\",\n        \"pin_description\": \"A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"LINE\",\n        \"pin_description\": \"Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VDD\",\n        \"pin_description\": \"Chip power supply pin. VDD should be bypassed to PGND.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OUT1\",\n        \"pin_description\": \"Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"PGND\",\n        \"pin_description\": \"Ground connection for the gate drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"OUT2\",\n        \"pin_description\": \"Gate drive output for the auxiliary switch with 0.3A drive current capability.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"CLK\",\n        \"pin_description\": \"Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"GND\",\n        \"pin_description\": \"Signal Ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"RAMP\",\n        \"pin_description\": \"A resistor (R4) and a capacitor (CR) program the feedforward ramp signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"OSC2\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC2 controls guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"OSC1\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC1 sets maximum on time.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"EAOUT\",\n        \"pin_description\": \"Output of the error amplifier and input to the PWM comparator.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"EAIN\",\n        \"pin_description\": \"Inverting input to the error amplifier. The noninverting input is internally set to 2.5V.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"REF\",\n        \"pin_description\": \"Precision 5.0V reference pin.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"SS\",\n        \"pin_description\": \"A capacitor from SS to ground programs the soft start time.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-1\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"SHTDWN\",\n        \"pin_description\": \"Comparator input to stop the chip. The threshold is 0.5V.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS292D\",\n    \"family_comparison\": \"OUT2 is normal (non-inverted). VDD UVLO ON threshold is 9V.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"16V\",\n      \"min_input_voltage\": \"9V\",\n      \"max_output_voltage\": \"未找到\",\n      \"min_output_voltage\": \"未找到\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"430kHz\",\n      \"quiescent_current\": \"1.5mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"未找到\",\n      \"operation_mode\": \"Active Clamp\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"No\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"No\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"No\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"No\",\n      \"output_under_voltage_protection\": \"No\",\n      \"output_over_load_protection\": \"Latch\",\n      \"output_short_circuit_protection\": \"Latch\",\n      \"over_temperature_protection\": \"No\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"电压模式\",\n      \"output_voltage_accuracy\": \"±2.4%\",\n      \"output_reference_voltage\": \"2.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC3580-2\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Consumer\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PWM控制器\",\n    \"category_lv3\": \"有源钳位PWM控制器\",\n    \"part_number_title\": \"Single Ended Active Clamp/Reset PWM\",\n    \"features\": [\n      \"Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive\",\n      \"Programmable deadtime (Turn-on Delay) Between Activation of Each Switch\",\n      \"Voltage Mode Control with Feedforward Operation\",\n      \"Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle\",\n      \"High Current Gate Driver for Both Main and Auxiliary Outputs\",\n      \"Multiple Protection Features with Latched Shutdown and Soft Restart\",\n      \"Low Supply Current (100 μA Startup, 1.5 mA Operation)\"\n    ],\n    \"description\": \"The UCC3580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. This version (UCC3580-2) has a 15V VDD UVLO threshold and a normal polarity auxiliary driver (OUT2). It is specified for the commercial temperature range of 0°C to 70°C.\",\n    \"applications\": [\n      \"Active clamp/reset switching converter topologies\",\n      \"Synchronous rectifier switching converter topologies\",\n      \"Active clamp forward converter\",\n      \"Off-line active clamp flyback converter\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UCC3580-2\",\n        \"order_device\": \"UCC3580DTR-2\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"D\",\n        \"carrier_description\": \"LARGE T&R\",\n        \"carrier_quantity\": 2500\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC3580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"DELAY\",\n        \"pin_description\": \"A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"LINE\",\n        \"pin_description\": \"Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VDD\",\n        \"pin_description\": \"Chip power supply pin. VDD should be bypassed to PGND.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OUT1\",\n        \"pin_description\": \"Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"PGND\",\n        \"pin_description\": \"Ground connection for the gate drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"OUT2\",\n        \"pin_description\": \"Gate drive output for the auxiliary switch with 0.3A drive current capability.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"CLK\",\n        \"pin_description\": \"Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"GND\",\n        \"pin_description\": \"Signal Ground.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"9\",\n        \"pin_name\": \"RAMP\",\n        \"pin_description\": \"A resistor (R4) and a capacitor (CR) program the feedforward ramp signal.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"10\",\n        \"pin_name\": \"OSC2\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC2 controls guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"11\",\n        \"pin_name\": \"OSC1\",\n        \"pin_description\": \"Oscillator programming pin. The resistor connected to OSC1 sets maximum on time.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"12\",\n        \"pin_name\": \"EAOUT\",\n        \"pin_description\": \"Output of the error amplifier and input to the PWM comparator.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"13\",\n        \"pin_name\": \"EAIN\",\n        \"pin_description\": \"Inverting input to the error amplifier. The noninverting input is internally set to 2.5V.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"14\",\n        \"pin_name\": \"REF\",\n        \"pin_description\": \"Precision 5.0V reference pin.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"15\",\n        \"pin_name\": \"SS\",\n        \"pin_description\": \"A capacitor from SS to ground programs the soft start time.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-2\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"16\",\n        \"pin_name\": \"SHTDWN\",\n        \"pin_description\": \"Comparator input to stop the chip. The threshold is 0.5V.\"\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS292D\",\n    \"family_comparison\": \"OUT2 is normal (non-inverted). VDD UVLO ON threshold is 15V.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"16V\",\n      \"min_input_voltage\": \"15V\",\n      \"max_output_voltage\": \"未找到\",\n      \"min_output_voltage\": \"未找到\",\n      \"max_output_current\": \"不适用\",\n      \"max_switch_frequency\": \"430kHz\",\n      \"quiescent_current\": \"1.5mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"未找到\",\n      \"operation_mode\": \"Active Clamp\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"No\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"No\",\n      \"power_good_indicator\": \"No\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"No\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"No\",\n      \"output_under_voltage_protection\": \"No\",\n      \"output_over_load_protection\": \"Latch\",\n      \"output_short_circuit_protection\": \"Latch\",\n      \"over_temperature_protection\": \"No\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"电压模式\",\n      \"output_voltage_accuracy\": \"±2.4%\",\n      \"output_reference_voltage\": \"2.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UCC3580-3\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Consumer\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PWM控制器\",\n    \"category_lv3\": \"有源钳位PWM控制器\",\n    \"part_number_title\": \"Single Ended Active Clamp/Reset PWM\",\n    \"features\": [\n      \"Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive\",\n      \"Programmable deadtime (Turn-on Delay) Between Activation of Each Switch\",\n      \"Voltage Mode Control with Feedforward Operation\",\n      \"Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle\",\n      \"High Current Gate Driver for Both Main and Auxiliary Outputs\",\n      \"Multiple Protection Features with Latched Shutdown and Soft Restart\",\n      \"Low Supply Current (100 μA Startup, 1.5 mA Operation)\"\n    ],\n    \"description\": \"The UCC3580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. This version (UCC3580-3) has a 9V VDD UVLO threshold and an inverted polarity auxiliary driver (OUT2). It is specified for the commercial temperature range of 0°C to 70°C.\",\n    \"applications\": [\n      \"Active clamp/reset switching converter topologies\",\n      \"Synchronous rectifier switching converter topologies\",\n      \"Active clamp forward converter\",\n      \"Off-line active clamp flyback converter\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UCC3580-3\",\n        \"order_device\": \"UCC3580DTR-3\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"D\",\n        \"carrier_description\": \"LARGE T&R\",\n        \"carrier_quantity\": 2500\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCC3580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"1\",\n        \"pin_name\": \"DELAY\",\n        \"pin_description\": \"A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"2\",\n        \"pin_name\": \"LINE\",\n        \"pin_description\": \"Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"3\",\n        \"pin_name\": \"VDD\",\n        \"pin_description\": \"Chip power supply pin. VDD should be bypassed to PGND.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"4\",\n        \"pin_name\": \"OUT1\",\n        \"pin_description\": \"Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"5\",\n        \"pin_name\": \"PGND\",\n        \"pin_description\": \"Ground connection for the gate drivers.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"6\",\n        \"pin_name\": \"OUT2\",\n        \"pin_description\": \"Gate drive output for the auxiliary switch with 0.3A drive current capability.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"7\",\n        \"pin_name\": \"CLK\",\n        \"pin_description\": \"Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time.\"\n      },\n      {\n        \"product_part_number\": \"UCC3580-3\",\n        \"package_type\": \"DIL-16/SOIC-16\",\n        \"pin_number\": \"8\",\n        \"pin_name\": \"GND\",\n        \"pin_description\": \"Signal Ground.\"\n      },\n      {\n        \"product_part_number\": \"U", "package": [{"type": "OPTION", "pin_count": "6", "pitch": "40", "height": "0.3", "width": "50", "length": "40"}]}