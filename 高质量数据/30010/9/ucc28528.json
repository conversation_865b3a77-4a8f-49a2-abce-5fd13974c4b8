[{"part_number": "UCC28521", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Obsolete", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC/PWM控制器", "category_lv3": "组合式PFC/PWM控制器", "part_number_title": "ADVANCED PFC/PWM COMBINATION CONTROLLER WITH TRAILING-EDGE/TRAILING-EDGE MODULATION", "features": ["Provides Control of PFC and PWM Power Stages In One Device", "Trailing-Edge PFC, Trailing-Edge PWM Modulation", "Built-In Sequencing of PFC and PWM Turn-On", "2-A Source and 3-A Sink Gate Drive for Both PFC and PWM Stages", "Typical 16-ns Rise Time and 7-ns Fall Time into 1-nF Loads", "PFC: Average-Current-Mode Control for Continuous Conduction Mode Operation", "PFC: Highly-Linear Multiplier for Near-Unity Power Factor", "PFC: Input Voltage Feedforward Implementation", "PFC: Improved Load Transient Response and Accurate Power Limiting", "PWM: Peak-Current-Mode Control Operation", "PWM: Programmable maximum Duty Cycle Limit Up to 90%", "PWM: Programmable Soft-Start"], "description": "The UCC28521 is a combination PFC/PWM controller that provides complete control for off-line power systems requiring harmonic reduction compliance. It employs an average current mode control architecture with trailing-edge modulation for both PFC and PWM stages. A key feature of the UCC28521 is that its PWM stage shuts off when the PFC bulk voltage drops to 71% of its nominal value, making it suitable for main AC-DC and DC-DC converters.", "applications": ["High-Efficiency Server and Desktop Supplies", "High-Efficiency Telecom AC-DC Converter", "ac-dc and main dc-dc converter"], "ordering_information": [{"part_number": "UCC28521", "order_device": "UCC28521DW", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "UCC28521", "package_type": "DW", "pins": [{"pin_number": "1", "pin_name": "VAOUT", "pin_description": "Output of the PFC transconductance voltage amplifier and it is internally connected to the Zero Power Detect comparator input and the multiplier input"}, {"pin_number": "2", "pin_name": "RT", "pin_description": "Oscillator programming pin that is set with a single resistor to GND"}, {"pin_number": "3", "pin_name": "VSENSE", "pin_description": "Inverting input to the PFC transconductance voltage amplifier, and input to the OVP, ENABLE and UVLO2 comparators"}, {"pin_number": "4", "pin_name": "D_MAX", "pin_description": "Positive input to set the maximum duty cycle clamp level of the PWM stage duty ratio can be between 0.09 and 0.90."}, {"pin_number": "5", "pin_name": "CT_BUFF", "pin_description": "Internally buffered PWM stage oscillator ramp output, typically used to program slope compensation with a single resistor"}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Analog ground"}, {"pin_number": "7", "pin_name": "VERR", "pin_description": "Feedback error voltage input for the PWM stage, typically connected to an optocoupler output"}, {"pin_number": "8", "pin_name": "ISENSE2", "pin_description": "Input for PWM stage current sense and peak current limit"}, {"pin_number": "9", "pin_name": "VCC", "pin_description": "Positive supply voltage pin"}, {"pin_number": "10", "pin_name": "GT2", "pin_description": "PWM stage gate drive output"}, {"pin_number": "11", "pin_name": "PWRGND", "pin_description": "Power ground for GT1, GT2 and high current return paths"}, {"pin_number": "12", "pin_name": "GT1", "pin_description": "PFC stage gate drive output"}, {"pin_number": "13", "pin_name": "SS2", "pin_description": "Soft start for the PWM stage"}, {"pin_number": "14", "pin_name": "PKLMT", "pin_description": "Voltage input to the PFC peak current limit comparator"}, {"pin_number": "15", "pin_name": "CAOUT", "pin_description": "Output of the current control amplifier of the PFC stage. CAOUT is internally connected to the PWM comparator input in the PFC stage"}, {"pin_number": "16", "pin_name": "ISENSE1", "pin_description": "Non-inverting input to the PFC stage current amplifier"}, {"pin_number": "17", "pin_name": "MOUT", "pin_description": "PFC multiplier high-impedance current output, internally connected to the current amplifier inverting input"}, {"pin_number": "18", "pin_name": "IAC", "pin_description": "Multiplier current input that is proportional to the instantaneous rectified line voltage"}, {"pin_number": "19", "pin_name": "VFF", "pin_description": "Voltage feedforward pin for the PFC stage, sources an IAC/2 current that should be externally filtered"}, {"pin_number": "20", "pin_name": "VREF", "pin_description": "Precision 7.5-V reference output"}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS608D, November 2005", "family_comparison": "UCC28521: PWM shuts off at 71% of the nominal bulk voltage. Application: ac-dc and main dc-dc converter. | UCC28528: PWM does not turn off with falling bulk voltage. Application: ac-dc and standby converter.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 2, "max_input_voltage": "18V", "min_input_voltage": "10.8V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "可调", "max_switch_frequency": "0.24MHz", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "可调", "operation_mode": "Trailing-Edge Modulation", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "External", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "PFC: Average Current Mode, PWM: Peak Current Mode", "output_voltage_accuracy": "±2%", "output_reference_voltage": "7.5V"}, "package": [{"type": "SOIC", "pin_count": "2", "pitch": "1.27", "height": "2.65", "width": "7.6", "length": "13.0"}]}, {"part_number": "UCC28528", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC/PWM控制器", "category_lv3": "组合式PFC/PWM控制器", "part_number_title": "ADVANCED PFC/PWM COMBINATION CONTROLLER WITH TRAILING-EDGE/TRAILING-EDGE MODULATION", "features": ["Provides Control of PFC and PWM Power Stages In One Device", "Trailing-Edge PFC, Trailing-Edge PWM Modulation", "Built-In Sequencing of PFC and PWM Turn-On", "2-A Source and 3-A Sink Gate Drive for Both PFC and PWM Stages", "Typical 16-ns Rise Time and 7-ns Fall Time into 1-nF Loads", "PFC: Average-Current-Mode Control for Continuous Conduction Mode Operation", "PFC: Highly-Linear Multiplier for Near-Unity Power Factor", "PFC: Input Voltage Feedforward Implementation", "PFC: Improved Load Transient Response and Accurate Power Limiting", "PWM: Peak-Current-Mode Control Operation", "PWM: Programmable maximum Duty Cycle Limit Up to 90%", "PWM: Programmable Soft-Start"], "description": "The UCC28528 is a combination PFC/PWM controller that provides complete control for off-line power systems requiring harmonic reduction compliance. It employs an average current mode control architecture with trailing-edge modulation for both PFC and PWM stages. The UCC28528 differs from the UCC28521 as its PWM stage does not turn off with falling bulk voltage, allowing it to operate continuously until VCC drops below the UVLO threshold. This makes it ideal for low power auxiliary and standby supplies.", "applications": ["High-Efficiency Server and Desktop Supplies", "High-Efficiency Telecom AC-DC Converter", "ac-dc and standby converter"], "ordering_information": [{"part_number": "UCC28528", "order_device": "UCC28528DW", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "105"}, {"part_number": "UCC28528", "order_device": "UCC28528DW.A", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "105"}], "pin_function": [{"product_part_number": "UCC28528", "package_type": "DW", "pins": [{"pin_number": "1", "pin_name": "VAOUT", "pin_description": "Output of the PFC transconductance voltage amplifier and it is internally connected to the Zero Power Detect comparator input and the multiplier input"}, {"pin_number": "2", "pin_name": "RT", "pin_description": "Oscillator programming pin that is set with a single resistor to GND"}, {"pin_number": "3", "pin_name": "VSENSE", "pin_description": "Inverting input to the PFC transconductance voltage amplifier, and input to the OVP, ENABLE and UVLO2 comparators"}, {"pin_number": "4", "pin_name": "D_MAX", "pin_description": "Positive input to set the maximum duty cycle clamp level of the PWM stage duty ratio can be between 0.09 and 0.90."}, {"pin_number": "5", "pin_name": "CT_BUFF", "pin_description": "Internally buffered PWM stage oscillator ramp output, typically used to program slope compensation with a single resistor"}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Analog ground"}, {"pin_number": "7", "pin_name": "VERR", "pin_description": "Feedback error voltage input for the PWM stage, typically connected to an optocoupler output"}, {"pin_number": "8", "pin_name": "ISENSE2", "pin_description": "Input for PWM stage current sense and peak current limit"}, {"pin_number": "9", "pin_name": "VCC", "pin_description": "Positive supply voltage pin"}, {"pin_number": "10", "pin_name": "GT2", "pin_description": "PWM stage gate drive output"}, {"pin_number": "11", "pin_name": "PWRGND", "pin_description": "Power ground for GT1, GT2 and high current return paths"}, {"pin_number": "12", "pin_name": "GT1", "pin_description": "PFC stage gate drive output"}, {"pin_number": "13", "pin_name": "SS2", "pin_description": "Soft start for the PWM stage"}, {"pin_number": "14", "pin_name": "PKLMT", "pin_description": "Voltage input to the PFC peak current limit comparator"}, {"pin_number": "15", "pin_name": "CAOUT", "pin_description": "Output of the current control amplifier of the PFC stage. CAOUT is internally connected to the PWM comparator input in the PFC stage"}, {"pin_number": "16", "pin_name": "ISENSE1", "pin_description": "Non-inverting input to the PFC stage current amplifier"}, {"pin_number": "17", "pin_name": "MOUT", "pin_description": "PFC multiplier high-impedance current output, internally connected to the current amplifier inverting input"}, {"pin_number": "18", "pin_name": "IAC", "pin_description": "Multiplier current input that is proportional to the instantaneous rectified line voltage"}, {"pin_number": "19", "pin_name": "VFF", "pin_description": "Voltage feedforward pin for the PFC stage, sources an IAC/2 current that should be externally filtered"}, {"pin_number": "20", "pin_name": "VREF", "pin_description": "Precision 7.5-V reference output"}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS608D, November 2005", "family_comparison": "UCC28521: PWM shuts off at 71% of the nominal bulk voltage. Application: ac-dc and main dc-dc converter. | UCC28528: PWM does not turn off with falling bulk voltage. Application: ac-dc and standby converter.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 2, "max_input_voltage": "18V", "min_input_voltage": "10.8V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "可调", "max_switch_frequency": "0.24MHz", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "可调", "operation_mode": "Trailing-Edge Modulation", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "External", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "PFC: Average Current Mode, PWM: Peak Current Mode", "output_voltage_accuracy": "±2%", "output_reference_voltage": "7.5V"}, "package": [{"type": "SOIC", "pin_count": "2", "pitch": "1.27", "height": "2.65", "width": "7.6", "length": "13.0"}]}]