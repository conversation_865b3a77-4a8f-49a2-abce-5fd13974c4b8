[{"part_number": "UCC2810", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电流模式PWM控制器", "part_number_title": "DUAL CHANNEL SYNCHRONIZED CURRENT-MODE PWM", "features": ["Single Oscillator Synchronizes Two PWMS", "150-μΑ Startup Supply Current", "2-mA Operating Supply Current", "Operation to 1 MHz", "Internal Soft-Start", "Full-Cycle Fault Restart", "Internal Leading-Edge Blanking of the Current Sense Signal", "1-A Totem Pole Outputs", "75-ns Typical Response from Current Sense to Output", "1.5% Tolerance Voltage Reference"], "description": "The UCC3810 is a high-speed BiCMOS controller integrating two synchronized pulse width modulators for use in off-line and dc-to-dc power supplies. The UCC3810 family provides perfect synchronization between two PWMs by using the same oscillator. The oscillator's sawtooth waveform can be used for slope compensation if required. Using a toggle flip-flop to alternate between modulators, the UCC3810 ensures that one PWM does not slave, interfere, or otherwise affect the other PWM. This toggle flip-flop also ensures that each PWM is limited to 50% maximum duty cycle, insuring adequate off-time to reset magnetic elements. This device contains many of the same elements of the UC3842 current mode controller family, combined with the enhancements of the UCC3802. This minimizes power supply parts count. Enhancements include leading edge blanking of the current sense signals, full cycle fault restart, CMOS output drivers, and outputs which remain low even when the supply voltage is removed.", "applications": ["off-line power supplies", "dc-to-dc power supplies"], "ordering_information": [{"part_number": "UCC2810", "order_device": "UCC2810DW", "package_type": "SOIC", "package_drawing_code": "DW", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC2810", "order_device": "UCC2810N", "package_type": "PDIP", "package_drawing_code": "N", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC2810", "order_device": "UCC2810DWTR", "package_type": "SOIC", "package_drawing_code": "DW", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "UCC2810", "package_type": "N/DW", "pins": [{"pin_number": "1", "pin_name": "SYNC", "pin_description": "This logic input can be used to synchronize the oscillator to a free running oscillator in another part. This pin is edge triggered with TTL thresholds, and requires at least a 10-ns-wide pulse. If unused, this pin can be grounded, open circuited, or connected to REF."}, {"pin_number": "2", "pin_name": "CT", "pin_description": "The timing capacitor of the oscillator. Recommended values of CT are between 100 pF and 1 nF. Connect the timing capacitor directly across CT and GND."}, {"pin_number": "3", "pin_name": "RT", "pin_description": "The oscillator charging current is set by the value of the resistor connected from RT to GND. This pin is regulated to 1 V, but the actual charging current is 10 V/RT. Recommended values of RT are between 10 kΩ and 470 kΩ. For a given frequency, higher timing resistors give higher maximum duty cycle and slightly lower overall power consumption."}, {"pin_number": "4", "pin_name": "FB1", "pin_description": "The high impedance inverting inputs of the error amplifiers."}, {"pin_number": "5", "pin_name": "COMP1", "pin_description": "Low impedance output of the error amplifiers."}, {"pin_number": "6", "pin_name": "CS1", "pin_description": "Current sense inputs to the PWM comparators. These inputs have leading edge blanking. For most applications, no input filtering is required. Leading edge blanking disconnects the CS inputs from all internal circuits for the first 55 ns of each PWM cycle. When used with very slow diodes or in other applications where the current sense signal is unusually noisy, a small current-sense R-C filter may be required."}, {"pin_number": "7", "pin_name": "OUT1", "pin_description": "The high-current push-pull outputs of the PWM are intended to drive power MOSFET gates through a small resistor. This resistor acts as both a current limiting resistor and as a damping impedance to minimize ringing and overshoot."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "To separate noise from the critical control circuits, this part has two different ground connections: GND and PWRGND. GND and PWRGND must be electrically connected together. However, use care to avoid coupling noise into GND."}, {"pin_number": "9", "pin_name": "PWRGND", "pin_description": "To separate noise from the critical control circuits, this part has two different ground connections: GND and PWRGND. GND and PWRGND must be electrically connected together."}, {"pin_number": "10", "pin_name": "OUT2", "pin_description": "The high-current push-pull outputs of the PWM are intended to drive power MOSFET gates through a small resistor. This resistor acts as both a current limiting resistor and as a damping impedance to minimize ringing and overshoot."}, {"pin_number": "11", "pin_name": "CS2", "pin_description": "Current sense inputs to the PWM comparators. These inputs have leading edge blanking. For most applications, no input filtering is required. Leading edge blanking disconnects the CS inputs from all internal circuits for the first 55 ns of each PWM cycle. When used with very slow diodes or in other applications where the current sense signal is unusually noisy, a small current-sense R-C filter may be required."}, {"pin_number": "12", "pin_name": "COMP2", "pin_description": "Low impedance output of the error amplifiers."}, {"pin_number": "13", "pin_name": "FB2", "pin_description": "The high impedance inverting inputs of the error amplifiers."}, {"pin_number": "14", "pin_name": "ENABLE2", "pin_description": "A logic input which disables PWM 2 when low. This input has no effect on PWM 1. This input is internally pulled high. In most applications it can be left floating. In unusually noisy applications, the input should be bypassed with a 1-nF ceramic capacitor. This input has TTL compatible thresholds."}, {"pin_number": "15", "pin_name": "REF", "pin_description": "The output of the 5-V reference. Bypass REF to GND with a ceramic capacitor ≥ 0.01-µF for best performance."}, {"pin_number": "16", "pin_name": "VCC", "pin_description": "The power input to the device. This pin supplies current to all functions including the high current output stages and the precision reference. Therefore, it is critical that VCC be directly bypassed to PWRGND with an 0.1-µF ceramic capacitor."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS162D", "family_comparison": "UCC2810 and UCC3810 are part of the same family, with the primary difference being the operating temperature range. UCC2810 operates from -40°C to 85°C, while UCC3810 operates from 0°C to 70°C. The family combines elements of the UC3842 current mode controller family with enhancements from the UCC3802.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 2, "max_input_voltage": "11V", "min_input_voltage": "7.1V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "1MHz", "quiescent_current": "2mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.55V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "1.5%", "output_reference_voltage": "2.5V"}, "package": [{"type": "SOIC", "pitch": "12.83", "height": "2.7", "length": "10.7", "width": "12.83", "pin_count": "2"}]}, {"part_number": "UCC3810", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电流模式PWM控制器", "part_number_title": "DUAL CHANNEL SYNCHRONIZED CURRENT-MODE PWM", "features": ["Single Oscillator Synchronizes Two PWMS", "150-μΑ Startup Supply Current", "2-mA Operating Supply Current", "Operation to 1 MHz", "Internal Soft-Start", "Full-Cycle Fault Restart", "Internal Leading-Edge Blanking of the Current Sense Signal", "1-A Totem Pole Outputs", "75-ns Typical Response from Current Sense to Output", "1.5% Tolerance Voltage Reference"], "description": "The UCC3810 is a high-speed BiCMOS controller integrating two synchronized pulse width modulators for use in off-line and dc-to-dc power supplies. The UCC3810 family provides perfect synchronization between two PWMs by using the same oscillator. The oscillator's sawtooth waveform can be used for slope compensation if required. Using a toggle flip-flop to alternate between modulators, the UCC3810 ensures that one PWM does not slave, interfere, or otherwise affect the other PWM. This toggle flip-flop also ensures that each PWM is limited to 50% maximum duty cycle, insuring adequate off-time to reset magnetic elements. This device contains many of the same elements of the UC3842 current mode controller family, combined with the enhancements of the UCC3802. This minimizes power supply parts count. Enhancements include leading edge blanking of the current sense signals, full cycle fault restart, CMOS output drivers, and outputs which remain low even when the supply voltage is removed.", "applications": ["off-line power supplies", "dc-to-dc power supplies"], "ordering_information": [{"part_number": "UCC3810", "order_device": "UCC3810DW", "package_type": "SOIC", "package_drawing_code": "DW", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC3810", "order_device": "UCC3810N", "package_type": "PDIP", "package_drawing_code": "N", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC3810", "order_device": "UCC3810DWTR", "package_type": "SOIC", "package_drawing_code": "DW", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "UCC3810", "package_type": "N/DW", "pins": [{"pin_number": "1", "pin_name": "SYNC", "pin_description": "This logic input can be used to synchronize the oscillator to a free running oscillator in another part. This pin is edge triggered with TTL thresholds, and requires at least a 10-ns-wide pulse. If unused, this pin can be grounded, open circuited, or connected to REF."}, {"pin_number": "2", "pin_name": "CT", "pin_description": "The timing capacitor of the oscillator. Recommended values of CT are between 100 pF and 1 nF. Connect the timing capacitor directly across CT and GND."}, {"pin_number": "3", "pin_name": "RT", "pin_description": "The oscillator charging current is set by the value of the resistor connected from RT to GND. This pin is regulated to 1 V, but the actual charging current is 10 V/RT. Recommended values of RT are between 10 kΩ and 470 kΩ. For a given frequency, higher timing resistors give higher maximum duty cycle and slightly lower overall power consumption."}, {"pin_number": "4", "pin_name": "FB1", "pin_description": "The high impedance inverting inputs of the error amplifiers."}, {"pin_number": "5", "pin_name": "COMP1", "pin_description": "Low impedance output of the error amplifiers."}, {"pin_number": "6", "pin_name": "CS1", "pin_description": "Current sense inputs to the PWM comparators. These inputs have leading edge blanking. For most applications, no input filtering is required. Leading edge blanking disconnects the CS inputs from all internal circuits for the first 55 ns of each PWM cycle. When used with very slow diodes or in other applications where the current sense signal is unusually noisy, a small current-sense R-C filter may be required."}, {"pin_number": "7", "pin_name": "OUT1", "pin_description": "The high-current push-pull outputs of the PWM are intended to drive power MOSFET gates through a small resistor. This resistor acts as both a current limiting resistor and as a damping impedance to minimize ringing and overshoot."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "To separate noise from the critical control circuits, this part has two different ground connections: GND and PWRGND. GND and PWRGND must be electrically connected together. However, use care to avoid coupling noise into GND."}, {"pin_number": "9", "pin_name": "PWRGND", "pin_description": "To separate noise from the critical control circuits, this part has two different ground connections: GND and PWRGND. GND and PWRGND must be electrically connected together."}, {"pin_number": "10", "pin_name": "OUT2", "pin_description": "The high-current push-pull outputs of the PWM are intended to drive power MOSFET gates through a small resistor. This resistor acts as both a current limiting resistor and as a damping impedance to minimize ringing and overshoot."}, {"pin_number": "11", "pin_name": "CS2", "pin_description": "Current sense inputs to the PWM comparators. These inputs have leading edge blanking. For most applications, no input filtering is required. Leading edge blanking disconnects the CS inputs from all internal circuits for the first 55 ns of each PWM cycle. When used with very slow diodes or in other applications where the current sense signal is unusually noisy, a small current-sense R-C filter may be required."}, {"pin_number": "12", "pin_name": "COMP2", "pin_description": "Low impedance output of the error amplifiers."}, {"pin_number": "13", "pin_name": "FB2", "pin_description": "The high impedance inverting inputs of the error amplifiers."}, {"pin_number": "14", "pin_name": "ENABLE2", "pin_description": "A logic input which disables PWM 2 when low. This input has no effect on PWM 1. This input is internally pulled high. In most applications it can be left floating. In unusually noisy applications, the input should be bypassed with a 1-nF ceramic capacitor. This input has TTL compatible thresholds."}, {"pin_number": "15", "pin_name": "REF", "pin_description": "The output of the 5-V reference. Bypass REF to GND with a ceramic capacitor ≥ 0.01-µF for best performance."}, {"pin_number": "16", "pin_name": "VCC", "pin_description": "The power input to the device. This pin supplies current to all functions including the high current output stages and the precision reference. Therefore, it is critical that VCC be directly bypassed to PWRGND with an 0.1-µF ceramic capacitor."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS162D", "family_comparison": "UCC2810 and UCC3810 are part of the same family, with the primary difference being the operating temperature range. UCC2810 operates from -40°C to 85°C, while UCC3810 operates from 0°C to 70°C. The family combines elements of the UC3842 current mode controller family with enhancements from the UCC3802.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 2, "max_input_voltage": "11V", "min_input_voltage": "7.1V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "1MHz", "quiescent_current": "2mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.55V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "1.5%", "output_reference_voltage": "2.5V"}, "package": [{"type": "SOIC", "pitch": "12.83", "height": "2.7", "length": "10.7", "width": "12.83", "pin_count": "2"}]}]