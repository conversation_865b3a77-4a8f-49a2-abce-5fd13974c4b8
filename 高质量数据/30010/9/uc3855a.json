[{"part_number": "UC2855A", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "高频PFC控制器", "part_number_title": "High Performance Power Factor Preregulator", "features": ["Controls Boost PWM to Near Unity Power Factor", "Fixed Frequency Average Current Mode Control Minimizes Line Current Distortion", "Built-in Active Snubber (ZVT) allows Operation to 500kHz, improved EMI and Efficiency", "Inductor Current Synthesizer allows Single Current Transformer Current Sense for Improved Efficiency and Noise Margin", "Accurate Analog Multiplier with Line Compensator allows for Universal Input Voltage Operation", "High Bandwidth (5MHz), Low Offset Current Amplifier", "Overvoltage and Overcurrent protection", "Two UVLO Threshold Options", "150μA Startup Supply Current Typical", "Precision 1% 7.5V Reference"], "description": "The UC3855A/B provides all the control features necessary for high power, high frequency PFC boost converters. The average current mode control method allows for stable, low distortion AC line current programming without the need for slope compensation. In addition, the UC3855 utilizes an active snubbing or ZVT (Zero Voltage Transition technique) to dramatically reduce diode recovery and MOSFET turn-on losses, resulting in lower EMI emissions and higher efficiency. Boost converter switching frequencies up to 500kHz are now realizable, requiring only an additional small MOSFET, diode, and inductor to resonantly soft switch the boost diode and switch. Average current sensing can be employed using a simple resistive shunt or a current sense transformer. Using the current sense transformer method, the internal current synthesizer circuit buffers the inductor current during the switch on-time, and reconstructs the inductor current during the switch off-time. Improved signal to noise ratio and negligible current sensing losses make this an attractive solution for higher power applications.\nThe UC3855A/B also features a single quadrant multiplier, squarer, and divider circuit which provides the programming signal for the current loop. The internal multiplier current limit reduces output power during low line conditions. An overvoltage protection circuit disables both controller outputs in the event of a boost output OV condition.\nLow startup supply current, UVLO with hysteresis, a 1% 7.5V reference, voltage amplifier with softstart, input supply voltage clamp, enable comparator, and overcurrent comparator complete the list of features. Available packages include: 20 pin N, DW, Q, J, and L.", "applications": ["High Power PFC Boost Converters", "Universal Input Power Systems", "High-Efficiency Power Supplies"], "ordering_information": [{"part_number": "UC2855A", "order_device": "UC2855ADW", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UC2855A", "order_device": "UC2855ADW.A", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "UC2855A", "package_type": "All Packages", "pins": [{"pin_number": "1 (DIL/SOIC)", "pin_name": "CAO", "pin_description": "This is the output of the wide bandwidth current amplifier and one of the inputs to the PWM duty cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1V to 7.5V."}, {"pin_number": "2 (DIL/SOIC)", "pin_name": "RVS", "pin_description": "The nominal 3V signal present on the VSENSE pin is buffered and brought out to the RVS pin. A current proportional to the output voltage is generated by connecting a resistor between this pin and GND. This current forms the second input to the current synthesizer."}, {"pin_number": "3 (DIL/SOIC)", "pin_name": "CI", "pin_description": "The level shifted current sense signal is impressed upon a capacitor connected between this pin and GND. The buffered current sense transformer signal charges the capacitor when the boost switch is on. When the switch is off, the current synthesizer discharges the capacitor at a rate proportional to the dI/dt of the boost inductor current."}, {"pin_number": "4 (DIL/SOIC)", "pin_name": "ION", "pin_description": "This pin is the current sensing input. It should be connected to the secondary side output of a current sensing transformer whose primary winding is in series with the boost switch. The resultant signal applied to this input is buffered and level shifted up a diode to the CI capacitor on the CI pin."}, {"pin_number": "5 (DIL/SOIC)", "pin_name": "CS", "pin_description": "The reconstructed inductor current waveform generated on the CI pin is level shifted down a diode drop to this pin. The waveform on this pin is compared to the multiplier output waveform through the average current sensing current amplifier. The input to the peak current limiting comparator is also connected to this pin. A voltage level greater than 1.5 volts on this pin will trip the comparator and disable the gate driver output."}, {"pin_number": "6 (DIL/SOIC)", "pin_name": "VRMS", "pin_description": "This pin is the feedforward line voltage compensation input to the multiplier. A voltage on VRMS proportional to the AC input RMS voltage commands the multiplier to alter the current command signal by 1/VRMS^2 to maintain a constant power balance. The input range for this pin extends from 0 to 5.5V."}, {"pin_number": "7 (DIL/SOIC)", "pin_name": "OVP", "pin_description": "This pin senses the boost output voltage through a voltage divider. The enable comparator input is TTL compatible and can be used as a remote shutdown port. Voltage levels above 7.5V will set the PWM latch and disable both ZVTOUT and GTOUT."}, {"pin_number": "8 (DIL/SOIC)", "pin_name": "REF", "pin_description": "REF is the output of the precision reference. The output is capable of supplying 25mA to peripheral circuitry and is internally short circuit current limited."}, {"pin_number": "9 (DIL/SOIC)", "pin_name": "VCC", "pin_description": "Positive supply rail for the IC. This pin is internally clamped to 20V."}, {"pin_number": "10 (DIL/SOIC)", "pin_name": "GTOUT", "pin_description": "The output of the PWM is a 1.5A peak totem pole MOSFET gate driver on GTOUT."}, {"pin_number": "11 (DIL/SOIC)", "pin_name": "GND", "pin_description": "All voltages are measured with respect to this pin."}, {"pin_number": "12 (DIL/SOIC)", "pin_name": "ZVTOUT", "pin_description": "The output of the ZVT block is a 750mA peak totem pole MOSFET gate driver on ZVTOUT."}, {"pin_number": "13 (DIL/SOIC)", "pin_name": "ZVS", "pin_description": "This pin senses when the drain voltage of the main MOSFET switch has reached approximately zero volts, and resets the ZVT latch via the ZVT comparator."}, {"pin_number": "14 (DIL/SOIC)", "pin_name": "CT", "pin_description": "A capacitor from CT to GND sets the PWM oscillator frequency."}, {"pin_number": "15 (DIL/SOIC)", "pin_name": "VAOUT", "pin_description": "This is the output of the voltage amplifier. At a given input RMS voltage, the voltage on this pin will vary directly with the output load. The output swing is limited from approximately 100mV to 6V."}, {"pin_number": "16 (DIL/SOIC)", "pin_name": "VSENSE", "pin_description": "This pin is the inverting input of the voltage amplifier and serves as the output voltage feedback point for the PFC boost converter. It senses the output voltage through a voltage divider which produces a nominal 3V."}, {"pin_number": "17 (DIL/SOIC)", "pin_name": "SS", "pin_description": "Soft-start VSS is discharged for VCC low conditions. When enabled, SS charges an external capacitor with a current source. This voltage is used as the voltage error signal during start-up."}, {"pin_number": "18 (DIL/SOIC)", "pin_name": "IMO", "pin_description": "This is the output of the multiplier, and the non-inverting input of the current amplifier. Since this output is a current, connect a resistor between this pin and ground."}, {"pin_number": "19 (DIL/SOIC)", "pin_name": "IAC", "pin_description": "This is a current input to the multiplier. The current into this pin should correspond to the instantaneous value of the rectified AC input line voltage."}, {"pin_number": "20 (DIL/SOIC)", "pin_name": "CA-", "pin_description": "This is the inverting input to the current amplifier. Connect the required compensation components between this pin and CAOUT."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS328B JUNE 1998 - REVISED October 2005", "family_comparison": "The A version has a 16V (nominal) VCC turn-on threshold, while the B version has a 10.5V (nominal) threshold. The UC2855 series has an operating temperature range of -40°C to 85°C, while the UC3855 series operates from 0°C to 70°C.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "22V", "min_input_voltage": "15.5V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "500kHz", "quiescent_current": "500µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.5V (on CS pin)", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "False", "integrated_ldo": "False", "dynamic_voltage_setting": "False", "pass_through_mode": "False", "load_disconnect": "False", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "1%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "20", "pitch": "1.27", "height": "2.65", "length": "12.8", "width": "7.5"}]}, {"part_number": "UC2855B", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "高频PFC控制器", "part_number_title": "High Performance Power Factor Preregulator", "features": ["Controls Boost PWM to Near Unity Power Factor", "Fixed Frequency Average Current Mode Control Minimizes Line Current Distortion", "Built-in Active Snubber (ZVT) allows Operation to 500kHz, improved EMI and Efficiency", "Inductor Current Synthesizer allows Single Current Transformer Current Sense for Improved Efficiency and Noise Margin", "Accurate Analog Multiplier with Line Compensator allows for Universal Input Voltage Operation", "High Bandwidth (5MHz), Low Offset Current Amplifier", "Overvoltage and Overcurrent protection", "Two UVLO Threshold Options", "150μA Startup Supply Current Typical", "Precision 1% 7.5V Reference"], "description": "The UC3855A/B provides all the control features necessary for high power, high frequency PFC boost converters. The average current mode control method allows for stable, low distortion AC line current programming without the need for slope compensation. In addition, the UC3855 utilizes an active snubbing or ZVT (Zero Voltage Transition technique) to dramatically reduce diode recovery and MOSFET turn-on losses, resulting in lower EMI emissions and higher efficiency. Boost converter switching frequencies up to 500kHz are now realizable, requiring only an additional small MOSFET, diode, and inductor to resonantly soft switch the boost diode and switch. Average current sensing can be employed using a simple resistive shunt or a current sense transformer. Using the current sense transformer method, the internal current synthesizer circuit buffers the inductor current during the switch on-time, and reconstructs the inductor current during the switch off-time. Improved signal to noise ratio and negligible current sensing losses make this an attractive solution for higher power applications.\nThe UC3855A/B also features a single quadrant multiplier, squarer, and divider circuit which provides the programming signal for the current loop. The internal multiplier current limit reduces output power during low line conditions. An overvoltage protection circuit disables both controller outputs in the event of a boost output OV condition.\nLow startup supply current, UVLO with hysteresis, a 1% 7.5V reference, voltage amplifier with softstart, input supply voltage clamp, enable comparator, and overcurrent comparator complete the list of features. Available packages include: 20 pin N, DW, Q, J, and L.", "applications": ["High Power PFC Boost Converters", "Universal Input Power Systems", "High-Efficiency Power Supplies"], "ordering_information": [{"part_number": "UC2855B", "order_device": "UC2855BDW", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UC2855B", "order_device": "UC2855BDW.A", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UC2855B", "order_device": "UC2855BDWTR", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UC2855B", "order_device": "UC2855BDWTR.A", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UC2855B", "order_device": "UC2855BN", "package_type": "PDIP", "package_drawing_code": "N (R-PDIP-T**)", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UC2855B", "order_device": "UC2855BN.A", "package_type": "PDIP", "package_drawing_code": "N (R-PDIP-T**)", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "UC2855B", "package_type": "All Packages", "pins": [{"pin_number": "1 (DIL/SOIC)", "pin_name": "CAO", "pin_description": "This is the output of the wide bandwidth current amplifier and one of the inputs to the PWM duty cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1V to 7.5V."}, {"pin_number": "2 (DIL/SOIC)", "pin_name": "RVS", "pin_description": "The nominal 3V signal present on the VSENSE pin is buffered and brought out to the RVS pin. A current proportional to the output voltage is generated by connecting a resistor between this pin and GND. This current forms the second input to the current synthesizer."}, {"pin_number": "3 (DIL/SOIC)", "pin_name": "CI", "pin_description": "The level shifted current sense signal is impressed upon a capacitor connected between this pin and GND. The buffered current sense transformer signal charges the capacitor when the boost switch is on. When the switch is off, the current synthesizer discharges the capacitor at a rate proportional to the dI/dt of the boost inductor current."}, {"pin_number": "4 (DIL/SOIC)", "pin_name": "ION", "pin_description": "This pin is the current sensing input. It should be connected to the secondary side output of a current sensing transformer whose primary winding is in series with the boost switch. The resultant signal applied to this input is buffered and level shifted up a diode to the CI capacitor on the CI pin."}, {"pin_number": "5 (DIL/SOIC)", "pin_name": "CS", "pin_description": "The reconstructed inductor current waveform generated on the CI pin is level shifted down a diode drop to this pin. The waveform on this pin is compared to the multiplier output waveform through the average current sensing current amplifier. The input to the peak current limiting comparator is also connected to this pin. A voltage level greater than 1.5 volts on this pin will trip the comparator and disable the gate driver output."}, {"pin_number": "6 (DIL/SOIC)", "pin_name": "VRMS", "pin_description": "This pin is the feedforward line voltage compensation input to the multiplier. A voltage on VRMS proportional to the AC input RMS voltage commands the multiplier to alter the current command signal by 1/VRMS^2 to maintain a constant power balance. The input range for this pin extends from 0 to 5.5V."}, {"pin_number": "7 (DIL/SOIC)", "pin_name": "OVP", "pin_description": "This pin senses the boost output voltage through a voltage divider. The enable comparator input is TTL compatible and can be used as a remote shutdown port. Voltage levels above 7.5V will set the PWM latch and disable both ZVTOUT and GTOUT."}, {"pin_number": "8 (DIL/SOIC)", "pin_name": "REF", "pin_description": "REF is the output of the precision reference. The output is capable of supplying 25mA to peripheral circuitry and is internally short circuit current limited."}, {"pin_number": "9 (DIL/SOIC)", "pin_name": "VCC", "pin_description": "Positive supply rail for the IC. This pin is internally clamped to 20V."}, {"pin_number": "10 (DIL/SOIC)", "pin_name": "GTOUT", "pin_description": "The output of the PWM is a 1.5A peak totem pole MOSFET gate driver on GTOUT."}, {"pin_number": "11 (DIL/SOIC)", "pin_name": "GND", "pin_description": "All voltages are measured with respect to this pin."}, {"pin_number": "12 (DIL/SOIC)", "pin_name": "ZVTOUT", "pin_description": "The output of the ZVT block is a 750mA peak totem pole MOSFET gate driver on ZVTOUT."}, {"pin_number": "13 (DIL/SOIC)", "pin_name": "ZVS", "pin_description": "This pin senses when the drain voltage of the main MOSFET switch has reached approximately zero volts, and resets the ZVT latch via the ZVT comparator."}, {"pin_number": "14 (DIL/SOIC)", "pin_name": "CT", "pin_description": "A capacitor from CT to GND sets the PWM oscillator frequency."}, {"pin_number": "15 (DIL/SOIC)", "pin_name": "VAOUT", "pin_description": "This is the output of the voltage amplifier. At a given input RMS voltage, the voltage on this pin will vary directly with the output load. The output swing is limited from approximately 100mV to 6V."}, {"pin_number": "16 (DIL/SOIC)", "pin_name": "VSENSE", "pin_description": "This pin is the inverting input of the voltage amplifier and serves as the output voltage feedback point for the PFC boost converter. It senses the output voltage through a voltage divider which produces a nominal 3V."}, {"pin_number": "17 (DIL/SOIC)", "pin_name": "SS", "pin_description": "Soft-start VSS is discharged for VCC low conditions. When enabled, SS charges an external capacitor with a current source. This voltage is used as the voltage error signal during start-up."}, {"pin_number": "18 (DIL/SOIC)", "pin_name": "IMO", "pin_description": "This is the output of the multiplier, and the non-inverting input of the current amplifier. Since this output is a current, connect a resistor between this pin and ground."}, {"pin_number": "19 (DIL/SOIC)", "pin_name": "IAC", "pin_description": "This is a current input to the multiplier. The current into this pin should correspond to the instantaneous value of the rectified AC input line voltage."}, {"pin_number": "20 (DIL/SOIC)", "pin_name": "CA-", "pin_description": "This is the inverting input to the current amplifier. Connect the required compensation components between this pin and CAOUT."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS328B JUNE 1998 - REVISED October 2005", "family_comparison": "The A version has a 16V (nominal) VCC turn-on threshold, while the B version has a 10.5V (nominal) threshold. The UC2855 series has an operating temperature range of -40°C to 85°C, while the UC3855 series operates from 0°C to 70°C.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "22V", "min_input_voltage": "10.5V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "500kHz", "quiescent_current": "500µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.5V (on CS pin)", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "False", "integrated_ldo": "False", "dynamic_voltage_setting": "False", "pass_through_mode": "False", "load_disconnect": "False", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "1%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "20", "pitch": "1.27", "height": "2.65", "length": "12.8", "width": "7.5"}]}, {"part_number": "UC3855A", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "高频PFC控制器", "part_number_title": "High Performance Power Factor Preregulator", "features": ["Controls Boost PWM to Near Unity Power Factor", "Fixed Frequency Average Current Mode Control Minimizes Line Current Distortion", "Built-in Active Snubber (ZVT) allows Operation to 500kHz, improved EMI and Efficiency", "Inductor Current Synthesizer allows Single Current Transformer Current Sense for Improved Efficiency and Noise Margin", "Accurate Analog Multiplier with Line Compensator allows for Universal Input Voltage Operation", "High Bandwidth (5MHz), Low Offset Current Amplifier", "Overvoltage and Overcurrent protection", "Two UVLO Threshold Options", "150μA Startup Supply Current Typical", "Precision 1% 7.5V Reference"], "description": "The UC3855A/B provides all the control features necessary for high power, high frequency PFC boost converters. The average current mode control method allows for stable, low distortion AC line current programming without the need for slope compensation. In addition, the UC3855 utilizes an active snubbing or ZVT (Zero Voltage Transition technique) to dramatically reduce diode recovery and MOSFET turn-on losses, resulting in lower EMI emissions and higher efficiency. Boost converter switching frequencies up to 500kHz are now realizable, requiring only an additional small MOSFET, diode, and inductor to resonantly soft switch the boost diode and switch. Average current sensing can be employed using a simple resistive shunt or a current sense transformer. Using the current sense transformer method, the internal current synthesizer circuit buffers the inductor current during the switch on-time, and reconstructs the inductor current during the switch off-time. Improved signal to noise ratio and negligible current sensing losses make this an attractive solution for higher power applications.\nThe UC3855A/B also features a single quadrant multiplier, squarer, and divider circuit which provides the programming signal for the current loop. The internal multiplier current limit reduces output power during low line conditions. An overvoltage protection circuit disables both controller outputs in the event of a boost output OV condition.\nLow startup supply current, UVLO with hysteresis, a 1% 7.5V reference, voltage amplifier with softstart, input supply voltage clamp, enable comparator, and overcurrent comparator complete the list of features. Available packages include: 20 pin N, DW, Q, J, and L.", "applications": ["High Power PFC Boost Converters", "Universal Input Power Systems", "High-Efficiency Power Supplies"], "ordering_information": [{"part_number": "UC3855A", "order_device": "UC3855ADW", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855A", "order_device": "UC3855ADW.A", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855A", "order_device": "UC3855ADWTR", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855A", "order_device": "UC3855ADWTR.A", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "UC3855A", "package_type": "All Packages", "pins": [{"pin_number": "1 (DIL/SOIC)", "pin_name": "CAO", "pin_description": "This is the output of the wide bandwidth current amplifier and one of the inputs to the PWM duty cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1V to 7.5V."}, {"pin_number": "2 (DIL/SOIC)", "pin_name": "RVS", "pin_description": "The nominal 3V signal present on the VSENSE pin is buffered and brought out to the RVS pin. A current proportional to the output voltage is generated by connecting a resistor between this pin and GND. This current forms the second input to the current synthesizer."}, {"pin_number": "3 (DIL/SOIC)", "pin_name": "CI", "pin_description": "The level shifted current sense signal is impressed upon a capacitor connected between this pin and GND. The buffered current sense transformer signal charges the capacitor when the boost switch is on. When the switch is off, the current synthesizer discharges the capacitor at a rate proportional to the dI/dt of the boost inductor current."}, {"pin_number": "4 (DIL/SOIC)", "pin_name": "ION", "pin_description": "This pin is the current sensing input. It should be connected to the secondary side output of a current sensing transformer whose primary winding is in series with the boost switch. The resultant signal applied to this input is buffered and level shifted up a diode to the CI capacitor on the CI pin."}, {"pin_number": "5 (DIL/SOIC)", "pin_name": "CS", "pin_description": "The reconstructed inductor current waveform generated on the CI pin is level shifted down a diode drop to this pin. The waveform on this pin is compared to the multiplier output waveform through the average current sensing current amplifier. The input to the peak current limiting comparator is also connected to this pin. A voltage level greater than 1.5 volts on this pin will trip the comparator and disable the gate driver output."}, {"pin_number": "6 (DIL/SOIC)", "pin_name": "VRMS", "pin_description": "This pin is the feedforward line voltage compensation input to the multiplier. A voltage on VRMS proportional to the AC input RMS voltage commands the multiplier to alter the current command signal by 1/VRMS^2 to maintain a constant power balance. The input range for this pin extends from 0 to 5.5V."}, {"pin_number": "7 (DIL/SOIC)", "pin_name": "OVP", "pin_description": "This pin senses the boost output voltage through a voltage divider. The enable comparator input is TTL compatible and can be used as a remote shutdown port. Voltage levels above 7.5V will set the PWM latch and disable both ZVTOUT and GTOUT."}, {"pin_number": "8 (DIL/SOIC)", "pin_name": "REF", "pin_description": "REF is the output of the precision reference. The output is capable of supplying 25mA to peripheral circuitry and is internally short circuit current limited."}, {"pin_number": "9 (DIL/SOIC)", "pin_name": "VCC", "pin_description": "Positive supply rail for the IC. This pin is internally clamped to 20V."}, {"pin_number": "10 (DIL/SOIC)", "pin_name": "GTOUT", "pin_description": "The output of the PWM is a 1.5A peak totem pole MOSFET gate driver on GTOUT."}, {"pin_number": "11 (DIL/SOIC)", "pin_name": "GND", "pin_description": "All voltages are measured with respect to this pin."}, {"pin_number": "12 (DIL/SOIC)", "pin_name": "ZVTOUT", "pin_description": "The output of the ZVT block is a 750mA peak totem pole MOSFET gate driver on ZVTOUT."}, {"pin_number": "13 (DIL/SOIC)", "pin_name": "ZVS", "pin_description": "This pin senses when the drain voltage of the main MOSFET switch has reached approximately zero volts, and resets the ZVT latch via the ZVT comparator."}, {"pin_number": "14 (DIL/SOIC)", "pin_name": "CT", "pin_description": "A capacitor from CT to GND sets the PWM oscillator frequency."}, {"pin_number": "15 (DIL/SOIC)", "pin_name": "VAOUT", "pin_description": "This is the output of the voltage amplifier. At a given input RMS voltage, the voltage on this pin will vary directly with the output load. The output swing is limited from approximately 100mV to 6V."}, {"pin_number": "16 (DIL/SOIC)", "pin_name": "VSENSE", "pin_description": "This pin is the inverting input of the voltage amplifier and serves as the output voltage feedback point for the PFC boost converter. It senses the output voltage through a voltage divider which produces a nominal 3V."}, {"pin_number": "17 (DIL/SOIC)", "pin_name": "SS", "pin_description": "Soft-start VSS is discharged for VCC low conditions. When enabled, SS charges an external capacitor with a current source. This voltage is used as the voltage error signal during start-up."}, {"pin_number": "18 (DIL/SOIC)", "pin_name": "IMO", "pin_description": "This is the output of the multiplier, and the non-inverting input of the current amplifier. Since this output is a current, connect a resistor between this pin and ground."}, {"pin_number": "19 (DIL/SOIC)", "pin_name": "IAC", "pin_description": "This is a current input to the multiplier. The current into this pin should correspond to the instantaneous value of the rectified AC input line voltage."}, {"pin_number": "20 (DIL/SOIC)", "pin_name": "CA-", "pin_description": "This is the inverting input to the current amplifier. Connect the required compensation components between this pin and CAOUT."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS328B JUNE 1998 - REVISED October 2005", "family_comparison": "The A version has a 16V (nominal) VCC turn-on threshold, while the B version has a 10.5V (nominal) threshold. The UC2855 series has an operating temperature range of -40°C to 85°C, while the UC3855 series operates from 0°C to 70°C.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "22V", "min_input_voltage": "15.5V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "500kHz", "quiescent_current": "500µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.5V (on CS pin)", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "False", "integrated_ldo": "False", "dynamic_voltage_setting": "False", "pass_through_mode": "False", "load_disconnect": "False", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "1%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "20", "pitch": "1.27", "height": "2.65", "length": "12.8", "width": "7.5"}]}, {"part_number": "UC3855B", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "高频PFC控制器", "part_number_title": "High Performance Power Factor Preregulator", "features": ["Controls Boost PWM to Near Unity Power Factor", "Fixed Frequency Average Current Mode Control Minimizes Line Current Distortion", "Built-in Active Snubber (ZVT) allows Operation to 500kHz, improved EMI and Efficiency", "Inductor Current Synthesizer allows Single Current Transformer Current Sense for Improved Efficiency and Noise Margin", "Accurate Analog Multiplier with Line Compensator allows for Universal Input Voltage Operation", "High Bandwidth (5MHz), Low Offset Current Amplifier", "Overvoltage and Overcurrent protection", "Two UVLO Threshold Options", "150μA Startup Supply Current Typical", "Precision 1% 7.5V Reference"], "description": "The UC3855A/B provides all the control features necessary for high power, high frequency PFC boost converters. The average current mode control method allows for stable, low distortion AC line current programming without the need for slope compensation. In addition, the UC3855 utilizes an active snubbing or ZVT (Zero Voltage Transition technique) to dramatically reduce diode recovery and MOSFET turn-on losses, resulting in lower EMI emissions and higher efficiency. Boost converter switching frequencies up to 500kHz are now realizable, requiring only an additional small MOSFET, diode, and inductor to resonantly soft switch the boost diode and switch. Average current sensing can be employed using a simple resistive shunt or a current sense transformer. Using the current sense transformer method, the internal current synthesizer circuit buffers the inductor current during the switch on-time, and reconstructs the inductor current during the switch off-time. Improved signal to noise ratio and negligible current sensing losses make this an attractive solution for higher power applications.\nThe UC3855A/B also features a single quadrant multiplier, squarer, and divider circuit which provides the programming signal for the current loop. The internal multiplier current limit reduces output power during low line conditions. An overvoltage protection circuit disables both controller outputs in the event of a boost output OV condition.\nLow startup supply current, UVLO with hysteresis, a 1% 7.5V reference, voltage amplifier with softstart, input supply voltage clamp, enable comparator, and overcurrent comparator complete the list of features. Available packages include: 20 pin N, DW, Q, J, and L.", "applications": ["High Power PFC Boost Converters", "Universal Input Power Systems", "High-Efficiency Power Supplies"], "ordering_information": [{"part_number": "UC3855B", "order_device": "UC3855BDW", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855B", "order_device": "UC3855BDW.A", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855B", "order_device": "UC3855BDWG4", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855B", "order_device": "UC3855BDWTR", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855B", "order_device": "UC3855BDWTR.A", "package_type": "SOIC", "package_drawing_code": "DW0020A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855B", "order_device": "UC3855BN", "package_type": "PDIP", "package_drawing_code": "N (R-PDIP-T**)", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855B", "order_device": "UC3855BN.A", "package_type": "PDIP", "package_drawing_code": "N (R-PDIP-T**)", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855B", "order_device": "UC3855BNG4", "package_type": "PDIP", "package_drawing_code": "N (R-PDIP-T**)", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "UC3855B", "package_type": "All Packages", "pins": [{"pin_number": "1 (DIL/SOIC)", "pin_name": "CAO", "pin_description": "This is the output of the wide bandwidth current amplifier and one of the inputs to the PWM duty cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1V to 7.5V."}, {"pin_number": "2 (DIL/SOIC)", "pin_name": "RVS", "pin_description": "The nominal 3V signal present on the VSENSE pin is buffered and brought out to the RVS pin. A current proportional to the output voltage is generated by connecting a resistor between this pin and GND. This current forms the second input to the current synthesizer."}, {"pin_number": "3 (DIL/SOIC)", "pin_name": "CI", "pin_description": "The level shifted current sense signal is impressed upon a capacitor connected between this pin and GND. The buffered current sense transformer signal charges the capacitor when the boost switch is on. When the switch is off, the current synthesizer discharges the capacitor at a rate proportional to the dI/dt of the boost inductor current."}, {"pin_number": "4 (DIL/SOIC)", "pin_name": "ION", "pin_description": "This pin is the current sensing input. It should be connected to the secondary side output of a current sensing transformer whose primary winding is in series with the boost switch. The resultant signal applied to this input is buffered and level shifted up a diode to the CI capacitor on the CI pin."}, {"pin_number": "5 (DIL/SOIC)", "pin_name": "CS", "pin_description": "The reconstructed inductor current waveform generated on the CI pin is level shifted down a diode drop to this pin. The waveform on this pin is compared to the multiplier output waveform through the average current sensing current amplifier. The input to the peak current limiting comparator is also connected to this pin. A voltage level greater than 1.5 volts on this pin will trip the comparator and disable the gate driver output."}, {"pin_number": "6 (DIL/SOIC)", "pin_name": "VRMS", "pin_description": "This pin is the feedforward line voltage compensation input to the multiplier. A voltage on VRMS proportional to the AC input RMS voltage commands the multiplier to alter the current command signal by 1/VRMS^2 to maintain a constant power balance. The input range for this pin extends from 0 to 5.5V."}, {"pin_number": "7 (DIL/SOIC)", "pin_name": "OVP", "pin_description": "This pin senses the boost output voltage through a voltage divider. The enable comparator input is TTL compatible and can be used as a remote shutdown port. Voltage levels above 7.5V will set the PWM latch and disable both ZVTOUT and GTOUT."}, {"pin_number": "8 (DIL/SOIC)", "pin_name": "REF", "pin_description": "REF is the output of the precision reference. The output is capable of supplying 25mA to peripheral circuitry and is internally short circuit current limited."}, {"pin_number": "9 (DIL/SOIC)", "pin_name": "VCC", "pin_description": "Positive supply rail for the IC. This pin is internally clamped to 20V."}, {"pin_number": "10 (DIL/SOIC)", "pin_name": "GTOUT", "pin_description": "The output of the PWM is a 1.5A peak totem pole MOSFET gate driver on GTOUT."}, {"pin_number": "11 (DIL/SOIC)", "pin_name": "GND", "pin_description": "All voltages are measured with respect to this pin."}, {"pin_number": "12 (DIL/SOIC)", "pin_name": "ZVTOUT", "pin_description": "The output of the ZVT block is a 750mA peak totem pole MOSFET gate driver on ZVTOUT."}, {"pin_number": "13 (DIL/SOIC)", "pin_name": "ZVS", "pin_description": "This pin senses when the drain voltage of the main MOSFET switch has reached approximately zero volts, and resets the ZVT latch via the ZVT comparator."}, {"pin_number": "14 (DIL/SOIC)", "pin_name": "CT", "pin_description": "A capacitor from CT to GND sets the PWM oscillator frequency."}, {"pin_number": "15 (DIL/SOIC)", "pin_name": "VAOUT", "pin_description": "This is the output of the voltage amplifier. At a given input RMS voltage, the voltage on this pin will vary directly with the output load. The output swing is limited from approximately 100mV to 6V."}, {"pin_number": "16 (DIL/SOIC)", "pin_name": "VSENSE", "pin_description": "This pin is the inverting input of the voltage amplifier and serves as the output voltage feedback point for the PFC boost converter. It senses the output voltage through a voltage divider which produces a nominal 3V."}, {"pin_number": "17 (DIL/SOIC)", "pin_name": "SS", "pin_description": "Soft-start VSS is discharged for VCC low conditions. When enabled, SS charges an external capacitor with a current source. This voltage is used as the voltage error signal during start-up."}, {"pin_number": "18 (DIL/SOIC)", "pin_name": "IMO", "pin_description": "This is the output of the multiplier, and the non-inverting input of the current amplifier. Since this output is a current, connect a resistor between this pin and ground."}, {"pin_number": "19 (DIL/SOIC)", "pin_name": "IAC", "pin_description": "This is a current input to the multiplier. The current into this pin should correspond to the instantaneous value of the rectified AC input line voltage."}, {"pin_number": "20 (DIL/SOIC)", "pin_name": "CA-", "pin_description": "This is the inverting input to the current amplifier. Connect the required compensation components between this pin and CAOUT."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS328B JUNE 1998 - REVISED October 2005", "family_comparison": "The A version has a 16V (nominal) VCC turn-on threshold, while the B version has a 10.5V (nominal) threshold. The UC2855 series has an operating temperature range of -40°C to 85°C, while the UC3855 series operates from 0°C to 70°C.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "22V", "min_input_voltage": "10.5V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "500kHz", "quiescent_current": "500µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.5V (on CS pin)", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "False", "integrated_ldo": "False", "dynamic_voltage_setting": "False", "pass_through_mode": "False", "load_disconnect": "False", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "1%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "20", "pitch": "1.27", "height": "2.65", "length": "12.8", "width": "7.5"}]}]