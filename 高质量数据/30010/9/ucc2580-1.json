[{"part_number": "UCC1580-1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "有源钳位PWM控制器", "part_number_title": "Single Ended Active Clamp/Reset PWM", "features": ["Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive", "Programmable deadtime (Turn-on Delay) Between Activation of Each Switch", "Voltage Mode Control with Feedforward Operation", "Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle", "High Current Gate Driver for Both Main and Auxiliary Outputs", "Multiple Protection Features with Latched Shutdown and Soft Restart", "Low Supply Current (100 μA Startup, 1.5 mA Operation)"], "description": "The UCC3580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. While containing all the necessary functions for fixed frequency, high performance pulse width modulation, the additional feature of this design is the inclusion of an auxiliary switch driver which complements the main power switch, and with a programmable deadtime or delay between each transition. This version (-1) has a normal (non-inverted) OUT2 polarity and a 9V UVLO turn-on threshold.", "applications": ["Active clamp forward converter", "Off-line active clamp flyback converter", "Synchronous rectifier switching converter"], "ordering_information": [], "pin_function": [{"pin_number": "1", "pin_name": "DELAY", "pin_description": "A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2."}, {"pin_number": "2", "pin_name": "LINE", "pin_description": "Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low."}, {"pin_number": "3", "pin_name": "VDD", "pin_description": "Chip power supply pin. VDD should be bypassed to PGND."}, {"pin_number": "4", "pin_name": "OUT1", "pin_description": "Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A."}, {"pin_number": "5", "pin_name": "PGND", "pin_description": "Ground connection for the gate drivers."}, {"pin_number": "6", "pin_name": "OUT2", "pin_description": "Gate drive output for the auxiliary switch with 0.3A drive current capability. Normal (non-inverted) polarity."}, {"pin_number": "7", "pin_name": "CLK", "pin_description": "Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Signal Ground."}, {"pin_number": "9", "pin_name": "RAMP", "pin_description": "Programs the feedforward ramp signal for the PWM comparator."}, {"pin_number": "10", "pin_name": "OSC2", "pin_description": "Oscillator programming pin. A resistor connected to OSC2 controls guaranteed off time."}, {"pin_number": "11", "pin_name": "OSC1", "pin_description": "Oscillator programming pin. The resistor connected to OSC1 sets maximum on time."}, {"pin_number": "12", "pin_name": "EAOUT", "pin_description": "Output of the error amplifier and input to the PWM comparator."}, {"pin_number": "13", "pin_name": "EAIN", "pin_description": "Inverting input to the error amplifier. Used for feedback and loop compensation."}, {"pin_number": "14", "pin_name": "REF", "pin_description": "Precision 5.0V reference pin."}, {"pin_number": "15", "pin_name": "SS", "pin_description": "A capacitor from SS to ground programs the soft start time."}, {"pin_number": "16", "pin_name": "SHTDWN", "pin_description": "Comparator input to stop the chip. The threshold is 0.5V."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS292D", "family_comparison": "The UCCx580 family has variants with different temperature ranges (UCC1580: -55°C to 125°C, UCC2580: -40°C to 85°C, UCC3580: 0°C to 70°C), UVLO thresholds (9V for -1/-3, 15V for -2/-4), and OUT2 polarity (normal for -1/-2, inverted for -3/-4).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "不适用", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "9V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "430kHz", "quiescent_current": "2.5mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "No", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "No", "output_short_circuit_protection": "No", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "±2.4%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "width": "4", "length": "10", "pin_count": "9"}]}, {"part_number": "UCC1580-2", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "有源钳位PWM控制器", "part_number_title": "Single Ended Active Clamp/Reset PWM", "features": ["Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive", "Programmable deadtime (Turn-on Delay) Between Activation of Each Switch", "Voltage Mode Control with Feedforward Operation", "Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle", "High Current Gate Driver for Both Main and Auxiliary Outputs", "Multiple Protection Features with Latched Shutdown and Soft Restart", "Low Supply Current (100 μA Startup, 1.5 mA Operation)"], "description": "The UCC3580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. While containing all the necessary functions for fixed frequency, high performance pulse width modulation, the additional feature of this design is the inclusion of an auxiliary switch driver which complements the main power switch, and with a programmable deadtime or delay between each transition. This version (-2) has a normal (non-inverted) OUT2 polarity and a 15V UVLO turn-on threshold.", "applications": ["Active clamp forward converter", "Off-line active clamp flyback converter", "Synchronous rectifier switching converter"], "ordering_information": [], "pin_function": [{"pin_number": "1", "pin_name": "DELAY", "pin_description": "A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2."}, {"pin_number": "2", "pin_name": "LINE", "pin_description": "Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low."}, {"pin_number": "3", "pin_name": "VDD", "pin_description": "Chip power supply pin. VDD should be bypassed to PGND."}, {"pin_number": "4", "pin_name": "OUT1", "pin_description": "Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A."}, {"pin_number": "5", "pin_name": "PGND", "pin_description": "Ground connection for the gate drivers."}, {"pin_number": "6", "pin_name": "OUT2", "pin_description": "Gate drive output for the auxiliary switch with 0.3A drive current capability. Normal (non-inverted) polarity."}, {"pin_number": "7", "pin_name": "CLK", "pin_description": "Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Signal Ground."}, {"pin_number": "9", "pin_name": "RAMP", "pin_description": "Programs the feedforward ramp signal for the PWM comparator."}, {"pin_number": "10", "pin_name": "OSC2", "pin_description": "Oscillator programming pin. A resistor connected to OSC2 controls guaranteed off time."}, {"pin_number": "11", "pin_name": "OSC1", "pin_description": "Oscillator programming pin. The resistor connected to OSC1 sets maximum on time."}, {"pin_number": "12", "pin_name": "EAOUT", "pin_description": "Output of the error amplifier and input to the PWM comparator."}, {"pin_number": "13", "pin_name": "EAIN", "pin_description": "Inverting input to the error amplifier. Used for feedback and loop compensation."}, {"pin_number": "14", "pin_name": "REF", "pin_description": "Precision 5.0V reference pin."}, {"pin_number": "15", "pin_name": "SS", "pin_description": "A capacitor from SS to ground programs the soft start time."}, {"pin_number": "16", "pin_name": "SHTDWN", "pin_description": "Comparator input to stop the chip. The threshold is 0.5V."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS292D", "family_comparison": "The UCCx580 family has variants with different temperature ranges (UCC1580: -55°C to 125°C, UCC2580: -40°C to 85°C, UCC3580: 0°C to 70°C), UVLO thresholds (9V for -1/-3, 15V for -2/-4), and OUT2 polarity (normal for -1/-2, inverted for -3/-4).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "不适用", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "15V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "430kHz", "quiescent_current": "2.5mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "No", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "No", "output_short_circuit_protection": "No", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "±2.4%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "width": "4", "length": "10", "pin_count": "9"}]}, {"part_number": "UCC1580-3", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "有源钳位PWM控制器", "part_number_title": "Single Ended Active Clamp/Reset PWM", "features": ["Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive", "Programmable deadtime (Turn-on Delay) Between Activation of Each Switch", "Voltage Mode Control with Feedforward Operation", "Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle", "High Current Gate Driver for Both Main and Auxiliary Outputs", "Multiple Protection Features with Latched Shutdown and Soft Restart", "Low Supply Current (100 μA Startup, 1.5 mA Operation)"], "description": "The UCC3580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. While containing all the necessary functions for fixed frequency, high performance pulse width modulation, the additional feature of this design is the inclusion of an auxiliary switch driver which complements the main power switch, and with a programmable deadtime or delay between each transition. This version (-3) has an inverted OUT2 polarity and a 9V UVLO turn-on threshold.", "applications": ["Active clamp forward converter", "Off-line active clamp flyback converter", "Synchronous rectifier switching converter"], "ordering_information": [], "pin_function": [{"pin_number": "1", "pin_name": "DELAY", "pin_description": "A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2."}, {"pin_number": "2", "pin_name": "LINE", "pin_description": "Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low."}, {"pin_number": "3", "pin_name": "VDD", "pin_description": "Chip power supply pin. VDD should be bypassed to PGND."}, {"pin_number": "4", "pin_name": "OUT1", "pin_description": "Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A."}, {"pin_number": "5", "pin_name": "PGND", "pin_description": "Ground connection for the gate drivers."}, {"pin_number": "6", "pin_name": "OUT2", "pin_description": "Gate drive output for the auxiliary switch with 0.3A drive current capability. Inverted polarity."}, {"pin_number": "7", "pin_name": "CLK", "pin_description": "Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Signal Ground."}, {"pin_number": "9", "pin_name": "RAMP", "pin_description": "Programs the feedforward ramp signal for the PWM comparator."}, {"pin_number": "10", "pin_name": "OSC2", "pin_description": "Oscillator programming pin. A resistor connected to OSC2 controls guaranteed off time."}, {"pin_number": "11", "pin_name": "OSC1", "pin_description": "Oscillator programming pin. The resistor connected to OSC1 sets maximum on time."}, {"pin_number": "12", "pin_name": "EAOUT", "pin_description": "Output of the error amplifier and input to the PWM comparator."}, {"pin_number": "13", "pin_name": "EAIN", "pin_description": "Inverting input to the error amplifier. Used for feedback and loop compensation."}, {"pin_number": "14", "pin_name": "REF", "pin_description": "Precision 5.0V reference pin."}, {"pin_number": "15", "pin_name": "SS", "pin_description": "A capacitor from SS to ground programs the soft start time."}, {"pin_number": "16", "pin_name": "SHTDWN", "pin_description": "Comparator input to stop the chip. The threshold is 0.5V."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS292D", "family_comparison": "The UCCx580 family has variants with different temperature ranges (UCC1580: -55°C to 125°C, UCC2580: -40°C to 85°C, UCC3580: 0°C to 70°C), UVLO thresholds (9V for -1/-3, 15V for -2/-4), and OUT2 polarity (normal for -1/-2, inverted for -3/-4).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "不适用", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "9V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "430kHz", "quiescent_current": "2.5mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "No", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "No", "output_short_circuit_protection": "No", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "±2.4%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "width": "4", "length": "10", "pin_count": "9"}]}, {"part_number": "UCC1580-4", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "有源钳位PWM控制器", "part_number_title": "Single Ended Active Clamp/Reset PWM", "features": ["Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive", "Programmable deadtime (Turn-on Delay) Between Activation of Each Switch", "Voltage Mode Control with Feedforward Operation", "Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle", "High Current Gate Driver for Both Main and Auxiliary Outputs", "Multiple Protection Features with Latched Shutdown and Soft Restart", "Low Supply Current (100 μA Startup, 1.5 mA Operation)"], "description": "The UCC3580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. While containing all the necessary functions for fixed frequency, high performance pulse width modulation, the additional feature of this design is the inclusion of an auxiliary switch driver which complements the main power switch, and with a programmable deadtime or delay between each transition. This version (-4) has an inverted OUT2 polarity and a 15V UVLO turn-on threshold.", "applications": ["Active clamp forward converter", "Off-line active clamp flyback converter", "Synchronous rectifier switching converter"], "ordering_information": [], "pin_function": [{"pin_number": "1", "pin_name": "DELAY", "pin_description": "A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2."}, {"pin_number": "2", "pin_name": "LINE", "pin_description": "Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low."}, {"pin_number": "3", "pin_name": "VDD", "pin_description": "Chip power supply pin. VDD should be bypassed to PGND."}, {"pin_number": "4", "pin_name": "OUT1", "pin_description": "Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A."}, {"pin_number": "5", "pin_name": "PGND", "pin_description": "Ground connection for the gate drivers."}, {"pin_number": "6", "pin_name": "OUT2", "pin_description": "Gate drive output for the auxiliary switch with 0.3A drive current capability. Inverted polarity."}, {"pin_number": "7", "pin_name": "CLK", "pin_description": "Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Signal Ground."}, {"pin_number": "9", "pin_name": "RAMP", "pin_description": "Programs the feedforward ramp signal for the PWM comparator."}, {"pin_number": "10", "pin_name": "OSC2", "pin_description": "Oscillator programming pin. A resistor connected to OSC2 controls guaranteed off time."}, {"pin_number": "11", "pin_name": "OSC1", "pin_description": "Oscillator programming pin. The resistor connected to OSC1 sets maximum on time."}, {"pin_number": "12", "pin_name": "EAOUT", "pin_description": "Output of the error amplifier and input to the PWM comparator."}, {"pin_number": "13", "pin_name": "EAIN", "pin_description": "Inverting input to the error amplifier. Used for feedback and loop compensation."}, {"pin_number": "14", "pin_name": "REF", "pin_description": "Precision 5.0V reference pin."}, {"pin_number": "15", "pin_name": "SS", "pin_description": "A capacitor from SS to ground programs the soft start time."}, {"pin_number": "16", "pin_name": "SHTDWN", "pin_description": "Comparator input to stop the chip. The threshold is 0.5V."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS292D", "family_comparison": "The UCCx580 family has variants with different temperature ranges (UCC1580: -55°C to 125°C, UCC2580: -40°C to 85°C, UCC3580: 0°C to 70°C), UVLO thresholds (9V for -1/-3, 15V for -2/-4), and OUT2 polarity (normal for -1/-2, inverted for -3/-4).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "不适用", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "15V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "430kHz", "quiescent_current": "2.5mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "No", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "No", "output_short_circuit_protection": "No", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "±2.4%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "width": "4", "length": "10", "pin_count": "9"}]}, {"part_number": "UCC2580-1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "有源钳位PWM控制器", "part_number_title": "Single Ended Active Clamp/Reset PWM", "features": ["Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive", "Programmable deadtime (Turn-on Delay) Between Activation of Each Switch", "Voltage Mode Control with Feedforward Operation", "Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle", "High Current Gate Driver for Both Main and Auxiliary Outputs", "Multiple Protection Features with Latched Shutdown and Soft Restart", "Low Supply Current (100 μA Startup, 1.5 mA Operation)"], "description": "The UCC3580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. While containing all the necessary functions for fixed frequency, high performance pulse width modulation, the additional feature of this design is the inclusion of an auxiliary switch driver which complements the main power switch, and with a programmable deadtime or delay between each transition. This version (-1) has a normal (non-inverted) OUT2 polarity and a 9V UVLO turn-on threshold.", "applications": ["Active clamp forward converter", "Off-line active clamp flyback converter", "Synchronous rectifier switching converter"], "ordering_information": [{"part_number": "UCC2580-1", "order_device": "UCC2580D-1", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC2580-1", "order_device": "UCC2580DTR-1", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"pin_number": "1", "pin_name": "DELAY", "pin_description": "A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2."}, {"pin_number": "2", "pin_name": "LINE", "pin_description": "Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low."}, {"pin_number": "3", "pin_name": "VDD", "pin_description": "Chip power supply pin. VDD should be bypassed to PGND."}, {"pin_number": "4", "pin_name": "OUT1", "pin_description": "Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A."}, {"pin_number": "5", "pin_name": "PGND", "pin_description": "Ground connection for the gate drivers."}, {"pin_number": "6", "pin_name": "OUT2", "pin_description": "Gate drive output for the auxiliary switch with 0.3A drive current capability. Normal (non-inverted) polarity."}, {"pin_number": "7", "pin_name": "CLK", "pin_description": "Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Signal Ground."}, {"pin_number": "9", "pin_name": "RAMP", "pin_description": "Programs the feedforward ramp signal for the PWM comparator."}, {"pin_number": "10", "pin_name": "OSC2", "pin_description": "Oscillator programming pin. A resistor connected to OSC2 controls guaranteed off time."}, {"pin_number": "11", "pin_name": "OSC1", "pin_description": "Oscillator programming pin. The resistor connected to OSC1 sets maximum on time."}, {"pin_number": "12", "pin_name": "EAOUT", "pin_description": "Output of the error amplifier and input to the PWM comparator."}, {"pin_number": "13", "pin_name": "EAIN", "pin_description": "Inverting input to the error amplifier. Used for feedback and loop compensation."}, {"pin_number": "14", "pin_name": "REF", "pin_description": "Precision 5.0V reference pin."}, {"pin_number": "15", "pin_name": "SS", "pin_description": "A capacitor from SS to ground programs the soft start time."}, {"pin_number": "16", "pin_name": "SHTDWN", "pin_description": "Comparator input to stop the chip. The threshold is 0.5V."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS292D", "family_comparison": "The UCCx580 family has variants with different temperature ranges (UCC1580: -55°C to 125°C, UCC2580: -40°C to 85°C, UCC3580: 0°C to 70°C), UVLO thresholds (9V for -1/-3, 15V for -2/-4), and OUT2 polarity (normal for -1/-2, inverted for -3/-4).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "不适用", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "9V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "430kHz", "quiescent_current": "2.5mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "No", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "No", "output_short_circuit_protection": "No", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "±2.4%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "width": "4", "length": "10", "pin_count": "9"}]}, {"part_number": "UCC2580-2", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "有源钳位PWM控制器", "part_number_title": "Single Ended Active Clamp/Reset PWM", "features": ["Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive", "Programmable deadtime (Turn-on Delay) Between Activation of Each Switch", "Voltage Mode Control with Feedforward Operation", "Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle", "High Current Gate Driver for Both Main and Auxiliary Outputs", "Multiple Protection Features with Latched Shutdown and Soft Restart", "Low Supply Current (100 μA Startup, 1.5 mA Operation)"], "description": "The UCC3580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. While containing all the necessary functions for fixed frequency, high performance pulse width modulation, the additional feature of this design is the inclusion of an auxiliary switch driver which complements the main power switch, and with a programmable deadtime or delay between each transition. This version (-2) has a normal (non-inverted) OUT2 polarity and a 15V UVLO turn-on threshold.", "applications": ["Active clamp forward converter", "Off-line active clamp flyback converter", "Synchronous rectifier switching converter"], "ordering_information": [{"part_number": "UCC2580-2", "order_device": "UCC2580DTR-2", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"pin_number": "1", "pin_name": "DELAY", "pin_description": "A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2."}, {"pin_number": "2", "pin_name": "LINE", "pin_description": "Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low."}, {"pin_number": "3", "pin_name": "VDD", "pin_description": "Chip power supply pin. VDD should be bypassed to PGND."}, {"pin_number": "4", "pin_name": "OUT1", "pin_description": "Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A."}, {"pin_number": "5", "pin_name": "PGND", "pin_description": "Ground connection for the gate drivers."}, {"pin_number": "6", "pin_name": "OUT2", "pin_description": "Gate drive output for the auxiliary switch with 0.3A drive current capability. Normal (non-inverted) polarity."}, {"pin_number": "7", "pin_name": "CLK", "pin_description": "Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Signal Ground."}, {"pin_number": "9", "pin_name": "RAMP", "pin_description": "Programs the feedforward ramp signal for the PWM comparator."}, {"pin_number": "10", "pin_name": "OSC2", "pin_description": "Oscillator programming pin. A resistor connected to OSC2 controls guaranteed off time."}, {"pin_number": "11", "pin_name": "OSC1", "pin_description": "Oscillator programming pin. The resistor connected to OSC1 sets maximum on time."}, {"pin_number": "12", "pin_name": "EAOUT", "pin_description": "Output of the error amplifier and input to the PWM comparator."}, {"pin_number": "13", "pin_name": "EAIN", "pin_description": "Inverting input to the error amplifier. Used for feedback and loop compensation."}, {"pin_number": "14", "pin_name": "REF", "pin_description": "Precision 5.0V reference pin."}, {"pin_number": "15", "pin_name": "SS", "pin_description": "A capacitor from SS to ground programs the soft start time."}, {"pin_number": "16", "pin_name": "SHTDWN", "pin_description": "Comparator input to stop the chip. The threshold is 0.5V."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS292D", "family_comparison": "The UCCx580 family has variants with different temperature ranges (UCC1580: -55°C to 125°C, UCC2580: -40°C to 85°C, UCC3580: 0°C to 70°C), UVLO thresholds (9V for -1/-3, 15V for -2/-4), and OUT2 polarity (normal for -1/-2, inverted for -3/-4).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "不适用", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "15V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "430kHz", "quiescent_current": "2.5mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "No", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "No", "output_short_circuit_protection": "No", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "±2.4%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "width": "4", "length": "10", "pin_count": "9"}]}, {"part_number": "UCC2580-3", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "有源钳位PWM控制器", "part_number_title": "Single Ended Active Clamp/Reset PWM", "features": ["Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive", "Programmable deadtime (Turn-on Delay) Between Activation of Each Switch", "Voltage Mode Control with Feedforward Operation", "Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle", "High Current Gate Driver for Both Main and Auxiliary Outputs", "Multiple Protection Features with Latched Shutdown and Soft Restart", "Low Supply Current (100 μA Startup, 1.5 mA Operation)"], "description": "The UCC3580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. While containing all the necessary functions for fixed frequency, high performance pulse width modulation, the additional feature of this design is the inclusion of an auxiliary switch driver which complements the main power switch, and with a programmable deadtime or delay between each transition. This version (-3) has an inverted OUT2 polarity and a 9V UVLO turn-on threshold.", "applications": ["Active clamp forward converter", "Off-line active clamp flyback converter", "Synchronous rectifier switching converter"], "ordering_information": [{"part_number": "UCC2580-3", "order_device": "UCC2580D-3", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC2580-3", "order_device": "UCC2580DTR-3", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"pin_number": "1", "pin_name": "DELAY", "pin_description": "A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2."}, {"pin_number": "2", "pin_name": "LINE", "pin_description": "Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low."}, {"pin_number": "3", "pin_name": "VDD", "pin_description": "Chip power supply pin. VDD should be bypassed to PGND."}, {"pin_number": "4", "pin_name": "OUT1", "pin_description": "Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A."}, {"pin_number": "5", "pin_name": "PGND", "pin_description": "Ground connection for the gate drivers."}, {"pin_number": "6", "pin_name": "OUT2", "pin_description": "Gate drive output for the auxiliary switch with 0.3A drive current capability. Inverted polarity."}, {"pin_number": "7", "pin_name": "CLK", "pin_description": "Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Signal Ground."}, {"pin_number": "9", "pin_name": "RAMP", "pin_description": "Programs the feedforward ramp signal for the PWM comparator."}, {"pin_number": "10", "pin_name": "OSC2", "pin_description": "Oscillator programming pin. A resistor connected to OSC2 controls guaranteed off time."}, {"pin_number": "11", "pin_name": "OSC1", "pin_description": "Oscillator programming pin. The resistor connected to OSC1 sets maximum on time."}, {"pin_number": "12", "pin_name": "EAOUT", "pin_description": "Output of the error amplifier and input to the PWM comparator."}, {"pin_number": "13", "pin_name": "EAIN", "pin_description": "Inverting input to the error amplifier. Used for feedback and loop compensation."}, {"pin_number": "14", "pin_name": "REF", "pin_description": "Precision 5.0V reference pin."}, {"pin_number": "15", "pin_name": "SS", "pin_description": "A capacitor from SS to ground programs the soft start time."}, {"pin_number": "16", "pin_name": "SHTDWN", "pin_description": "Comparator input to stop the chip. The threshold is 0.5V."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS292D", "family_comparison": "The UCCx580 family has variants with different temperature ranges (UCC1580: -55°C to 125°C, UCC2580: -40°C to 85°C, UCC3580: 0°C to 70°C), UVLO thresholds (9V for -1/-3, 15V for -2/-4), and OUT2 polarity (normal for -1/-2, inverted for -3/-4).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "不适用", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "9V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "430kHz", "quiescent_current": "2.5mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "No", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "No", "output_short_circuit_protection": "No", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "±2.4%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "width": "4", "length": "10", "pin_count": "9"}]}, {"part_number": "UCC2580-4", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Obsolete", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "有源钳位PWM控制器", "part_number_title": "Single Ended Active Clamp/Reset PWM", "features": ["Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive", "Programmable deadtime (Turn-on Delay) Between Activation of Each Switch", "Voltage Mode Control with Feedforward Operation", "Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle", "High Current Gate Driver for Both Main and Auxiliary Outputs", "Multiple Protection Features with Latched Shutdown and Soft Restart", "Low Supply Current (100 μA Startup, 1.5 mA Operation)"], "description": "The UCC3580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. While containing all the necessary functions for fixed frequency, high performance pulse width modulation, the additional feature of this design is the inclusion of an auxiliary switch driver which complements the main power switch, and with a programmable deadtime or delay between each transition. This version (-4) has an inverted OUT2 polarity and a 15V UVLO turn-on threshold.", "applications": ["Active clamp forward converter", "Off-line active clamp flyback converter", "Synchronous rectifier switching converter"], "ordering_information": [], "pin_function": [{"pin_number": "1", "pin_name": "DELAY", "pin_description": "A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2."}, {"pin_number": "2", "pin_name": "LINE", "pin_description": "Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low."}, {"pin_number": "3", "pin_name": "VDD", "pin_description": "Chip power supply pin. VDD should be bypassed to PGND."}, {"pin_number": "4", "pin_name": "OUT1", "pin_description": "Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A."}, {"pin_number": "5", "pin_name": "PGND", "pin_description": "Ground connection for the gate drivers."}, {"pin_number": "6", "pin_name": "OUT2", "pin_description": "Gate drive output for the auxiliary switch with 0.3A drive current capability. Inverted polarity."}, {"pin_number": "7", "pin_name": "CLK", "pin_description": "Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Signal Ground."}, {"pin_number": "9", "pin_name": "RAMP", "pin_description": "Programs the feedforward ramp signal for the PWM comparator."}, {"pin_number": "10", "pin_name": "OSC2", "pin_description": "Oscillator programming pin. A resistor connected to OSC2 controls guaranteed off time."}, {"pin_number": "11", "pin_name": "OSC1", "pin_description": "Oscillator programming pin. The resistor connected to OSC1 sets maximum on time."}, {"pin_number": "12", "pin_name": "EAOUT", "pin_description": "Output of the error amplifier and input to the PWM comparator."}, {"pin_number": "13", "pin_name": "EAIN", "pin_description": "Inverting input to the error amplifier. Used for feedback and loop compensation."}, {"pin_number": "14", "pin_name": "REF", "pin_description": "Precision 5.0V reference pin."}, {"pin_number": "15", "pin_name": "SS", "pin_description": "A capacitor from SS to ground programs the soft start time."}, {"pin_number": "16", "pin_name": "SHTDWN", "pin_description": "Comparator input to stop the chip. The threshold is 0.5V."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS292D", "family_comparison": "The UCCx580 family has variants with different temperature ranges (UCC1580: -55°C to 125°C, UCC2580: -40°C to 85°C, UCC3580: 0°C to 70°C), UVLO thresholds (9V for -1/-3, 15V for -2/-4), and OUT2 polarity (normal for -1/-2, inverted for -3/-4).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "不适用", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "15V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "430kHz", "quiescent_current": "2.5mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "No", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "No", "output_short_circuit_protection": "No", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "±2.4%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "width": "4", "length": "10", "pin_count": "9"}]}, {"part_number": "UCC3580-1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "有源钳位PWM控制器", "part_number_title": "Single Ended Active Clamp/Reset PWM", "features": ["Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive", "Programmable deadtime (Turn-on Delay) Between Activation of Each Switch", "Voltage Mode Control with Feedforward Operation", "Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle", "High Current Gate Driver for Both Main and Auxiliary Outputs", "Multiple Protection Features with Latched Shutdown and Soft Restart", "Low Supply Current (100 μA Startup, 1.5 mA Operation)"], "description": "The UCC3580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. While containing all the necessary functions for fixed frequency, high performance pulse width modulation, the additional feature of this design is the inclusion of an auxiliary switch driver which complements the main power switch, and with a programmable deadtime or delay between each transition. This version (-1) has a normal (non-inverted) OUT2 polarity and a 9V UVLO turn-on threshold.", "applications": ["Active clamp forward converter", "Off-line active clamp flyback converter", "Synchronous rectifier switching converter"], "ordering_information": [{"part_number": "UCC3580-1", "order_device": "UCC3580DTR-1", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "未找到", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"pin_number": "1", "pin_name": "DELAY", "pin_description": "A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2."}, {"pin_number": "2", "pin_name": "LINE", "pin_description": "Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low."}, {"pin_number": "3", "pin_name": "VDD", "pin_description": "Chip power supply pin. VDD should be bypassed to PGND."}, {"pin_number": "4", "pin_name": "OUT1", "pin_description": "Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A."}, {"pin_number": "5", "pin_name": "PGND", "pin_description": "Ground connection for the gate drivers."}, {"pin_number": "6", "pin_name": "OUT2", "pin_description": "Gate drive output for the auxiliary switch with 0.3A drive current capability. Normal (non-inverted) polarity."}, {"pin_number": "7", "pin_name": "CLK", "pin_description": "Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Signal Ground."}, {"pin_number": "9", "pin_name": "RAMP", "pin_description": "Programs the feedforward ramp signal for the PWM comparator."}, {"pin_number": "10", "pin_name": "OSC2", "pin_description": "Oscillator programming pin. A resistor connected to OSC2 controls guaranteed off time."}, {"pin_number": "11", "pin_name": "OSC1", "pin_description": "Oscillator programming pin. The resistor connected to OSC1 sets maximum on time."}, {"pin_number": "12", "pin_name": "EAOUT", "pin_description": "Output of the error amplifier and input to the PWM comparator."}, {"pin_number": "13", "pin_name": "EAIN", "pin_description": "Inverting input to the error amplifier. Used for feedback and loop compensation."}, {"pin_number": "14", "pin_name": "REF", "pin_description": "Precision 5.0V reference pin."}, {"pin_number": "15", "pin_name": "SS", "pin_description": "A capacitor from SS to ground programs the soft start time."}, {"pin_number": "16", "pin_name": "SHTDWN", "pin_description": "Comparator input to stop the chip. The threshold is 0.5V."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS292D", "family_comparison": "The UCCx580 family has variants with different temperature ranges (UCC1580: -55°C to 125°C, UCC2580: -40°C to 85°C, UCC3580: 0°C to 70°C), UVLO thresholds (9V for -1/-3, 15V for -2/-4), and OUT2 polarity (normal for -1/-2, inverted for -3/-4).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "不适用", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "9V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "430kHz", "quiescent_current": "2.5mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "No", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "No", "output_short_circuit_protection": "No", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "±2.4%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "width": "4", "length": "10", "pin_count": "9"}]}, {"part_number": "UCC3580-2", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "有源钳位PWM控制器", "part_number_title": "Single Ended Active Clamp/Reset PWM", "features": ["Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive", "Programmable deadtime (Turn-on Delay) Between Activation of Each Switch", "Voltage Mode Control with Feedforward Operation", "Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle", "High Current Gate Driver for Both Main and Auxiliary Outputs", "Multiple Protection Features with Latched Shutdown and Soft Restart", "Low Supply Current (100 μA Startup, 1.5 mA Operation)"], "description": "The UCC3580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. While containing all the necessary functions for fixed frequency, high performance pulse width modulation, the additional feature of this design is the inclusion of an auxiliary switch driver which complements the main power switch, and with a programmable deadtime or delay between each transition. This version (-2) has a normal (non-inverted) OUT2 polarity and a 15V UVLO turn-on threshold.", "applications": ["Active clamp forward converter", "Off-line active clamp flyback converter", "Synchronous rectifier switching converter"], "ordering_information": [{"part_number": "UCC3580-2", "order_device": "UCC3580DTR-2", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "未找到", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"pin_number": "1", "pin_name": "DELAY", "pin_description": "A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2."}, {"pin_number": "2", "pin_name": "LINE", "pin_description": "Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low."}, {"pin_number": "3", "pin_name": "VDD", "pin_description": "Chip power supply pin. VDD should be bypassed to PGND."}, {"pin_number": "4", "pin_name": "OUT1", "pin_description": "Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A."}, {"pin_number": "5", "pin_name": "PGND", "pin_description": "Ground connection for the gate drivers."}, {"pin_number": "6", "pin_name": "OUT2", "pin_description": "Gate drive output for the auxiliary switch with 0.3A drive current capability. Normal (non-inverted) polarity."}, {"pin_number": "7", "pin_name": "CLK", "pin_description": "Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Signal Ground."}, {"pin_number": "9", "pin_name": "RAMP", "pin_description": "Programs the feedforward ramp signal for the PWM comparator."}, {"pin_number": "10", "pin_name": "OSC2", "pin_description": "Oscillator programming pin. A resistor connected to OSC2 controls guaranteed off time."}, {"pin_number": "11", "pin_name": "OSC1", "pin_description": "Oscillator programming pin. The resistor connected to OSC1 sets maximum on time."}, {"pin_number": "12", "pin_name": "EAOUT", "pin_description": "Output of the error amplifier and input to the PWM comparator."}, {"pin_number": "13", "pin_name": "EAIN", "pin_description": "Inverting input to the error amplifier. Used for feedback and loop compensation."}, {"pin_number": "14", "pin_name": "REF", "pin_description": "Precision 5.0V reference pin."}, {"pin_number": "15", "pin_name": "SS", "pin_description": "A capacitor from SS to ground programs the soft start time."}, {"pin_number": "16", "pin_name": "SHTDWN", "pin_description": "Comparator input to stop the chip. The threshold is 0.5V."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS292D", "family_comparison": "The UCCx580 family has variants with different temperature ranges (UCC1580: -55°C to 125°C, UCC2580: -40°C to 85°C, UCC3580: 0°C to 70°C), UVLO thresholds (9V for -1/-3, 15V for -2/-4), and OUT2 polarity (normal for -1/-2, inverted for -3/-4).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "不适用", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "15V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "430kHz", "quiescent_current": "2.5mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "No", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "No", "output_short_circuit_protection": "No", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "±2.4%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "width": "4", "length": "10", "pin_count": "9"}]}, {"part_number": "UCC3580-3", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "有源钳位PWM控制器", "part_number_title": "Single Ended Active Clamp/Reset PWM", "features": ["Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive", "Programmable deadtime (Turn-on Delay) Between Activation of Each Switch", "Voltage Mode Control with Feedforward Operation", "Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle", "High Current Gate Driver for Both Main and Auxiliary Outputs", "Multiple Protection Features with Latched Shutdown and Soft Restart", "Low Supply Current (100 μA Startup, 1.5 mA Operation)"], "description": "The UCC3580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. While containing all the necessary functions for fixed frequency, high performance pulse width modulation, the additional feature of this design is the inclusion of an auxiliary switch driver which complements the main power switch, and with a programmable deadtime or delay between each transition. This version (-3) has an inverted OUT2 polarity and a 9V UVLO turn-on threshold.", "applications": ["Active clamp forward converter", "Off-line active clamp flyback converter", "Synchronous rectifier switching converter"], "ordering_information": [{"part_number": "UCC3580-3", "order_device": "UCC3580DTR-3", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "未找到", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"pin_number": "1", "pin_name": "DELAY", "pin_description": "A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2."}, {"pin_number": "2", "pin_name": "LINE", "pin_description": "Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low."}, {"pin_number": "3", "pin_name": "VDD", "pin_description": "Chip power supply pin. VDD should be bypassed to PGND."}, {"pin_number": "4", "pin_name": "OUT1", "pin_description": "Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A."}, {"pin_number": "5", "pin_name": "PGND", "pin_description": "Ground connection for the gate drivers."}, {"pin_number": "6", "pin_name": "OUT2", "pin_description": "Gate drive output for the auxiliary switch with 0.3A drive current capability. Inverted polarity."}, {"pin_number": "7", "pin_name": "CLK", "pin_description": "Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Signal Ground."}, {"pin_number": "9", "pin_name": "RAMP", "pin_description": "Programs the feedforward ramp signal for the PWM comparator."}, {"pin_number": "10", "pin_name": "OSC2", "pin_description": "Oscillator programming pin. A resistor connected to OSC2 controls guaranteed off time."}, {"pin_number": "11", "pin_name": "OSC1", "pin_description": "Oscillator programming pin. The resistor connected to OSC1 sets maximum on time."}, {"pin_number": "12", "pin_name": "EAOUT", "pin_description": "Output of the error amplifier and input to the PWM comparator."}, {"pin_number": "13", "pin_name": "EAIN", "pin_description": "Inverting input to the error amplifier. Used for feedback and loop compensation."}, {"pin_number": "14", "pin_name": "REF", "pin_description": "Precision 5.0V reference pin."}, {"pin_number": "15", "pin_name": "SS", "pin_description": "A capacitor from SS to ground programs the soft start time."}, {"pin_number": "16", "pin_name": "SHTDWN", "pin_description": "Comparator input to stop the chip. The threshold is 0.5V."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS292D", "family_comparison": "The UCCx580 family has variants with different temperature ranges (UCC1580: -55°C to 125°C, UCC2580: -40°C to 85°C, UCC3580: 0°C to 70°C), UVLO thresholds (9V for -1/-3, 15V for -2/-4), and OUT2 polarity (normal for -1/-2, inverted for -3/-4).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "不适用", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "9V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "430kHz", "quiescent_current": "2.5mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "No", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "No", "output_short_circuit_protection": "No", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "±2.4%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "width": "4", "length": "10", "pin_count": "9"}]}, {"part_number": "UCC3580-4", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "有源钳位PWM控制器", "part_number_title": "Single Ended Active Clamp/Reset PWM", "features": ["Provides Auxiliary Switch Activation Complementary to Main Power Switch Drive", "Programmable deadtime (Turn-on Delay) Between Activation of Each Switch", "Voltage Mode Control with Feedforward Operation", "Programmable Limits for Both Transformer Volt- Second Product and PWM Duty Cycle", "High Current Gate Driver for Both Main and Auxiliary Outputs", "Multiple Protection Features with Latched Shutdown and Soft Restart", "Low Supply Current (100 μA Startup, 1.5 mA Operation)"], "description": "The UCC3580 family of PWM controllers is designed to implement a variety of active clamp/reset and synchronous rectifier switching converter topologies. While containing all the necessary functions for fixed frequency, high performance pulse width modulation, the additional feature of this design is the inclusion of an auxiliary switch driver which complements the main power switch, and with a programmable deadtime or delay between each transition. This version (-4) has an inverted OUT2 polarity and a 15V UVLO turn-on threshold.", "applications": ["Active clamp forward converter", "Off-line active clamp flyback converter", "Synchronous rectifier switching converter"], "ordering_information": [{"part_number": "UCC3580-4", "order_device": "UCC3580DTR-4", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "未找到", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"pin_number": "1", "pin_name": "DELAY", "pin_description": "A resistor from DELAY to GND programs the nonoverlap delay between OUT1 and OUT2."}, {"pin_number": "2", "pin_name": "LINE", "pin_description": "Hysteretic comparator input. Thresholds are 5.0V and 4.5V. Used to sense input line voltage and turn off OUT1 when the line is low."}, {"pin_number": "3", "pin_name": "VDD", "pin_description": "Chip power supply pin. VDD should be bypassed to PGND."}, {"pin_number": "4", "pin_name": "OUT1", "pin_description": "Gate drive output for the main switch capable of sourcing up to 0.5A and sinking 1A."}, {"pin_number": "5", "pin_name": "PGND", "pin_description": "Ground connection for the gate drivers."}, {"pin_number": "6", "pin_name": "OUT2", "pin_description": "Gate drive output for the auxiliary switch with 0.3A drive current capability. Inverted polarity."}, {"pin_number": "7", "pin_name": "CLK", "pin_description": "Oscillator clock output pin from a low impedance CMOS driver. CLK is high during guaranteed off time."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Signal Ground."}, {"pin_number": "9", "pin_name": "RAMP", "pin_description": "Programs the feedforward ramp signal for the PWM comparator."}, {"pin_number": "10", "pin_name": "OSC2", "pin_description": "Oscillator programming pin. A resistor connected to OSC2 controls guaranteed off time."}, {"pin_number": "11", "pin_name": "OSC1", "pin_description": "Oscillator programming pin. The resistor connected to OSC1 sets maximum on time."}, {"pin_number": "12", "pin_name": "EAOUT", "pin_description": "Output of the error amplifier and input to the PWM comparator."}, {"pin_number": "13", "pin_name": "EAIN", "pin_description": "Inverting input to the error amplifier. Used for feedback and loop compensation."}, {"pin_number": "14", "pin_name": "REF", "pin_description": "Precision 5.0V reference pin."}, {"pin_number": "15", "pin_name": "SS", "pin_description": "A capacitor from SS to ground programs the soft start time."}, {"pin_number": "16", "pin_name": "SHTDWN", "pin_description": "Comparator input to stop the chip. The threshold is 0.5V."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS292D", "family_comparison": "The UCCx580 family has variants with different temperature ranges (UCC1580: -55°C to 125°C, UCC2580: -40°C to 85°C, UCC3580: 0°C to 70°C), UVLO thresholds (9V for -1/-3, 15V for -2/-4), and OUT2 polarity (normal for -1/-2, inverted for -3/-4).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "不适用", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "15V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "430kHz", "quiescent_current": "2.5mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "No", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "No", "output_short_circuit_protection": "No", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "±2.4%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "width": "4", "length": "10", "pin_count": "9"}]}]