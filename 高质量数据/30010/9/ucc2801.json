[{"part_number": "UCC28019", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "连续导通模式(CCM)PFC控制器", "part_number_title": "8-Pin Continuous Conduction Mode (CCM) PFC Controller", "features": ["8-pin Solution Without Sensing Line Voltage Reduces External Components", "Wide-Range Universal AC Input Voltage", "Fixed 65-kHz Operating Frequency", "Maximum Duty Cycle of 97%", "Output Over/Under-Voltage Protection", "Input Brown-Out Protection", "Cycle-by-Cycle Peak Current Limiting", "Open Loop Detection", "Low-Power User Controlled Standby Mode"], "description": "The UCC28019 8-pin active Power Factor Correction (PFC) controller uses the boost topology operating in Continuous Conduction Mode (CCM). The controller is suitable for systems in the 100 W to >2 kW range over a wide-range universal ac line input. Startup current during under-voltage lockout is less than 200 μA. The user can control low power standby mode by pulling the VSENSE pin below 0.77 V. Low-distortion wave-shaping of the input current using average current mode control is achieved without input line sensing, reducing the Bill of Materials component count. Simple external networks allow for flexible compensation of the current and voltage control loops. The switching frequency is internally fixed and trimmed to better than 5% accuracy at 25°C. Fast 1.5-A gate peak current drives the external switch. Numerous system-level protection features include peak current limit, soft over-current detection, open-loop detection, input brown-out detection, output over-voltage protection/under-voltage detection, a no-power discharge path on VCOMP, and overload protection on ICOMP. Soft-Start limits boost current during start-up. A trimmed internal reference provides accurate protection thresholds and regulation set-point. An internal clamp limits the gate drive voltage to 12.5 V.", "applications": ["CCM Boost Power Factor Correction Power Converters in the 100 W to >2 kW Range", "Server and Desktop Power Supplies", "Telecom Rectifiers", "Industrial Electronics", "Home Electronics"], "ordering_information": [{"part_number": "UCC28019", "order_device": "UCC28019D", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28019", "order_device": "UCC28019DR", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28019", "order_device": "UCC28019P", "package_type": "PDIP", "package_drawing_code": "未找到", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC28019", "package_type": "SOIC-8, PDIP-8", "pins": [{"pin_number": "1", "pin_name": "GND", "pin_description": "Device ground reference."}, {"pin_number": "2", "pin_name": "ICOMP", "pin_description": "Transconductance current amplifier output. A capacitor connected to GND provides compensation and averaging of the current sense signal in the current control loop. The controller is disabled if the voltage on ICOMP is less than 0.6 V."}, {"pin_number": "3", "pin_name": "ISENSE", "pin_description": "An input for the voltage across the external current sense resistor, which represents the instantaneous current through the PFC boost inductor. This voltage is averaged to eliminate the effects of noise and ripple. Soft Over Current (SOC) limits the average inductor current. Cycle-by-cycle peak current limit (PCL) immediately shuts off the GATE drive if the peak-limit voltage is exceeded. Use a 220-Ω resistor between this pin and the current sense resistor to limit inrush-surge currents into this pin."}, {"pin_number": "4", "pin_name": "VINS", "pin_description": "Input Brown Out Protection (IBOP) detects when the system ac-input voltage is above a user-defined normal operating level, or below a user-defined 'brown-out' level. A filtered resistor-divider network connects from this pin to the rectified-mains node. At startup the controller is disabled until the VINS voltage exceeds a threshold of 1.5 V, initiating a soft-start. The controller is also disabled if VINS drops below the brown-out threshold of 0.8 V. Operation will not resume until both VINS and VSENSE voltages exceed their enable thresholds, initiating another soft-start."}, {"pin_number": "5", "pin_name": "VCOMP", "pin_description": "Transconductance voltage error amplifier output. A resistor-capacitor network connected from this pin to GND provides compensation. VCOMP is held at GND until VCC, VINS, and VSENSE all exceed their threshold voltages. Once these conditions are satisfied, VCOMP is charged until the VSENSE voltage reaches 95% of its nominal regulation level. When the Enhanced Dynamic Response (EDR) is engaged, additional current is applied to VCOMP to reduce the charge time. EDR additional current is inhibited during soft-start. Soft-start is programmed by the capacitance on this pin."}, {"pin_number": "6", "pin_name": "VSENSE", "pin_description": "An external resistor-divider network connected from this pin to the PFC output voltage provides feedback sensing for output voltage regulation. A small capacitor from this pin to GND filters high-frequency noise. Standby disables the controller and discharges VCOMP when the voltage at VSENSE drops below the enable threshold of 0.8V. An internal 100nA current source pulls VSENSE to GND for Open-Loop Protection (OLP), including pin disconnection. Output over-voltage protection (OVP) disables the GATE output when VSENSE exceeds 105% of the reference voltage. Enhanced Dynamic Response (EDR) rapidly returns the output voltage to its normal regulation level when a system line or load step causes VSENSE to fall below 95% of the reference voltage."}, {"pin_number": "7", "pin_name": "VCC", "pin_description": "External bias supply input. Under Voltage Lock Out (UVLO) disables the controller until VCC exceeds a turn-on threshold of 10.5 V. Operation continues until VCC falls below the turn-off (UVLO) threshold of 9.5 V. A ceramic by-pass capacitor of 0.1 µF minimum value should be connected from VCC to GND as close to the device as possible for high frequency filtering of the VCC voltage."}, {"pin_number": "8", "pin_name": "GATE", "pin_description": "Integrated push-pull gate driver for one or more external power MOSFETs. 2.0-A sink and 1.5-A source capability. Output voltage is clamped at 12.5 V."}]}], "datasheet_cn": "未找到", "datasheet_en": "slus755b.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "265V", "min_input_voltage": "85V", "max_output_voltage": "Adjustable", "min_output_voltage": "Adjustable", "max_output_current": "未找到", "max_switch_frequency": "0.065MHz", "quiescent_current": "2100uA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "Externally Set", "operation_mode": "CCM", "output_voltage_config_method": "Adjustable", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "2", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "length": "4.9", "width": "3.91", "pin_count": "8"}]}, {"part_number": "UCC2800", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电流模式PWM控制器", "part_number_title": "UCC280x 低功耗 BiCMOS 电流模式PWM 控制器", "features": ["100µA 典型启动电源电流", "500µA 典型工作电源电流", "工作频率高达 1MHz", "内部软启动", "内部故障软启动", "电流检测信号的内部前沿消隐", "1A 图腾柱输出", "从电流检测到栅极驱动输出的典型响应时间为 70ns", "1.5% 容差电压基准", "与 UC3842 和 UC3842A 相同的引脚排列"], "description": "UCC280x 系列高速、低功耗集成电路包含离线和直流/直流固定频率电流模式开关模式电源所需的所有控制及驱动元件，所需元件数量极少。这些器件采用与 UCx84x 系列相同的引脚配置，还提供内部全周期软启动和电流检测输入的内部前沿消隐等附加功能。UCC280x 系列提供了多种封装、温度范围、最大占空比以及临界电压电平选项。UCC2803 和 UCC2805 等基准电压较低的器件尤为适合电池供电系统，而UCC2802 和 UCC2804 具有较高的基准电压和 UVLO迟滞，是离线电源应用的理想选择。UCC280x 系列器件的额定工作温度范围为 -40°C 至 125°C。", "applications": ["开关模式电源 (SMPS)", "直流/直流转换器", "电源模块", "汽车 PSU", "电池供电型 PSU"], "ordering_information": [{"part_number": "UCC2800", "order_device": "UCC2800DTR", "package_type": "SOIC", "package_drawing_code": "D", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC2800", "order_device": "UCC2800PW", "package_type": "TSSOP", "package_drawing_code": "PW", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC2800", "package_type": "SOIC/TSSOP", "pins": [{"pin_number": "1", "pin_name": "COMP", "pin_description": "COMP 是误差放大器的输出和 PWM 比较器的输入。 UCC280x系列误差放大器是真正的、低输出阻抗的2MHz运算放大器。因此， COMP 端子可同时拉出和灌入电流。但误差放大器内部有电流限制，因此用户可 通过在外部将COMP 强制连接到GND来实现零占空比。"}, {"pin_number": "2", "pin_name": "FB", "pin_description": "FB 是误差放大器的反相输入。为了获得最佳的稳定性，使FB 引线长度保持尽可能短，并使FB 杂散电容保持尽可能小。"}, {"pin_number": "3", "pin_name": "Cs", "pin_description": "CS 是电流检测比较器的输入。UCC280x系列产品配有两种电流检测比较器： PWM 比较器和过流比较器。 UCC280x系列产品具备数字电流检测滤波功能，可在紧随OUT引脚上升沿之后 的 100ns 间隔内将CS 端子与电流检测比较器断开。这种数字滤波功能也称为前 沿消隐，意味着在大多数应用中，CS 端子上无需使用模拟滤波（RC 滤波器）。 与外部 RC 滤波器技术相比，前沿消隐可实现更短的有效CS至OUT传播延迟。 但请注意，OUT信号的最小非零导通时间直接受前沿消隐和CS 到OUT传播延迟的影响。过流比较器仅用于故障检测，超出过流阈值会触发软启动周期。"}, {"pin_number": "4", "pin_name": "RC", "pin_description": "RC 是振荡器时序引脚。对于固定频率操作，通过将电阻器从REF连接到RC来 设置计时电容器充电电流。通过将计时电容器从RC 连接到GND来设置频率。为 了获得最佳性能，应保持计时电容器引线尽可能短且直接连接到GND。如果可 能，为计时电容器和所有其他功能使用单独的接地走线。"}, {"pin_number": "5", "pin_name": "GND", "pin_description": "GND 是此器件上所有功能的参考接地和电源接地。"}, {"pin_number": "6", "pin_name": "OUT", "pin_description": "OUT 是大电流功率驱动器的输出，能够驱动峰值电流超过±750mA 的功率 MOSFET的栅极。当VCC 低于阈值时，OUT主动保持为低电平。 大电流功率驱动器由 FET输出器件组成，可以完全切换到 GND 和VcC。该输出 级还可为过冲和欠冲提供非常低的阻抗。这意味着在许多情况下，无需使用外部 肖特基钳位二极管。"}, {"pin_number": "7", "pin_name": "vcc", "pin_description": "VCC 是该器件的电源输入连接。在正常工作条件下，通过限流电阻器为VCC 供 电。尽管静态VCC 电流极低，但总电源电流更高，具体取决于OUT电流。总 VCC 电流是静态VCC 电流和平均OUT 电流的总和。已知工作频率和 MOSFET 栅极电荷(Qg)，可以通过公式计算平均OUT电流。为了防止出现噪声问题，使用尽可能靠近VCC引脚的陶瓷电容器将VCC旁路至 GND。除陶瓷电容器外，还可以使用电解电容器。在VCC与接地端之间，必须靠 近器件将一个至少为1uF 的电容器与一个0.1uF 陶瓷电容器并联。"}, {"pin_number": "8", "pin_name": "REF", "pin_description": "REF 是误差放大器的电压基准，也是IC 中许多其他功能的电压基准。REF 还可 作为IC上高速开关逻辑电路的逻辑电源。 当 VCC 大于1V且低于UVLO 阈值时，REF 通过 5kΩ 电阻器下拉至接地。这意 味着 REF可用作指示电源系统状态的逻辑输出。通过尽可能靠近引脚的陶瓷电容 器将 REF旁路至GND，这点对于基准稳定性来说非常重要。除陶瓷电容器外， 还可以使用电解电容器。最低需要配备0.1uF的陶瓷电容器。基准上的外部负载 （高于2.5mA）需要额外的REF旁路。 为了防止高速开关瞬态的噪声问题，请使用靠近IC封装的陶瓷电容器将REF 旁 路至接地。"}]}], "datasheet_cn": "SLUS121H", "datasheet_en": "未找到", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "11V", "min_input_voltage": "7.2V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "1A", "max_switch_frequency": "1MHz", "quiescent_current": "500uA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "1.55V", "operation_mode": "异步", "output_voltage_config_method": "Adjustable", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "Internal", "input_over_voltage_protection": "13.5V", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "1.5", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "length": "4.9", "width": "3.91", "pin_count": "8"}]}, {"part_number": "UCC2801", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电流模式PWM控制器", "part_number_title": "UCC280x 低功耗 BiCMOS 电流模式PWM 控制器", "features": ["100µA 典型启动电源电流", "500µA 典型工作电源电流", "工作频率高达 1MHz", "内部软启动", "内部故障软启动", "电流检测信号的内部前沿消隐", "1A 图腾柱输出", "从电流检测到栅极驱动输出的典型响应时间为 70ns", "1.5% 容差电压基准", "与 UC3842 和 UC3842A 相同的引脚排列"], "description": "UCC280x 系列高速、低功耗集成电路包含离线和直流/直流固定频率电流模式开关模式电源所需的所有控制及驱动元件，所需元件数量极少。这些器件采用与 UCx84x 系列相同的引脚配置，还提供内部全周期软启动和电流检测输入的内部前沿消隐等附加功能。UCC280x 系列提供了多种封装、温度范围、最大占空比以及临界电压电平选项。UCC2803 和 UCC2805 等基准电压较低的器件尤为适合电池供电系统，而UCC2802 和 UCC2804 具有较高的基准电压和 UVLO迟滞，是离线电源应用的理想选择。UCC280x 系列器件的额定工作温度范围为 -40°C 至 125°C。", "applications": ["开关模式电源 (SMPS)", "直流/直流转换器", "电源模块", "汽车 PSU", "电池供电型 PSU"], "ordering_information": [{"part_number": "UCC2801", "order_device": "UCC2801DTR", "package_type": "SOIC", "package_drawing_code": "D", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC2801", "order_device": "UCC2801PW", "package_type": "TSSOP", "package_drawing_code": "PW", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC2801", "package_type": "SOIC/TSSOP", "pins": [{"pin_number": "1", "pin_name": "COMP", "pin_description": "COMP 是误差放大器的输出和 PWM 比较器的输入。 UCC280x系列误差放大器是真正的、低输出阻抗的2MHz运算放大器。因此， COMP 端子可同时拉出和灌入电流。但误差放大器内部有电流限制，因此用户可 通过在外部将COMP 强制连接到GND来实现零占空比。"}, {"pin_number": "2", "pin_name": "FB", "pin_description": "FB 是误差放大器的反相输入。为了获得最佳的稳定性，使FB 引线长度保持尽可能短，并使FB 杂散电容保持尽可能小。"}, {"pin_number": "3", "pin_name": "Cs", "pin_description": "CS 是电流检测比较器的输入。UCC280x系列产品配有两种电流检测比较器： PWM 比较器和过流比较器。 UCC280x系列产品具备数字电流检测滤波功能，可在紧随OUT引脚上升沿之后 的 100ns 间隔内将CS 端子与电流检测比较器断开。这种数字滤波功能也称为前 沿消隐，意味着在大多数应用中，CS 端子上无需使用模拟滤波（RC 滤波器）。 与外部 RC 滤波器技术相比，前沿消隐可实现更短的有效CS至OUT传播延迟。 但请注意，OUT信号的最小非零导通时间直接受前沿消隐和CS 到OUT传播延迟的影响。过流比较器仅用于故障检测，超出过流阈值会触发软启动周期。"}, {"pin_number": "4", "pin_name": "RC", "pin_description": "RC 是振荡器时序引脚。对于固定频率操作，通过将电阻器从REF连接到RC来 设置计时电容器充电电流。通过将计时电容器从RC 连接到GND来设置频率。为 了获得最佳性能，应保持计时电容器引线尽可能短且直接连接到GND。如果可 能，为计时电容器和所有其他功能使用单独的接地走线。"}, {"pin_number": "5", "pin_name": "GND", "pin_description": "GND 是此器件上所有功能的参考接地和电源接地。"}, {"pin_number": "6", "pin_name": "OUT", "pin_description": "OUT 是大电流功率驱动器的输出，能够驱动峰值电流超过±750mA 的功率 MOSFET的栅极。当VCC 低于阈值时，OUT主动保持为低电平。 大电流功率驱动器由 FET输出器件组成，可以完全切换到 GND 和VcC。该输出 级还可为过冲和欠冲提供非常低的阻抗。这意味着在许多情况下，无需使用外部 肖特基钳位二极管。"}, {"pin_number": "7", "pin_name": "vcc", "pin_description": "VCC 是该器件的电源输入连接。在正常工作条件下，通过限流电阻器为VCC 供 电。尽管静态VCC 电流极低，但总电源电流更高，具体取决于OUT电流。总 VCC 电流是静态VCC 电流和平均OUT 电流的总和。已知工作频率和 MOSFET 栅极电荷(Qg)，可以通过公式计算平均OUT电流。为了防止出现噪声问题，使用尽可能靠近VCC引脚的陶瓷电容器将VCC旁路至 GND。除陶瓷电容器外，还可以使用电解电容器。在VCC与接地端之间，必须靠 近器件将一个至少为1uF 的电容器与一个0.1uF 陶瓷电容器并联。"}, {"pin_number": "8", "pin_name": "REF", "pin_description": "REF 是误差放大器的电压基准，也是IC 中许多其他功能的电压基准。REF 还可 作为IC上高速开关逻辑电路的逻辑电源。 当 VCC 大于1V且低于UVLO 阈值时，REF 通过 5kΩ 电阻器下拉至接地。这意 味着 REF可用作指示电源系统状态的逻辑输出。通过尽可能靠近引脚的陶瓷电容 器将 REF旁路至GND，这点对于基准稳定性来说非常重要。除陶瓷电容器外， 还可以使用电解电容器。最低需要配备0.1uF的陶瓷电容器。基准上的外部负载 （高于2.5mA）需要额外的REF旁路。 为了防止高速开关瞬态的噪声问题，请使用靠近IC封装的陶瓷电容器将REF 旁 路至接地。"}]}], "datasheet_cn": "SLUS121H", "datasheet_en": "未找到", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "11V", "min_input_voltage": "9.4V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "1A", "max_switch_frequency": "1MHz", "quiescent_current": "500uA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "1.55V", "operation_mode": "异步", "output_voltage_config_method": "Adjustable", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "Internal", "input_over_voltage_protection": "13.5V", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "1.5", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "length": "4.9", "width": "3.91", "pin_count": "8"}]}, {"part_number": "UCC2802", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电流模式PWM控制器", "part_number_title": "UCC280x 低功耗 BiCMOS 电流模式PWM 控制器", "features": ["100µA 典型启动电源电流", "500µA 典型工作电源电流", "工作频率高达 1MHz", "内部软启动", "内部故障软启动", "电流检测信号的内部前沿消隐", "1A 图腾柱输出", "从电流检测到栅极驱动输出的典型响应时间为 70ns", "1.5% 容差电压基准", "与 UC3842 和 UC3842A 相同的引脚排列"], "description": "UCC280x 系列高速、低功耗集成电路包含离线和直流/直流固定频率电流模式开关模式电源所需的所有控制及驱动元件，所需元件数量极少。这些器件采用与 UCx84x 系列相同的引脚配置，还提供内部全周期软启动和电流检测输入的内部前沿消隐等附加功能。UCC280x 系列提供了多种封装、温度范围、最大占空比以及临界电压电平选项。UCC2803 和 UCC2805 等基准电压较低的器件尤为适合电池供电系统，而UCC2802 和 UCC2804 具有较高的基准电压和 UVLO迟滞，是离线电源应用的理想选择。UCC280x 系列器件的额定工作温度范围为 -40°C 至 125°C。", "applications": ["开关模式电源 (SMPS)", "直流/直流转换器", "电源模块", "汽车 PSU", "电池供电型 PSU"], "ordering_information": [{"part_number": "UCC2802", "order_device": "UCC2802DTR", "package_type": "SOIC", "package_drawing_code": "D", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC2802", "order_device": "UCC2802PW", "package_type": "TSSOP", "package_drawing_code": "PW", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC2802", "package_type": "SOIC/TSSOP", "pins": [{"pin_number": "1", "pin_name": "COMP", "pin_description": "COMP 是误差放大器的输出和 PWM 比较器的输入。 UCC280x系列误差放大器是真正的、低输出阻抗的2MHz运算放大器。因此， COMP 端子可同时拉出和灌入电流。但误差放大器内部有电流限制，因此用户可 通过在外部将COMP 强制连接到GND来实现零占空比。"}, {"pin_number": "2", "pin_name": "FB", "pin_description": "FB 是误差放大器的反相输入。为了获得最佳的稳定性，使FB 引线长度保持尽可能短，并使FB 杂散电容保持尽可能小。"}, {"pin_number": "3", "pin_name": "Cs", "pin_description": "CS 是电流检测比较器的输入。UCC280x系列产品配有两种电流检测比较器： PWM 比较器和过流比较器。 UCC280x系列产品具备数字电流检测滤波功能，可在紧随OUT引脚上升沿之后 的 100ns 间隔内将CS 端子与电流检测比较器断开。这种数字滤波功能也称为前 沿消隐，意味着在大多数应用中，CS 端子上无需使用模拟滤波（RC 滤波器）。 与外部 RC 滤波器技术相比，前沿消隐可实现更短的有效CS至OUT传播延迟。 但请注意，OUT信号的最小非零导通时间直接受前沿消隐和CS 到OUT传播延迟的影响。过流比较器仅用于故障检测，超出过流阈值会触发软启动周期。"}, {"pin_number": "4", "pin_name": "RC", "pin_description": "RC 是振荡器时序引脚。对于固定频率操作，通过将电阻器从REF连接到RC来 设置计时电容器充电电流。通过将计时电容器从RC 连接到GND来设置频率。为 了获得最佳性能，应保持计时电容器引线尽可能短且直接连接到GND。如果可 能，为计时电容器和所有其他功能使用单独的接地走线。"}, {"pin_number": "5", "pin_name": "GND", "pin_description": "GND 是此器件上所有功能的参考接地和电源接地。"}, {"pin_number": "6", "pin_name": "OUT", "pin_description": "OUT 是大电流功率驱动器的输出，能够驱动峰值电流超过±750mA 的功率 MOSFET的栅极。当VCC 低于阈值时，OUT主动保持为低电平。 大电流功率驱动器由 FET输出器件组成，可以完全切换到 GND 和VcC。该输出 级还可为过冲和欠冲提供非常低的阻抗。这意味着在许多情况下，无需使用外部 肖特基钳位二极管。"}, {"pin_number": "7", "pin_name": "vcc", "pin_description": "VCC 是该器件的电源输入连接。在正常工作条件下，通过限流电阻器为VCC 供 电。尽管静态VCC 电流极低，但总电源电流更高，具体取决于OUT电流。总 VCC 电流是静态VCC 电流和平均OUT 电流的总和。已知工作频率和 MOSFET 栅极电荷(Qg)，可以通过公式计算平均OUT电流。为了防止出现噪声问题，使用尽可能靠近VCC引脚的陶瓷电容器将VCC旁路至 GND。除陶瓷电容器外，还可以使用电解电容器。在VCC与接地端之间，必须靠 近器件将一个至少为1uF 的电容器与一个0.1uF 陶瓷电容器并联。"}, {"pin_number": "8", "pin_name": "REF", "pin_description": "REF 是误差放大器的电压基准，也是IC 中许多其他功能的电压基准。REF 还可 作为IC上高速开关逻辑电路的逻辑电源。 当 VCC 大于1V且低于UVLO 阈值时，REF 通过 5kΩ 电阻器下拉至接地。这意 味着 REF可用作指示电源系统状态的逻辑输出。通过尽可能靠近引脚的陶瓷电容 器将 REF旁路至GND，这点对于基准稳定性来说非常重要。除陶瓷电容器外， 还可以使用电解电容器。最低需要配备0.1uF的陶瓷电容器。基准上的外部负载 （高于2.5mA）需要额外的REF旁路。 为了防止高速开关瞬态的噪声问题，请使用靠近IC封装的陶瓷电容器将REF 旁 路至接地。"}]}], "datasheet_cn": "SLUS121H", "datasheet_en": "未找到", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "11V", "min_input_voltage": "12.5V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "1A", "max_switch_frequency": "1MHz", "quiescent_current": "500uA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "1.55V", "operation_mode": "异步", "output_voltage_config_method": "Adjustable", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "Internal", "input_over_voltage_protection": "13.5V", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "1.5", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "length": "4.9", "width": "3.91", "pin_count": "8"}]}, {"part_number": "UCC2803", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电流模式PWM控制器", "part_number_title": "UCC280x 低功耗 BiCMOS 电流模式PWM 控制器", "features": ["100µA 典型启动电源电流", "500µA 典型工作电源电流", "工作频率高达 1MHz", "内部软启动", "内部故障软启动", "电流检测信号的内部前沿消隐", "1A 图腾柱输出", "从电流检测到栅极驱动输出的典型响应时间为 70ns", "1.5% 容差电压基准", "与 UC3842 和 UC3842A 相同的引脚排列"], "description": "UCC280x 系列高速、低功耗集成电路包含离线和直流/直流固定频率电流模式开关模式电源所需的所有控制及驱动元件，所需元件数量极少。这些器件采用与 UCx84x 系列相同的引脚配置，还提供内部全周期软启动和电流检测输入的内部前沿消隐等附加功能。UCC280x 系列提供了多种封装、温度范围、最大占空比以及临界电压电平选项。UCC2803 和 UCC2805 等基准电压较低的器件尤为适合电池供电系统，而UCC2802 和 UCC2804 具有较高的基准电压和 UVLO迟滞，是离线电源应用的理想选择。UCC280x 系列器件的额定工作温度范围为 -40°C 至 125°C。", "applications": ["开关模式电源 (SMPS)", "直流/直流转换器", "电源模块", "汽车 PSU", "电池供电型 PSU"], "ordering_information": [{"part_number": "UCC2803", "order_device": "UCC2803DTR", "package_type": "SOIC", "package_drawing_code": "D", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC2803", "order_device": "UCC2803PWTR", "package_type": "TSSOP", "package_drawing_code": "PW", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC2803", "package_type": "SOIC/TSSOP", "pins": [{"pin_number": "1", "pin_name": "COMP", "pin_description": "COMP 是误差放大器的输出和 PWM 比较器的输入。 UCC280x系列误差放大器是真正的、低输出阻抗的2MHz运算放大器。因此， COMP 端子可同时拉出和灌入电流。但误差放大器内部有电流限制，因此用户可 通过在外部将COMP 强制连接到GND来实现零占空比。"}, {"pin_number": "2", "pin_name": "FB", "pin_description": "FB 是误差放大器的反相输入。为了获得最佳的稳定性，使FB 引线长度保持尽可能短，并使FB 杂散电容保持尽可能小。"}, {"pin_number": "3", "pin_name": "Cs", "pin_description": "CS 是电流检测比较器的输入。UCC280x系列产品配有两种电流检测比较器： PWM 比较器和过流比较器。 UCC280x系列产品具备数字电流检测滤波功能，可在紧随OUT引脚上升沿之后 的 100ns 间隔内将CS 端子与电流检测比较器断开。这种数字滤波功能也称为前 沿消隐，意味着在大多数应用中，CS 端子上无需使用模拟滤波（RC 滤波器）。 与外部 RC 滤波器技术相比，前沿消隐可实现更短的有效CS至OUT传播延迟。 但请注意，OUT信号的最小非零导通时间直接受前沿消隐和CS 到OUT传播延迟的影响。过流比较器仅用于故障检测，超出过流阈值会触发软启动周期。"}, {"pin_number": "4", "pin_name": "RC", "pin_description": "RC 是振荡器时序引脚。对于固定频率操作，通过将电阻器从REF连接到RC来 设置计时电容器充电电流。通过将计时电容器从RC 连接到GND来设置频率。为 了获得最佳性能，应保持计时电容器引线尽可能短且直接连接到GND。如果可 能，为计时电容器和所有其他功能使用单独的接地走线。"}, {"pin_number": "5", "pin_name": "GND", "pin_description": "GND 是此器件上所有功能的参考接地和电源接地。"}, {"pin_number": "6", "pin_name": "OUT", "pin_description": "OUT 是大电流功率驱动器的输出，能够驱动峰值电流超过±750mA 的功率 MOSFET的栅极。当VCC 低于阈值时，OUT主动保持为低电平。 大电流功率驱动器由 FET输出器件组成，可以完全切换到 GND 和VcC。该输出 级还可为过冲和欠冲提供非常低的阻抗。这意味着在许多情况下，无需使用外部 肖特基钳位二极管。"}, {"pin_number": "7", "pin_name": "vcc", "pin_description": "VCC 是该器件的电源输入连接。在正常工作条件下，通过限流电阻器为VCC 供 电。尽管静态VCC 电流极低，但总电源电流更高，具体取决于OUT电流。总 VCC 电流是静态VCC 电流和平均OUT 电流的总和。已知工作频率和 MOSFET 栅极电荷(Qg)，可以通过公式计算平均OUT电流。为了防止出现噪声问题，使用尽可能靠近VCC引脚的陶瓷电容器将VCC旁路至 GND。除陶瓷电容器外，还可以使用电解电容器。在VCC与接地端之间，必须靠 近器件将一个至少为1uF 的电容器与一个0.1uF 陶瓷电容器并联。"}, {"pin_number": "8", "pin_name": "REF", "pin_description": "REF 是误差放大器的电压基准，也是IC 中许多其他功能的电压基准。REF 还可 作为IC上高速开关逻辑电路的逻辑电源。 当 VCC 大于1V且低于UVLO 阈值时，REF 通过 5kΩ 电阻器下拉至接地。这意 味着 REF可用作指示电源系统状态的逻辑输出。通过尽可能靠近引脚的陶瓷电容 器将 REF旁路至GND，这点对于基准稳定性来说非常重要。除陶瓷电容器外， 还可以使用电解电容器。最低需要配备0.1uF的陶瓷电容器。基准上的外部负载 （高于2.5mA）需要额外的REF旁路。 为了防止高速开关瞬态的噪声问题，请使用靠近IC封装的陶瓷电容器将REF 旁 路至接地。"}]}], "datasheet_cn": "SLUS121H", "datasheet_en": "未找到", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "11V", "min_input_voltage": "4.1V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "1A", "max_switch_frequency": "1MHz", "quiescent_current": "500uA", "high_side_mosfet_resistance": "未找到", "low_side_mosfet_resistance": "未找到", "over_current_protection_threshold": "1.55V", "operation_mode": "异步", "output_voltage_config_method": "Adjustable", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "Internal", "input_over_voltage_protection": "13.5V", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "1.5", "output_reference_voltage": "4V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "length": "4.9", "width": "3.91", "pin_count": "8"}]}]