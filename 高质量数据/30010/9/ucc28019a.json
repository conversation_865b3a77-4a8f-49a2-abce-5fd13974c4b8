{"part_number": "UCC28019A", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "CCM PFC控制器", "part_number_title": "UCC28019A 8-Pin Continuous Conduction Mode (CCM) PFC Controller", "features": ["8-Pin Solution Reduces External Components", "Wide-Range Universal AC Input Voltage", "Fixed 65-kHz Operating Frequency", "Maximum Duty Cycle of 98% (typ.)", "Output Over/Undervoltage Protection", "Input Brown-Out Protection", "Cycle-by-Cycle Peak Current Limiting", "Open Loop Detection", "Low-Power User-Controlled Standby Mode"], "description": "The UCC28019A 8-pin active Power Factor Correction (PFC) controller uses the boost topology operating in Continuous Conduction Mode (CCM). The controller is suitable for systems in the 100 W to >2 kW range over a wide-range universal ac line input. Start-up current during undervoltage lockout is less than 200 μA. The user can control low power standby mode by pulling the VSENSE pin below 0.77 V. Low-distortion wave shaping of the input current using average current mode control is achieved without input line sensing, reducing the external component count. Simple external networks allow for flexible compensation of the current and voltage control loops. The switching frequency is internally fixed and trimmed to better than ±5% accuracy at 25°C. Fast 1.5-A peak gate current drives the external switch. Numerous system-level protection features include peak current limit, soft over-current, open-loop detection, input brown-out, and output over/undervoltage. Soft-start limits boost current during start-up. A trimmed internal reference provides accurate protection thresholds and a regulation set-point. An internal clamp limits the gate drive voltage to 12.5 V.", "applications": ["CCM Boost Power Factor Correction Power Converters in the 100 W to > 2 kW Range", "Digital TV", "Home Electronics", "White Goods and Industrial Electronics", "Server and Desktop Power Supplies"], "ordering_information": [{"part_number": "UCC28019A", "order_device": "UCC28019AD", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28019A", "order_device": "UCC28019AD.A", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28019A", "order_device": "UCC28019ADR", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28019A", "order_device": "UCC28019ADR.A", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28019A", "order_device": "UCC28019ADR.B", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "Adjustable", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC28019A", "package_type": "SOIC, PDIP", "pin_number": "1", "pin_name": "GND", "pin_description": "Ground: device ground reference."}, {"product_part_number": "UCC28019A", "package_type": "SOIC, PDIP", "pin_number": "2", "pin_name": "ICOMP", "pin_description": "Current loop compensation: Transconductance current amplifier output. A capacitor connected to GND provides compensation and averaging of the current sense signal in the current control loop. The controller is disabled if the voltage on ICOMP is less than 0.6 V."}, {"product_part_number": "UCC28019A", "package_type": "SOIC, PDIP", "pin_number": "3", "pin_name": "ISENSE", "pin_description": "Inductor current sense: Input for the voltage across the external current sense resistor, which represents the instantaneous current through the PFC boost inductor. This voltage is averaged by the current amplifier to eliminate the effects of ripple and noise. Soft Over Current (SOC) limits the average inductor current. Cycle-by-cycle Peak Current Limit (PCL) immediately shuts off the GATE drive if the peak-limit voltage is exceeded. An internal 1.5-µA current source pulls ISENSE above 0.1 V to shut down PFC operation if this pin becomes open-circuited. Use a 220-Ω resistor between this pin and the current sense resistor to limit inrush-surge currents into this pin."}, {"product_part_number": "UCC28019A", "package_type": "SOIC, PDIP", "pin_number": "4", "pin_name": "VINS", "pin_description": "Input ac voltage sense: A filtered resistor-divider network connects from this pin to the rectified-mains node. Input Brown-Out Protection (IBOP) detects when the system ac-input voltage is above a user-defined normal operating level, or below a user-defined 'brown-out' level. At startup the controller is disabled until the VINS voltage exceeds a threshold of 1.5 V, initiating a soft start. The controller is also disabled if VINS drops below the brown-out threshold of 0.8 V. Operation will not resume until both VINS and VSENSE voltages exceed their enable thresholds, initiating another soft start."}, {"product_part_number": "UCC28019A", "package_type": "SOIC, PDIP", "pin_number": "5", "pin_name": "VCOMP", "pin_description": "Voltage loop compensation: Transconductance voltage error amplifier output. A resistor-capacitor network connected from this pin to GND provides compensation. VCOMP is held at GND until VCC, VINS, and VSENSE all exceed their threshold voltages. Once these conditions are satisfied, VCOMP is charged until the VSENSE voltage reaches 99% of its nominal regulation level. When Enhanced Dynamic Response (EDR) is engaged, a higher transconductance is applied to VCOMP to reduce the charge time for faster transient response. Soft Start is programmed by the capacitance on this pin. The EDR higher transconductance is inhibited during Soft Start."}, {"product_part_number": "UCC28019A", "package_type": "SOIC, PDIP", "pin_number": "6", "pin_name": "VSENSE", "pin_description": "Output voltage sense: An external resistor-divider network connected from this pin to the PFC output voltage provides feedback sensing for regulation to the internal 5-V reference voltage. A small capacitor from this pin to GND filters high-frequency noise. Standby mode disables the controller and discharges VCOMP when the voltage at VSENSE drops below the enable threshold of 0.8 V. An internal 100-nA current source pulls VSENSE to GND for Open-Loop Protection (OLP), including pin disconnection. Output Over-Voltage Protection (OVP) disables the GATE output when VSENSE exceeds 105% of the reference voltage. Enhanced Dynamic Response (EDR) rapidly returns the output voltage to its normal regulation level when a system line or load step causes VSENSE to fall below 95% of the reference voltage."}, {"product_part_number": "UCC28019A", "package_type": "SOIC, PDIP", "pin_number": "7", "pin_name": "VCC", "pin_description": "Device supply: External bias supply input. Under-Voltage Lockout (UVLO) disables the controller until VCC exceeds a turn-on threshold of 10.5 V. Operation continues until VCC falls below the turn-off (UVLO) threshold of 9.5 V. A ceramic by-pass capacitor of 0.1 µF minimum value should be connected from VCC to GND as close to the device as possible for high frequency filtering of the VCC voltage."}, {"product_part_number": "UCC28019A", "package_type": "SOIC, PDIP", "pin_number": "8", "pin_name": "GATE", "pin_description": "Gate drive: Integrated push-pull gate driver for one or more external power MOSFETs. Typical 2.0-A sink and 1.5-A source capability. Output voltage is typically clamped at 12.5 V."}], "datasheet_cn": "未找到", "datasheet_en": "UCC28019A_SLUS828D.pdf", "family_comparison": "Related Products: UCC28019 (8-Pin CCM PFC Controller), UCC3817/18 (Full-Feature PFC Controller), UC2853A (8-Pin CCM PFC Controller)", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "265V", "min_input_voltage": "85V", "max_output_voltage": "Adjustable", "min_output_voltage": "Adjustable", "max_output_current": "Application Dependent", "max_switch_frequency": "65kHz", "quiescent_current": "2.2mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "Adjustable", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "2", "output_reference_voltage": "5"}, "package": [{"type": "OPTION", "length": "4.9", "width": "3.91", "pin_count": "8", "pitch": "1.27", "height": "1.75"}]}