[{"part_number": "UCC28050", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "临界导通模式(CRM/TM)", "part_number_title": "UCC2805x, UCC3805x Transition Mode PFC Controller", "features": ["Transition Mode PFC Controller for Low Implementation Cost", "Industry Pin Compatibility With Improved Feature Set", "Improved Transient Response With Slew-Rate Comparator", "Zero Power Detect to Prevent Overvoltage Protection (OVP) During Light Load Conditions", "Accurate Internal VREF for Tight Output Regulation", "Two UVLO Options", "OVP, Open-Feedback Protection, and Enable Circuits", "±750-mA Peak Gate Drive Current", "Low Start-Up and Operating Currents", "Lead (Pb)-Free Packages"], "description": "The UCC38050 and UCC38051 are PFC controllers for low-to-medium power applications requiring compliance with IEC 1000-3-2 harmonic reduction standard. The controllers are designed for a boost preregulator operating in transition mode (also referred to as boundary-conduction mode or critical conduction-mode operation). They feature a transconductance voltage amplifier for feedback error processing, a simple multiplier for generating a current command proportional to the input voltage, a current-sense (PWM) comparator, PWM logic, and a totem-pole driver for driving an external FET. In the transition mode operation, the PWM circuit is self-oscillating, with the turnon being governed by an inductor zero-current detector (ZCD pin), and the turnoff being governed by the current-sense comparator. Additionally, the controller provides features such as peak current limit, default timer, overvoltage protection (OVP) and enable.", "applications": ["Single-Stage PFC Flyback Converters for Lighting and Motor Drives", "Switch-Mode Power Supplies for Desktops, Monitors, TVs, and Set Top Boxes (STBs)", "AC Adapter Front-End Power Supplies", "Electronic Ballasts"], "ordering_information": [{"part_number": "UCC28050", "order_device": "UCC28050D", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "105"}, {"part_number": "UCC28050", "order_device": "UCC28050DR", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "105"}, {"part_number": "UCC28050", "order_device": "UCC28050P", "package_type": "PDIP", "package_drawing_code": "P (R-PDIP-T8)", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "105"}], "pin_function": [{"product_part_number": "UCC2805x, UCC3805x", "package_type": "SOIC-8, PDIP-8", "pins": [{"pin_number": "1", "pin_name": "VO_SNS", "pin_description": "This pin senses the boost regulator output voltage through a voltage divider. Internally, this pin is the inverting input to the transconductance amplifier (with a nominal value of 2.5 V) and also is input to the OVP comparator. Additionally, pulling this pin below the ENABLE threshold turns off the output switching, ensuring that the gate drive is held off while the boost output is pre-charging, and also ensuring no runaway if the feedback path is open."}, {"pin_number": "2", "pin_name": "COMP", "pin_description": "Output of the transconductance error amplifier. Loop compensation components are connected between this pin and ground. The output current capability of this pin is 10-μA under normal conditions, but increases to approximately 1-mA when the differential input is greater than the specified values in the specifications table. This voltage is one of the inputs to the multiplier, with a dynamic input range of 2.5 V to 3.8 V. During zero power or overvoltage conditions, this pin goes below 2.5 V nominal. When it goes below 2.3 V, the zero power comparator is activated, which prevents the gate drive from switching."}, {"pin_number": "3", "pin_name": "MULTIN", "pin_description": "This pin senses the instantaneous boost regulator input voltage through a voltage divider. The voltage acts as one of the inputs to the internal multiplier. Recommended operating range is 0 V to 2.5 V at high line."}, {"pin_number": "4", "pin_name": "CS", "pin_description": "This pin senses the instantaneous switch current in the boost switch and uses it as the internal ramp for PWM comparator. The internal circuitry filters out switching noise spikes without requiring external components. In addition, an external R-C filter may be required to suppress the noise spikes. An internal clamp on the multiplier output terminates the switching cycle if this pin voltage exceeds 1.7 V. Additional external filtering may be required."}, {"pin_number": "5", "pin_name": "ZCD", "pin_description": "Input for the zero current detect comparator. The boost inductor current is indirectly sensed through the bias winding on the boost inductor. The ZCD pin input goes low when the inductor current reaches zero and that transition is detected. Internal active voltage clamps are provided to prevent this pin from going below ground or too high. If zero current is not detected within 400 µs, a reset timer sets the latch and gate drive."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "The chip reference ground. All bypassing elements are connected to ground pin with shortest loops feasible."}, {"pin_number": "7", "pin_name": "DRV", "pin_description": "The gate drive output for an external boost switch. This output is capable of delivering up to 750-mA peak currents during turn-on and turn-off. An external gate drive resistor may be needed to limit the peak current depending on the Vcc voltage being used. Below the UVLO threshold, the output is held low."}, {"pin_number": "8", "pin_name": "VCC", "pin_description": "The supply voltage for the chip. This pin should be bypassed with a high-frequency capacitor (greater than 0.1-µF) and tied to GND. The UCC38050 has a wide UVLO hysteresis of approximately 6.3 V that allows use of a lower value supply capacitor on this pin for quicker and easier start-up. The UCC38051 has a narrow UVLO hysteresis with of about 2.8 V, and a start-up voltage of about 12.5 V for applications where the operation of the PFC device must be controlled by a downstream PWM controller."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS515G", "family_comparison": "UCCx8050与UCCx8051的主要参数差异在于：1. UVLO开启阈值：UCCx8050为15.8V，UCCx8051为12.5V。2. gM放大器源电流：UCCx8050典型值为1.3mA，UCCx8051为300µA。", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "10.7V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "未找到", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.7V on CS pin", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "脉冲跳跃", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "±2%", "output_reference_voltage": "2.5V"}, "package": [{"type": "OPTION", "pin_count": "1", "pitch": "1.27", "height": "1.75", "width": "3.98", "length": "10.16"}]}, {"part_number": "UCC28051", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "临界导通模式(CRM/TM)", "part_number_title": "UCC2805x, UCC3805x Transition Mode PFC Controller", "features": ["Transition Mode PFC Controller for Low Implementation Cost", "Industry Pin Compatibility With Improved Feature Set", "Improved Transient Response With Slew-Rate Comparator", "Zero Power Detect to Prevent Overvoltage Protection (OVP) During Light Load Conditions", "Accurate Internal VREF for Tight Output Regulation", "Two UVLO Options", "OVP, Open-Feedback Protection, and Enable Circuits", "±750-mA Peak Gate Drive Current", "Low Start-Up and Operating Currents", "Lead (Pb)-Free Packages"], "description": "The UCC38050 and UCC38051 are PFC controllers for low-to-medium power applications requiring compliance with IEC 1000-3-2 harmonic reduction standard. The controllers are designed for a boost preregulator operating in transition mode (also referred to as boundary-conduction mode or critical conduction-mode operation). They feature a transconductance voltage amplifier for feedback error processing, a simple multiplier for generating a current command proportional to the input voltage, a current-sense (PWM) comparator, PWM logic, and a totem-pole driver for driving an external FET. In the transition mode operation, the PWM circuit is self-oscillating, with the turnon being governed by an inductor zero-current detector (ZCD pin), and the turnoff being governed by the current-sense comparator. Additionally, the controller provides features such as peak current limit, default timer, overvoltage protection (OVP) and enable.", "applications": ["Single-Stage PFC Flyback Converters for Lighting and Motor Drives", "Switch-Mode Power Supplies for Desktops, Monitors, TVs, and Set Top Boxes (STBs)", "AC Adapter Front-End Power Supplies", "Electronic Ballasts"], "ordering_information": [{"part_number": "UCC28051", "order_device": "UCC28051D", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "105"}, {"part_number": "UCC28051", "order_device": "UCC28051DG4", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "105"}, {"part_number": "UCC28051", "order_device": "UCC28051DR", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "105"}, {"part_number": "UCC28051", "order_device": "UCC28051DRG4", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "105"}, {"part_number": "UCC28051", "order_device": "UCC28051P", "package_type": "PDIP", "package_drawing_code": "P (R-PDIP-T8)", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "105"}], "pin_function": [{"product_part_number": "UCC2805x, UCC3805x", "package_type": "SOIC-8, PDIP-8", "pins": [{"pin_number": "1", "pin_name": "VO_SNS", "pin_description": "This pin senses the boost regulator output voltage through a voltage divider. Internally, this pin is the inverting input to the transconductance amplifier (with a nominal value of 2.5 V) and also is input to the OVP comparator. Additionally, pulling this pin below the ENABLE threshold turns off the output switching, ensuring that the gate drive is held off while the boost output is pre-charging, and also ensuring no runaway if the feedback path is open."}, {"pin_number": "2", "pin_name": "COMP", "pin_description": "Output of the transconductance error amplifier. Loop compensation components are connected between this pin and ground. The output current capability of this pin is 10-μA under normal conditions, but increases to approximately 1-mA when the differential input is greater than the specified values in the specifications table. This voltage is one of the inputs to the multiplier, with a dynamic input range of 2.5 V to 3.8 V. During zero power or overvoltage conditions, this pin goes below 2.5 V nominal. When it goes below 2.3 V, the zero power comparator is activated, which prevents the gate drive from switching."}, {"pin_number": "3", "pin_name": "MULTIN", "pin_description": "This pin senses the instantaneous boost regulator input voltage through a voltage divider. The voltage acts as one of the inputs to the internal multiplier. Recommended operating range is 0 V to 2.5 V at high line."}, {"pin_number": "4", "pin_name": "CS", "pin_description": "This pin senses the instantaneous switch current in the boost switch and uses it as the internal ramp for PWM comparator. The internal circuitry filters out switching noise spikes without requiring external components. In addition, an external R-C filter may be required to suppress the noise spikes. An internal clamp on the multiplier output terminates the switching cycle if this pin voltage exceeds 1.7 V. Additional external filtering may be required."}, {"pin_number": "5", "pin_name": "ZCD", "pin_description": "Input for the zero current detect comparator. The boost inductor current is indirectly sensed through the bias winding on the boost inductor. The ZCD pin input goes low when the inductor current reaches zero and that transition is detected. Internal active voltage clamps are provided to prevent this pin from going below ground or too high. If zero current is not detected within 400 µs, a reset timer sets the latch and gate drive."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "The chip reference ground. All bypassing elements are connected to ground pin with shortest loops feasible."}, {"pin_number": "7", "pin_name": "DRV", "pin_description": "The gate drive output for an external boost switch. This output is capable of delivering up to 750-mA peak currents during turn-on and turn-off. An external gate drive resistor may be needed to limit the peak current depending on the Vcc voltage being used. Below the UVLO threshold, the output is held low."}, {"pin_number": "8", "pin_name": "VCC", "pin_description": "The supply voltage for the chip. This pin should be bypassed with a high-frequency capacitor (greater than 0.1-µF) and tied to GND. The UCC38050 has a wide UVLO hysteresis of approximately 6.3 V that allows use of a lower value supply capacitor on this pin for quicker and easier start-up. The UCC38051 has a narrow UVLO hysteresis with of about 2.8 V, and a start-up voltage of about 12.5 V for applications where the operation of the PFC device must be controlled by a downstream PWM controller."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS515G", "family_comparison": "UCCx8050与UCCx8051的主要参数差异在于：1. UVLO开启阈值：UCCx8050为15.8V，UCCx8051为12.5V。2. gM放大器源电流：UCCx8050典型值为1.3mA，UCCx8051为300µA。", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "10.7V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "未找到", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.7V on CS pin", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "脉冲跳跃", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "±2%", "output_reference_voltage": "2.5V"}, "package": [{"type": "OPTION", "pin_count": "1", "pitch": "1.27", "height": "1.75", "width": "3.98", "length": "10.16"}]}, {"part_number": "UCC38050", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "临界导通模式(CRM/TM)", "part_number_title": "UCC2805x, UCC3805x Transition Mode PFC Controller", "features": ["Transition Mode PFC Controller for Low Implementation Cost", "Industry Pin Compatibility With Improved Feature Set", "Improved Transient Response With Slew-Rate Comparator", "Zero Power Detect to Prevent Overvoltage Protection (OVP) During Light Load Conditions", "Accurate Internal VREF for Tight Output Regulation", "Two UVLO Options", "OVP, Open-Feedback Protection, and Enable Circuits", "±750-mA Peak Gate Drive Current", "Low Start-Up and Operating Currents", "Lead (Pb)-Free Packages"], "description": "The UCC38050 and UCC38051 are PFC controllers for low-to-medium power applications requiring compliance with IEC 1000-3-2 harmonic reduction standard. The controllers are designed for a boost preregulator operating in transition mode (also referred to as boundary-conduction mode or critical conduction-mode operation). They feature a transconductance voltage amplifier for feedback error processing, a simple multiplier for generating a current command proportional to the input voltage, a current-sense (PWM) comparator, PWM logic, and a totem-pole driver for driving an external FET. In the transition mode operation, the PWM circuit is self-oscillating, with the turnon being governed by an inductor zero-current detector (ZCD pin), and the turnoff being governed by the current-sense comparator. Additionally, the controller provides features such as peak current limit, default timer, overvoltage protection (OVP) and enable.", "applications": ["Single-Stage PFC Flyback Converters for Lighting and Motor Drives", "Switch-Mode Power Supplies for Desktops, Monitors, TVs, and Set Top Boxes (STBs)", "AC Adapter Front-End Power Supplies", "Electronic Ballasts"], "ordering_information": [{"part_number": "UCC38050", "order_device": "UCC38050D", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC38050", "order_device": "UCC38050DR", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC38050", "order_device": "UCC38050P", "package_type": "PDIP", "package_drawing_code": "P (R-PDIP-T8)", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "UCC2805x, UCC3805x", "package_type": "SOIC-8, PDIP-8", "pins": [{"pin_number": "1", "pin_name": "VO_SNS", "pin_description": "This pin senses the boost regulator output voltage through a voltage divider. Internally, this pin is the inverting input to the transconductance amplifier (with a nominal value of 2.5 V) and also is input to the OVP comparator. Additionally, pulling this pin below the ENABLE threshold turns off the output switching, ensuring that the gate drive is held off while the boost output is pre-charging, and also ensuring no runaway if the feedback path is open."}, {"pin_number": "2", "pin_name": "COMP", "pin_description": "Output of the transconductance error amplifier. Loop compensation components are connected between this pin and ground. The output current capability of this pin is 10-μA under normal conditions, but increases to approximately 1-mA when the differential input is greater than the specified values in the specifications table. This voltage is one of the inputs to the multiplier, with a dynamic input range of 2.5 V to 3.8 V. During zero power or overvoltage conditions, this pin goes below 2.5 V nominal. When it goes below 2.3 V, the zero power comparator is activated, which prevents the gate drive from switching."}, {"pin_number": "3", "pin_name": "MULTIN", "pin_description": "This pin senses the instantaneous boost regulator input voltage through a voltage divider. The voltage acts as one of the inputs to the internal multiplier. Recommended operating range is 0 V to 2.5 V at high line."}, {"pin_number": "4", "pin_name": "CS", "pin_description": "This pin senses the instantaneous switch current in the boost switch and uses it as the internal ramp for PWM comparator. The internal circuitry filters out switching noise spikes without requiring external components. In addition, an external R-C filter may be required to suppress the noise spikes. An internal clamp on the multiplier output terminates the switching cycle if this pin voltage exceeds 1.7 V. Additional external filtering may be required."}, {"pin_number": "5", "pin_name": "ZCD", "pin_description": "Input for the zero current detect comparator. The boost inductor current is indirectly sensed through the bias winding on the boost inductor. The ZCD pin input goes low when the inductor current reaches zero and that transition is detected. Internal active voltage clamps are provided to prevent this pin from going below ground or too high. If zero current is not detected within 400 µs, a reset timer sets the latch and gate drive."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "The chip reference ground. All bypassing elements are connected to ground pin with shortest loops feasible."}, {"pin_number": "7", "pin_name": "DRV", "pin_description": "The gate drive output for an external boost switch. This output is capable of delivering up to 750-mA peak currents during turn-on and turn-off. An external gate drive resistor may be needed to limit the peak current depending on the Vcc voltage being used. Below the UVLO threshold, the output is held low."}, {"pin_number": "8", "pin_name": "VCC", "pin_description": "The supply voltage for the chip. This pin should be bypassed with a high-frequency capacitor (greater than 0.1-µF) and tied to GND. The UCC38050 has a wide UVLO hysteresis of approximately 6.3 V that allows use of a lower value supply capacitor on this pin for quicker and easier start-up. The UCC38051 has a narrow UVLO hysteresis with of about 2.8 V, and a start-up voltage of about 12.5 V for applications where the operation of the PFC device must be controlled by a downstream PWM controller."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS515G", "family_comparison": "UCCx8050与UCCx8051的主要参数差异在于：1. UVLO开启阈值：UCCx8050为15.8V，UCCx8051为12.5V。2. gM放大器源电流：UCCx8050典型值为1.3mA，UCCx8051为300µA。", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "10.7V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "未找到", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.7V on CS pin", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "脉冲跳跃", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "±1.6%", "output_reference_voltage": "2.5V"}, "package": [{"type": "OPTION", "pin_count": "1", "pitch": "1.27", "height": "1.75", "width": "3.98", "length": "10.16"}]}, {"part_number": "UCC38051", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "临界导通模式(CRM/TM)", "part_number_title": "UCC2805x, UCC3805x Transition Mode PFC Controller", "features": ["Transition Mode PFC Controller for Low Implementation Cost", "Industry Pin Compatibility With Improved Feature Set", "Improved Transient Response With Slew-Rate Comparator", "Zero Power Detect to Prevent Overvoltage Protection (OVP) During Light Load Conditions", "Accurate Internal VREF for Tight Output Regulation", "Two UVLO Options", "OVP, Open-Feedback Protection, and Enable Circuits", "±750-mA Peak Gate Drive Current", "Low Start-Up and Operating Currents", "Lead (Pb)-Free Packages"], "description": "The UCC38050 and UCC38051 are PFC controllers for low-to-medium power applications requiring compliance with IEC 1000-3-2 harmonic reduction standard. The controllers are designed for a boost preregulator operating in transition mode (also referred to as boundary-conduction mode or critical conduction-mode operation). They feature a transconductance voltage amplifier for feedback error processing, a simple multiplier for generating a current command proportional to the input voltage, a current-sense (PWM) comparator, PWM logic, and a totem-pole driver for driving an external FET. In the transition mode operation, the PWM circuit is self-oscillating, with the turnon being governed by an inductor zero-current detector (ZCD pin), and the turnoff being governed by the current-sense comparator. Additionally, the controller provides features such as peak current limit, default timer, overvoltage protection (OVP) and enable.", "applications": ["Single-Stage PFC Flyback Converters for Lighting and Motor Drives", "Switch-Mode Power Supplies for Desktops, Monitors, TVs, and Set Top Boxes (STBs)", "AC Adapter Front-End Power Supplies", "Electronic Ballasts"], "ordering_information": [{"part_number": "UCC38051", "order_device": "UCC38051D", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "UCC2805x, UCC3805x", "package_type": "SOIC-8, PDIP-8", "pins": [{"pin_number": "1", "pin_name": "VO_SNS", "pin_description": "This pin senses the boost regulator output voltage through a voltage divider. Internally, this pin is the inverting input to the transconductance amplifier (with a nominal value of 2.5 V) and also is input to the OVP comparator. Additionally, pulling this pin below the ENABLE threshold turns off the output switching, ensuring that the gate drive is held off while the boost output is pre-charging, and also ensuring no runaway if the feedback path is open."}, {"pin_number": "2", "pin_name": "COMP", "pin_description": "Output of the transconductance error amplifier. Loop compensation components are connected between this pin and ground. The output current capability of this pin is 10-μA under normal conditions, but increases to approximately 1-mA when the differential input is greater than the specified values in the specifications table. This voltage is one of the inputs to the multiplier, with a dynamic input range of 2.5 V to 3.8 V. During zero power or overvoltage conditions, this pin goes below 2.5 V nominal. When it goes below 2.3 V, the zero power comparator is activated, which prevents the gate drive from switching."}, {"pin_number": "3", "pin_name": "MULTIN", "pin_description": "This pin senses the instantaneous boost regulator input voltage through a voltage divider. The voltage acts as one of the inputs to the internal multiplier. Recommended operating range is 0 V to 2.5 V at high line."}, {"pin_number": "4", "pin_name": "CS", "pin_description": "This pin senses the instantaneous switch current in the boost switch and uses it as the internal ramp for PWM comparator. The internal circuitry filters out switching noise spikes without requiring external components. In addition, an external R-C filter may be required to suppress the noise spikes. An internal clamp on the multiplier output terminates the switching cycle if this pin voltage exceeds 1.7 V. Additional external filtering may be required."}, {"pin_number": "5", "pin_name": "ZCD", "pin_description": "Input for the zero current detect comparator. The boost inductor current is indirectly sensed through the bias winding on the boost inductor. The ZCD pin input goes low when the inductor current reaches zero and that transition is detected. Internal active voltage clamps are provided to prevent this pin from going below ground or too high. If zero current is not detected within 400 µs, a reset timer sets the latch and gate drive."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "The chip reference ground. All bypassing elements are connected to ground pin with shortest loops feasible."}, {"pin_number": "7", "pin_name": "DRV", "pin_description": "The gate drive output for an external boost switch. This output is capable of delivering up to 750-mA peak currents during turn-on and turn-off. An external gate drive resistor may be needed to limit the peak current depending on the Vcc voltage being used. Below the UVLO threshold, the output is held low."}, {"pin_number": "8", "pin_name": "VCC", "pin_description": "The supply voltage for the chip. This pin should be bypassed with a high-frequency capacitor (greater than 0.1-µF) and tied to GND. The UCC38050 has a wide UVLO hysteresis of approximately 6.3 V that allows use of a lower value supply capacitor on this pin for quicker and easier start-up. The UCC38051 has a narrow UVLO hysteresis with of about 2.8 V, and a start-up voltage of about 12.5 V for applications where the operation of the PFC device must be controlled by a downstream PWM controller."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS515G", "family_comparison": "UCCx8050与UCCx8051的主要参数差异在于：1. UVLO开启阈值：UCCx8050为15.8V，UCCx8051为12.5V。2. gM放大器源电流：UCCx8050典型值为1.3mA，UCCx8051为300µA。", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "10.7V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "未找到", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.7V on CS pin", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "脉冲跳跃", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "±1.6%", "output_reference_voltage": "2.5V"}, "package": [{"type": "OPTION", "pin_count": "1", "pitch": "1.27", "height": "1.75", "width": "3.98", "length": "10.16"}]}]