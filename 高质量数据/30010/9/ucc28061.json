[{"part_number": "UCC28060", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "功率因数校正(PFC)控制器", "category_lv3": "交错式PFC控制器", "part_number_title": "Natural Interleaving™ Transition-Mode PFC Controller With Improved Audible Noise Immunity", "features": ["Phase Management Capability", "FailSafe OVP with Dual Paths Prevents Output Over-Voltage Conditions Caused by Voltage-Sensing Failures", "Sensorless Current Shaping Simplifies Board Layout and Improves Efficiency", "Inrush Safe Current Limiting: Prevents MOSFET conduction during inrush, Eliminates reverse recovery events in output rectifiers", "Improved Audible Noise Performance", "Soft Start on Overvoltage", "Integrated Brownout", "Improved Efficiency and Design Flexibility over Traditional, Single-Phase Continuous Conduction Mode (CCM)", "Input Filter and Output Capacitor Current Cancellation: Reduced current ripple for higher system reliability and smaller bulk capacitor, Reduced EMI filter size", "Enables Use of Low-Cost Diodes without Extensive Snubber Circuitry", "Improved Light-Load Efficiency", "Improved Transient Response", "Complete System-Level Protection", "1-A Source/1.8-A Sink Gate Drivers"], "description": "Optimized for consumer applications concerned with audible noise elimination, this solution extends the advantages of transition mode—high efficiency with low-cost components—to higher power ratings than previously possible. By utilizing a Natural Interleaving technique, both channels operate as masters (that is, there is no slave channel) synchronized to the same frequency. This approach delivers inherently strong matching, faster responses, and ensures that each channel operates in transition mode. Complete system-level protections feature input brownout, output over-voltage, open-loop, overload, soft-start, phase-fail detection, and thermal shutdown. The additional FailSafe over-voltage protection (OVP) feature protects against shorts to an intermediate voltage that, if undetected, could lead to catastrophic device failure.", "applications": ["100-W to 800-W Power Supplies", "Gaming", "D to A Set Top Boxes", "Adapters", "LCD, Plasma and DLP™ TVs", "Home Audio Systems"], "ordering_information": [], "pin_function": [{"product_part_number": "UCC28060", "package_type": "SOIC-16", "pin_number": "1", "pin_name": "ZCDB", "pin_description": "Zero current detection input for phase B: These inputs expect to see a negative edge when the inductor current in the respective phases go to zero. The inputs are clamped at 0 V and 3 V. Signals should be coupled through a series resistor that limits the clamping current to less than ±3 mA. Connect these pins through a current limiting resistor to the zero crossing detection windings of the appropriate boost inductor. The inductor winding must be connected so that this voltage drops when inductor current decays to zero. When the inductor current drops to zero, the ZCD input must drop below the falling threshold, approximately 1 V, to cause the gate drive output to rise. When the power MOSFET turns off, the ZCD input must rise above the rising threshold, approximately 1.7 V, to arm the logic for another falling ZCD edge."}, {"product_part_number": "UCC28060", "package_type": "SOIC-16", "pin_number": "2", "pin_name": "VSENSE", "pin_description": "Output dc voltage sense: Connect this pin to a voltage divider across the output of the power converter. The error amplifier reference voltage is 6 V. Select the output voltage divider ratio for the desired output voltage. Connect the ground side of this divider to ground through a separate short trace for best output regulation accuracy and noise immunity. VSENSE can be pulled low by an open-drain logic output or 6-V logic output in series with a low-leakage diode to disable the outputs and reduce VCC current. If VSENSE is disconnected, open-loop protection provides an internal current source to pull VSENSE low, turning off the gate drivers."}, {"product_part_number": "UCC28060", "package_type": "SOIC-16", "pin_number": "3", "pin_name": "TSET", "pin_description": "Timing set: PWM on-time programming input. Connect a resistor from TSET to AGND to set the on-time versus COMP voltage and the minimum period at the gate drive outputs."}, {"product_part_number": "UCC28060", "package_type": "SOIC-16", "pin_number": "4", "pin_name": "PHB", "pin_description": "Phase B enable: This pin turns on/off channel B of the boost converter. The commanded on-time for channel A is immediately doubled when channel B is disabled, which helps to keep COMP voltage constant during the phase management transient. The PHB pin allows the user to add external phase management circuitry if they desire. To disable phase management, connect the PHB pin to the VREF pin."}, {"product_part_number": "UCC28060", "package_type": "SOIC-16", "pin_number": "5", "pin_name": "COMP", "pin_description": "Error amplifier output: The error amplifier is a transconductance amplifier, so this output is a high-impedance current source. Connect voltage regulation loop compensation components from this pin to AGND. The on-time seen at the gate drive outputs is proportional to the voltage at this pin minus an offset of approximately 125 mV. During soft-start events (undervoltage, brownout, disable or output over voltage), COMP is pulled low. Normal operation only resumes after the soft-start event clears and COMP has been discharged below 0.5 V, making sure that the circuit restarts with a low COMP voltage and a short on-time. Do not connect COMP to a low-impedance source that would interfere with COMP falling below 0.5 V."}, {"product_part_number": "UCC28060", "package_type": "SOIC-16", "pin_number": "6", "pin_name": "AGND", "pin_description": "Analog ground: Connect analog signal bypass capacitors, compensation components, and analog signal returns to this pin. Connect the analog and power grounds at a single point to isolate high-current noise signals of the power components from interference with the low-current analog circuits."}, {"product_part_number": "UCC28060", "package_type": "SOIC-16", "pin_number": "7", "pin_name": "VINAC", "pin_description": "Input ac voltage sense: For normal operation, connect this pin to a voltage divider across the rectified input power mains. When the voltage on VINAC remains below the brownout threshold for more than the brownout filter time, the device enters a brownout mode and both output drives are disabled. Select the input voltage divider ratio for the desired brownout threshold. Select the divider impedance for the desired brownout hysteresis."}, {"product_part_number": "UCC28060", "package_type": "SOIC-16", "pin_number": "8", "pin_name": "HVSEN", "pin_description": "High voltage output sense: The UCC28061 incorporates FailSafe OVP so that any single failure does not allow the output to boost above safe levels. Output over-voltage is monitored by both VSENSE and HVSEN and shuts down the PWM if either pin exceeds the appropriate over-voltage threshold. Using two pins to monitor for over-voltage provides redundant protection and fault tolerance. HVSEN can also be used to enable a downstream power converter when the voltage on HVSEN is within the operating region. Select the HVSEN divider ratio for the desired over-voltage and power-good thresholds. Select the HVSEN divider impedance for the desired power-good hysteresis. During operation, HVSEN must never fall below 0.8 V. Dropping HVSEN below 0.8 V puts the UCC28061 into a special test mode, used only for factory testing. A bypass capacitor from HVSEN to AGND is recommended to filter noise and prevent false over-voltage shutdown."}, {"product_part_number": "UCC28060", "package_type": "SOIC-16", "pin_number": "9", "pin_name": "PWMCNTL", "pin_description": "PWM enable logic output: This open-drain output goes low when HVSEN is within the HVSEN good region and the ZCDA and ZCDB inputs are switching correctly if operating in two-phase mode (see PHB Pin). Otherwise, PWMCNTL is high impedance."}, {"product_part_number": "UCC28060", "package_type": "SOIC-16", "pin_number": "10", "pin_name": "CS", "pin_description": "Current sense input: Connect the current sense resistor and the negative terminal of the diode bridge to this pin. Connect the return of the current sense resistor to the AGND pin with a separate trace. As input current increases, the voltage on CS goes more negative. This cycle-by-cycle over-current protection limits input current by turning off both gate driver (GDx) outputs when CS is more negative than the CS rising threshold (approximately -200 mV). The GD outputs remain low until CS falls to the CS falling threshold (approximately -15 mV). Current sense is blanked for approximately 100 ns following the falling edge of either GD output. This blanking filters noise that occurs when current switches from a power FET to a boost diode. In most cases, no additional current sense filtering is required. If filtering is required, the filter series resistance must be under 100 Ω to maintain accuracy. To prevent excessive negative voltage on the CS pin during inrush conditions, connect the current sensing resistor to the CS pin through a low value external resistor."}, {"product_part_number": "UCC28060", "package_type": "SOIC-16", "pin_number": "11", "pin_name": "GDB", "pin_description": "Channel B gate drive output: Connect this pin to the gate of the power FET for phase B through the shortest connection practical. If it is necessary to use a trace longer than 0.5 inch (12.6 mm) for this connection, some ringing may occur due to trace series inductance. This ringing can be reduced by adding a 5-Ω to 10-Ω resistor in series with GDB."}, {"product_part_number": "UCC28060", "package_type": "SOIC-16", "pin_number": "12", "pin_name": "VCC", "pin_description": "Bias supply input: Connect this pin to a controlled bias supply of between 14 V and 21 V. Also connect a 0.1-µF ceramic bypass capacitor from this pin to PGND with the shortest possible board trace. This supply powers all circuits in the device and must be capable of delivering 6 mA dc plus the transient power MOSFET gate charging current."}, {"product_part_number": "UCC28060", "package_type": "SOIC-16", "pin_number": "13", "pin_name": "PGND", "pin_description": "Power ground for the integrated circuit: Connect this pin to AGND through a separate short trace to isolate gate driver noise from analog signals."}, {"product_part_number": "UCC28060", "package_type": "SOIC-16", "pin_number": "14", "pin_name": "GDA", "pin_description": "Channel A gate drive output: Connect this pin to the gate of the power FET for phase A through the shortest connection practical. If it is necessary to use a trace longer than 0.5 inch (12.6 mm) for this connection, some ringing may occur due to trace series inductance. This ringing can be reduced by adding a 5-Ω to 10-Ω resistor in series with GDA."}, {"product_part_number": "UCC28060", "package_type": "SOIC-16", "pin_number": "15", "pin_name": "VREF", "pin_description": "Voltage reference output: Connect a 0.1-µF ceramic bypass capacitor from this pin to AGND. VREF turns off during VCC undervoltage and VSENSE disable to save supply current and increase efficiency. This 6 VDC reference can be used to bias other circuits requiring less than 2 mA of total supply current."}, {"product_part_number": "UCC28060", "package_type": "SOIC-16", "pin_number": "16", "pin_name": "ZCDA", "pin_description": "Zero current detection input for phase A: These inputs expect to see a negative edge when the inductor current in the respective phases go to zero. The inputs are clamped at 0 V and 3 V. Signals should be coupled through a series resistor that limits the clamping current to less than ±3 mA. Connect these pins through a current limiting resistor to the zero crossing detection windings of the appropriate boost inductor. The inductor winding must be connected so that this voltage drops when inductor current decays to zero. When the inductor current drops to zero, the ZCD input must drop below the falling threshold, approximately 1 V, to cause the gate drive output to rise. When the power MOSFET turns off, the ZCD input must rise above the rising threshold, approximately 1.7 V, to arm the logic for another falling ZCD edge."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS837A", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 2, "channel_count": 2, "max_input_voltage": "未找到", "min_input_voltage": "未找到", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "未找到", "max_switch_frequency": "未找到", "quiescent_current": "100µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "-0.2V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Phase Shedding, Pulse Skipping", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Auto Recovery", "output_discharge": "False", "integrated_ldo": "False", "dynamic_voltage_setting": "False", "pass_through_mode": "False", "load_disconnect": "False", "loop_control_mode": "Transition Mode", "output_voltage_accuracy": "未找到", "output_reference_voltage": "6V"}, "package": [{"type": "SOIC", "pin_count": "1", "pitch": "1.27", "height": "1.75", "width": "6.2", "length": "10.0"}]}, {"part_number": "UCC28061", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "功率因数校正(PFC)控制器", "category_lv3": "交错式PFC控制器", "part_number_title": "Natural Interleaving™ Transition-Mode PFC Controller With Improved Audible Noise Immunity", "features": ["Phase Management Capability", "FailSafe OVP with Dual Paths Prevents Output Over-Voltage Conditions Caused by Voltage-Sensing Failures", "Sensorless Current Shaping Simplifies Board Layout and Improves Efficiency", "Inrush Safe Current Limiting: Prevents MOSFET conduction during inrush, Eliminates reverse recovery events in output rectifiers", "Improved Audible Noise Performance", "Soft Start on Overvoltage", "Integrated Brownout", "Improved Efficiency and Design Flexibility over Traditional, Single-Phase Continuous Conduction Mode (CCM)", "Input Filter and Output Capacitor Current Cancellation: Reduced current ripple for higher system reliability and smaller bulk capacitor, Reduced EMI filter size", "Enables Use of Low-Cost Diodes without Extensive Snubber Circuitry", "Improved Light-Load Efficiency", "Improved Transient Response", "Complete System-Level Protection", "1-A Source/1.8-A Sink Gate Drivers"], "description": "Optimized for consumer applications concerned with audible noise elimination, this solution extends the advantages of transition mode—high efficiency with low-cost components—to higher power ratings than previously possible. By utilizing a Natural Interleaving technique, both channels operate as masters (that is, there is no slave channel) synchronized to the same frequency. This approach delivers inherently strong matching, faster responses, and ensures that each channel operates in transition mode. Complete system-level protections feature input brownout, output over-voltage, open-loop, overload, soft-start, phase-fail detection, and thermal shutdown. The additional FailSafe over-voltage protection (OVP) feature protects against shorts to an intermediate voltage that, if undetected, could lead to catastrophic device failure.", "applications": ["100-W to 800-W Power Supplies", "Gaming", "D to A Set Top Boxes", "Adapters", "LCD, Plasma and DLP™ TVs", "Home Audio Systems"], "ordering_information": [{"part_number": "UCC28061", "order_device": "UCC28061D", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125", "status": "Active", "carrier_description": "TUBE", "carrier_quantity": 40, "marking": "UCC28061", "pin_count": 16, "length": 9.9, "width": 3.9, "height": 1.75, "pitch": 1.27, "application_grade": "Automotive"}, {"part_number": "UCC28061", "order_device": "UCC28061D.B", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125", "status": "Active", "carrier_description": "TUBE", "carrier_quantity": 40, "marking": "UCC28061", "pin_count": 16, "length": 9.9, "width": 3.9, "height": 1.75, "pitch": 1.27, "application_grade": "Automotive"}, {"part_number": "UCC28061", "order_device": "UCC28061DG4", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125", "status": "Active", "carrier_description": "TUBE", "carrier_quantity": 40, "marking": "UCC28061", "pin_count": 16, "length": 9.9, "width": 3.9, "height": 1.75, "pitch": 1.27, "application_grade": "Automotive"}, {"part_number": "UCC28061", "order_device": "UCC28061DR", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125", "status": "Active", "carrier_description": "LARGE T&R", "carrier_quantity": 2500, "marking": "UCC28061", "pin_count": 16, "length": 9.9, "width": 3.9, "height": 1.75, "pitch": 1.27, "application_grade": "Automotive"}, {"part_number": "UCC28061", "order_device": "UCC28061DR.B", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "125", "status": "Active", "carrier_description": "LARGE T&R", "carrier_quantity": 2500, "marking": "UCC28061", "pin_count": 16, "length": 9.9, "width": 3.9, "height": 1.75, "pitch": 1.27, "application_grade": "Automotive"}], "pin_function": [{"product_part_number": "UCC28061", "package_type": "SOIC 16-Pin (D)", "pin_number": "1", "pin_name": "ZCDB", "pin_description": "Zero current detection input for phase B: These inputs expect to see a negative edge when the inductor current in the respective phases go to zero. The inputs are clamped at 0 V and 3 V. Signals should be coupled through a series resistor that limits the clamping current to less than ±3 mA. Connect these pins through a current limiting resistor to the zero crossing detection windings of the appropriate boost inductor. The inductor winding must be connected so that this voltage drops when inductor current decays to zero. When the inductor current drops to zero, the ZCD input must drop below the falling threshold, approximately 1 V, to cause the gate drive output to rise. When the power MOSFET turns off, the ZCD input must rise above the rising threshold, approximately 1.7 V, to arm the logic for another falling ZCD edge."}, {"product_part_number": "UCC28061", "package_type": "SOIC 16-Pin (D)", "pin_number": "2", "pin_name": "VSENSE", "pin_description": "Output dc voltage sense: Connect this pin to a voltage divider across the output of the power converter. The error amplifier reference voltage is 6 V. Select the output voltage divider ratio for the desired output voltage. Connect the ground side of this divider to ground through a separate short trace for best output regulation accuracy and noise immunity. VSENSE can be pulled low by an open-drain logic output or 6-V logic output in series with a low-leakage diode to disable the outputs and reduce VCC current. If VSENSE is disconnected, open-loop protection provides an internal current source to pull VSENSE low, turning off the gate drivers."}, {"product_part_number": "UCC28061", "package_type": "SOIC 16-Pin (D)", "pin_number": "3", "pin_name": "TSET", "pin_description": "Timing set: PWM on-time programming input. Connect a resistor from TSET to AGND to set the on-time versus COMP voltage and the minimum period at the gate drive outputs."}, {"product_part_number": "UCC28061", "package_type": "SOIC 16-Pin (D)", "pin_number": "4", "pin_name": "PHB", "pin_description": "Phase B enable: This pin turns on/off channel B of the boost converter. The commanded on-time for channel A is immediately doubled when channel B is disabled, which helps to keep COMP voltage constant during the phase management transient. The PHB pin allows the user to add external phase management circuitry if they desire. To disable phase management, connect the PHB pin to the VREF pin."}, {"product_part_number": "UCC28061", "package_type": "SOIC 16-Pin (D)", "pin_number": "5", "pin_name": "COMP", "pin_description": "Error amplifier output: The error amplifier is a transconductance amplifier, so this output is a high-impedance current source. Connect voltage regulation loop compensation components from this pin to AGND. The on-time seen at the gate drive outputs is proportional to the voltage at this pin minus an offset of approximately 125 mV. During soft-start events (undervoltage, brownout, disable or output over voltage), COMP is pulled low. Normal operation only resumes after the soft-start event clears and COMP has been discharged below 0.5 V, making sure that the circuit restarts with a low COMP voltage and a short on-time. Do not connect COMP to a low-impedance source that would interfere with COMP falling below 0.5 V."}, {"product_part_number": "UCC28061", "package_type": "SOIC 16-Pin (D)", "pin_number": "6", "pin_name": "AGND", "pin_description": "Analog ground: Connect analog signal bypass capacitors, compensation components, and analog signal returns to this pin. Connect the analog and power grounds at a single point to isolate high-current noise signals of the power components from interference with the low-current analog circuits."}, {"product_part_number": "UCC28061", "package_type": "SOIC 16-Pin (D)", "pin_number": "7", "pin_name": "VINAC", "pin_description": "Input ac voltage sense: For normal operation, connect this pin to a voltage divider across the rectified input power mains. When the voltage on VINAC remains below the brownout threshold for more than the brownout filter time, the device enters a brownout mode and both output drives are disabled. Select the input voltage divider ratio for the desired brownout threshold. Select the divider impedance for the desired brownout hysteresis."}, {"product_part_number": "UCC28061", "package_type": "SOIC 16-Pin (D)", "pin_number": "8", "pin_name": "HVSEN", "pin_description": "High voltage output sense: The UCC28061 incorporates FailSafe OVP so that any single failure does not allow the output to boost above safe levels. Output over-voltage is monitored by both VSENSE and HVSEN and shuts down the PWM if either pin exceeds the appropriate over-voltage threshold. Using two pins to monitor for over-voltage provides redundant protection and fault tolerance. HVSEN can also be used to enable a downstream power converter when the voltage on HVSEN is within the operating region. Select the HVSEN divider ratio for the desired over-voltage and power-good thresholds. Select the HVSEN divider impedance for the desired power-good hysteresis. During operation, HVSEN must never fall below 0.8 V. Dropping HVSEN below 0.8 V puts the UCC28061 into a special test mode, used only for factory testing. A bypass capacitor from HVSEN to AGND is recommended to filter noise and prevent false over-voltage shutdown."}, {"product_part_number": "UCC28061", "package_type": "SOIC 16-Pin (D)", "pin_number": "9", "pin_name": "PWMCNTL", "pin_description": "PWM enable logic output: This open-drain output goes low when HVSEN is within the HVSEN good region and the ZCDA and ZCDB inputs are switching correctly if operating in two-phase mode (see PHB Pin). Otherwise, PWMCNTL is high impedance."}, {"product_part_number": "UCC28061", "package_type": "SOIC 16-Pin (D)", "pin_number": "10", "pin_name": "CS", "pin_description": "Current sense input: Connect the current sense resistor and the negative terminal of the diode bridge to this pin. Connect the return of the current sense resistor to the AGND pin with a separate trace. As input current increases, the voltage on CS goes more negative. This cycle-by-cycle over-current protection limits input current by turning off both gate driver (GDx) outputs when CS is more negative than the CS rising threshold (approximately -200 mV). The GD outputs remain low until CS falls to the CS falling threshold (approximately -15 mV). Current sense is blanked for approximately 100 ns following the falling edge of either GD output. This blanking filters noise that occurs when current switches from a power FET to a boost diode. In most cases, no additional current sense filtering is required. If filtering is required, the filter series resistance must be under 100 Ω to maintain accuracy. To prevent excessive negative voltage on the CS pin during inrush conditions, connect the current sensing resistor to the CS pin through a low value external resistor."}, {"product_part_number": "UCC28061", "package_type": "SOIC 16-Pin (D)", "pin_number": "11", "pin_name": "GDB", "pin_description": "Channel B gate drive output: Connect this pin to the gate of the power FET for phase B through the shortest connection practical. If it is necessary to use a trace longer than 0.5 inch (12.6 mm) for this connection, some ringing may occur due to trace series inductance. This ringing can be reduced by adding a 5-Ω to 10-Ω resistor in series with GDB."}, {"product_part_number": "UCC28061", "package_type": "SOIC 16-Pin (D)", "pin_number": "12", "pin_name": "VCC", "pin_description": "Bias supply input: Connect this pin to a controlled bias supply of between 14 V and 21 V. Also connect a 0.1-µF ceramic bypass capacitor from this pin to PGND with the shortest possible board trace. This supply powers all circuits in the device and must be capable of delivering 6 mA dc plus the transient power MOSFET gate charging current."}, {"product_part_number": "UCC28061", "package_type": "SOIC 16-Pin (D)", "pin_number": "13", "pin_name": "PGND", "pin_description": "Power ground for the integrated circuit: Connect this pin to AGND through a separate short trace to isolate gate driver noise from analog signals."}, {"product_part_number": "UCC28061", "package_type": "SOIC 16-Pin (D)", "pin_number": "14", "pin_name": "GDA", "pin_description": "Channel A gate drive output: Connect this pin to the gate of the power FET for phase A through the shortest connection practical. If it is necessary to use a trace longer than 0.5 inch (12.6 mm) for this connection, some ringing may occur due to trace series inductance. This ringing can be reduced by adding a 5-Ω to 10-Ω resistor in series with GDA."}, {"product_part_number": "UCC28061", "package_type": "SOIC 16-Pin (D)", "pin_number": "15", "pin_name": "VREF", "pin_description": "Voltage reference output: Connect a 0.1-µF ceramic bypass capacitor from this pin to AGND. VREF turns off during VCC undervoltage and VSENSE disable to save supply current and increase efficiency. This 6 VDC reference can be used to bias other circuits requiring less than 2 mA of total supply current."}, {"product_part_number": "UCC28061", "package_type": "SOIC 16-Pin (D)", "pin_number": "16", "pin_name": "ZCDA", "pin_description": "Zero current detection input for phase A: These inputs expect to see a negative edge when the inductor current in the respective phases go to zero. The inputs are clamped at 0 V and 3 V. Signals should be coupled through a series resistor that limits the clamping current to less than ±3 mA. Connect these pins through a current limiting resistor to the zero crossing detection windings of the appropriate boost inductor. The inductor winding must be connected so that this voltage drops when inductor current decays to zero. When the inductor current drops to zero, the ZCD input must drop below the falling threshold, approximately 1 V, to cause the gate drive output to rise. When the power MOSFET turns off, the ZCD input must rise above the rising threshold, approximately 1.7 V, to arm the logic for another falling ZCD edge."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS837A", "family_comparison": [{"device": "UCC28051", "description": "PFC controller for low to medium power applications"}, {"device": "UCC28019", "description": "8-pin continuous conduction mode (CCM) PFC controller"}, {"device": "UCC28060", "description": "Natural Interleaving™ Dual-Phase Transition-Mode PFC Controller"}], "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 2, "channel_count": 2, "max_input_voltage": "265V", "min_input_voltage": "85V", "max_output_voltage": "418V", "min_output_voltage": "375V", "max_output_current": "0.77A", "max_switch_frequency": "0.55MHz", "quiescent_current": "100µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "-0.2V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Phase Shedding, Pulse Skipping", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Auto Recovery", "output_discharge": "False", "integrated_ldo": "False", "dynamic_voltage_setting": "False", "pass_through_mode": "False", "load_disconnect": "False", "loop_control_mode": "Transition Mode", "output_voltage_accuracy": "±3%", "output_reference_voltage": "6V"}, "package": [{"type": "SOIC", "pin_count": "1", "pitch": "1.27", "height": "1.75", "width": "6.2", "length": "10.0"}]}]