{"response": "\n[\n  {\n    \"part_number\": \"UC1854A\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Military\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PFC控制器\",\n    \"category_lv3\": \"升压型PFC控制器\",\n    \"part_number_title\": \"ADVANCED HIGH-POWER FACTOR PREREGULATOR\",\n    \"features\": [\n      \"Controls Boost PWM to Near-Unity Power Factor\",\n      \"Limits Line Current Distortion To <3%\",\n      \"World-Wide Operation Without Switches\",\n      \"Accurate Power Limiting\",\n      \"Fixed-Frequency Average Current-Mode Control\",\n      \"High Bandwidth (5 MHz), Low-Offset Current Amplifier\",\n      \"Integrated Current- and Voltage-Amplifier Output Clamps\",\n      \"Multiplier Improvements: Linearity, 500 mV VAC Offset (Eliminates External Resistor), 0 V to 5 V Multout Common-Mode Range\",\n      \"VREF GOOD Comparator\",\n      \"Faster and Improved Accuracy ENABLE Comparator\",\n      \"UVLO Options (16 V/10 V or 10.5 V/10 V)\",\n      \"300-μA Start-Up Supply Current\"\n    ],\n    \"description\": \"The UC3854A/B products are pin compatible enhanced versions of the UC3854. Like the UC3854, these products provide all of the functions necessary for active power factor corrected preregulators. The controller achieves near unity power factor by shaping the ac input line current waveform to correspond to the ac input line voltage. To do this the UC3854A/B uses average current mode control. Average current mode control maintains stable, low distortion sinusoidal line current without the need for slope compensation, unlike peak current mode control. A 1%, 7.5-V reference, fixed frequency oscillator, PWM, voltage amplifierwith soft-start, line voltage feedforward (VRMS squarer), input supply voltage clamp, and over current comparator round out the lilst of feataures.\",\n    \"applications\": [\n      \"有源功率因数校正预调整器\"\n    ],\n    \"ordering_information\": [],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCx854A/B\",\n        \"package_type\": \"J/N/DW\",\n        \"pins\": [\n          {\n            \"pin_number\": \"1\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"6\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"11\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"16\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      },\n      {\n        \"product_part_number\": \"UCx854A/B\",\n        \"package_type\": \"Q/L\",\n        \"pins\": [\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"17\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"18\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"19\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"20\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS329E\",\n    \"family_comparison\": \"The UCx854A/B family differs primarily in temperature grade and UVLO thresholds. UC1854 series is for military grade (-55°C to 125°C). UC2854 series is for industrial grade (-40°C to 85°C). UC3854 series is for commercial grade (0°C to 70°C). The 'A' versions have a 16V/10V UVLO threshold, while the 'B' versions have a 10.5V/10V UVLO threshold.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"20V\",\n      \"min_input_voltage\": \"16V\",\n      \"max_output_voltage\": \"未找到\",\n      \"min_output_voltage\": \"未找到\",\n      \"max_output_current\": \"未找到\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"12mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"外部可调\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"无\",\n      \"power_good_indicator\": \"Yes\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"未找到\",\n      \"input_under_voltage_protection\": \"UVLO (16V/10V)\",\n      \"output_over_voltage_protection\": \"Auto Recovery\",\n      \"output_under_voltage_protection\": \"未找到\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Current Limit\",\n      \"over_temperature_protection\": \"未找到\",\n      \"output_discharge\": \"False\",\n      \"integrated_ldo\": \"False\",\n      \"dynamic_voltage_setting\": \"False\",\n      \"pass_through_mode\": \"False\",\n      \"load_disconnect\": \"False\",\n      \"loop_control_mode\": \"平均电流模式\",\n      \"output_voltage_accuracy\": \"±3.33%\",\n      \"output_reference_voltage\": \"3V\"\n    }\n  },\n  {\n    \"part_number\": \"UC1854B\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Military\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PFC控制器\",\n    \"category_lv3\": \"升压型PFC控制器\",\n    \"part_number_title\": \"ADVANCED HIGH-POWER FACTOR PREREGULATOR\",\n    \"features\": [\n      \"Controls Boost PWM to Near-Unity Power Factor\",\n      \"Limits Line Current Distortion To <3%\",\n      \"World-Wide Operation Without Switches\",\n      \"Accurate Power Limiting\",\n      \"Fixed-Frequency Average Current-Mode Control\",\n      \"High Bandwidth (5 MHz), Low-Offset Current Amplifier\",\n      \"Integrated Current- and Voltage-Amplifier Output Clamps\",\n      \"Multiplier Improvements: Linearity, 500 mV VAC Offset (Eliminates External Resistor), 0 V to 5 V Multout Common-Mode Range\",\n      \"VREF GOOD Comparator\",\n      \"Faster and Improved Accuracy ENABLE Comparator\",\n      \"UVLO Options (16 V/10 V or 10.5 V/10 V)\",\n      \"300-μA Start-Up Supply Current\"\n    ],\n    \"description\": \"The UC3854A/B products are pin compatible enhanced versions of the UC3854. Like the UC3854, these products provide all of the functions necessary for active power factor corrected preregulators. The controller achieves near unity power factor by shaping the ac input line current waveform to correspond to the ac input line voltage. To do this the UC3854A/B uses average current mode control. Average current mode control maintains stable, low distortion sinusoidal line current without the need for slope compensation, unlike peak current mode control. A 1%, 7.5-V reference, fixed frequency oscillator, PWM, voltage amplifierwith soft-start, line voltage feedforward (VRMS squarer), input supply voltage clamp, and over current comparator round out the lilst of feataures.\",\n    \"applications\": [\n      \"有源功率因数校正预调整器\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UC1854B\",\n        \"order_device\": \"UC1854BJ\",\n        \"status\": \"Active\",\n        \"package_type\": \"CDIP\",\n        \"package_code\": \"J\",\n        \"carrier_description\": \"TUBE\",\n        \"carrier_quantity\": 25,\n        \"package_drawing_code\": \"未找到\",\n        \"marking\": \"UC1854BJ\",\n        \"pin_count\": 16,\n        \"length\": null,\n        \"width\": null,\n        \"height\": null,\n        \"pitch\": null,\n        \"min_operation_temp\": -55,\n        \"max_operation_temp\": 125,\n        \"output_voltage\": null,\n        \"application_grade\": \"Military\"\n      },\n      {\n        \"part_number\": \"UC1854B\",\n        \"order_device\": \"UC1854BJ/883B\",\n        \"status\": \"Active\",\n        \"package_type\": \"CDIP\",\n        \"package_code\": \"J\",\n        \"carrier_description\": \"TUBE\",\n        \"carrier_quantity\": 25,\n        \"package_drawing_code\": \"未找到\",\n        \"marking\": \"5962-9326102MEA UC1854BJ/883B\",\n        \"pin_count\": 16,\n        \"length\": null,\n        \"width\": null,\n        \"height\": null,\n        \"pitch\": null,\n        \"min_operation_temp\": -55,\n        \"max_operation_temp\": 125,\n        \"output_voltage\": null,\n        \"application_grade\": \"Military\"\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCx854A/B\",\n        \"package_type\": \"J/N/DW\",\n        \"pins\": [\n          {\n            \"pin_number\": \"1\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"6\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"11\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"16\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      },\n      {\n        \"product_part_number\": \"UCx854A/B\",\n        \"package_type\": \"Q/L\",\n        \"pins\": [\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"17\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"18\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"19\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"20\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS329E\",\n    \"family_comparison\": \"The UCx854A/B family differs primarily in temperature grade and UVLO thresholds. UC1854 series is for military grade (-55°C to 125°C). UC2854 series is for industrial grade (-40°C to 85°C). UC3854 series is for commercial grade (0°C to 70°C). The 'A' versions have a 16V/10V UVLO threshold, while the 'B' versions have a 10.5V/10V UVLO threshold.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"20V\",\n      \"min_input_voltage\": \"10.5V\",\n      \"max_output_voltage\": \"未找到\",\n      \"min_output_voltage\": \"未找到\",\n      \"max_output_current\": \"未找到\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"12mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"外部可调\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"无\",\n      \"power_good_indicator\": \"Yes\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"未找到\",\n      \"input_under_voltage_protection\": \"UVLO (10.5V/10V)\",\n      \"output_over_voltage_protection\": \"Auto Recovery\",\n      \"output_under_voltage_protection\": \"未找到\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Current Limit\",\n      \"over_temperature_protection\": \"未找到\",\n      \"output_discharge\": \"False\",\n      \"integrated_ldo\": \"False\",\n      \"dynamic_voltage_setting\": \"False\",\n      \"pass_through_mode\": \"False\",\n      \"load_disconnect\": \"False\",\n      \"loop_control_mode\": \"平均电流模式\",\n      \"output_voltage_accuracy\": \"±3.33%\",\n      \"output_reference_voltage\": \"3V\"\n    }\n  },\n  {\n    \"part_number\": \"UC2854A\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Industry\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PFC控制器\",\n    \"category_lv3\": \"升压型PFC控制器\",\n    \"part_number_title\": \"ADVANCED HIGH-POWER FACTOR PREREGULATOR\",\n    \"features\": [\n      \"Controls Boost PWM to Near-Unity Power Factor\",\n      \"Limits Line Current Distortion To <3%\",\n      \"World-Wide Operation Without Switches\",\n      \"Accurate Power Limiting\",\n      \"Fixed-Frequency Average Current-Mode Control\",\n      \"High Bandwidth (5 MHz), Low-Offset Current Amplifier\",\n      \"Integrated Current- and Voltage-Amplifier Output Clamps\",\n      \"Multiplier Improvements: Linearity, 500 mV VAC Offset (Eliminates External Resistor), 0 V to 5 V Multout Common-Mode Range\",\n      \"VREF GOOD Comparator\",\n      \"Faster and Improved Accuracy ENABLE Comparator\",\n      \"UVLO Options (16 V/10 V or 10.5 V/10 V)\",\n      \"300-μA Start-Up Supply Current\"\n    ],\n    \"description\": \"The UC3854A/B products are pin compatible enhanced versions of the UC3854. Like the UC3854, these products provide all of the functions necessary for active power factor corrected preregulators. The controller achieves near unity power factor by shaping the ac input line current waveform to correspond to the ac input line voltage. To do this the UC3854A/B uses average current mode control. Average current mode control maintains stable, low distortion sinusoidal line current without the need for slope compensation, unlike peak current mode control. A 1%, 7.5-V reference, fixed frequency oscillator, PWM, voltage amplifierwith soft-start, line voltage feedforward (VRMS squarer), input supply voltage clamp, and over current comparator round out the lilst of feataures.\",\n    \"applications\": [\n      \"有源功率因数校正预调整器\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UC2854A\",\n        \"order_device\": \"UC2854ADW\",\n        \"status\": \"Active\",\n        \"package_type\": \"SOIC\",\n        \"package_code\": \"DW\",\n        \"carrier_description\": \"TUBE\",\n        \"carrier_quantity\": 40,\n        \"package_drawing_code\": \"DW0016A\",\n        \"marking\": \"UC2854ADW\",\n        \"pin_count\": 16,\n        \"length\": 10.3,\n        \"width\": 7.5,\n        \"height\": 2.65,\n        \"pitch\": 1.27,\n        \"min_operation_temp\": -40,\n        \"max_operation_temp\": 85,\n        \"output_voltage\": null,\n        \"application_grade\": \"Industrial\"\n      },\n      {\n        \"part_number\": \"UC2854A\",\n        \"order_device\": \"UC2854ADWTR\",\n        \"status\": \"Active\",\n        \"package_type\": \"SOIC\",\n        \"package_code\": \"DW\",\n        \"carrier_description\": \"LARGE T&R\",\n        \"carrier_quantity\": 2000,\n        \"package_drawing_code\": \"DW0016A\",\n        \"marking\": \"UC2854ADW\",\n        \"pin_count\": 16,\n        \"length\": 10.3,\n        \"width\": 7.5,\n        \"height\": 2.65,\n        \"pitch\": 1.27,\n        \"min_operation_temp\": -40,\n        \"max_operation_temp\": 85,\n        \"output_voltage\": null,\n        \"application_grade\": \"Industrial\"\n      },\n      {\n        \"part_number\": \"UC2854A\",\n        \"order_device\": \"UC2854AN\",\n        \"status\": \"Active\",\n        \"package_type\": \"PDIP\",\n        \"package_code\": \"N\",\n        \"carrier_description\": \"TUBE\",\n        \"carrier_quantity\": 25,\n        \"package_drawing_code\": \"N (R-PDIP-T**)\",\n        \"marking\": \"UC2854AN\",\n        \"pin_count\": 16,\n        \"length\": null,\n        \"width\": null,\n        \"height\": null,\n        \"pitch\": null,\n        \"min_operation_temp\": -40,\n        \"max_operation_temp\": 85,\n        \"output_voltage\": null,\n        \"application_grade\": \"Industrial\"\n      },\n      {\n        \"part_number\": \"UC2854A\",\n        \"order_device\": \"UC2854J\",\n        \"status\": \"Active\",\n        \"package_type\": \"CDIP\",\n        \"package_code\": \"J\",\n        \"carrier_description\": \"TUBE\",\n        \"carrier_quantity\": 25,\n        \"package_drawing_code\": \"J (R-GDIP-T**)\",\n        \"marking\": \"UC2854J\",\n        \"pin_count\": 16,\n        \"length\": null,\n        \"width\": null,\n        \"height\": null,\n        \"pitch\": null,\n        \"min_operation_temp\": -40,\n        \"max_operation_temp\": 85,\n        \"output_voltage\": null,\n        \"application_grade\": \"Industrial\"\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCx854A/B\",\n        \"package_type\": \"J/N/DW\",\n        \"pins\": [\n          {\n            \"pin_number\": \"1\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"6\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"11\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"16\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      },\n      {\n        \"product_part_number\": \"UCx854A/B\",\n        \"package_type\": \"Q/L\",\n        \"pins\": [\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"17\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"18\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"19\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"20\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS329E\",\n    \"family_comparison\": \"The UCx854A/B family differs primarily in temperature grade and UVLO thresholds. UC1854 series is for military grade (-55°C to 125°C). UC2854 series is for industrial grade (-40°C to 85°C). UC3854 series is for commercial grade (0°C to 70°C). The 'A' versions have a 16V/10V UVLO threshold, while the 'B' versions have a 10.5V/10V UVLO threshold.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"20V\",\n      \"min_input_voltage\": \"16V\",\n      \"max_output_voltage\": \"未找到\",\n      \"min_output_voltage\": \"未找到\",\n      \"max_output_current\": \"未找到\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"12mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"外部可调\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"无\",\n      \"power_good_indicator\": \"Yes\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"未找到\",\n      \"input_under_voltage_protection\": \"UVLO (16V/10V)\",\n      \"output_over_voltage_protection\": \"Auto Recovery\",\n      \"output_under_voltage_protection\": \"未找到\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Current Limit\",\n      \"over_temperature_protection\": \"未找到\",\n      \"output_discharge\": \"False\",\n      \"integrated_ldo\": \"False\",\n      \"dynamic_voltage_setting\": \"False\",\n      \"pass_through_mode\": \"False\",\n      \"load_disconnect\": \"False\",\n      \"loop_control_mode\": \"平均电流模式\",\n      \"output_voltage_accuracy\": \"±3.33%\",\n      \"output_reference_voltage\": \"3V\"\n    }\n  },\n  {\n    \"part_number\": \"UC2854B\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Industry\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PFC控制器\",\n    \"category_lv3\": \"升压型PFC控制器\",\n    \"part_number_title\": \"ADVANCED HIGH-POWER FACTOR PREREGULATOR\",\n    \"features\": [\n      \"Controls Boost PWM to Near-Unity Power Factor\",\n      \"Limits Line Current Distortion To <3%\",\n      \"World-Wide Operation Without Switches\",\n      \"Accurate Power Limiting\",\n      \"Fixed-Frequency Average Current-Mode Control\",\n      \"High Bandwidth (5 MHz), Low-Offset Current Amplifier\",\n      \"Integrated Current- and Voltage-Amplifier Output Clamps\",\n      \"Multiplier Improvements: Linearity, 500 mV VAC Offset (Eliminates External Resistor), 0 V to 5 V Multout Common-Mode Range\",\n      \"VREF GOOD Comparator\",\n      \"Faster and Improved Accuracy ENABLE Comparator\",\n      \"UVLO Options (16 V/10 V or 10.5 V/10 V)\",\n      \"300-μA Start-Up Supply Current\"\n    ],\n    \"description\": \"The UC3854A/B products are pin compatible enhanced versions of the UC3854. Like the UC3854, these products provide all of the functions necessary for active power factor corrected preregulators. The controller achieves near unity power factor by shaping the ac input line current waveform to correspond to the ac input line voltage. To do this the UC3854A/B uses average current mode control. Average current mode control maintains stable, low distortion sinusoidal line current without the need for slope compensation, unlike peak current mode control. A 1%, 7.5-V reference, fixed frequency oscillator, PWM, voltage amplifierwith soft-start, line voltage feedforward (VRMS squarer), input supply voltage clamp, and over current comparator round out the lilst of feataures.\",\n    \"applications\": [\n      \"有源功率因数校正预调整器\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UC2854B\",\n        \"order_device\": \"UC2854BDW\",\n        \"status\": \"Active\",\n        \"package_type\": \"SOIC\",\n        \"package_code\": \"DW\",\n        \"carrier_description\": \"TUBE\",\n        \"carrier_quantity\": 40,\n        \"package_drawing_code\": \"DW0016A\",\n        \"marking\": \"UC2854BDW\",\n        \"pin_count\": 16,\n        \"length\": 10.3,\n        \"width\": 7.5,\n        \"height\": 2.65,\n        \"pitch\": 1.27,\n        \"min_operation_temp\": -40,\n        \"max_operation_temp\": 85,\n        \"output_voltage\": null,\n        \"application_grade\": \"Industrial\"\n      },\n      {\n        \"part_number\": \"UC2854B\",\n        \"order_device\": \"UC2854BDWTR\",\n        \"status\": \"Active\",\n        \"package_type\": \"SOIC\",\n        \"package_code\": \"DW\",\n        \"carrier_description\": \"LARGE T&R\",\n        \"carrier_quantity\": 2000,\n        \"package_drawing_code\": \"DW0016A\",\n        \"marking\": \"UC2854BDW\",\n        \"pin_count\": 16,\n        \"length\": 10.3,\n        \"width\": 7.5,\n        \"height\": 2.65,\n        \"pitch\": 1.27,\n        \"min_operation_temp\": -40,\n        \"max_operation_temp\": 85,\n        \"output_voltage\": null,\n        \"application_grade\": \"Industrial\"\n      },\n      {\n        \"part_number\": \"UC2854B\",\n        \"order_device\": \"UC2854BN\",\n        \"status\": \"Active\",\n        \"package_type\": \"PDIP\",\n        \"package_code\": \"N\",\n        \"carrier_description\": \"TUBE\",\n        \"carrier_quantity\": 25,\n        \"package_drawing_code\": \"N (R-PDIP-T**)\",\n        \"marking\": \"UC2854BN\",\n        \"pin_count\": 16,\n        \"length\": null,\n        \"width\": null,\n        \"height\": null,\n        \"pitch\": null,\n        \"min_operation_temp\": -40,\n        \"max_operation_temp\": 85,\n        \"output_voltage\": null,\n        \"application_grade\": \"Industrial\"\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCx854A/B\",\n        \"package_type\": \"J/N/DW\",\n        \"pins\": [\n          {\n            \"pin_number\": \"1\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"6\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"11\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"16\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      },\n      {\n        \"product_part_number\": \"UCx854A/B\",\n        \"package_type\": \"Q/L\",\n        \"pins\": [\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"17\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"18\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"19\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"20\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS329E\",\n    \"family_comparison\": \"The UCx854A/B family differs primarily in temperature grade and UVLO thresholds. UC1854 series is for military grade (-55°C to 125°C). UC2854 series is for industrial grade (-40°C to 85°C). UC3854 series is for commercial grade (0°C to 70°C). The 'A' versions have a 16V/10V UVLO threshold, while the 'B' versions have a 10.5V/10V UVLO threshold.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"20V\",\n      \"min_input_voltage\": \"10.5V\",\n      \"max_output_voltage\": \"未找到\",\n      \"min_output_voltage\": \"未找到\",\n      \"max_output_current\": \"未找到\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"12mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"外部可调\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"无\",\n      \"power_good_indicator\": \"Yes\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"未找到\",\n      \"input_under_voltage_protection\": \"UVLO (10.5V/10V)\",\n      \"output_over_voltage_protection\": \"Auto Recovery\",\n      \"output_under_voltage_protection\": \"未找到\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Current Limit\",\n      \"over_temperature_protection\": \"未找到\",\n      \"output_discharge\": \"False\",\n      \"integrated_ldo\": \"False\",\n      \"dynamic_voltage_setting\": \"False\",\n      \"pass_through_mode\": \"False\",\n      \"load_disconnect\": \"False\",\n      \"loop_control_mode\": \"平均电流模式\",\n      \"output_voltage_accuracy\": \"±3.33%\",\n      \"output_reference_voltage\": \"3V\"\n    }\n  },\n  {\n    \"part_number\": \"UC3854A\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Consumer\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PFC控制器\",\n    \"category_lv3\": \"升压型PFC控制器\",\n    \"part_number_title\": \"ADVANCED HIGH-POWER FACTOR PREREGULATOR\",\n    \"features\": [\n      \"Controls Boost PWM to Near-Unity Power Factor\",\n      \"Limits Line Current Distortion To <3%\",\n      \"World-Wide Operation Without Switches\",\n      \"Accurate Power Limiting\",\n      \"Fixed-Frequency Average Current-Mode Control\",\n      \"High Bandwidth (5 MHz), Low-Offset Current Amplifier\",\n      \"Integrated Current- and Voltage-Amplifier Output Clamps\",\n      \"Multiplier Improvements: Linearity, 500 mV VAC Offset (Eliminates External Resistor), 0 V to 5 V Multout Common-Mode Range\",\n      \"VREF GOOD Comparator\",\n      \"Faster and Improved Accuracy ENABLE Comparator\",\n      \"UVLO Options (16 V/10 V or 10.5 V/10 V)\",\n      \"300-μA Start-Up Supply Current\"\n    ],\n    \"description\": \"The UC3854A/B products are pin compatible enhanced versions of the UC3854. Like the UC3854, these products provide all of the functions necessary for active power factor corrected preregulators. The controller achieves near unity power factor by shaping the ac input line current waveform to correspond to the ac input line voltage. To do this the UC3854A/B uses average current mode control. Average current mode control maintains stable, low distortion sinusoidal line current without the need for slope compensation, unlike peak current mode control. A 1%, 7.5-V reference, fixed frequency oscillator, PWM, voltage amplifierwith soft-start, line voltage feedforward (VRMS squarer), input supply voltage clamp, and over current comparator round out the lilst of feataures.\",\n    \"applications\": [\n      \"有源功率因数校正预调整器\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UC3854A\",\n        \"order_device\": \"UC3854ADW\",\n        \"status\": \"Active\",\n        \"package_type\": \"SOIC\",\n        \"package_code\": \"DW\",\n        \"carrier_description\": \"TUBE\",\n        \"carrier_quantity\": \"40\",\n        \"package_drawing_code\": \"未找到\",\n        \"marking\": \"UC3854ADW\",\n        \"pin_count\": \"16\",\n        \"length\": \"未找到\",\n        \"width\": \"未找到\",\n        \"height\": \"未找到\",\n        \"pitch\": \"未找到\",\n        \"min_operation_temp\": \"0\",\n        \"max_operation_temp\": \"70\",\n        \"output_voltage\": \"Not Applicable\",\n        \"application_grade\": \"Commercial\"\n      },\n      {\n        \"part_number\": \"UC3854A\",\n        \"order_device\": \"UC3854ADWTR\",\n        \"status\": \"Active\",\n        \"package_type\": \"SOIC\",\n        \"package_code\": \"DW\",\n        \"carrier_description\": \"LARGE T&R\",\n        \"carrier_quantity\": \"2000\",\n        \"package_drawing_code\": \"未找到\",\n        \"marking\": \"UC3854ADW\",\n        \"pin_count\": \"16\",\n        \"length\": \"未找到\",\n        \"width\": \"未找到\",\n        \"height\": \"未找到\",\n        \"pitch\": \"未找到\",\n        \"min_operation_temp\": \"0\",\n        \"max_operation_temp\": \"70\",\n        \"output_voltage\": \"Not Applicable\",\n        \"application_grade\": \"Commercial\"\n      },\n      {\n        \"part_number\": \"UC3854A\",\n        \"order_device\": \"UC3854AN\",\n        \"status\": \"Active\",\n        \"package_type\": \"PDIP\",\n        \"package_code\": \"N\",\n        \"carrier_description\": \"TUBE\",\n        \"carrier_quantity\": \"25\",\n        \"package_drawing_code\": \"未找到\",\n        \"marking\": \"UC3854AN\",\n        \"pin_count\": \"16\",\n        \"length\": \"未找到\",\n        \"width\": \"未找到\",\n        \"height\": \"未找到\",\n        \"pitch\": \"未找到\",\n        \"min_operation_temp\": \"0\",\n        \"max_operation_temp\": \"70\",\n        \"output_voltage\": \"Not Applicable\",\n        \"application_grade\": \"Commercial\"\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCx854A/B\",\n        \"package_type\": \"J/N/DW\",\n        \"pins\": [\n          {\n            \"pin_number\": \"1\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"6\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"11\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"16\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      },\n      {\n        \"product_part_number\": \"UCx854A/B\",\n        \"package_type\": \"Q/L\",\n        \"pins\": [\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"17\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"18\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"19\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"20\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS329E\",\n    \"family_comparison\": \"The UCx854A/B family differs primarily in temperature grade and UVLO thresholds. UC1854 series is for military grade (-55°C to 125°C). UC2854 series is for industrial grade (-40°C to 85°C). UC3854 series is for commercial grade (0°C to 70°C). The 'A' versions have a 16V/10V UVLO threshold, while the 'B' versions have a 10.5V/10V UVLO threshold.\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"20V\",\n      \"min_input_voltage\": \"16V\",\n      \"max_output_voltage\": \"未找到\",\n      \"min_output_voltage\": \"未找到\",\n      \"max_output_current\": \"未找到\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"12mA\",\n      \"high_side_mosfet_resistance\": \"不适用\",\n      \"low_side_mosfet_resistance\": \"不适用\",\n      \"over_current_protection_threshold\": \"外部可调\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"无\",\n      \"power_good_indicator\": \"Yes\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"未找到\",\n      \"input_under_voltage_protection\": \"UVLO (16V/10V)\",\n      \"output_over_voltage_protection\": \"Auto Recovery\",\n      \"output_under_voltage_protection\": \"未找到\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Current Limit\",\n      \"over_temperature_protection\": \"未找到\",\n      \"output_discharge\": \"False\",\n      \"integrated_ldo\": \"False\",\n      \"dynamic_voltage_setting\": \"False\",\n      \"pass_through_mode\": \"False\",\n      \"load_disconnect\": \"False\",\n      \"loop_control_mode\": \"平均电流模式\",\n      \"output_voltage_accuracy\": \"±3.33%\",\n      \"output_reference_voltage\": \"3V\"\n    }\n  },\n  {\n    \"part_number\": \"UC3854B\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Consumer\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"PFC控制器\",\n    \"category_lv3\": \"升压型PFC控制器\",\n    \"part_number_title\": \"ADVANCED HIGH-POWER FACTOR PREREGULATOR\",\n    \"features\": [\n      \"Controls Boost PWM to Near-Unity Power Factor\",\n      \"Limits Line Current Distortion To <3%\",\n      \"World-Wide Operation Without Switches\",\n      \"Accurate Power Limiting\",\n      \"Fixed-Frequency Average Current-Mode Control\",\n      \"High Bandwidth (5 MHz), Low-Offset Current Amplifier\",\n      \"Integrated Current- and Voltage-Amplifier Output Clamps\",\n      \"Multiplier Improvements: Linearity, 500 mV VAC Offset (Eliminates External Resistor), 0 V to 5 V Multout Common-Mode Range\",\n      \"VREF GOOD Comparator\",\n      \"Faster and Improved Accuracy ENABLE Comparator\",\n      \"UVLO Options (16 V/10 V or 10.5 V/10 V)\",\n      \"300-μA Start-Up Supply Current\"\n    ],\n    \"description\": \"The UC3854A/B products are pin compatible enhanced versions of the UC3854. Like the UC3854, these products provide all of the functions necessary for active power factor corrected preregulators. The controller achieves near unity power factor by shaping the ac input line current waveform to correspond to the ac input line voltage. To do this the UC3854A/B uses average current mode control. Average current mode control maintains stable, low distortion sinusoidal line current without the need for slope compensation, unlike peak current mode control. A 1%, 7.5-V reference, fixed frequency oscillator, PWM, voltage amplifierwith soft-start, line voltage feedforward (VRMS squarer), input supply voltage clamp, and over current comparator round out the lilst of feataures.\",\n    \"applications\": [\n      \"有源功率因数校正预调整器\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UC3854B\",\n        \"order_device\": \"UC3854BDW\",\n        \"status\": \"Active\",\n        \"package_type\": \"SOIC\",\n        \"package_code\": \"DW\",\n        \"carrier_description\": \"TUBE\",\n        \"carrier_quantity\": \"40\",\n        \"package_drawing_code\": \"未找到\",\n        \"marking\": \"UC3854BDW\",\n        \"pin_count\": \"16\",\n        \"length\": \"未找到\",\n        \"width\": \"未找到\",\n        \"height\": \"未找到\",\n        \"pitch\": \"未找到\",\n        \"min_operation_temp\": \"0\",\n        \"max_operation_temp\": \"70\",\n        \"output_voltage\": \"Not Applicable\",\n        \"application_grade\": \"Commercial\"\n      },\n      {\n        \"part_number\": \"UC3854B\",\n        \"order_device\": \"UC3854BDWTR\",\n        \"status\": \"Active\",\n        \"package_type\": \"SOIC\",\n        \"package_code\": \"DW\",\n        \"carrier_description\": \"LARGE T&R\",\n        \"carrier_quantity\": \"2000\",\n        \"package_drawing_code\": \"未找到\",\n        \"marking\": \"UC3854BDW\",\n        \"pin_count\": \"16\",\n        \"length\": \"未找到\",\n        \"width\": \"未找到\",\n        \"height\": \"未找到\",\n        \"pitch\": \"未找到\",\n        \"min_operation_temp\": \"0\",\n        \"max_operation_temp\": \"70\",\n        \"output_voltage\": \"Not Applicable\",\n        \"application_grade\": \"Commercial\"\n      },\n      {\n        \"part_number\": \"UC3854B\",\n        \"order_device\": \"UC3854BN\",\n        \"status\": \"Active\",\n        \"package_type\": \"PDIP\",\n        \"package_code\": \"N\",\n        \"carrier_description\": \"TUBE\",\n        \"carrier_quantity\": \"25\",\n        \"package_drawing_code\": \"未找到\",\n        \"marking\": \"UC3854BN\",\n        \"pin_count\": \"16\",\n        \"length\": \"未找到\",\n        \"width\": \"未找到\",\n        \"height\": \"未找到\",\n        \"pitch\": \"未找到\",\n        \"min_operation_temp\": \"0\",\n        \"max_operation_temp\": \"70\",\n        \"output_voltage\": \"Not Applicable\",\n        \"application_grade\": \"Commercial\"\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UCx854A/B\",\n        \"package_type\": \"J/N/DW\",\n        \"pins\": [\n          {\n            \"pin_number\": \"1\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"6\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"11\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, a reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"16\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      },\n      {\n        \"product_part_number\": \"UCx854A/B\",\n        \"package_type\": \"Q/L\",\n        \"pins\": [\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"17\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"18\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"19\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"20\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"", "package": [{"type": "OPTION", "pin_count": "3854", "pitch": "1.27", "height": "2.65", "width": "7.5", "length": "10.3"}]}