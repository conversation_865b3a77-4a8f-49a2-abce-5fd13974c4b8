[{"part_number": "UCC25705", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关控制器", "category_lv3": "电压模式PWM控制器", "part_number_title": "UCC25705, <PERSON><PERSON><PERSON>706, <PERSON>C35705, UCC35706 HIGH-SPEED VOLTAGE MODE PULSE WIDTH MODULATOR", "features": ["Greater Than 4-MHz Operation", "Integrated Oscillator / Voltage Feed Forward Compensation", ">4:1 Input Voltage Range", "25-ns Current Limit Delay", "Programmable Maximum Duty Cycle Clamp", "Optocoupler Interface", "50-μA Start-Up Current", "4.2-mA Operating Current @ 1 MHz", "Smallest Footprint of the 8-pin MSOP Package Minimizes Board Area and Height"], "description": "The UCC35705 and UCC35706 devices are 8-pin voltage mode primary side controllers with fast over-current protection. These devices are used as core high-speed building blocks in high performance isolated and non-isolated power converters. UCC35705/UCC35706 devices feature a high speed oscillator with integrated feed-forward compensation for improved converter performance. A typical current sense to output delay time of 25 ns provides fast response to overload conditions. The IC also provides an accurate programmable maximum duty cycle clamp for increased protection which can also be disabled for the oscillator to run at maximum possible duty cycle. Two UVLO options are offered. The UCC35705 with lower turn-on voltage is intended for dc-to-dc converters while the higher turn-on voltage and the wider UVLO range of the UCC35706 is better suited for offline applications. The UCC35705/UCC35706 family is offered in 8-pin MSOP (DGK), SOIC (D) and PDIP (P) packages.", "applications": ["isolated power converters", "non-isolated power converters", "dc-to-dc converters"], "ordering_information": [{"part_number": "UCC25705", "order_device": "UCC25705D", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC25705", "order_device": "UCC25705DGK", "package_type": "VSSOP", "package_drawing_code": "DGK0008A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC25705", "order_device": "UCC25705DGKTR", "package_type": "VSSOP", "package_drawing_code": "DGK0008A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC25705", "order_device": "UCC25705DTR", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "UCCx570x", "package_type": "D, P, DGK", "pins": [{"pin_number": "1", "pin_name": "ILIM", "pin_description": "Provides a pulse-by-pulse current limit by terminating the PWM pulse when the input is above 200 mV. This provides a high speed (25 ns typical) path to reset the PWM latch, allowing for a pulse-by-pulse current limit."}, {"pin_number": "2", "pin_name": "FB", "pin_description": "Input to the PWM comparator. This pin is intended to interface with an optocoupler. Input impedance is 50-kΩ typical."}, {"pin_number": "3", "pin_name": "VFF", "pin_description": "The feed-forward pin provides the controller with a voltage proportional to the power supply input voltage. When the voltage on VFF is less than 1.0 V, both the output and oscillator are disabled."}, {"pin_number": "4", "pin_name": "DISCH", "pin_description": "A resistor to VIN sets the oscillator discharge current programming a maximum duty cycle. When grounded, an internal comparator switches the oscillator to a quick discharge mode. A small 100-pF capacitor between DISCH and GND may reduce oscillator jitter without impacting feed-forward performance. IDISCH must be between 25 μA and 250 μA over the entire VIN range."}, {"pin_number": "5", "pin_name": "RC", "pin_description": "The oscillator can be configured to provide a maximum duty cycle clamp. In this mode the on-time is set by RT and CT, while the off-time is set by RDISCH and CT. When the DISCH pin is grounded, the duty cycle clamp is disabled. The RC pin then provides a low impedance path to ground CT during the off time."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Ground return pin."}, {"pin_number": "7", "pin_name": "OUT", "pin_description": "The output is intended to drive an external FET driver or other high impedance circuits, but is not intended to directly drive a power MOSFET. This improves the controller's noise immunity."}, {"pin_number": "8", "pin_name": "VDD", "pin_description": "Power supply pin. This pin should be bypassed with a 0.1-μF capacitor for proper operation. The undervoltage lockout function of the UCC35705/6 allows for a low current startup mode and ensures that all circuits become active in a known state."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS473B - REVISED OCTOBER 2010", "family_comparison": "Two UVLO options are offered. The UCC35705 with lower turn-on voltage (8.8V typ) is intended for dc-to-dc converters while the higher turn-on voltage (12V typ) and the wider UVLO range of the UCC35706 is better suited for offline applications. The UCC2570x family operates over -40°C to 85°C, while the UCC3570x family operates over 0°C to 70°C.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "15V", "min_input_voltage": "8.0V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": ">4MHz", "quiescent_current": "4.2mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "200mV", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "不适用", "output_reference_voltage": "不适用"}, "package": [{"type": "OPTION", "pin_count": "8", "pitch": "1.27", "height": "1.75", "width": "6.19", "length": "5.0"}]}, {"part_number": "UCC25706", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关控制器", "category_lv3": "电压模式PWM控制器", "part_number_title": "UCC25705, <PERSON><PERSON><PERSON>706, <PERSON>C35705, UCC35706 HIGH-SPEED VOLTAGE MODE PULSE WIDTH MODULATOR", "features": ["Greater Than 4-MHz Operation", "Integrated Oscillator / Voltage Feed Forward Compensation", ">4:1 Input Voltage Range", "25-ns Current Limit Delay", "Programmable Maximum Duty Cycle Clamp", "Optocoupler Interface", "50-μA Start-Up Current", "4.2-mA Operating Current @ 1 MHz", "Smallest Footprint of the 8-pin MSOP Package Minimizes Board Area and Height"], "description": "The UCC35705 and UCC35706 devices are 8-pin voltage mode primary side controllers with fast over-current protection. These devices are used as core high-speed building blocks in high performance isolated and non-isolated power converters. UCC35705/UCC35706 devices feature a high speed oscillator with integrated feed-forward compensation for improved converter performance. A typical current sense to output delay time of 25 ns provides fast response to overload conditions. The IC also provides an accurate programmable maximum duty cycle clamp for increased protection which can also be disabled for the oscillator to run at maximum possible duty cycle. Two UVLO options are offered. The UCC35705 with lower turn-on voltage is intended for dc-to-dc converters while the higher turn-on voltage and the wider UVLO range of the UCC35706 is better suited for offline applications. The UCC35705/UCC35706 family is offered in 8-pin MSOP (DGK), SOIC (D) and PDIP (P) packages.", "applications": ["isolated power converters", "non-isolated power converters", "offline applications"], "ordering_information": [{"part_number": "UCC25706", "order_device": "UCC25706D", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC25706", "order_device": "UCC25706DGK", "package_type": "VSSOP", "package_drawing_code": "DGK0008A", "output_voltage": "可调", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "UCCx570x", "package_type": "D, P, DGK", "pins": [{"pin_number": "1", "pin_name": "ILIM", "pin_description": "Provides a pulse-by-pulse current limit by terminating the PWM pulse when the input is above 200 mV. This provides a high speed (25 ns typical) path to reset the PWM latch, allowing for a pulse-by-pulse current limit."}, {"pin_number": "2", "pin_name": "FB", "pin_description": "Input to the PWM comparator. This pin is intended to interface with an optocoupler. Input impedance is 50-kΩ typical."}, {"pin_number": "3", "pin_name": "VFF", "pin_description": "The feed-forward pin provides the controller with a voltage proportional to the power supply input voltage. When the voltage on VFF is less than 1.0 V, both the output and oscillator are disabled."}, {"pin_number": "4", "pin_name": "DISCH", "pin_description": "A resistor to VIN sets the oscillator discharge current programming a maximum duty cycle. When grounded, an internal comparator switches the oscillator to a quick discharge mode. A small 100-pF capacitor between DISCH and GND may reduce oscillator jitter without impacting feed-forward performance. IDISCH must be between 25 μA and 250 μA over the entire VIN range."}, {"pin_number": "5", "pin_name": "RC", "pin_description": "The oscillator can be configured to provide a maximum duty cycle clamp. In this mode the on-time is set by RT and CT, while the off-time is set by RDISCH and CT. When the DISCH pin is grounded, the duty cycle clamp is disabled. The RC pin then provides a low impedance path to ground CT during the off time."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Ground return pin."}, {"pin_number": "7", "pin_name": "OUT", "pin_description": "The output is intended to drive an external FET driver or other high impedance circuits, but is not intended to directly drive a power MOSFET. This improves the controller's noise immunity."}, {"pin_number": "8", "pin_name": "VDD", "pin_description": "Power supply pin. This pin should be bypassed with a 0.1-μF capacitor for proper operation. The undervoltage lockout function of the UCC35705/6 allows for a low current startup mode and ensures that all circuits become active in a known state."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS473B - REVISED OCTOBER 2010", "family_comparison": "Two UVLO options are offered. The UCC35705 with lower turn-on voltage (8.8V typ) is intended for dc-to-dc converters while the higher turn-on voltage (12V typ) and the wider UVLO range of the UCC35706 is better suited for offline applications. The UCC2570x family operates over -40°C to 85°C, while the UCC3570x family operates over 0°C to 70°C.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "15V", "min_input_voltage": "11.2V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": ">4MHz", "quiescent_current": "4.2mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "200mV", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "不适用", "output_reference_voltage": "不适用"}, "package": [{"type": "OPTION", "pin_count": "8", "pitch": "1.27", "height": "1.75", "width": "6.19", "length": "5.0"}]}, {"part_number": "UCC35705", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关控制器", "category_lv3": "电压模式PWM控制器", "part_number_title": "UCC25705, <PERSON><PERSON><PERSON>706, <PERSON>C35705, UCC35706 HIGH-SPEED VOLTAGE MODE PULSE WIDTH MODULATOR", "features": ["Greater Than 4-MHz Operation", "Integrated Oscillator / Voltage Feed Forward Compensation", ">4:1 Input Voltage Range", "25-ns Current Limit Delay", "Programmable Maximum Duty Cycle Clamp", "Optocoupler Interface", "50-μA Start-Up Current", "4.2-mA Operating Current @ 1 MHz", "Smallest Footprint of the 8-pin MSOP Package Minimizes Board Area and Height"], "description": "The UCC35705 and UCC35706 devices are 8-pin voltage mode primary side controllers with fast over-current protection. These devices are used as core high-speed building blocks in high performance isolated and non-isolated power converters. UCC35705/UCC35706 devices feature a high speed oscillator with integrated feed-forward compensation for improved converter performance. A typical current sense to output delay time of 25 ns provides fast response to overload conditions. The IC also provides an accurate programmable maximum duty cycle clamp for increased protection which can also be disabled for the oscillator to run at maximum possible duty cycle. Two UVLO options are offered. The UCC35705 with lower turn-on voltage is intended for dc-to-dc converters while the higher turn-on voltage and the wider UVLO range of the UCC35706 is better suited for offline applications. The UCC35705/UCC35706 family is offered in 8-pin MSOP (DGK), SOIC (D) and PDIP (P) packages.", "applications": ["isolated power converters", "non-isolated power converters", "dc-to-dc converters"], "ordering_information": [{"part_number": "UCC35705", "order_device": "UCC35705D", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC35705", "order_device": "UCC35705DGK", "package_type": "VSSOP", "package_drawing_code": "DGK0008A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC35705", "order_device": "UCC35705DGKG4", "package_type": "VSSOP", "package_drawing_code": "DGK0008A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC35705", "order_device": "UCC35705DTR", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "UCCx570x", "package_type": "D, P, DGK", "pins": [{"pin_number": "1", "pin_name": "ILIM", "pin_description": "Provides a pulse-by-pulse current limit by terminating the PWM pulse when the input is above 200 mV. This provides a high speed (25 ns typical) path to reset the PWM latch, allowing for a pulse-by-pulse current limit."}, {"pin_number": "2", "pin_name": "FB", "pin_description": "Input to the PWM comparator. This pin is intended to interface with an optocoupler. Input impedance is 50-kΩ typical."}, {"pin_number": "3", "pin_name": "VFF", "pin_description": "The feed-forward pin provides the controller with a voltage proportional to the power supply input voltage. When the voltage on VFF is less than 1.0 V, both the output and oscillator are disabled."}, {"pin_number": "4", "pin_name": "DISCH", "pin_description": "A resistor to VIN sets the oscillator discharge current programming a maximum duty cycle. When grounded, an internal comparator switches the oscillator to a quick discharge mode. A small 100-pF capacitor between DISCH and GND may reduce oscillator jitter without impacting feed-forward performance. IDISCH must be between 25 μA and 250 μA over the entire VIN range."}, {"pin_number": "5", "pin_name": "RC", "pin_description": "The oscillator can be configured to provide a maximum duty cycle clamp. In this mode the on-time is set by RT and CT, while the off-time is set by RDISCH and CT. When the DISCH pin is grounded, the duty cycle clamp is disabled. The RC pin then provides a low impedance path to ground CT during the off time."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Ground return pin."}, {"pin_number": "7", "pin_name": "OUT", "pin_description": "The output is intended to drive an external FET driver or other high impedance circuits, but is not intended to directly drive a power MOSFET. This improves the controller's noise immunity."}, {"pin_number": "8", "pin_name": "VDD", "pin_description": "Power supply pin. This pin should be bypassed with a 0.1-μF capacitor for proper operation. The undervoltage lockout function of the UCC35705/6 allows for a low current startup mode and ensures that all circuits become active in a known state."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS473B - REVISED OCTOBER 2010", "family_comparison": "Two UVLO options are offered. The UCC35705 with lower turn-on voltage (8.8V typ) is intended for dc-to-dc converters while the higher turn-on voltage (12V typ) and the wider UVLO range of the UCC35706 is better suited for offline applications. The UCC2570x family operates over -40°C to 85°C, while the UCC3570x family operates over 0°C to 70°C.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "15V", "min_input_voltage": "8.0V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": ">4MHz", "quiescent_current": "4.2mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "200mV", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "不适用", "output_reference_voltage": "不适用"}, "package": [{"type": "OPTION", "pin_count": "8", "pitch": "1.27", "height": "1.75", "width": "6.19", "length": "5.0"}]}, {"part_number": "UCC35706", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关控制器", "category_lv3": "电压模式PWM控制器", "part_number_title": "UCC25705, <PERSON><PERSON><PERSON>706, <PERSON>C35705, UCC35706 HIGH-SPEED VOLTAGE MODE PULSE WIDTH MODULATOR", "features": ["Greater Than 4-MHz Operation", "Integrated Oscillator / Voltage Feed Forward Compensation", ">4:1 Input Voltage Range", "25-ns Current Limit Delay", "Programmable Maximum Duty Cycle Clamp", "Optocoupler Interface", "50-μA Start-Up Current", "4.2-mA Operating Current @ 1 MHz", "Smallest Footprint of the 8-pin MSOP Package Minimizes Board Area and Height"], "description": "The UCC35705 and UCC35706 devices are 8-pin voltage mode primary side controllers with fast over-current protection. These devices are used as core high-speed building blocks in high performance isolated and non-isolated power converters. UCC35705/UCC35706 devices feature a high speed oscillator with integrated feed-forward compensation for improved converter performance. A typical current sense to output delay time of 25 ns provides fast response to overload conditions. The IC also provides an accurate programmable maximum duty cycle clamp for increased protection which can also be disabled for the oscillator to run at maximum possible duty cycle. Two UVLO options are offered. The UCC35705 with lower turn-on voltage is intended for dc-to-dc converters while the higher turn-on voltage and the wider UVLO range of the UCC35706 is better suited for offline applications. The UCC35705/UCC35706 family is offered in 8-pin MSOP (DGK), SOIC (D) and PDIP (P) packages.", "applications": ["isolated power converters", "non-isolated power converters", "offline applications"], "ordering_information": [{"part_number": "UCC35706", "order_device": "UCC35706D", "package_type": "SOIC", "package_drawing_code": "D0008A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC35706", "order_device": "UCC35706DGK", "package_type": "VSSOP", "package_drawing_code": "DGK0008A", "output_voltage": "可调", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "UCCx570x", "package_type": "D, P, DGK", "pins": [{"pin_number": "1", "pin_name": "ILIM", "pin_description": "Provides a pulse-by-pulse current limit by terminating the PWM pulse when the input is above 200 mV. This provides a high speed (25 ns typical) path to reset the PWM latch, allowing for a pulse-by-pulse current limit."}, {"pin_number": "2", "pin_name": "FB", "pin_description": "Input to the PWM comparator. This pin is intended to interface with an optocoupler. Input impedance is 50-kΩ typical."}, {"pin_number": "3", "pin_name": "VFF", "pin_description": "The feed-forward pin provides the controller with a voltage proportional to the power supply input voltage. When the voltage on VFF is less than 1.0 V, both the output and oscillator are disabled."}, {"pin_number": "4", "pin_name": "DISCH", "pin_description": "A resistor to VIN sets the oscillator discharge current programming a maximum duty cycle. When grounded, an internal comparator switches the oscillator to a quick discharge mode. A small 100-pF capacitor between DISCH and GND may reduce oscillator jitter without impacting feed-forward performance. IDISCH must be between 25 μA and 250 μA over the entire VIN range."}, {"pin_number": "5", "pin_name": "RC", "pin_description": "The oscillator can be configured to provide a maximum duty cycle clamp. In this mode the on-time is set by RT and CT, while the off-time is set by RDISCH and CT. When the DISCH pin is grounded, the duty cycle clamp is disabled. The RC pin then provides a low impedance path to ground CT during the off time."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Ground return pin."}, {"pin_number": "7", "pin_name": "OUT", "pin_description": "The output is intended to drive an external FET driver or other high impedance circuits, but is not intended to directly drive a power MOSFET. This improves the controller's noise immunity."}, {"pin_number": "8", "pin_name": "VDD", "pin_description": "Power supply pin. This pin should be bypassed with a 0.1-μF capacitor for proper operation. The undervoltage lockout function of the UCC35705/6 allows for a low current startup mode and ensures that all circuits become active in a known state."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS473B - REVISED OCTOBER 2010", "family_comparison": "Two UVLO options are offered. The UCC35705 with lower turn-on voltage (8.8V typ) is intended for dc-to-dc converters while the higher turn-on voltage (12V typ) and the wider UVLO range of the UCC35706 is better suited for offline applications. The UCC2570x family operates over -40°C to 85°C, while the UCC3570x family operates over 0°C to 70°C.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "15V", "min_input_voltage": "11.2V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": ">4MHz", "quiescent_current": "4.2mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "200mV", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "不适用", "output_reference_voltage": "不适用"}, "package": [{"type": "OPTION", "pin_count": "8", "pitch": "1.27", "height": "1.75", "width": "6.19", "length": "5.0"}]}]