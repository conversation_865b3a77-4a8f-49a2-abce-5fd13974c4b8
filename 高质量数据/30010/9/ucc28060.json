{"part_number": "UCC28060", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "功率因数校正(PFC)控制器", "category_lv3": "交错式PFC控制器", "part_number_title": "Natural Interleaving™ DUAL-PHA<PERSON> TRANSITION-MODE PFC CONTROLLER", "features": ["Easy Phase Management Facilitates Compliance to Light-Load Efficient Standards", "FailSafe OVP with Dual Paths Prevents Output Over-voltage Conditions Caused by Voltage-Sensing Failures", "Sensorless Current Shaping Simplifies Board Layout and Improves Efficiency", "Inrush Safe Current Limiting: Prevents MOSFET conduction during inrush, Eliminates reverse recovery events in output rectifiers", "Cost Savings", "Improved Efficiency and Design Flexibility over Traditional, Single-Phase Continuous Conduction Mode (CCM)", "Input Filter and Output Capacitor Current Cancellation: Reduced current ripple for higher system reliability and smaller bulk capacitor, Reduced EMI filter size", "Enables Use of Low-Cost Diodes without Extensive Snubber Circuitry", "Improved Light-Load Efficiency", "Improved Transient Response", "Complete System-Level Protection", "1-A Source/1.8-A Sink Gate Drivers", "Operating Temperature Range: –40°C to +125°C in an SOIC 16-pin package"], "description": "Optimized for high-volume consumer applications, this solution extends the advantages of transition mode—high efficiency with low-cost components—to higher power ratings than previously possible. By utilizing a Natural Interleaving technique, both channels operate as masters (that is, there is no slave channel) synchronized to the same frequency. This approach delivers inherently strong matching, faster responses, and ensures that each channel operates in transition mode. Complete system-level protections feature input brownout, output over-voltage, open-loop, overload, soft-start, phase-fail detection, and thermal shutdown. The additional FailSafe over-voltage protection (OVP) feature protects against shorts to an intermediate voltage that, if undetected, could lead to catastrophic device failure.", "applications": ["100-W to 800-W Power Supplies", "LCD, Plasma, and DLP® TVs", "Computer Power Supplies", "Entry Level Servers", "Electronic Lighting Ballasts"], "ordering_information": [{"part_number": "UCC28060", "order_device": "UCC28060D", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28060", "order_device": "UCC28060D.B", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28060", "order_device": "UCC28060DG4", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28060", "order_device": "UCC28060DR", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28060", "order_device": "UCC28060DR.B", "package_type": "SOIC", "package_drawing_code": "D (R-PDSO-G16)", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC28060", "package_type": "SOIC 16-Pin (D)", "pins": [{"pin_number": "1", "pin_name": "ZCDB", "pin_description": "Zero current detection input for phase B. Expects a negative edge when inductor current goes to zero. Clamped at 0V and 3V."}, {"pin_number": "2", "pin_name": "VSENSE", "pin_description": "Output dc voltage sense. Connect to a voltage divider across the output. Error amplifier reference is 6V. Also used for open-loop protection and disable."}, {"pin_number": "3", "pin_name": "TSET", "pin_description": "Timing set. Connect a resistor from TSET to AGND to set the on-time versus COMP voltage and the minimum period."}, {"pin_number": "4", "pin_name": "PHB", "pin_description": "Phase B enable. Turns on/off channel B of the boost converter. Can be connected to COMP for easy phase management or driven by external logic."}, {"pin_number": "5", "pin_name": "COMP", "pin_description": "Error amplifier output. High-impedance current source. Connect voltage regulation loop compensation components to AGND."}, {"pin_number": "6", "pin_name": "AGND", "pin_description": "Analog ground. Connect analog signal bypass capacitors, compensation components, and analog signal returns to this pin."}, {"pin_number": "7", "pin_name": "VINAC", "pin_description": "Input ac voltage sense. Connect to a voltage divider across the rectified input mains to sense input voltage range and brownout."}, {"pin_number": "8", "pin_name": "HVSEN", "pin_description": "High voltage output sense. Used for FailSafe OVP and to enable a downstream power converter. Monitors output over-voltage redundantly with VSENSE."}, {"pin_number": "9", "pin_name": "PWMCNTL", "pin_description": "PWM enable logic output. Open-drain output goes low when HVSEN is within the good region and ZCD inputs are switching correctly."}, {"pin_number": "10", "pin_name": "CS", "pin_description": "Current sense input. Connect to the current sense resistor for cycle-by-cycle over-current protection."}, {"pin_number": "11", "pin_name": "GDB", "pin_description": "Channel B gate drive output. Connect to the gate of the power FET for phase B."}, {"pin_number": "12", "pin_name": "VCC", "pin_description": "Bias supply input. Connect to a controlled bias supply of between 14V and 21V."}, {"pin_number": "13", "pin_name": "PGND", "pin_description": "Power ground for the integrated circuit. Connect to AGND through a separate short trace."}, {"pin_number": "14", "pin_name": "GDA", "pin_description": "Channel A gate drive output. Connect to the gate of the power FET for phase A."}, {"pin_number": "15", "pin_name": "VREF", "pin_description": "Voltage reference output. 6 VDC reference. Can be used to bias other circuits requiring less than 2 mA."}, {"pin_number": "16", "pin_name": "ZCDA", "pin_description": "Zero current detection input for phase A. Expects a negative edge when inductor current goes to zero. Clamped at 0V and 3V."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS767E-MAY 2007-REVISED NOVEMBER 2008", "family_comparison": [{"device": "UCC28051", "description": "PFC controller for low to medium power applications"}, {"device": "UCC28019", "description": "8-pin continuous conduction mode (CCM) PFC controller"}], "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 2, "channel_count": 2, "max_input_voltage": "265V", "min_input_voltage": "85V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "可调", "max_switch_frequency": "550kHz", "quiescent_current": "100µA", "high_side_mosfet_resistance": "不适用(外部器件)", "low_side_mosfet_resistance": "不适用(外部器件)", "over_current_protection_threshold": "-0.2V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Phase Shedding / Pulse Skipping", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Auto Recovery", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "Transition Mode Control", "output_voltage_accuracy": "±3%", "output_reference_voltage": "6V"}, "package": [{"type": "SOIC", "pin_count": "1", "pitch": "1.27", "height": "1.75", "width": "6.2", "length": "10.0"}]}