{"part_number": "UCC28019", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "功率因数校正 (PFC) 控制器", "category_lv3": "连续导通模式 (CCM) PFC 控制器", "part_number_title": "UCC28019 8-Pin Continuous Conduction Mode (CCM) PFC Controller", "features": ["8-pin Solution Without Sensing Line Voltage Reduces External Components", "Wide-Range Universal AC Input Voltage", "Fixed 65-kHz Operating Frequency", "Maximum Duty Cycle of 97%", "Output Over/Under-Voltage Protection", "Input Brown-Out Protection", "Cycle-by-Cycle Peak Current Limiting", "Open Loop Detection", "Low-Power User Controlled Standby Mode"], "description": "The UCC28019 8-pin active Power Factor Correction (PFC) controller uses the boost topology operating in Continuous Conduction Mode (CCM). The controller is suitable for systems in the 100 W to >2 kW range over a wide-range universal ac line input. Startup current during under-voltage lockout is less than 200 μA. The user can control low power standby mode by pulling the VSENSE pin below 0.77 V. Low-distortion wave-shaping of the input current using average current mode control is achieved without input line sensing, reducing the Bill of Materials component count. Simple external networks allow for flexible compensation of the current and voltage control loops. The switching frequency is internally fixed and trimmed to better than 5% accuracy at 25°C. Fast 1.5-A gate peak current drives the external switch. Numerous system-level protection features include peak current limit, soft over-current detection, open-loop detection, input brown-out detection, output over-voltage protection/under-voltage detection, a no-power discharge path on VCOMP, and overload protection on ICOMP. Soft-Start limits boost current during start-up. A trimmed internal reference provides accurate protection thresholds and regulation set-point. An internal clamp limits the gate drive voltage to 12.5 V.", "applications": ["CCM Boost Power Factor Correction Power Converters in the 100 W to >2 kW Range", "Server and Desktop Power Supplies", "Telecom Rectifiers", "Industrial Electronics", "Home Electronics"], "ordering_information": [{"part_number": "UCC28019", "order_device": "UCC28019D", "package_type": "SOIC", "package_drawing_code": "D", "output_voltage": "不适用", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28019", "order_device": "UCC28019DR", "package_type": "SOIC", "package_drawing_code": "D", "output_voltage": "不适用", "min_operation_temp": "-40", "max_operation_temp": "125"}, {"part_number": "UCC28019", "order_device": "UCC28019P", "package_type": "PDIP", "package_drawing_code": "P", "output_voltage": "不适用", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC28019", "package_type": "SOIC-8, PDIP-8", "pins": [{"pin_number": "1", "pin_name": "GND", "pin_description": "Device ground reference."}, {"pin_number": "2", "pin_name": "ICOMP", "pin_description": "Transconductance current amplifier output. A capacitor connected to GND provides compensation and averaging of the current sense signal in the current control loop. The controller is disabled if the voltage on ICOMP is less than 0.6 V."}, {"pin_number": "3", "pin_name": "ISENSE", "pin_description": "An input for the voltage across the external current sense resistor, which represents the instantaneous current through the PFC boost inductor. This voltage is averaged to eliminate the effects of noise and ripple. Soft Over Current (SOC) limits the average inductor current. Cycle-by-cycle peak current limit (PCL) immediately shuts off the GATE drive if the peak-limit voltage is exceeded. Use a 220-Ω resistor between this pin and the current sense resistor to limit inrush-surge currents into this pin."}, {"pin_number": "4", "pin_name": "VINS", "pin_description": "Input Brown Out Protection (IBOP) detects when the system ac-input voltage is above a user-defined normal operating level, or below a user-defined 'brown-out' level. At startup the controller is disabled until the VINS voltage exceeds a threshold of 1.5 V, initiating a soft-start. The controller is also disabled if VINS drops below the brown-out threshold of 0.8 V."}, {"pin_number": "5", "pin_name": "VCOMP", "pin_description": "Transconductance voltage error amplifier output. A resistor-capacitor network connected from this pin to GND provides compensation. VCOMP is held at GND until VCC, VINS, and VSENSE all exceed their threshold voltages. Soft-start is programmed by the capacitance on this pin."}, {"pin_number": "6", "pin_name": "VSENSE", "pin_description": "An external resistor-divider network connected from this pin to the PFC output voltage provides feedback sensing for output voltage regulation. Standby disables the controller when VSENSE drops below 0.8V. Output over-voltage protection (OVP) disables the GATE output when VSENSE exceeds 105% of the reference voltage. Enhanced Dynamic Response (EDR) is triggered when VSENSE falls below 95% of the reference voltage."}, {"pin_number": "7", "pin_name": "VCC", "pin_description": "External bias supply input. Under Voltage Lock Out (UVLO) disables the controller until VCC exceeds a turn-on threshold of 10.5 V. Operation continues until VCC falls below the turn-off (UVLO) threshold of 9.5 V."}, {"pin_number": "8", "pin_name": "GATE", "pin_description": "Integrated push-pull gate driver for one or more external power MOSFETs. 2.0-A sink and 1.5-A source capability. Output voltage is clamped at 12.5 V."}]}], "datasheet_cn": "未找到", "datasheet_en": "UCC28019.pdf (SLUS755B, 2007-12)", "family_comparison": "UCC3817/18: Full-Feature PFC Controller; UC2853A: 8-Pin CCM PFC Controller", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "21V", "min_input_voltage": "10.5V", "max_output_voltage": "可外部设定", "min_output_voltage": "可外部设定", "max_output_current": "不适用", "max_switch_frequency": "71kHz", "quiescent_current": "2900μA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.08V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "±2%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pitch": "1.27", "height": "1.75", "length": "4.9", "width": "3.91", "pin_count": "8"}]}