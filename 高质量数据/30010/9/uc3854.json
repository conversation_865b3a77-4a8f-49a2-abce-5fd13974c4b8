{"response": "\n[\n  {\n    \"part_number\": \"UC1854\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Military\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"功率因数校正(PFC)控制器\",\n    \"category_lv3\": \"升压(Boost)型PFC控制器\",\n    \"part_number_title\": \"ADVANCED HIGH-POWER FACTOR PREREGULATOR\",\n    \"features\": [\n      \"Controls Boost PWM to Near-Unity Power Factor\",\n      \"Limits Line Current Distortion To <3%\",\n      \"World-Wide Operation Without Switches\",\n      \"Accurate Power Limiting\",\n      \"Fixed-Frequency Average Current-Mode Control\",\n      \"High Bandwidth (5 MHz), Low-Offset Current Amplifier\",\n      \"Integrated Current- and Voltage-Amplifier Output Clamps\",\n      \"Multiplier Improvements: Linearity, 500 mV VAC Offset (Eliminates External Resistor), 0 V to 5 V Multout Common-Mode Range\",\n      \"VREF GOOD Comparator\",\n      \"Faster and Improved Accuracy ENABLE Comparator\",\n      \"UVLO Options (16 V/10 V or 10.5 V/10 V)\",\n      \"300-μA Start-Up Supply Current\"\n    ],\n    \"description\": \"The UC1854 provides active-power factor correction for power systems that otherwise would draw nonsinusoidal current from sinusoidal power lines. This device implements all the control functions necessary to build a power supply capable of optimally using available power-line current while minimizing linecurrent distortion. To do this, the UC1854 contains a voltage amplifier, an analog multiplier and divider, a current amplifier, and a fixed-frequency PWM. In addition, the UC1854 contains a power MOSFETcompatible gate driver, 7.5-V reference, line anticipator, load-enable comparator, low-supply detector, and overcurrent comparator. The UC1854 uses average current-mode control to accomplish fixed-frequency current control with stability and low distortion. Unlike peak current-mode, average current control accurately maintains sinusoidal line current without slope compensation and with minimal response to noise transients.\",\n    \"applications\": [\n      \"Offline AC-to-DC Converters\",\n      \"Medical, Industrial, Telecom, and IT Power Supplies\",\n      \"Uninterruptible Power Supplies (UPS)\",\n      \"Appliances and White Goods\",\n      \"Active power factor corrected preregulators\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UC1854\",\n        \"order_device\": \"5962-9326101MEA\",\n        \"package_type\": \"CDIP\",\n        \"package_drawing_code\": \"J\",\n        \"min_operation_temp\": \"-55\",\n        \"max_operation_temp\": \"125\"\n      },\n      {\n        \"part_number\": \"UC1854\",\n        \"order_device\": \"UC1854J\",\n        \"package_type\": \"CDIP\",\n        \"package_drawing_code\": \"J\",\n        \"min_operation_temp\": \"-55\",\n        \"max_operation_temp\": \"125\"\n      },\n      {\n        \"part_number\": \"UC1854\",\n        \"order_device\": \"UC1854J883B\",\n        \"package_type\": \"CDIP\",\n        \"package_drawing_code\": \"J\",\n        \"min_operation_temp\": \"-55\",\n        \"max_operation_temp\": \"125\"\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UC1854\",\n        \"package_type\": \"J/N/DW\",\n        \"pins\": [\n          {\n            \"pin_number\": \"1\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"6\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"11\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"16\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      },\n      {\n        \"product_part_number\": \"UC1854\",\n        \"package_type\": \"Q/L\",\n        \"pins\": [\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"18\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"20\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"17\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"19\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          }\n        ]\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS329E-MONTH 2003-REVISED JANUARY 2008\",\n    \"family_comparison\": \"未找到\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"20V\",\n      \"min_input_voltage\": \"16V\",\n      \"max_output_voltage\": \"可调\",\n      \"min_output_voltage\": \"可调\",\n      \"max_output_current\": \"未找到\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"12mA\",\n      \"high_side_mosfet_resistance\": \"不适用(控制器)\",\n      \"low_side_mosfet_resistance\": \"不适用(控制器)\",\n      \"over_current_protection_threshold\": \"外部可调\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"未找到\",\n      \"power_good_indicator\": \"Yes\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"No\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"No\",\n      \"output_under_voltage_protection\": \"No\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Fold Back\",\n      \"over_temperature_protection\": \"No\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"平均电流模式\",\n      \"output_voltage_accuracy\": \"2%\",\n      \"output_reference_voltage\": \"7.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UC1854A\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Military\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"功率因数校正(PFC)控制器\",\n    \"category_lv3\": \"升压(Boost)型PFC控制器\",\n    \"part_number_title\": \"ADVANCED HIGH-POWER FACTOR PREREGULATOR\",\n    \"features\": [\n      \"Controls Boost PWM to Near-Unity Power Factor\",\n      \"Limits Line Current Distortion To <3%\",\n      \"World-Wide Operation Without Switches\",\n      \"Accurate Power Limiting\",\n      \"Fixed-Frequency Average Current-Mode Control\",\n      \"High Bandwidth (5 MHz), Low-Offset Current Amplifier\",\n      \"Integrated Current- and Voltage-Amplifier Output Clamps\",\n      \"Multiplier Improvements: Linearity, 500 mV VAC Offset (Eliminates External Resistor), 0 V to 5 V Multout Common-Mode Range\",\n      \"VREF GOOD Comparator\",\n      \"Faster and Improved Accuracy ENABLE Comparator\",\n      \"UVLO Options (16 V/10 V or 10.5 V/10 V)\",\n      \"300-μA Start-Up Supply Current\"\n    ],\n    \"description\": \"The UC1854 provides active-power factor correction for power systems that otherwise would draw nonsinusoidal current from sinusoidal power lines. This device implements all the control functions necessary to build a power supply capable of optimally using available power-line current while minimizing linecurrent distortion. To do this, the UC1854 contains a voltage amplifier, an analog multiplier and divider, a current amplifier, and a fixed-frequency PWM. In addition, the UC1854 contains a power MOSFETcompatible gate driver, 7.5-V reference, line anticipator, load-enable comparator, low-supply detector, and overcurrent comparator. The UC1854 uses average current-mode control to accomplish fixed-frequency current control with stability and low distortion. Unlike peak current-mode, average current control accurately maintains sinusoidal line current without slope compensation and with minimal response to noise transients.\",\n    \"applications\": [\n      \"Offline AC-to-DC Converters\",\n      \"Medical, Industrial, Telecom, and IT Power Supplies\",\n      \"Uninterruptible Power Supplies (UPS)\",\n      \"Appliances and White Goods\",\n      \"Active power factor corrected preregulators\"\n    ],\n    \"ordering_information\": [],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UC1854A\",\n        \"package_type\": \"J/N/DW\",\n        \"pins\": [\n          {\n            \"pin_number\": \"1\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"6\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"11\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"16\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      },\n      {\n        \"product_part_number\": \"UC1854A\",\n        \"package_type\": \"Q/L\",\n        \"pins\": [\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"18\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"20\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"17\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"19\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          }\n        ]\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS329E-MONTH 2003-REVISED JANUARY 2008\",\n    \"family_comparison\": \"未找到\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"20V\",\n      \"min_input_voltage\": \"16V\",\n      \"max_output_voltage\": \"可调\",\n      \"min_output_voltage\": \"可调\",\n      \"max_output_current\": \"未找到\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"12mA\",\n      \"high_side_mosfet_resistance\": \"不适用(控制器)\",\n      \"low_side_mosfet_resistance\": \"不适用(控制器)\",\n      \"over_current_protection_threshold\": \"外部可调\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"未找到\",\n      \"power_good_indicator\": \"Yes\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"No\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"No\",\n      \"output_under_voltage_protection\": \"No\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Fold Back\",\n      \"over_temperature_protection\": \"No\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"平均电流模式\",\n      \"output_voltage_accuracy\": \"2%\",\n      \"output_reference_voltage\": \"7.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UC1854B\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Military\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"功率因数校正(PFC)控制器\",\n    \"category_lv3\": \"升压(Boost)型PFC控制器\",\n    \"part_number_title\": \"ADVANCED HIGH-POWER FACTOR PREREGULATOR\",\n    \"features\": [\n      \"Controls Boost PWM to Near-Unity Power Factor\",\n      \"Limits Line Current Distortion To <3%\",\n      \"World-Wide Operation Without Switches\",\n      \"Accurate Power Limiting\",\n      \"Fixed-Frequency Average Current-Mode Control\",\n      \"High Bandwidth (5 MHz), Low-Offset Current Amplifier\",\n      \"Integrated Current- and Voltage-Amplifier Output Clamps\",\n      \"Multiplier Improvements: Linearity, 500 mV VAC Offset (Eliminates External Resistor), 0 V to 5 V Multout Common-Mode Range\",\n      \"VREF GOOD Comparator\",\n      \"Faster and Improved Accuracy ENABLE Comparator\",\n      \"UVLO Options (16 V/10 V or 10.5 V/10 V)\",\n      \"300-μA Start-Up Supply Current\"\n    ],\n    \"description\": \"The UC1854 provides active-power factor correction for power systems that otherwise would draw nonsinusoidal current from sinusoidal power lines. This device implements all the control functions necessary to build a power supply capable of optimally using available power-line current while minimizing linecurrent distortion. To do this, the UC1854 contains a voltage amplifier, an analog multiplier and divider, a current amplifier, and a fixed-frequency PWM. In addition, the UC1854 contains a power MOSFETcompatible gate driver, 7.5-V reference, line anticipator, load-enable comparator, low-supply detector, and overcurrent comparator. The UC1854 uses average current-mode control to accomplish fixed-frequency current control with stability and low distortion. Unlike peak current-mode, average current control accurately maintains sinusoidal line current without slope compensation and with minimal response to noise transients.\",\n    \"applications\": [\n      \"Offline AC-to-DC Converters\",\n      \"Medical, Industrial, Telecom, and IT Power Supplies\",\n      \"Uninterruptible Power Supplies (UPS)\",\n      \"Appliances and White Goods\",\n      \"Active power factor corrected preregulators\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UC1854B\",\n        \"order_device\": \"UC1854BJ\",\n        \"package_type\": \"CDIP\",\n        \"package_drawing_code\": \"J\",\n        \"min_operation_temp\": \"-55\",\n        \"max_operation_temp\": \"125\"\n      },\n      {\n        \"part_number\": \"UC1854B\",\n        \"order_device\": \"UC1854BJ/883B\",\n        \"package_type\": \"CDIP\",\n        \"package_drawing_code\": \"J\",\n        \"min_operation_temp\": \"-55\",\n        \"max_operation_temp\": \"125\"\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UC1854B\",\n        \"package_type\": \"J/N/DW\",\n        \"pins\": [\n          {\n            \"pin_number\": \"1\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"6\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"11\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"16\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      },\n      {\n        \"product_part_number\": \"UC1854B\",\n        \"package_type\": \"Q/L\",\n        \"pins\": [\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"18\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"20\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"17\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"19\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          }\n        ]\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS329E-MONTH 2003-REVISED JANUARY 2008\",\n    \"family_comparison\": \"未找到\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"20V\",\n      \"min_input_voltage\": \"10.5V\",\n      \"max_output_voltage\": \"可调\",\n      \"min_output_voltage\": \"可调\",\n      \"max_output_current\": \"未找到\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"12mA\",\n      \"high_side_mosfet_resistance\": \"不适用(控制器)\",\n      \"low_side_mosfet_resistance\": \"不适用(控制器)\",\n      \"over_current_protection_threshold\": \"外部可调\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"未找到\",\n      \"power_good_indicator\": \"Yes\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"No\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"No\",\n      \"output_under_voltage_protection\": \"No\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Fold Back\",\n      \"over_temperature_protection\": \"No\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"平均电流模式\",\n      \"output_voltage_accuracy\": \"2%\",\n      \"output_reference_voltage\": \"7.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UC2854\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Industry\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"功率因数校正(PFC)控制器\",\n    \"category_lv3\": \"升压(Boost)型PFC控制器\",\n    \"part_number_title\": \"ADVANCED HIGH-POWER FACTOR PREREGULATOR\",\n    \"features\": [\n      \"Controls Boost PWM to Near-Unity Power Factor\",\n      \"Limits Line Current Distortion To <3%\",\n      \"World-Wide Operation Without Switches\",\n      \"Accurate Power Limiting\",\n      \"Fixed-Frequency Average Current-Mode Control\",\n      \"High Bandwidth (5 MHz), Low-Offset Current Amplifier\",\n      \"Integrated Current- and Voltage-Amplifier Output Clamps\",\n      \"Multiplier Improvements: Linearity, 500 mV VAC Offset (Eliminates External Resistor), 0 V to 5 V Multout Common-Mode Range\",\n      \"VREF GOOD Comparator\",\n      \"Faster and Improved Accuracy ENABLE Comparator\",\n      \"UVLO Options (16 V/10 V or 10.5 V/10 V)\",\n      \"300-μA Start-Up Supply Current\"\n    ],\n    \"description\": \"The UC1854 provides active-power factor correction for power systems that otherwise would draw nonsinusoidal current from sinusoidal power lines. This device implements all the control functions necessary to build a power supply capable of optimally using available power-line current while minimizing linecurrent distortion. To do this, the UC1854 contains a voltage amplifier, an analog multiplier and divider, a current amplifier, and a fixed-frequency PWM. In addition, the UC1854 contains a power MOSFETcompatible gate driver, 7.5-V reference, line anticipator, load-enable comparator, low-supply detector, and overcurrent comparator. The UC1854 uses average current-mode control to accomplish fixed-frequency current control with stability and low distortion. Unlike peak current-mode, average current control accurately maintains sinusoidal line current without slope compensation and with minimal response to noise transients.\",\n    \"applications\": [\n      \"Offline AC-to-DC Converters\",\n      \"Medical, Industrial, Telecom, and IT Power Supplies\",\n      \"Uninterruptible Power Supplies (UPS)\",\n      \"Appliances and White Goods\",\n      \"Active power factor corrected preregulators\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UC2854\",\n        \"order_device\": \"UC2854BJ\",\n        \"package_type\": \"CDIP\",\n        \"package_drawing_code\": \"J\",\n        \"min_operation_temp\": \"-40\",\n        \"max_operation_temp\": \"85\"\n      },\n      {\n        \"part_number\": \"UC2854\",\n        \"order_device\": \"UC2854DW\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"DW\",\n        \"min_operation_temp\": \"-40\",\n        \"max_operation_temp\": \"85\"\n      },\n      {\n        \"part_number\": \"UC2854\",\n        \"order_device\": \"UC2854DWTR\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"DW\",\n        \"min_operation_temp\": \"-40\",\n        \"max_operation_temp\": \"85\"\n      },\n      {\n        \"part_number\": \"UC2854\",\n        \"order_device\": \"UC2854N\",\n        \"package_type\": \"PDIP\",\n        \"package_drawing_code\": \"N\",\n        \"min_operation_temp\": \"-40\",\n        \"max_operation_temp\": \"85\"\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UC2854\",\n        \"package_type\": \"J/N/DW\",\n        \"pins\": [\n          {\n            \"pin_number\": \"1\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"6\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"11\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"16\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      },\n      {\n        \"product_part_number\": \"UC2854\",\n        \"package_type\": \"Q/L\",\n        \"pins\": [\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"18\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"20\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"17\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"19\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          }\n        ]\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS329E-MONTH 2003-REVISED JANUARY 2008\",\n    \"family_comparison\": \"未找到\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"20V\",\n      \"min_input_voltage\": \"16V\",\n      \"max_output_voltage\": \"可调\",\n      \"min_output_voltage\": \"可调\",\n      \"max_output_current\": \"未找到\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"12mA\",\n      \"high_side_mosfet_resistance\": \"不适用(控制器)\",\n      \"low_side_mosfet_resistance\": \"不适用(控制器)\",\n      \"over_current_protection_threshold\": \"外部可调\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"未找到\",\n      \"power_good_indicator\": \"Yes\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"No\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"No\",\n      \"output_under_voltage_protection\": \"No\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Fold Back\",\n      \"over_temperature_protection\": \"No\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"平均电流模式\",\n      \"output_voltage_accuracy\": \"2%\",\n      \"output_reference_voltage\": \"7.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UC2854A\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Industry\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"功率因数校正(PFC)控制器\",\n    \"category_lv3\": \"升压(Boost)型PFC控制器\",\n    \"part_number_title\": \"ADVANCED HIGH-POWER FACTOR PREREGULATOR\",\n    \"features\": [\n      \"Controls Boost PWM to Near-Unity Power Factor\",\n      \"Limits Line Current Distortion To <3%\",\n      \"World-Wide Operation Without Switches\",\n      \"Accurate Power Limiting\",\n      \"Fixed-Frequency Average Current-Mode Control\",\n      \"High Bandwidth (5 MHz), Low-Offset Current Amplifier\",\n      \"Integrated Current- and Voltage-Amplifier Output Clamps\",\n      \"Multiplier Improvements: Linearity, 500 mV VAC Offset (Eliminates External Resistor), 0 V to 5 V Multout Common-Mode Range\",\n      \"VREF GOOD Comparator\",\n      \"Faster and Improved Accuracy ENABLE Comparator\",\n      \"UVLO Options (16 V/10 V or 10.5 V/10 V)\",\n      \"300-μA Start-Up Supply Current\"\n    ],\n    \"description\": \"The UC1854 provides active-power factor correction for power systems that otherwise would draw nonsinusoidal current from sinusoidal power lines. This device implements all the control functions necessary to build a power supply capable of optimally using available power-line current while minimizing linecurrent distortion. To do this, the UC1854 contains a voltage amplifier, an analog multiplier and divider, a current amplifier, and a fixed-frequency PWM. In addition, the UC1854 contains a power MOSFETcompatible gate driver, 7.5-V reference, line anticipator, load-enable comparator, low-supply detector, and overcurrent comparator. The UC1854 uses average current-mode control to accomplish fixed-frequency current control with stability and low distortion. Unlike peak current-mode, average current control accurately maintains sinusoidal line current without slope compensation and with minimal response to noise transients.\",\n    \"applications\": [\n      \"Offline AC-to-DC Converters\",\n      \"Medical, Industrial, Telecom, and IT Power Supplies\",\n      \"Uninterruptible Power Supplies (UPS)\",\n      \"Appliances and White Goods\",\n      \"Active power factor corrected preregulators\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UC2854A\",\n        \"order_device\": \"UC2854ADW\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"DW\",\n        \"min_operation_temp\": \"-40\",\n        \"max_operation_temp\": \"85\"\n      },\n      {\n        \"part_number\": \"UC2854A\",\n        \"order_device\": \"UC2854ADWTR\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"DW\",\n        \"min_operation_temp\": \"-40\",\n        \"max_operation_temp\": \"85\"\n      },\n      {\n        \"part_number\": \"UC2854A\",\n        \"order_device\": \"UC2854AN\",\n        \"package_type\": \"PDIP\",\n        \"package_drawing_code\": \"N\",\n        \"min_operation_temp\": \"-40\",\n        \"max_operation_temp\": \"85\"\n      },\n      {\n        \"part_number\": \"UC2854A\",\n        \"order_device\": \"UC2854J\",\n        \"package_type\": \"CDIP\",\n        \"package_drawing_code\": \"J\",\n        \"min_operation_temp\": \"-40\",\n        \"max_operation_temp\": \"85\"\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UC2854A\",\n        \"package_type\": \"J/N/DW\",\n        \"pins\": [\n          {\n            \"pin_number\": \"1\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"6\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"11\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"16\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      },\n      {\n        \"product_part_number\": \"UC2854A\",\n        \"package_type\": \"Q/L\",\n        \"pins\": [\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"18\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"20\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"17\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"19\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          }\n        ]\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS329E-MONTH 2003-REVISED JANUARY 2008\",\n    \"family_comparison\": \"未找到\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"20V\",\n      \"min_input_voltage\": \"16V\",\n      \"max_output_voltage\": \"可调\",\n      \"min_output_voltage\": \"可调\",\n      \"max_output_current\": \"未找到\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"12mA\",\n      \"high_side_mosfet_resistance\": \"不适用(控制器)\",\n      \"low_side_mosfet_resistance\": \"不适用(控制器)\",\n      \"over_current_protection_threshold\": \"外部可调\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"未找到\",\n      \"power_good_indicator\": \"Yes\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"No\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"No\",\n      \"output_under_voltage_protection\": \"No\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Fold Back\",\n      \"over_temperature_protection\": \"No\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"平均电流模式\",\n      \"output_voltage_accuracy\": \"2%\",\n      \"output_reference_voltage\": \"7.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UC2854B\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Industry\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"功率因数校正(PFC)控制器\",\n    \"category_lv3\": \"升压(Boost)型PFC控制器\",\n    \"part_number_title\": \"ADVANCED HIGH-POWER FACTOR PREREGULATOR\",\n    \"features\": [\n      \"Controls Boost PWM to Near-Unity Power Factor\",\n      \"Limits Line Current Distortion To <3%\",\n      \"World-Wide Operation Without Switches\",\n      \"Accurate Power Limiting\",\n      \"Fixed-Frequency Average Current-Mode Control\",\n      \"High Bandwidth (5 MHz), Low-Offset Current Amplifier\",\n      \"Integrated Current- and Voltage-Amplifier Output Clamps\",\n      \"Multiplier Improvements: Linearity, 500 mV VAC Offset (Eliminates External Resistor), 0 V to 5 V Multout Common-Mode Range\",\n      \"VREF GOOD Comparator\",\n      \"Faster and Improved Accuracy ENABLE Comparator\",\n      \"UVLO Options (16 V/10 V or 10.5 V/10 V)\",\n      \"300-μA Start-Up Supply Current\"\n    ],\n    \"description\": \"The UC1854 provides active-power factor correction for power systems that otherwise would draw nonsinusoidal current from sinusoidal power lines. This device implements all the control functions necessary to build a power supply capable of optimally using available power-line current while minimizing linecurrent distortion. To do this, the UC1854 contains a voltage amplifier, an analog multiplier and divider, a current amplifier, and a fixed-frequency PWM. In addition, the UC1854 contains a power MOSFETcompatible gate driver, 7.5-V reference, line anticipator, load-enable comparator, low-supply detector, and overcurrent comparator. The UC1854 uses average current-mode control to accomplish fixed-frequency current control with stability and low distortion. Unlike peak current-mode, average current control accurately maintains sinusoidal line current without slope compensation and with minimal response to noise transients.\",\n    \"applications\": [\n      \"Offline AC-to-DC Converters\",\n      \"Medical, Industrial, Telecom, and IT Power Supplies\",\n      \"Uninterruptible Power Supplies (UPS)\",\n      \"Appliances and White Goods\",\n      \"Active power factor corrected preregulators\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UC2854B\",\n        \"order_device\": \"UC2854BDW\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"DW\",\n        \"min_operation_temp\": \"-40\",\n        \"max_operation_temp\": \"85\"\n      },\n      {\n        \"part_number\": \"UC2854B\",\n        \"order_device\": \"UC2854BDWTR\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"DW\",\n        \"min_operation_temp\": \"-40\",\n        \"max_operation_temp\": \"85\"\n      },\n      {\n        \"part_number\": \"UC2854B\",\n        \"order_device\": \"UC2854BN\",\n        \"package_type\": \"PDIP\",\n        \"package_drawing_code\": \"N\",\n        \"min_operation_temp\": \"-40\",\n        \"max_operation_temp\": \"85\"\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product_part_number\": \"UC2854B\",\n        \"package_type\": \"J/N/DW\",\n        \"pins\": [\n          {\n            \"pin_number\": \"1\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"6\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"11\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"16\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          }\n        ]\n      },\n      {\n        \"product_part_number\": \"UC2854B\",\n        \"package_type\": \"Q/L\",\n        \"pins\": [\n          {\n            \"pin_number\": \"4\",\n            \"pin_name\": \"CAO\",\n            \"pin_description\": \"Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty-cycle comparator. The output signal generated by this amplifier commands the PWM to force the correct input current. The output can swing from 0.1 V to 7.5 V.\"\n          },\n          {\n            \"pin_number\": \"18\",\n            \"pin_name\": \"CT\",\n            \"pin_description\": \"Capacitor from CT to GND sets the PWM oscillator frequency.\"\n          },\n          {\n            \"pin_number\": \"13\",\n            \"pin_name\": \"ENA\",\n            \"pin_description\": \"A nominal voltage above 2.65 V on this pin allows the device to begin operating. Once operating, the device shuts off if this pin goes below 2.15 V nominal.\"\n          },\n          {\n            \"pin_number\": \"2\",\n            \"pin_name\": \"GND\",\n            \"pin_description\": \"All bypass and timing capacitors connected to GND should have leads as short and direct as possible. All voltages are measured with respect GND.\"\n          },\n          {\n            \"pin_number\": \"20\",\n            \"pin_name\": \"GTDRV\",\n            \"pin_description\": \"Output of the PWM is a 1.5-A peak totem-pole MOSFET gate driver on GTDRV. Use a series gate resistor of at least 5 Ω to prevent interaction between the gate impedance and the GTDRV output driver that might cause the GTDRV output to overshoot excessively. Some overshoot of the GTDRV output is always expected when driving a capacitive load.\"\n          },\n          {\n            \"pin_number\": \"8\",\n            \"pin_name\": \"IAC\",\n            \"pin_description\": \"Current input to the multiplier, proportional to the instantaneous line voltage. This input to the analog multiplier is a current. The multiplier is tailored for very low distortion from this current input (IAC) to MOUT, so this is the only multiplier input that should be used for sensing instantaneous line voltage.\"\n          },\n          {\n            \"pin_number\": \"5\",\n            \"pin_name\": \"ISENSE\",\n            \"pin_description\": \"Switch current sensing input. This is the inverting input to the current amplifier. This input and the non-inverting input MOUT remain functional down to and below GND. Care should be taken to avoid taking these inputs below -0.5 V, because they are protected with diodes to GND.\"\n          },\n          {\n            \"pin_number\": \"7\",\n            \"pin_name\": \"MOUT\",\n            \"pin_description\": \"Multiplier output and current sense plus. The output of the analog multiplier and the non-inverting input of the current amplifier are connected together at MOUT. The cautions about taking ISENSE below -0.5 V also apply to MOUT. As the multiplier output is a current, this is a high-impedance input similar to ISENSE, so the current amplifier can be configured as a differential amplifier to reject GND noise. IMOUT≤ 2 × IAC\"\n          },\n          {\n            \"pin_number\": \"3\",\n            \"pin_name\": \"PKLMT\",\n            \"pin_description\": \"Peak limit. The threshold for PKLMT is 0.0 V. Connect this input to the negative voltage on the current sense resistor. Use a resistor to REF to offset the negative current sense signal up to GND.\"\n          },\n          {\n            \"pin_number\": \"15\",\n            \"pin_name\": \"RSET\",\n            \"pin_description\": \"Oscillator charging current and multiplier limit set. A resistor from RSET to ground programs oscillator charging current.\"\n          },\n          {\n            \"pin_number\": \"17\",\n            \"pin_name\": \"SS\",\n            \"pin_description\": \"Soft-start. SS remains at GND as long as the device is disabled or Vcc is too low. SS pulls up to over 3 V by an internal 14-µA current source when both Vcc becomes valid and the device is enabled. SS acts as the reference input to the voltage amplifier if SS is below VREF. With a large capacitor from SS to GND, the reference to the voltage regulating amplifier rises slowly, and increase the PWM duty cycle slowly. In the event of a disable command or a supply dropout, SS will quickly discharge to ground and disable the PWM.\"\n          },\n          {\n            \"pin_number\": \"9\",\n            \"pin_name\": \"VAO\",\n            \"pin_description\": \"Voltage amplifier output\"\n          },\n          {\n            \"pin_number\": \"19\",\n            \"pin_name\": \"VCC\",\n            \"pin_description\": \"Positive supply rail\"\n          },\n          {\n            \"pin_number\": \"12\",\n            \"pin_name\": \"VREF\",\n            \"pin_description\": \"Used to set the peak limit point and as an internal reference for various device functions. This voltage must be present for the device to operate.\"\n          },\n          {\n            \"pin_number\": \"10\",\n            \"pin_name\": \"VRMS\",\n            \"pin_description\": \"One of the inputs into the multiplier. This pin provides the input RMS voltage to the multiplier circuitry.\"\n          },\n          {\n            \"pin_number\": \"14\",\n            \"pin_name\": \"VSENSE\",\n            \"pin_description\": \"This pin provides the feedback from the output. This input goes into the voltage error amplifier and the output of the error amplifier is another of the inputs into the multiplier circuit.\"\n          }\n        ]\n      }\n    ],\n    \"datasheet_cn\": \"未找到\",\n    \"datasheet_en\": \"SLUS329E-MONTH 2003-REVISED JANUARY 2008\",\n    \"family_comparison\": \"未找到\",\n    \"attributes\": {\n      \"power_mos_integrated\": \"外置(控制器)\",\n      \"power_device_type\": \"MOSFET\",\n      \"power_phase_number\": 1,\n      \"channel_count\": 1,\n      \"max_input_voltage\": \"20V\",\n      \"min_input_voltage\": \"10.5V\",\n      \"max_output_voltage\": \"可调\",\n      \"min_output_voltage\": \"可调\",\n      \"max_output_current\": \"未找到\",\n      \"max_switch_frequency\": \"120kHz\",\n      \"quiescent_current\": \"12mA\",\n      \"high_side_mosfet_resistance\": \"不适用(控制器)\",\n      \"low_side_mosfet_resistance\": \"不适用(控制器)\",\n      \"over_current_protection_threshold\": \"外部可调\",\n      \"operation_mode\": \"异步\",\n      \"output_voltage_config_method\": \"可调\",\n      \"communication_interface\": \"无\",\n      \"enable_function\": \"Yes\",\n      \"light_load_mode\": \"未找到\",\n      \"power_good_indicator\": \"Yes\",\n      \"soft_start\": \"外部可调\",\n      \"input_over_voltage_protection\": \"No\",\n      \"input_under_voltage_protection\": \"UVLO\",\n      \"output_over_voltage_protection\": \"No\",\n      \"output_under_voltage_protection\": \"No\",\n      \"output_over_load_protection\": \"Current Limit\",\n      \"output_short_circuit_protection\": \"Fold Back\",\n      \"over_temperature_protection\": \"No\",\n      \"output_discharge\": \"No\",\n      \"integrated_ldo\": \"No\",\n      \"dynamic_voltage_setting\": \"No\",\n      \"pass_through_mode\": \"No\",\n      \"load_disconnect\": \"No\",\n      \"loop_control_mode\": \"平均电流模式\",\n      \"output_voltage_accuracy\": \"2%\",\n      \"output_reference_voltage\": \"7.5V\"\n    }\n  },\n  {\n    \"part_number\": \"UC3854\",\n    \"manufacturer\": \"Texas Instruments\",\n    \"country\": \"美国\",\n    \"manufacturing_status\": \"Active\",\n    \"application_grade\": \"Consumer\",\n    \"category_lv1\": \"电源管理芯片\",\n    \"category_lv2\": \"功率因数校正(PFC)控制器\",\n    \"category_lv3\": \"升压(Boost)型PFC控制器\",\n    \"part_number_title\": \"ADVANCED HIGH-POWER FACTOR PREREGULATOR\",\n    \"features\": [\n      \"Controls Boost PWM to Near-Unity Power Factor\",\n      \"Limits Line Current Distortion To <3%\",\n      \"World-Wide Operation Without Switches\",\n      \"Accurate Power Limiting\",\n      \"Fixed-Frequency Average Current-Mode Control\",\n      \"High Bandwidth (5 MHz), Low-Offset Current Amplifier\",\n      \"Integrated Current- and Voltage-Amplifier Output Clamps\",\n      \"Multiplier Improvements: Linearity, 500 mV VAC Offset (Eliminates External Resistor), 0 V to 5 V Multout Common-Mode Range\",\n      \"VREF GOOD Comparator\",\n      \"Faster and Improved Accuracy ENABLE Comparator\",\n      \"UVLO Options (16 V/10 V or 10.5 V/10 V)\",\n      \"300-μA Start-Up Supply Current\"\n    ],\n    \"description\": \"The UC1854 provides active-power factor correction for power systems that otherwise would draw nonsinusoidal current from sinusoidal power lines. This device implements all the control functions necessary to build a power supply capable of optimally using available power-line current while minimizing linecurrent distortion. To do this, the UC1854 contains a voltage amplifier, an analog multiplier and divider, a current amplifier, and a fixed-frequency PWM. In addition, the UC1854 contains a power MOSFETcompatible gate driver, 7.5-V reference, line anticipator, load-enable comparator, low-supply detector, and overcurrent comparator. The UC1854 uses average current-mode control to accomplish fixed-frequency current control with stability and low distortion. Unlike peak current-mode, average current control accurately maintains sinusoidal line current without slope compensation and with minimal response to noise transients.\",\n    \"applications\": [\n      \"Offline AC-to-DC Converters\",\n      \"Medical, Industrial, Telecom, and IT Power Supplies\",\n      \"Uninterruptible Power Supplies (UPS)\",\n      \"Appliances and White Goods\",\n      \"Active power factor corrected preregulators\"\n    ],\n    \"ordering_information\": [\n      {\n        \"part_number\": \"UC3854\",\n        \"order_device\": \"UC3854DW\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"DW\",\n        \"min_operation_temp\": \"0\",\n        \"max_operation_temp\": \"70\"\n      },\n      {\n        \"part_number\": \"UC3854\",\n        \"order_device\": \"UC3854DWG4\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"DW\",\n        \"min_operation_temp\": \"0\",\n        \"max_operation_temp\": \"70\"\n      },\n      {\n        \"part_number\": \"UC3854\",\n        \"order_device\": \"UC3854DWTR\",\n        \"package_type\": \"SOIC\",\n        \"package_drawing_code\": \"DW\",\n        \"min_operation_temp\": \"0\",\n        \"max_operation_temp\": \"70\"\n      },\n      {\n        \"part_number\": \"UC3854\",\n        \"order_device\": \"UC3854N\",\n        \"package_type\": \"PDIP\",\n        \"package_drawing_code\": \"N\",\n        \"min_operation_temp\": \"0\",\n        \"max_operation_temp\": \"70\"\n      },\n      {\n        \"part_number\": \"UC3854\",\n        \"order_device\": \"UC3854NG4\",\n        \"package_type\": \"PDIP\",\n        \"package_drawing_code\": \"N\",\n        \"min_operation_temp\": \"0\",\n        \"max_operation_temp\": \"70\"\n      }\n    ],\n    \"pin_function\": [\n      {\n        \"product", "package": [{"type": "DESCRIPTION", "pin_count": "3854", "pitch": "1.27", "height": "35.0", "width": "13.97", "length": "506.0"}]}