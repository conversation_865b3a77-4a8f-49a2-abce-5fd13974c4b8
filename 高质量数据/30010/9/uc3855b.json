[{"part_number": "UC2855A", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "功率因数校正(PFC)", "part_number_title": "High Performance Power Factor Preregulator", "features": ["Controls Boost PWM to Near Unity Power Factor", "Fixed Frequency Average Current Mode Control Minimizes Line Current Distortion", "Built-in Active Snubber (ZVT) allows Operation to 500kHz, improved EMI and Efficiency", "Inductor Current Synthesizer allows Single Current Transformer Current Sense for Improved Efficiency and Noise Margin", "Accurate Analog Multiplier with Line Compensator allows for Universal Input Voltage Operation", "High Bandwidth (5MHz), Low Offset Current Amplifier", "Overvoltage and Overcurrent protection", "Two UVLO Threshold Options", "150μA Startup Supply Current Typical", "Precision 1% 7.5V Reference"], "description": "The UC3855A/B provides all the control features necessary for high power, high frequency PFC boost converters. The average current mode control method allows for stable, low distortion AC line current programming without the need for slope compensation. In addition, the UC3855 utilizes an active snubbing or ZVT (Zero Voltage Transition technique) to dramatically reduce diode recovery and MOSFET turn-on losses, resulting in lower EMI emissions and higher efficiency. Boost converter switching frequencies up to 500kHz are now realizable, requiring only an additional small MOSFET, diode, and inductor to resonantly soft switch the boost diode and switch. Average current sensing can be employed using a simple resistive shunt or a current sense transformer. Using the current sense transformer method, the internal current synthesizer circuit buffers the inductor current during the switch on-time, and reconstructs the inductor current during the switch off-time. Improved signal to noise ratio and negligible current sensing losses make this an attractive solution for higher power applications.\nThe UC3855A/B also features a single quadrant multiplier, squarer, and divider circuit which provides the programming signal for the current loop. The internal multiplier current limit reduces output power during low line conditions. An overvoltage protection circuit disables both controller outputs in the event of a boost output OV condition.\nLow startup supply current, UVLO with hysteresis, a 1% 7.5V reference, voltage amplifier with softstart, input supply voltage clamp, enable comparator, and overcurrent comparator complete the list of features. Available packages include: 20 pin N, DW, Q, J, and L.", "applications": ["High power, high frequency PFC boost converters"], "ordering_information": [{"part_number": "UC2855A", "order_device": "UC2855ADW", "package_type": "SOIC", "package_drawing_code": "DW0020A", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UC2855A", "order_device": "UC2855ADW.A", "package_type": "SOIC", "package_drawing_code": "DW0020A", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "1 (DIL/SOIC), 14 (PLCC)", "pin_name": "CAO", "pin_description": "Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty cycle comparator. The output can swing from 0.1V to 7.5V."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "2 (DIL/SOIC), 16 (PLCC)", "pin_name": "RVS", "pin_description": "The nominal 3V signal present on the VSENSE pin is buffered and brought out to the RVS pin. A current proportional to the output voltage is generated by connecting a resistor between this pin and GND. This current forms the second input to the current synthesizer."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "3 (DIL/SOIC), 17 (PLCC)", "pin_name": "CI", "pin_description": "The level shifted current sense signal is impressed upon a capacitor connected between this pin and GND. The current synthesizer discharges the capacitor to reconstruct the inductor current."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "4 (DIL/SOIC), 18 (PLCC)", "pin_name": "ION", "pin_description": "Current sensing input. It should be connected to the secondary side output of a current sensing transformer. The ION buffer has a source only output."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "5 (DIL/SOIC), 19 (PLCC)", "pin_name": "CS", "pin_description": "The reconstructed inductor current waveform from the CI pin is level shifted and compared to the multiplier output. Also serves as the input to the peak current limiting comparator (1.5V threshold)."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "6 (DIL/SOIC), 20 (PLCC)", "pin_name": "VRMS", "pin_description": "Feedforward line voltage compensation input to the multiplier. A voltage proportional to the AC input RMS voltage commands the multiplier to alter the current command signal by 1/VRMS^2."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "7 (DIL/SOIC), 1 (PLCC)", "pin_name": "OVP", "pin_description": "Senses the boost output voltage for overvoltage protection (>7.5V) and also serves as a TTL compatible enable input (<1.8V to disable)."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "8 (DIL/SOIC), 2 (PLCC)", "pin_name": "REF", "pin_description": "Output of the precision 7.5V reference. Capable of supplying 25mA."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "9 (DIL/SOIC), 3 (PLCC)", "pin_name": "VCC", "pin_description": "Positive supply rail for the IC. Internally clamped to 20V."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "10 (DIL/SOIC), 4 (PLCC)", "pin_name": "GTOUT", "pin_description": "The output of the PWM, a 1.5A peak totem pole MOSFET gate driver for the main boost switch."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "11 (DIL/SOIC), 5 (PLCC)", "pin_name": "GND", "pin_description": "Ground reference for all voltages."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "12 (DIL/SOIC), 6 (PLCC)", "pin_name": "ZVTOUT", "pin_description": "The output of the ZVT block, a 750mA peak totem pole MOSFET gate driver for the active snubber (ZVT) switch."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "13 (DIL/SOIC), 7 (PLCC)", "pin_name": "ZVS", "pin_description": "Senses when the drain voltage of the main MOSFET switch has reached approximately zero volts to reset the ZVT latch."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "14 (DIL/SOIC), 8 (PLCC)", "pin_name": "CT", "pin_description": "A capacitor from CT to GND sets the PWM oscillator frequency."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "15 (DIL/SOIC), 9 (PLCC)", "pin_name": "VAOUT", "pin_description": "Output of the voltage amplifier. At a given input RMS voltage, the voltage on this pin will vary directly with the output load."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "16 (DIL/SOIC), 10 (PLCC)", "pin_name": "VSENSE", "pin_description": "Inverting input of the voltage amplifier and serves as the output voltage feedback point for the PFC boost converter."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "17 (DIL/SOIC), 13 (PLCC)", "pin_name": "SS", "pin_description": "Soft-start pin. An external capacitor is charged to slowly increase the PWM duty cycle during start-up."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "18 (DIL/SOIC), 15 (PLCC)", "pin_name": "IMO", "pin_description": "Output of the multiplier, and the non-inverting input of the current amplifier. This is a current output."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "19 (DIL/SOIC), 12 (PLCC)", "pin_name": "IAC", "pin_description": "Current input to the multiplier. The current into this pin should correspond to the instantaneous value of the rectified AC input line voltage."}, {"product_part_number": "UC2855A", "package_type": "ALL", "pin_number": "20 (DIL/SOIC), 14 (PLCC)", "pin_name": "CA-", "pin_description": "Inverting input to the current amplifier. Connect compensation components between this pin and CAOUT."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS328B JUNE 1998 – REVISED OCTOBER 2005", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "20V", "min_input_voltage": "15.5V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "500kHz", "quiescent_current": "17mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.5V", "operation_mode": "平均电流模式", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "未找到", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "1%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "20", "pitch": "1.27", "height": "2.65", "width": "2.65", "length": "2.65"}]}, {"part_number": "UC2855B", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "功率因数校正(PFC)", "part_number_title": "High Performance Power Factor Preregulator", "features": ["Controls Boost PWM to Near Unity Power Factor", "Fixed Frequency Average Current Mode Control Minimizes Line Current Distortion", "Built-in Active Snubber (ZVT) allows Operation to 500kHz, improved EMI and Efficiency", "Inductor Current Synthesizer allows Single Current Transformer Current Sense for Improved Efficiency and Noise Margin", "Accurate Analog Multiplier with Line Compensator allows for Universal Input Voltage Operation", "High Bandwidth (5MHz), Low Offset Current Amplifier", "Overvoltage and Overcurrent protection", "Two UVLO Threshold Options", "150μA Startup Supply Current Typical", "Precision 1% 7.5V Reference"], "description": "The UC3855A/B provides all the control features necessary for high power, high frequency PFC boost converters. The average current mode control method allows for stable, low distortion AC line current programming without the need for slope compensation. In addition, the UC3855 utilizes an active snubbing or ZVT (Zero Voltage Transition technique) to dramatically reduce diode recovery and MOSFET turn-on losses, resulting in lower EMI emissions and higher efficiency. Boost converter switching frequencies up to 500kHz are now realizable, requiring only an additional small MOSFET, diode, and inductor to resonantly soft switch the boost diode and switch. Average current sensing can be employed using a simple resistive shunt or a current sense transformer. Using the current sense transformer method, the internal current synthesizer circuit buffers the inductor current during the switch on-time, and reconstructs the inductor current during the switch off-time. Improved signal to noise ratio and negligible current sensing losses make this an attractive solution for higher power applications.\nThe UC3855A/B also features a single quadrant multiplier, squarer, and divider circuit which provides the programming signal for the current loop. The internal multiplier current limit reduces output power during low line conditions. An overvoltage protection circuit disables both controller outputs in the event of a boost output OV condition.\nLow startup supply current, UVLO with hysteresis, a 1% 7.5V reference, voltage amplifier with softstart, input supply voltage clamp, enable comparator, and overcurrent comparator complete the list of features. Available packages include: 20 pin N, DW, Q, J, and L.", "applications": ["High power, high frequency PFC boost converters"], "ordering_information": [{"part_number": "UC2855B", "order_device": "UC2855BDW", "package_type": "SOIC", "package_drawing_code": "DW0020A", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UC2855B", "order_device": "UC2855BDW.A", "package_type": "SOIC", "package_drawing_code": "DW0020A", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UC2855B", "order_device": "UC2855BDWTR", "package_type": "SOIC", "package_drawing_code": "DW0020A", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UC2855B", "order_device": "UC2855BDWTR.A", "package_type": "SOIC", "package_drawing_code": "DW0020A", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UC2855B", "order_device": "UC2855BN", "package_type": "PDIP", "package_drawing_code": "N (R-PDIP-T**)", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UC2855B", "order_device": "UC2855BN.A", "package_type": "PDIP", "package_drawing_code": "N (R-PDIP-T**)", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "1 (DIL/SOIC), 14 (PLCC)", "pin_name": "CAO", "pin_description": "Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty cycle comparator. The output can swing from 0.1V to 7.5V."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "2 (DIL/SOIC), 16 (PLCC)", "pin_name": "RVS", "pin_description": "The nominal 3V signal present on the VSENSE pin is buffered and brought out to the RVS pin. A current proportional to the output voltage is generated by connecting a resistor between this pin and GND. This current forms the second input to the current synthesizer."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "3 (DIL/SOIC), 17 (PLCC)", "pin_name": "CI", "pin_description": "The level shifted current sense signal is impressed upon a capacitor connected between this pin and GND. The current synthesizer discharges the capacitor to reconstruct the inductor current."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "4 (DIL/SOIC), 18 (PLCC)", "pin_name": "ION", "pin_description": "Current sensing input. It should be connected to the secondary side output of a current sensing transformer. The ION buffer has a source only output."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "5 (DIL/SOIC), 19 (PLCC)", "pin_name": "CS", "pin_description": "The reconstructed inductor current waveform from the CI pin is level shifted and compared to the multiplier output. Also serves as the input to the peak current limiting comparator (1.5V threshold)."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "6 (DIL/SOIC), 20 (PLCC)", "pin_name": "VRMS", "pin_description": "Feedforward line voltage compensation input to the multiplier. A voltage proportional to the AC input RMS voltage commands the multiplier to alter the current command signal by 1/VRMS^2."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "7 (DIL/SOIC), 1 (PLCC)", "pin_name": "OVP", "pin_description": "Senses the boost output voltage for overvoltage protection (>7.5V) and also serves as a TTL compatible enable input (<1.8V to disable)."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "8 (DIL/SOIC), 2 (PLCC)", "pin_name": "REF", "pin_description": "Output of the precision 7.5V reference. Capable of supplying 25mA."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "9 (DIL/SOIC), 3 (PLCC)", "pin_name": "VCC", "pin_description": "Positive supply rail for the IC. Internally clamped to 20V."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "10 (DIL/SOIC), 4 (PLCC)", "pin_name": "GTOUT", "pin_description": "The output of the PWM, a 1.5A peak totem pole MOSFET gate driver for the main boost switch."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "11 (DIL/SOIC), 5 (PLCC)", "pin_name": "GND", "pin_description": "Ground reference for all voltages."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "12 (DIL/SOIC), 6 (PLCC)", "pin_name": "ZVTOUT", "pin_description": "The output of the ZVT block, a 750mA peak totem pole MOSFET gate driver for the active snubber (ZVT) switch."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "13 (DIL/SOIC), 7 (PLCC)", "pin_name": "ZVS", "pin_description": "Senses when the drain voltage of the main MOSFET switch has reached approximately zero volts to reset the ZVT latch."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "14 (DIL/SOIC), 8 (PLCC)", "pin_name": "CT", "pin_description": "A capacitor from CT to GND sets the PWM oscillator frequency."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "15 (DIL/SOIC), 9 (PLCC)", "pin_name": "VAOUT", "pin_description": "Output of the voltage amplifier. At a given input RMS voltage, the voltage on this pin will vary directly with the output load."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "16 (DIL/SOIC), 10 (PLCC)", "pin_name": "VSENSE", "pin_description": "Inverting input of the voltage amplifier and serves as the output voltage feedback point for the PFC boost converter."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "17 (DIL/SOIC), 13 (PLCC)", "pin_name": "SS", "pin_description": "Soft-start pin. An external capacitor is charged to slowly increase the PWM duty cycle during start-up."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "18 (DIL/SOIC), 15 (PLCC)", "pin_name": "IMO", "pin_description": "Output of the multiplier, and the non-inverting input of the current amplifier. This is a current output."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "19 (DIL/SOIC), 12 (PLCC)", "pin_name": "IAC", "pin_description": "Current input to the multiplier. The current into this pin should correspond to the instantaneous value of the rectified AC input line voltage."}, {"product_part_number": "UC2855B", "package_type": "ALL", "pin_number": "20 (DIL/SOIC), 14 (PLCC)", "pin_name": "CA-", "pin_description": "Inverting input to the current amplifier. Connect compensation components between this pin and CAOUT."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS328B JUNE 1998 – REVISED OCTOBER 2005", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "20V", "min_input_voltage": "10.5V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "500kHz", "quiescent_current": "17mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.5V", "operation_mode": "平均电流模式", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "未找到", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "1%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "20", "pitch": "1.27", "height": "2.65", "width": "2.65", "length": "2.65"}]}, {"part_number": "UC3855A", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Commercial", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "功率因数校正(PFC)", "part_number_title": "High Performance Power Factor Preregulator", "features": ["Controls Boost PWM to Near Unity Power Factor", "Fixed Frequency Average Current Mode Control Minimizes Line Current Distortion", "Built-in Active Snubber (ZVT) allows Operation to 500kHz, improved EMI and Efficiency", "Inductor Current Synthesizer allows Single Current Transformer Current Sense for Improved Efficiency and Noise Margin", "Accurate Analog Multiplier with Line Compensator allows for Universal Input Voltage Operation", "High Bandwidth (5MHz), Low Offset Current Amplifier", "Overvoltage and Overcurrent protection", "Two UVLO Threshold Options", "150μA Startup Supply Current Typical", "Precision 1% 7.5V Reference"], "description": "The UC3855A/B provides all the control features necessary for high power, high frequency PFC boost converters. The average current mode control method allows for stable, low distortion AC line current programming without the need for slope compensation. In addition, the UC3855 utilizes an active snubbing or ZVT (Zero Voltage Transition technique) to dramatically reduce diode recovery and MOSFET turn-on losses, resulting in lower EMI emissions and higher efficiency. Boost converter switching frequencies up to 500kHz are now realizable, requiring only an additional small MOSFET, diode, and inductor to resonantly soft switch the boost diode and switch. Average current sensing can be employed using a simple resistive shunt or a current sense transformer. Using the current sense transformer method, the internal current synthesizer circuit buffers the inductor current during the switch on-time, and reconstructs the inductor current during the switch off-time. Improved signal to noise ratio and negligible current sensing losses make this an attractive solution for higher power applications.\nThe UC3855A/B also features a single quadrant multiplier, squarer, and divider circuit which provides the programming signal for the current loop. The internal multiplier current limit reduces output power during low line conditions. An overvoltage protection circuit disables both controller outputs in the event of a boost output OV condition.\nLow startup supply current, UVLO with hysteresis, a 1% 7.5V reference, voltage amplifier with softstart, input supply voltage clamp, enable comparator, and overcurrent comparator complete the list of features. Available packages include: 20 pin N, DW, Q, J, and L.", "applications": ["High power, high frequency PFC boost converters"], "ordering_information": [{"part_number": "UC3855A", "order_device": "UC3855ADW", "package_type": "SOIC", "package_drawing_code": "DW0020A", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855A", "order_device": "UC3855ADW.A", "package_type": "SOIC", "package_drawing_code": "DW0020A", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855A", "order_device": "UC3855ADWTR", "package_type": "SOIC", "package_drawing_code": "DW0020A", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855A", "order_device": "UC3855ADWTR.A", "package_type": "SOIC", "package_drawing_code": "DW0020A", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "1 (DIL/SOIC), 14 (PLCC)", "pin_name": "CAO", "pin_description": "Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty cycle comparator. The output can swing from 0.1V to 7.5V."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "2 (DIL/SOIC), 16 (PLCC)", "pin_name": "RVS", "pin_description": "The nominal 3V signal present on the VSENSE pin is buffered and brought out to the RVS pin. A current proportional to the output voltage is generated by connecting a resistor between this pin and GND. This current forms the second input to the current synthesizer."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "3 (DIL/SOIC), 17 (PLCC)", "pin_name": "CI", "pin_description": "The level shifted current sense signal is impressed upon a capacitor connected between this pin and GND. The current synthesizer discharges the capacitor to reconstruct the inductor current."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "4 (DIL/SOIC), 18 (PLCC)", "pin_name": "ION", "pin_description": "Current sensing input. It should be connected to the secondary side output of a current sensing transformer. The ION buffer has a source only output."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "5 (DIL/SOIC), 19 (PLCC)", "pin_name": "CS", "pin_description": "The reconstructed inductor current waveform from the CI pin is level shifted and compared to the multiplier output. Also serves as the input to the peak current limiting comparator (1.5V threshold)."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "6 (DIL/SOIC), 20 (PLCC)", "pin_name": "VRMS", "pin_description": "Feedforward line voltage compensation input to the multiplier. A voltage proportional to the AC input RMS voltage commands the multiplier to alter the current command signal by 1/VRMS^2."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "7 (DIL/SOIC), 1 (PLCC)", "pin_name": "OVP", "pin_description": "Senses the boost output voltage for overvoltage protection (>7.5V) and also serves as a TTL compatible enable input (<1.8V to disable)."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "8 (DIL/SOIC), 2 (PLCC)", "pin_name": "REF", "pin_description": "Output of the precision 7.5V reference. Capable of supplying 25mA."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "9 (DIL/SOIC), 3 (PLCC)", "pin_name": "VCC", "pin_description": "Positive supply rail for the IC. Internally clamped to 20V."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "10 (DIL/SOIC), 4 (PLCC)", "pin_name": "GTOUT", "pin_description": "The output of the PWM, a 1.5A peak totem pole MOSFET gate driver for the main boost switch."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "11 (DIL/SOIC), 5 (PLCC)", "pin_name": "GND", "pin_description": "Ground reference for all voltages."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "12 (DIL/SOIC), 6 (PLCC)", "pin_name": "ZVTOUT", "pin_description": "The output of the ZVT block, a 750mA peak totem pole MOSFET gate driver for the active snubber (ZVT) switch."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "13 (DIL/SOIC), 7 (PLCC)", "pin_name": "ZVS", "pin_description": "Senses when the drain voltage of the main MOSFET switch has reached approximately zero volts to reset the ZVT latch."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "14 (DIL/SOIC), 8 (PLCC)", "pin_name": "CT", "pin_description": "A capacitor from CT to GND sets the PWM oscillator frequency."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "15 (DIL/SOIC), 9 (PLCC)", "pin_name": "VAOUT", "pin_description": "Output of the voltage amplifier. At a given input RMS voltage, the voltage on this pin will vary directly with the output load."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "16 (DIL/SOIC), 10 (PLCC)", "pin_name": "VSENSE", "pin_description": "Inverting input of the voltage amplifier and serves as the output voltage feedback point for the PFC boost converter."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "17 (DIL/SOIC), 13 (PLCC)", "pin_name": "SS", "pin_description": "Soft-start pin. An external capacitor is charged to slowly increase the PWM duty cycle during start-up."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "18 (DIL/SOIC), 15 (PLCC)", "pin_name": "IMO", "pin_description": "Output of the multiplier, and the non-inverting input of the current amplifier. This is a current output."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "19 (DIL/SOIC), 12 (PLCC)", "pin_name": "IAC", "pin_description": "Current input to the multiplier. The current into this pin should correspond to the instantaneous value of the rectified AC input line voltage."}, {"product_part_number": "UC3855A", "package_type": "ALL", "pin_number": "20 (DIL/SOIC), 14 (PLCC)", "pin_name": "CA-", "pin_description": "Inverting input to the current amplifier. Connect compensation components between this pin and CAOUT."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS328B JUNE 1998 – REVISED OCTOBER 2005", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "20V", "min_input_voltage": "15.5V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "500kHz", "quiescent_current": "17mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.5V", "operation_mode": "平均电流模式", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "未找到", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "1%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "20", "pitch": "1.27", "height": "2.65", "width": "2.65", "length": "2.65"}]}, {"part_number": "UC3855B", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Commercial", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "功率因数校正(PFC)", "part_number_title": "High Performance Power Factor Preregulator", "features": ["Controls Boost PWM to Near Unity Power Factor", "Fixed Frequency Average Current Mode Control Minimizes Line Current Distortion", "Built-in Active Snubber (ZVT) allows Operation to 500kHz, improved EMI and Efficiency", "Inductor Current Synthesizer allows Single Current Transformer Current Sense for Improved Efficiency and Noise Margin", "Accurate Analog Multiplier with Line Compensator allows for Universal Input Voltage Operation", "High Bandwidth (5MHz), Low Offset Current Amplifier", "Overvoltage and Overcurrent protection", "Two UVLO Threshold Options", "150μA Startup Supply Current Typical", "Precision 1% 7.5V Reference"], "description": "The UC3855A/B provides all the control features necessary for high power, high frequency PFC boost converters. The average current mode control method allows for stable, low distortion AC line current programming without the need for slope compensation. In addition, the UC3855 utilizes an active snubbing or ZVT (Zero Voltage Transition technique) to dramatically reduce diode recovery and MOSFET turn-on losses, resulting in lower EMI emissions and higher efficiency. Boost converter switching frequencies up to 500kHz are now realizable, requiring only an additional small MOSFET, diode, and inductor to resonantly soft switch the boost diode and switch. Average current sensing can be employed using a simple resistive shunt or a current sense transformer. Using the current sense transformer method, the internal current synthesizer circuit buffers the inductor current during the switch on-time, and reconstructs the inductor current during the switch off-time. Improved signal to noise ratio and negligible current sensing losses make this an attractive solution for higher power applications.\nThe UC3855A/B also features a single quadrant multiplier, squarer, and divider circuit which provides the programming signal for the current loop. The internal multiplier current limit reduces output power during low line conditions. An overvoltage protection circuit disables both controller outputs in the event of a boost output OV condition.\nLow startup supply current, UVLO with hysteresis, a 1% 7.5V reference, voltage amplifier with softstart, input supply voltage clamp, enable comparator, and overcurrent comparator complete the list of features. Available packages include: 20 pin N, DW, Q, J, and L.", "applications": ["High power, high frequency PFC boost converters"], "ordering_information": [{"part_number": "UC3855B", "order_device": "UC3855BDW", "package_type": "SOIC", "package_drawing_code": "DW0020A", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855B", "order_device": "UC3855BDW.A", "package_type": "SOIC", "package_drawing_code": "DW0020A", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855B", "order_device": "UC3855BDWG4", "package_type": "SOIC", "package_drawing_code": "DW0020A", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855B", "order_device": "UC3855BDWTR", "package_type": "SOIC", "package_drawing_code": "DW0020A", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855B", "order_device": "UC3855BDWTR.A", "package_type": "SOIC", "package_drawing_code": "DW0020A", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855B", "order_device": "UC3855BN", "package_type": "PDIP", "package_drawing_code": "N (R-PDIP-T**)", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855B", "order_device": "UC3855BN.A", "package_type": "PDIP", "package_drawing_code": "N (R-PDIP-T**)", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3855B", "order_device": "UC3855BNG4", "package_type": "PDIP", "package_drawing_code": "N (R-PDIP-T**)", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "1 (DIL/SOIC), 14 (PLCC)", "pin_name": "CAO", "pin_description": "Output of the wide bandwidth current amplifier and one of the inputs to the PWM duty cycle comparator. The output can swing from 0.1V to 7.5V."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "2 (DIL/SOIC), 16 (PLCC)", "pin_name": "RVS", "pin_description": "The nominal 3V signal present on the VSENSE pin is buffered and brought out to the RVS pin. A current proportional to the output voltage is generated by connecting a resistor between this pin and GND. This current forms the second input to the current synthesizer."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "3 (DIL/SOIC), 17 (PLCC)", "pin_name": "CI", "pin_description": "The level shifted current sense signal is impressed upon a capacitor connected between this pin and GND. The current synthesizer discharges the capacitor to reconstruct the inductor current."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "4 (DIL/SOIC), 18 (PLCC)", "pin_name": "ION", "pin_description": "Current sensing input. It should be connected to the secondary side output of a current sensing transformer. The ION buffer has a source only output."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "5 (DIL/SOIC), 19 (PLCC)", "pin_name": "CS", "pin_description": "The reconstructed inductor current waveform from the CI pin is level shifted and compared to the multiplier output. Also serves as the input to the peak current limiting comparator (1.5V threshold)."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "6 (DIL/SOIC), 20 (PLCC)", "pin_name": "VRMS", "pin_description": "Feedforward line voltage compensation input to the multiplier. A voltage proportional to the AC input RMS voltage commands the multiplier to alter the current command signal by 1/VRMS^2."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "7 (DIL/SOIC), 1 (PLCC)", "pin_name": "OVP", "pin_description": "Senses the boost output voltage for overvoltage protection (>7.5V) and also serves as a TTL compatible enable input (<1.8V to disable)."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "8 (DIL/SOIC), 2 (PLCC)", "pin_name": "REF", "pin_description": "Output of the precision 7.5V reference. Capable of supplying 25mA."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "9 (DIL/SOIC), 3 (PLCC)", "pin_name": "VCC", "pin_description": "Positive supply rail for the IC. Internally clamped to 20V."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "10 (DIL/SOIC), 4 (PLCC)", "pin_name": "GTOUT", "pin_description": "The output of the PWM, a 1.5A peak totem pole MOSFET gate driver for the main boost switch."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "11 (DIL/SOIC), 5 (PLCC)", "pin_name": "GND", "pin_description": "Ground reference for all voltages."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "12 (DIL/SOIC), 6 (PLCC)", "pin_name": "ZVTOUT", "pin_description": "The output of the ZVT block, a 750mA peak totem pole MOSFET gate driver for the active snubber (ZVT) switch."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "13 (DIL/SOIC), 7 (PLCC)", "pin_name": "ZVS", "pin_description": "Senses when the drain voltage of the main MOSFET switch has reached approximately zero volts to reset the ZVT latch."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "14 (DIL/SOIC), 8 (PLCC)", "pin_name": "CT", "pin_description": "A capacitor from CT to GND sets the PWM oscillator frequency."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "15 (DIL/SOIC), 9 (PLCC)", "pin_name": "VAOUT", "pin_description": "Output of the voltage amplifier. At a given input RMS voltage, the voltage on this pin will vary directly with the output load."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "16 (DIL/SOIC), 10 (PLCC)", "pin_name": "VSENSE", "pin_description": "Inverting input of the voltage amplifier and serves as the output voltage feedback point for the PFC boost converter."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "17 (DIL/SOIC), 13 (PLCC)", "pin_name": "SS", "pin_description": "Soft-start pin. An external capacitor is charged to slowly increase the PWM duty cycle during start-up."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "18 (DIL/SOIC), 15 (PLCC)", "pin_name": "IMO", "pin_description": "Output of the multiplier, and the non-inverting input of the current amplifier. This is a current output."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "19 (DIL/SOIC), 12 (PLCC)", "pin_name": "IAC", "pin_description": "Current input to the multiplier. The current into this pin should correspond to the instantaneous value of the rectified AC input line voltage."}, {"product_part_number": "UC3855B", "package_type": "ALL", "pin_number": "20 (DIL/SOIC), 14 (PLCC)", "pin_name": "CA-", "pin_description": "Inverting input to the current amplifier. Connect compensation components between this pin and CAOUT."}], "datasheet_cn": "未找到", "datasheet_en": "SLUS328B JUNE 1998 – REVISED OCTOBER 2005", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "20V", "min_input_voltage": "10.5V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "500kHz", "quiescent_current": "17mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.5V", "operation_mode": "平均电流模式", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "未找到", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "1%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "20", "pitch": "1.27", "height": "2.65", "width": "2.65", "length": "2.65"}]}]