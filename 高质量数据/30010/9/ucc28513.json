[{"part_number": "UCC28510", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Obsolete", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "PFC/PWM组合控制器", "part_number_title": "UCC<PERSON>510, <PERSON><PERSON><PERSON>511, <PERSON><PERSON><PERSON>512, <PERSON><PERSON><PERSON>513, <PERSON><PERSON>28514, <PERSON>C28515, <PERSON><PERSON>28516, <PERSON>C28517 Advanced PFC/PWM Combination Controllers", "features": ["Provides Control of PFC and PWM Power Stages In One Device", "Leading-Edge PFC, Trailing-Edge PWM Modulation for Reduced Ripple", "Built-In Sequencing of PFC and PWM Turn-On", "2-A Source and 3-A Sink Gate Drive for Both PFC and PWM Stages", "Typical 16-ns Rise Time and 7-ns Fall Time into 1-nF Loads", "PFC Features: Average-Current-Mode Control for Continuous Conduction Mode Operation, Highly-Linear Multiplier for Near-Unity Power Factor, Input Voltage Feedforward Implementation, Improved Load Transient Response, Accurate Power Limiting, Zero Power Detect", "PWM Features: Peak-Current-Mode Control Operation, 1:1 or 1:2 PFC:PWM Frequency Options, Programmable maximum duty cycle, Programmable Soft-Start, Two Hysteresis Options for Differing Hold-Up Time Requirements"], "description": "The UCC28510 series of combination PFC/PWM controllers provide complete control functionality for any off-line power system requiring compliance with the IEC1000-3-2 harmonic reduction requirements. By combining the control and drive signals for the PFC and the PWM stages into a single device, significant performance and cost benefits are gained. By managing the modulation mechanisms of the two stages (leading-edge modulation for PFC and trailing-edge modulation for PWM), the ripple current in the boost capacitor is minimized. Based on the average current mode control architecture with input voltage feedforward of prior PFC/PWM combination controllers, these devices offer performance advantages. Two new key PWM features are programmable maximum duty cycle and the 2x PWM frequency options to the base PFC frequency. For the PFC stage, the devices feature an improved multiplier and the use of a transconductance amplifier for enhanced transient response. The core of the PFC section is in a three-input multiplier that generates the reference signal for the line current. The UCC28510 series features a highly linearized multiplier circuit capable of producing a low distortion reference for the line current over the full range of line and load conditions. A low-offset, high-bandwidth current error amplifier ensures that the actual inductor current (sensed through a resistor in the return path) follows the multiplier output command signal. The output voltage error is processed through a transconductance voltage amplifier.", "applications": ["Off-line power systems requiring IEC1000-3-2 harmonic reduction compliance"], "ordering_information": [{"part_number": "UCC28510", "order_device": "UCC28510DW", "package_type": "SOIC", "package_drawing_code": "DW"}, {"part_number": "UCC28510", "order_device": "UCC28510N", "package_type": "PDIP", "package_drawing_code": "N"}], "pin_function": [{"product_part_number": "UCC28510", "package_type": "ALL", "pins": [{"pin_number": "1", "pin_name": "VAOUT", "pin_description": "Output of the PFC transconductance voltage amplifier and it is internally connected to the Zero Power Detect comparator input and the multiplier input"}, {"pin_number": "2", "pin_name": "RT", "pin_description": "Oscillator programming pin that is set with a single resistor to GND"}, {"pin_number": "3", "pin_name": "VSENSE", "pin_description": "Inverting input to the PFC transconductance voltage amplifier, and input to the OVP, ENABLE and UVLO2 comparators"}, {"pin_number": "4", "pin_name": "D_MAX", "pin_description": "Positive input to set the maximum duty cycle clamp level of the PWM stage"}, {"pin_number": "5", "pin_name": "CT_BUFF", "pin_description": "Internally buffered PWM stage oscillator ramp output, typically used to program slope compensation with a single resistor"}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Analog ground"}, {"pin_number": "7", "pin_name": "VERR", "pin_description": "Feedback error voltage input for the PWM stage, typically connected to an optocoupler output"}, {"pin_number": "8", "pin_name": "ISENSE2", "pin_description": "Input for PWM stage current sense and peak current limit"}, {"pin_number": "9", "pin_name": "VCC", "pin_description": "Positive supply voltage pin"}, {"pin_number": "10", "pin_name": "GT2", "pin_description": "PWM stage gate drive output"}, {"pin_number": "11", "pin_name": "PWRGND", "pin_description": "Power ground for GT1, GT2 and high current return paths"}, {"pin_number": "12", "pin_name": "GT1", "pin_description": "PFC stage gate drive output"}, {"pin_number": "13", "pin_name": "SS2", "pin_description": "Soft start for the PWM stage"}, {"pin_number": "14", "pin_name": "PKLMT", "pin_description": "Voltage input to the PFC peak current limit comparator"}, {"pin_number": "15", "pin_name": "CAOUT", "pin_description": "Output of the current control amplifier of the PFC stage. CAOUT is internally connected to the PWM comparator input in the PFC stage"}, {"pin_number": "16", "pin_name": "ISENSE1", "pin_description": "Non-inverting input to the PFC stage current amplifier"}, {"pin_number": "17", "pin_name": "MOUT", "pin_description": "PFC multiplier high-impedance current output, internally connected to the current amplifier inverting input"}, {"pin_number": "18", "pin_name": "IAC", "pin_description": "Multiplier current input that is proportional to the instantaneous rectified line voltage"}, {"pin_number": "19", "pin_name": "VFF", "pin_description": "Voltage feedforward pin for the PFC stage, sources an IAC/2 current that should be externally filtered"}, {"pin_number": "20", "pin_name": "VREF", "pin_description": "Precision 7.5-V reference output"}]}], "datasheet_cn": "未找到", "datasheet_en": "slus517c.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "20V", "min_input_voltage": "16V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "600kHz", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "PWM: 1.3V, PFC: 0V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "Zero Power Detect", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "UVLO", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "PFC: Average Current Mode, PWM: Peak Current Mode", "output_voltage_accuracy": "±2%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "2", "pitch": "1.27", "height": "2.65", "width": "7.6", "length": "26.92"}]}, {"part_number": "UCC28511", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "PFC/PWM组合控制器", "part_number_title": "UCC<PERSON>510, <PERSON><PERSON><PERSON>511, <PERSON><PERSON><PERSON>512, <PERSON><PERSON><PERSON>513, <PERSON><PERSON>28514, <PERSON>C28515, <PERSON><PERSON>28516, <PERSON>C28517 Advanced PFC/PWM Combination Controllers", "features": ["Provides Control of PFC and PWM Power Stages In One Device", "Leading-Edge PFC, Trailing-Edge PWM Modulation for Reduced Ripple", "Built-In Sequencing of PFC and PWM Turn-On", "2-A Source and 3-A Sink Gate Drive for Both PFC and PWM Stages", "Typical 16-ns Rise Time and 7-ns Fall Time into 1-nF Loads", "PFC Features: Average-Current-Mode Control for Continuous Conduction Mode Operation, Highly-Linear Multiplier for Near-Unity Power Factor, Input Voltage Feedforward Implementation, Improved Load Transient Response, Accurate Power Limiting, Zero Power Detect", "PWM Features: Peak-Current-Mode Control Operation, 1:1 or 1:2 PFC:PWM Frequency Options, Programmable maximum duty cycle, Programmable Soft-Start, Two Hysteresis Options for Differing Hold-Up Time Requirements"], "description": "The UCC28510 series of combination PFC/PWM controllers provide complete control functionality for any off-line power system requiring compliance with the IEC1000-3-2 harmonic reduction requirements. By combining the control and drive signals for the PFC and the PWM stages into a single device, significant performance and cost benefits are gained. By managing the modulation mechanisms of the two stages (leading-edge modulation for PFC and trailing-edge modulation for PWM), the ripple current in the boost capacitor is minimized. Based on the average current mode control architecture with input voltage feedforward of prior PFC/PWM combination controllers, these devices offer performance advantages. Two new key PWM features are programmable maximum duty cycle and the 2x PWM frequency options to the base PFC frequency. For the PFC stage, the devices feature an improved multiplier and the use of a transconductance amplifier for enhanced transient response. The core of the PFC section is in a three-input multiplier that generates the reference signal for the line current. The UCC28510 series features a highly linearized multiplier circuit capable of producing a low distortion reference for the line current over the full range of line and load conditions. A low-offset, high-bandwidth current error amplifier ensures that the actual inductor current (sensed through a resistor in the return path) follows the multiplier output command signal. The output voltage error is processed through a transconductance voltage amplifier.", "applications": ["Off-line power systems requiring IEC1000-3-2 harmonic reduction requirements"], "ordering_information": [{"part_number": "UCC28511", "order_device": "UCC28511DW", "package_type": "SOIC", "package_drawing_code": "DW"}, {"part_number": "UCC28511", "order_device": "UCC28511N", "package_type": "PDIP", "package_drawing_code": "N"}], "pin_function": [{"product_part_number": "UCC28511", "package_type": "ALL", "pins": [{"pin_number": "1", "pin_name": "VAOUT", "pin_description": "Output of the PFC transconductance voltage amplifier and it is internally connected to the Zero Power Detect comparator input and the multiplier input"}, {"pin_number": "2", "pin_name": "RT", "pin_description": "Oscillator programming pin that is set with a single resistor to GND"}, {"pin_number": "3", "pin_name": "VSENSE", "pin_description": "Inverting input to the PFC transconductance voltage amplifier, and input to the OVP, ENABLE and UVLO2 comparators"}, {"pin_number": "4", "pin_name": "D_MAX", "pin_description": "Positive input to set the maximum duty cycle clamp level of the PWM stage"}, {"pin_number": "5", "pin_name": "CT_BUFF", "pin_description": "Internally buffered PWM stage oscillator ramp output, typically used to program slope compensation with a single resistor"}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Analog ground"}, {"pin_number": "7", "pin_name": "VERR", "pin_description": "Feedback error voltage input for the PWM stage, typically connected to an optocoupler output"}, {"pin_number": "8", "pin_name": "ISENSE2", "pin_description": "Input for PWM stage current sense and peak current limit"}, {"pin_number": "9", "pin_name": "VCC", "pin_description": "Positive supply voltage pin"}, {"pin_number": "10", "pin_name": "GT2", "pin_description": "PWM stage gate drive output"}, {"pin_number": "11", "pin_name": "PWRGND", "pin_description": "Power ground for GT1, GT2 and high current return paths"}, {"pin_number": "12", "pin_name": "GT1", "pin_description": "PFC stage gate drive output"}, {"pin_number": "13", "pin_name": "SS2", "pin_description": "Soft start for the PWM stage"}, {"pin_number": "14", "pin_name": "PKLMT", "pin_description": "Voltage input to the PFC peak current limit comparator"}, {"pin_number": "15", "pin_name": "CAOUT", "pin_description": "Output of the current control amplifier of the PFC stage. CAOUT is internally connected to the PWM comparator input in the PFC stage"}, {"pin_number": "16", "pin_name": "ISENSE1", "pin_description": "Non-inverting input to the PFC stage current amplifier"}, {"pin_number": "17", "pin_name": "MOUT", "pin_description": "PFC multiplier high-impedance current output, internally connected to the current amplifier inverting input"}, {"pin_number": "18", "pin_name": "IAC", "pin_description": "Multiplier current input that is proportional to the instantaneous rectified line voltage"}, {"pin_number": "19", "pin_name": "VFF", "pin_description": "Voltage feedforward pin for the PFC stage, sources an IAC/2 current that should be externally filtered"}, {"pin_number": "20", "pin_name": "VREF", "pin_description": "Precision 7.5-V reference output"}]}], "datasheet_cn": "未找到", "datasheet_en": "slus517c.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "20V", "min_input_voltage": "10.2V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "600kHz", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "PWM: 1.3V, PFC: 0V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "Zero Power Detect", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "UVLO", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "PFC: Average Current Mode, PWM: Peak Current Mode", "output_voltage_accuracy": "±2%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "2", "pitch": "1.27", "height": "2.65", "width": "7.6", "length": "26.92"}]}, {"part_number": "UCC28512", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "PFC/PWM组合控制器", "part_number_title": "UCC<PERSON>510, <PERSON><PERSON><PERSON>511, <PERSON><PERSON><PERSON>512, <PERSON><PERSON><PERSON>513, <PERSON><PERSON>28514, <PERSON>C28515, <PERSON><PERSON>28516, <PERSON>C28517 Advanced PFC/PWM Combination Controllers", "features": ["Provides Control of PFC and PWM Power Stages In One Device", "Leading-Edge PFC, Trailing-Edge PWM Modulation for Reduced Ripple", "Built-In Sequencing of PFC and PWM Turn-On", "2-A Source and 3-A Sink Gate Drive for Both PFC and PWM Stages", "Typical 16-ns Rise Time and 7-ns Fall Time into 1-nF Loads", "PFC Features: Average-Current-Mode Control for Continuous Conduction Mode Operation, Highly-Linear Multiplier for Near-Unity Power Factor, Input Voltage Feedforward Implementation, Improved Load Transient Response, Accurate Power Limiting, Zero Power Detect", "PWM Features: Peak-Current-Mode Control Operation, 1:1 or 1:2 PFC:PWM Frequency Options, Programmable maximum duty cycle, Programmable Soft-Start, Two Hysteresis Options for Differing Hold-Up Time Requirements"], "description": "The UCC28510 series of combination PFC/PWM controllers provide complete control functionality for any off-line power system requiring compliance with the IEC1000-3-2 harmonic reduction requirements. By combining the control and drive signals for the PFC and the PWM stages into a single device, significant performance and cost benefits are gained. By managing the modulation mechanisms of the two stages (leading-edge modulation for PFC and trailing-edge modulation for PWM), the ripple current in the boost capacitor is minimized. Based on the average current mode control architecture with input voltage feedforward of prior PFC/PWM combination controllers, these devices offer performance advantages. Two new key PWM features are programmable maximum duty cycle and the 2x PWM frequency options to the base PFC frequency. For the PFC stage, the devices feature an improved multiplier and the use of a transconductance amplifier for enhanced transient response. The core of the PFC section is in a three-input multiplier that generates the reference signal for the line current. The UCC28510 series features a highly linearized multiplier circuit capable of producing a low distortion reference for the line current over the full range of line and load conditions. A low-offset, high-bandwidth current error amplifier ensures that the actual inductor current (sensed through a resistor in the return path) follows the multiplier output command signal. The output voltage error is processed through a transconductance voltage amplifier.", "applications": ["Off-line power systems requiring IEC1000-3-2 harmonic reduction requirements"], "ordering_information": [{"part_number": "UCC28512", "order_device": "UCC28512DW", "package_type": "SOIC", "package_drawing_code": "DW"}, {"part_number": "UCC28512", "order_device": "UCC28512DWR", "package_type": "SOIC", "package_drawing_code": "DW"}, {"part_number": "UCC28512", "order_device": "UCC28512N", "package_type": "PDIP", "package_drawing_code": "N"}], "pin_function": [{"product_part_number": "UCC28512", "package_type": "ALL", "pins": [{"pin_number": "1", "pin_name": "VAOUT", "pin_description": "Output of the PFC transconductance voltage amplifier and it is internally connected to the Zero Power Detect comparator input and the multiplier input"}, {"pin_number": "2", "pin_name": "RT", "pin_description": "Oscillator programming pin that is set with a single resistor to GND"}, {"pin_number": "3", "pin_name": "VSENSE", "pin_description": "Inverting input to the PFC transconductance voltage amplifier, and input to the OVP, ENABLE and UVLO2 comparators"}, {"pin_number": "4", "pin_name": "D_MAX", "pin_description": "Positive input to set the maximum duty cycle clamp level of the PWM stage"}, {"pin_number": "5", "pin_name": "CT_BUFF", "pin_description": "Internally buffered PWM stage oscillator ramp output, typically used to program slope compensation with a single resistor"}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Analog ground"}, {"pin_number": "7", "pin_name": "VERR", "pin_description": "Feedback error voltage input for the PWM stage, typically connected to an optocoupler output"}, {"pin_number": "8", "pin_name": "ISENSE2", "pin_description": "Input for PWM stage current sense and peak current limit"}, {"pin_number": "9", "pin_name": "VCC", "pin_description": "Positive supply voltage pin"}, {"pin_number": "10", "pin_name": "GT2", "pin_description": "PWM stage gate drive output"}, {"pin_number": "11", "pin_name": "PWRGND", "pin_description": "Power ground for GT1, GT2 and high current return paths"}, {"pin_number": "12", "pin_name": "GT1", "pin_description": "PFC stage gate drive output"}, {"pin_number": "13", "pin_name": "SS2", "pin_description": "Soft start for the PWM stage"}, {"pin_number": "14", "pin_name": "PKLMT", "pin_description": "Voltage input to the PFC peak current limit comparator"}, {"pin_number": "15", "pin_name": "CAOUT", "pin_description": "Output of the current control amplifier of the PFC stage. CAOUT is internally connected to the PWM comparator input in the PFC stage"}, {"pin_number": "16", "pin_name": "ISENSE1", "pin_description": "Non-inverting input to the PFC stage current amplifier"}, {"pin_number": "17", "pin_name": "MOUT", "pin_description": "PFC multiplier high-impedance current output, internally connected to the current amplifier inverting input"}, {"pin_number": "18", "pin_name": "IAC", "pin_description": "Multiplier current input that is proportional to the instantaneous rectified line voltage"}, {"pin_number": "19", "pin_name": "VFF", "pin_description": "Voltage feedforward pin for the PFC stage, sources an IAC/2 current that should be externally filtered"}, {"pin_number": "20", "pin_name": "VREF", "pin_description": "Precision 7.5-V reference output"}]}], "datasheet_cn": "未找到", "datasheet_en": "slus517c.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "20V", "min_input_voltage": "16V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "600kHz", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "PWM: 1.3V, PFC: 0V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "Zero Power Detect", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "UVLO", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "PFC: Average Current Mode, PWM: Peak Current Mode", "output_voltage_accuracy": "±2%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "2", "pitch": "1.27", "height": "2.65", "width": "7.6", "length": "26.92"}]}, {"part_number": "UCC28513", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "PFC/PWM组合控制器", "part_number_title": "UCC<PERSON>510, <PERSON><PERSON><PERSON>511, <PERSON><PERSON><PERSON>512, <PERSON><PERSON><PERSON>513, <PERSON><PERSON>28514, <PERSON>C28515, <PERSON><PERSON>28516, <PERSON>C28517 Advanced PFC/PWM Combination Controllers", "features": ["Provides Control of PFC and PWM Power Stages In One Device", "Leading-Edge PFC, Trailing-Edge PWM Modulation for Reduced Ripple", "Built-In Sequencing of PFC and PWM Turn-On", "2-A Source and 3-A Sink Gate Drive for Both PFC and PWM Stages", "Typical 16-ns Rise Time and 7-ns Fall Time into 1-nF Loads", "PFC Features: Average-Current-Mode Control for Continuous Conduction Mode Operation, Highly-Linear Multiplier for Near-Unity Power Factor, Input Voltage Feedforward Implementation, Improved Load Transient Response, Accurate Power Limiting, Zero Power Detect", "PWM Features: Peak-Current-Mode Control Operation, 1:1 or 1:2 PFC:PWM Frequency Options, Programmable maximum duty cycle, Programmable Soft-Start, Two Hysteresis Options for Differing Hold-Up Time Requirements"], "description": "The UCC28510 series of combination PFC/PWM controllers provide complete control functionality for any off-line power system requiring compliance with the IEC1000-3-2 harmonic reduction requirements. By combining the control and drive signals for the PFC and the PWM stages into a single device, significant performance and cost benefits are gained. By managing the modulation mechanisms of the two stages (leading-edge modulation for PFC and trailing-edge modulation for PWM), the ripple current in the boost capacitor is minimized. Based on the average current mode control architecture with input voltage feedforward of prior PFC/PWM combination controllers, these devices offer performance advantages. Two new key PWM features are programmable maximum duty cycle and the 2x PWM frequency options to the base PFC frequency. For the PFC stage, the devices feature an improved multiplier and the use of a transconductance amplifier for enhanced transient response. The core of the PFC section is in a three-input multiplier that generates the reference signal for the line current. The UCC28510 series features a highly linearized multiplier circuit capable of producing a low distortion reference for the line current over the full range of line and load conditions. A low-offset, high-bandwidth current error amplifier ensures that the actual inductor current (sensed through a resistor in the return path) follows the multiplier output command signal. The output voltage error is processed through a transconductance voltage amplifier.", "applications": ["Off-line power systems requiring IEC1000-3-2 harmonic reduction requirements"], "ordering_information": [{"part_number": "UCC28513", "order_device": "UCC28513DW", "package_type": "SOIC", "package_drawing_code": "DW"}, {"part_number": "UCC28513", "order_device": "UCC28513DWR", "package_type": "SOIC", "package_drawing_code": "DW"}, {"part_number": "UCC28513", "order_device": "UCC28513N", "package_type": "PDIP", "package_drawing_code": "N"}], "pin_function": [{"product_part_number": "UCC28513", "package_type": "ALL", "pins": [{"pin_number": "1", "pin_name": "VAOUT", "pin_description": "Output of the PFC transconductance voltage amplifier and it is internally connected to the Zero Power Detect comparator input and the multiplier input"}, {"pin_number": "2", "pin_name": "RT", "pin_description": "Oscillator programming pin that is set with a single resistor to GND"}, {"pin_number": "3", "pin_name": "VSENSE", "pin_description": "Inverting input to the PFC transconductance voltage amplifier, and input to the OVP, ENABLE and UVLO2 comparators"}, {"pin_number": "4", "pin_name": "D_MAX", "pin_description": "Positive input to set the maximum duty cycle clamp level of the PWM stage"}, {"pin_number": "5", "pin_name": "CT_BUFF", "pin_description": "Internally buffered PWM stage oscillator ramp output, typically used to program slope compensation with a single resistor"}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Analog ground"}, {"pin_number": "7", "pin_name": "VERR", "pin_description": "Feedback error voltage input for the PWM stage, typically connected to an optocoupler output"}, {"pin_number": "8", "pin_name": "ISENSE2", "pin_description": "Input for PWM stage current sense and peak current limit"}, {"pin_number": "9", "pin_name": "VCC", "pin_description": "Positive supply voltage pin"}, {"pin_number": "10", "pin_name": "GT2", "pin_description": "PWM stage gate drive output"}, {"pin_number": "11", "pin_name": "PWRGND", "pin_description": "Power ground for GT1, GT2 and high current return paths"}, {"pin_number": "12", "pin_name": "GT1", "pin_description": "PFC stage gate drive output"}, {"pin_number": "13", "pin_name": "SS2", "pin_description": "Soft start for the PWM stage"}, {"pin_number": "14", "pin_name": "PKLMT", "pin_description": "Voltage input to the PFC peak current limit comparator"}, {"pin_number": "15", "pin_name": "CAOUT", "pin_description": "Output of the current control amplifier of the PFC stage. CAOUT is internally connected to the PWM comparator input in the PFC stage"}, {"pin_number": "16", "pin_name": "ISENSE1", "pin_description": "Non-inverting input to the PFC stage current amplifier"}, {"pin_number": "17", "pin_name": "MOUT", "pin_description": "PFC multiplier high-impedance current output, internally connected to the current amplifier inverting input"}, {"pin_number": "18", "pin_name": "IAC", "pin_description": "Multiplier current input that is proportional to the instantaneous rectified line voltage"}, {"pin_number": "19", "pin_name": "VFF", "pin_description": "Voltage feedforward pin for the PFC stage, sources an IAC/2 current that should be externally filtered"}, {"pin_number": "20", "pin_name": "VREF", "pin_description": "Precision 7.5-V reference output"}]}], "datasheet_cn": "未找到", "datasheet_en": "slus517c.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "20V", "min_input_voltage": "10.2V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "600kHz", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "PWM: 1.3V, PFC: 0V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "Zero Power Detect", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "UVLO", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "PFC: Average Current Mode, PWM: Peak Current Mode", "output_voltage_accuracy": "±2%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "2", "pitch": "1.27", "height": "2.65", "width": "7.6", "length": "26.92"}]}, {"part_number": "UCC28514", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Obsolete", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "PFC/PWM组合控制器", "part_number_title": "UCC<PERSON>510, <PERSON><PERSON><PERSON>511, <PERSON><PERSON><PERSON>512, <PERSON><PERSON><PERSON>513, <PERSON><PERSON>28514, <PERSON>C28515, <PERSON><PERSON>28516, <PERSON>C28517 Advanced PFC/PWM Combination Controllers", "features": ["Provides Control of PFC and PWM Power Stages In One Device", "Leading-Edge PFC, Trailing-Edge PWM Modulation for Reduced Ripple", "Built-In Sequencing of PFC and PWM Turn-On", "2-A Source and 3-A Sink Gate Drive for Both PFC and PWM Stages", "Typical 16-ns Rise Time and 7-ns Fall Time into 1-nF Loads", "PFC Features: Average-Current-Mode Control for Continuous Conduction Mode Operation, Highly-Linear Multiplier for Near-Unity Power Factor, Input Voltage Feedforward Implementation, Improved Load Transient Response, Accurate Power Limiting, Zero Power Detect", "PWM Features: Peak-Current-Mode Control Operation, 1:1 or 1:2 PFC:PWM Frequency Options, Programmable maximum duty cycle, Programmable Soft-Start, Two Hysteresis Options for Differing Hold-Up Time Requirements"], "description": "The UCC28510 series of combination PFC/PWM controllers provide complete control functionality for any off-line power system requiring compliance with the IEC1000-3-2 harmonic reduction requirements. By combining the control and drive signals for the PFC and the PWM stages into a single device, significant performance and cost benefits are gained. By managing the modulation mechanisms of the two stages (leading-edge modulation for PFC and trailing-edge modulation for PWM), the ripple current in the boost capacitor is minimized. Based on the average current mode control architecture with input voltage feedforward of prior PFC/PWM combination controllers, these devices offer performance advantages. Two new key PWM features are programmable maximum duty cycle and the 2x PWM frequency options to the base PFC frequency. For the PFC stage, the devices feature an improved multiplier and the use of a transconductance amplifier for enhanced transient response. The core of the PFC section is in a three-input multiplier that generates the reference signal for the line current. The UCC28510 series features a highly linearized multiplier circuit capable of producing a low distortion reference for the line current over the full range of line and load conditions. A low-offset, high-bandwidth current error amplifier ensures that the actual inductor current (sensed through a resistor in the return path) follows the multiplier output command signal. The output voltage error is processed through a transconductance voltage amplifier.", "applications": ["Off-line power systems requiring IEC1000-3-2 harmonic reduction requirements"], "ordering_information": [{"part_number": "UCC28514", "order_device": "UCC28514DW", "package_type": "SOIC", "package_drawing_code": "DW"}, {"part_number": "UCC28514", "order_device": "UCC28514N", "package_type": "PDIP", "package_drawing_code": "N"}], "pin_function": [{"product_part_number": "UCC28514", "package_type": "ALL", "pins": [{"pin_number": "1", "pin_name": "VAOUT", "pin_description": "Output of the PFC transconductance voltage amplifier and it is internally connected to the Zero Power Detect comparator input and the multiplier input"}, {"pin_number": "2", "pin_name": "RT", "pin_description": "Oscillator programming pin that is set with a single resistor to GND"}, {"pin_number": "3", "pin_name": "VSENSE", "pin_description": "Inverting input to the PFC transconductance voltage amplifier, and input to the OVP, ENABLE and UVLO2 comparators"}, {"pin_number": "4", "pin_name": "D_MAX", "pin_description": "Positive input to set the maximum duty cycle clamp level of the PWM stage"}, {"pin_number": "5", "pin_name": "CT_BUFF", "pin_description": "Internally buffered PWM stage oscillator ramp output, typically used to program slope compensation with a single resistor"}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Analog ground"}, {"pin_number": "7", "pin_name": "VERR", "pin_description": "Feedback error voltage input for the PWM stage, typically connected to an optocoupler output"}, {"pin_number": "8", "pin_name": "ISENSE2", "pin_description": "Input for PWM stage current sense and peak current limit"}, {"pin_number": "9", "pin_name": "VCC", "pin_description": "Positive supply voltage pin"}, {"pin_number": "10", "pin_name": "GT2", "pin_description": "PWM stage gate drive output"}, {"pin_number": "11", "pin_name": "PWRGND", "pin_description": "Power ground for GT1, GT2 and high current return paths"}, {"pin_number": "12", "pin_name": "GT1", "pin_description": "PFC stage gate drive output"}, {"pin_number": "13", "pin_name": "SS2", "pin_description": "Soft start for the PWM stage"}, {"pin_number": "14", "pin_name": "PKLMT", "pin_description": "Voltage input to the PFC peak current limit comparator"}, {"pin_number": "15", "pin_name": "CAOUT", "pin_description": "Output of the current control amplifier of the PFC stage. CAOUT is internally connected to the PWM comparator input in the PFC stage"}, {"pin_number": "16", "pin_name": "ISENSE1", "pin_description": "Non-inverting input to the PFC stage current amplifier"}, {"pin_number": "17", "pin_name": "MOUT", "pin_description": "PFC multiplier high-impedance current output, internally connected to the current amplifier inverting input"}, {"pin_number": "18", "pin_name": "IAC", "pin_description": "Multiplier current input that is proportional to the instantaneous rectified line voltage"}, {"pin_number": "19", "pin_name": "VFF", "pin_description": "Voltage feedforward pin for the PFC stage, sources an IAC/2 current that should be externally filtered"}, {"pin_number": "20", "pin_name": "VREF", "pin_description": "Precision 7.5-V reference output"}]}], "datasheet_cn": "未找到", "datasheet_en": "slus517c.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "20V", "min_input_voltage": "16V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "600kHz", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "PWM: 1.3V, PFC: 0V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "Zero Power Detect", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "UVLO", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "PFC: Average Current Mode, PWM: Peak Current Mode", "output_voltage_accuracy": "±2%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "2", "pitch": "1.27", "height": "2.65", "width": "7.6", "length": "26.92"}]}, {"part_number": "UCC28515", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Obsolete", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "PFC/PWM组合控制器", "part_number_title": "UCC<PERSON>510, <PERSON><PERSON><PERSON>511, <PERSON><PERSON><PERSON>512, <PERSON><PERSON><PERSON>513, <PERSON><PERSON>28514, <PERSON>C28515, <PERSON><PERSON>28516, <PERSON>C28517 Advanced PFC/PWM Combination Controllers", "features": ["Provides Control of PFC and PWM Power Stages In One Device", "Leading-Edge PFC, Trailing-Edge PWM Modulation for Reduced Ripple", "Built-In Sequencing of PFC and PWM Turn-On", "2-A Source and 3-A Sink Gate Drive for Both PFC and PWM Stages", "Typical 16-ns Rise Time and 7-ns Fall Time into 1-nF Loads", "PFC Features: Average-Current-Mode Control for Continuous Conduction Mode Operation, Highly-Linear Multiplier for Near-Unity Power Factor, Input Voltage Feedforward Implementation, Improved Load Transient Response, Accurate Power Limiting, Zero Power Detect", "PWM Features: Peak-Current-Mode Control Operation, 1:1 or 1:2 PFC:PWM Frequency Options, Programmable maximum duty cycle, Programmable Soft-Start, Two Hysteresis Options for Differing Hold-Up Time Requirements"], "description": "The UCC28510 series of combination PFC/PWM controllers provide complete control functionality for any off-line power system requiring compliance with the IEC1000-3-2 harmonic reduction requirements. By combining the control and drive signals for the PFC and the PWM stages into a single device, significant performance and cost benefits are gained. By managing the modulation mechanisms of the two stages (leading-edge modulation for PFC and trailing-edge modulation for PWM), the ripple current in the boost capacitor is minimized. Based on the average current mode control architecture with input voltage feedforward of prior PFC/PWM combination controllers, these devices offer performance advantages. Two new key PWM features are programmable maximum duty cycle and the 2x PWM frequency options to the base PFC frequency. For the PFC stage, the devices feature an improved multiplier and the use of a transconductance amplifier for enhanced transient response. The core of the PFC section is in a three-input multiplier that generates the reference signal for the line current. The UCC28510 series features a highly linearized multiplier circuit capable of producing a low distortion reference for the line current over the full range of line and load conditions. A low-offset, high-bandwidth current error amplifier ensures that the actual inductor current (sensed through a resistor in the return path) follows the multiplier output command signal. The output voltage error is processed through a transconductance voltage amplifier.", "applications": ["Off-line power systems requiring IEC1000-3-2 harmonic reduction requirements"], "ordering_information": [{"part_number": "UCC28515", "order_device": "UCC28515DW", "package_type": "SOIC", "package_drawing_code": "DW"}, {"part_number": "UCC28515", "order_device": "UCC28515N", "package_type": "PDIP", "package_drawing_code": "N"}], "pin_function": [{"product_part_number": "UCC28515", "package_type": "ALL", "pins": [{"pin_number": "1", "pin_name": "VAOUT", "pin_description": "Output of the PFC transconductance voltage amplifier and it is internally connected to the Zero Power Detect comparator input and the multiplier input"}, {"pin_number": "2", "pin_name": "RT", "pin_description": "Oscillator programming pin that is set with a single resistor to GND"}, {"pin_number": "3", "pin_name": "VSENSE", "pin_description": "Inverting input to the PFC transconductance voltage amplifier, and input to the OVP, ENABLE and UVLO2 comparators"}, {"pin_number": "4", "pin_name": "D_MAX", "pin_description": "Positive input to set the maximum duty cycle clamp level of the PWM stage"}, {"pin_number": "5", "pin_name": "CT_BUFF", "pin_description": "Internally buffered PWM stage oscillator ramp output, typically used to program slope compensation with a single resistor"}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Analog ground"}, {"pin_number": "7", "pin_name": "VERR", "pin_description": "Feedback error voltage input for the PWM stage, typically connected to an optocoupler output"}, {"pin_number": "8", "pin_name": "ISENSE2", "pin_description": "Input for PWM stage current sense and peak current limit"}, {"pin_number": "9", "pin_name": "VCC", "pin_description": "Positive supply voltage pin"}, {"pin_number": "10", "pin_name": "GT2", "pin_description": "PWM stage gate drive output"}, {"pin_number": "11", "pin_name": "PWRGND", "pin_description": "Power ground for GT1, GT2 and high current return paths"}, {"pin_number": "12", "pin_name": "GT1", "pin_description": "PFC stage gate drive output"}, {"pin_number": "13", "pin_name": "SS2", "pin_description": "Soft start for the PWM stage"}, {"pin_number": "14", "pin_name": "PKLMT", "pin_description": "Voltage input to the PFC peak current limit comparator"}, {"pin_number": "15", "pin_name": "CAOUT", "pin_description": "Output of the current control amplifier of the PFC stage. CAOUT is internally connected to the PWM comparator input in the PFC stage"}, {"pin_number": "16", "pin_name": "ISENSE1", "pin_description": "Non-inverting input to the PFC stage current amplifier"}, {"pin_number": "17", "pin_name": "MOUT", "pin_description": "PFC multiplier high-impedance current output, internally connected to the current amplifier inverting input"}, {"pin_number": "18", "pin_name": "IAC", "pin_description": "Multiplier current input that is proportional to the instantaneous rectified line voltage"}, {"pin_number": "19", "pin_name": "VFF", "pin_description": "Voltage feedforward pin for the PFC stage, sources an IAC/2 current that should be externally filtered"}, {"pin_number": "20", "pin_name": "VREF", "pin_description": "Precision 7.5-V reference output"}]}], "datasheet_cn": "未找到", "datasheet_en": "slus517c.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "20V", "min_input_voltage": "10.2V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "600kHz", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "PWM: 1.3V, PFC: 0V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "Zero Power Detect", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "UVLO", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "PFC: Average Current Mode, PWM: Peak Current Mode", "output_voltage_accuracy": "±2%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "2", "pitch": "1.27", "height": "2.65", "width": "7.6", "length": "26.92"}]}, {"part_number": "UCC28516", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Obsolete", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "PFC/PWM组合控制器", "part_number_title": "UCC<PERSON>510, <PERSON><PERSON><PERSON>511, <PERSON><PERSON><PERSON>512, <PERSON><PERSON><PERSON>513, <PERSON><PERSON>28514, <PERSON>C28515, <PERSON><PERSON>28516, <PERSON>C28517 Advanced PFC/PWM Combination Controllers", "features": ["Provides Control of PFC and PWM Power Stages In One Device", "Leading-Edge PFC, Trailing-Edge PWM Modulation for Reduced Ripple", "Built-In Sequencing of PFC and PWM Turn-On", "2-A Source and 3-A Sink Gate Drive for Both PFC and PWM Stages", "Typical 16-ns Rise Time and 7-ns Fall Time into 1-nF Loads", "PFC Features: Average-Current-Mode Control for Continuous Conduction Mode Operation, Highly-Linear Multiplier for Near-Unity Power Factor, Input Voltage Feedforward Implementation, Improved Load Transient Response, Accurate Power Limiting, Zero Power Detect", "PWM Features: Peak-Current-Mode Control Operation, 1:1 or 1:2 PFC:PWM Frequency Options, Programmable maximum duty cycle, Programmable Soft-Start, Two Hysteresis Options for Differing Hold-Up Time Requirements"], "description": "The UCC28510 series of combination PFC/PWM controllers provide complete control functionality for any off-line power system requiring compliance with the IEC1000-3-2 harmonic reduction requirements. By combining the control and drive signals for the PFC and the PWM stages into a single device, significant performance and cost benefits are gained. By managing the modulation mechanisms of the two stages (leading-edge modulation for PFC and trailing-edge modulation for PWM), the ripple current in the boost capacitor is minimized. Based on the average current mode control architecture with input voltage feedforward of prior PFC/PWM combination controllers, these devices offer performance advantages. Two new key PWM features are programmable maximum duty cycle and the 2x PWM frequency options to the base PFC frequency. For the PFC stage, the devices feature an improved multiplier and the use of a transconductance amplifier for enhanced transient response. The core of the PFC section is in a three-input multiplier that generates the reference signal for the line current. The UCC28510 series features a highly linearized multiplier circuit capable of producing a low distortion reference for the line current over the full range of line and load conditions. A low-offset, high-bandwidth current error amplifier ensures that the actual inductor current (sensed through a resistor in the return path) follows the multiplier output command signal. The output voltage error is processed through a transconductance voltage amplifier.", "applications": ["Off-line power systems requiring IEC1000-3-2 harmonic reduction requirements"], "ordering_information": [{"part_number": "UCC28516", "order_device": "UCC28516DW", "package_type": "SOIC", "package_drawing_code": "DW"}, {"part_number": "UCC28516", "order_device": "UCC28516N", "package_type": "PDIP", "package_drawing_code": "N"}], "pin_function": [{"product_part_number": "UCC28516", "package_type": "ALL", "pins": [{"pin_number": "1", "pin_name": "VAOUT", "pin_description": "Output of the PFC transconductance voltage amplifier and it is internally connected to the Zero Power Detect comparator input and the multiplier input"}, {"pin_number": "2", "pin_name": "RT", "pin_description": "Oscillator programming pin that is set with a single resistor to GND"}, {"pin_number": "3", "pin_name": "VSENSE", "pin_description": "Inverting input to the PFC transconductance voltage amplifier, and input to the OVP, ENABLE and UVLO2 comparators"}, {"pin_number": "4", "pin_name": "D_MAX", "pin_description": "Positive input to set the maximum duty cycle clamp level of the PWM stage"}, {"pin_number": "5", "pin_name": "CT_BUFF", "pin_description": "Internally buffered PWM stage oscillator ramp output, typically used to program slope compensation with a single resistor"}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Analog ground"}, {"pin_number": "7", "pin_name": "VERR", "pin_description": "Feedback error voltage input for the PWM stage, typically connected to an optocoupler output"}, {"pin_number": "8", "pin_name": "ISENSE2", "pin_description": "Input for PWM stage current sense and peak current limit"}, {"pin_number": "9", "pin_name": "VCC", "pin_description": "Positive supply voltage pin"}, {"pin_number": "10", "pin_name": "GT2", "pin_description": "PWM stage gate drive output"}, {"pin_number": "11", "pin_name": "PWRGND", "pin_description": "Power ground for GT1, GT2 and high current return paths"}, {"pin_number": "12", "pin_name": "GT1", "pin_description": "PFC stage gate drive output"}, {"pin_number": "13", "pin_name": "SS2", "pin_description": "Soft start for the PWM stage"}, {"pin_number": "14", "pin_name": "PKLMT", "pin_description": "Voltage input to the PFC peak current limit comparator"}, {"pin_number": "15", "pin_name": "CAOUT", "pin_description": "Output of the current control amplifier of the PFC stage. CAOUT is internally connected to the PWM comparator input in the PFC stage"}, {"pin_number": "16", "pin_name": "ISENSE1", "pin_description": "Non-inverting input to the PFC stage current amplifier"}, {"pin_number": "17", "pin_name": "MOUT", "pin_description": "PFC multiplier high-impedance current output, internally connected to the current amplifier inverting input"}, {"pin_number": "18", "pin_name": "IAC", "pin_description": "Multiplier current input that is proportional to the instantaneous rectified line voltage"}, {"pin_number": "19", "pin_name": "VFF", "pin_description": "Voltage feedforward pin for the PFC stage, sources an IAC/2 current that should be externally filtered"}, {"pin_number": "20", "pin_name": "VREF", "pin_description": "Precision 7.5-V reference output"}]}], "datasheet_cn": "未找到", "datasheet_en": "slus517c.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "20V", "min_input_voltage": "16V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "600kHz", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "PWM: 1.3V, PFC: 0V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "Zero Power Detect", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "UVLO", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "PFC: Average Current Mode, PWM: Peak Current Mode", "output_voltage_accuracy": "±2%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "2", "pitch": "1.27", "height": "2.65", "width": "7.6", "length": "26.92"}]}, {"part_number": "UCC28517", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Obsolete", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "PFC/PWM组合控制器", "part_number_title": "UCC<PERSON>510, <PERSON><PERSON><PERSON>511, <PERSON><PERSON><PERSON>512, <PERSON><PERSON><PERSON>513, <PERSON><PERSON>28514, <PERSON>C28515, <PERSON><PERSON>28516, <PERSON>C28517 Advanced PFC/PWM Combination Controllers", "features": ["Provides Control of PFC and PWM Power Stages In One Device", "Leading-Edge PFC, Trailing-Edge PWM Modulation for Reduced Ripple", "Built-In Sequencing of PFC and PWM Turn-On", "2-A Source and 3-A Sink Gate Drive for Both PFC and PWM Stages", "Typical 16-ns Rise Time and 7-ns Fall Time into 1-nF Loads", "PFC Features: Average-Current-Mode Control for Continuous Conduction Mode Operation, Highly-Linear Multiplier for Near-Unity Power Factor, Input Voltage Feedforward Implementation, Improved Load Transient Response, Accurate Power Limiting, Zero Power Detect", "PWM Features: Peak-Current-Mode Control Operation, 1:1 or 1:2 PFC:PWM Frequency Options, Programmable maximum duty cycle, Programmable Soft-Start, Two Hysteresis Options for Differing Hold-Up Time Requirements"], "description": "The UCC28510 series of combination PFC/PWM controllers provide complete control functionality for any off-line power system requiring compliance with the IEC1000-3-2 harmonic reduction requirements. By combining the control and drive signals for the PFC and the PWM stages into a single device, significant performance and cost benefits are gained. By managing the modulation mechanisms of the two stages (leading-edge modulation for PFC and trailing-edge modulation for PWM), the ripple current in the boost capacitor is minimized. Based on the average current mode control architecture with input voltage feedforward of prior PFC/PWM combination controllers, these devices offer performance advantages. Two new key PWM features are programmable maximum duty cycle and the 2x PWM frequency options to the base PFC frequency. For the PFC stage, the devices feature an improved multiplier and the use of a transconductance amplifier for enhanced transient response. The core of the PFC section is in a three-input multiplier that generates the reference signal for the line current. The UCC28510 series features a highly linearized multiplier circuit capable of producing a low distortion reference for the line current over the full range of line and load conditions. A low-offset, high-bandwidth current error amplifier ensures that the actual inductor current (sensed through a resistor in the return path) follows the multiplier output command signal. The output voltage error is processed through a transconductance voltage amplifier.", "applications": ["Off-line power systems requiring IEC1000-3-2 harmonic reduction requirements"], "ordering_information": [{"part_number": "UCC28517", "order_device": "UCC28517DW", "package_type": "SOIC", "package_drawing_code": "DW"}, {"part_number": "UCC28517", "order_device": "UCC28517N", "package_type": "PDIP", "package_drawing_code": "N"}], "pin_function": [{"product_part_number": "UCC28517", "package_type": "ALL", "pins": [{"pin_number": "1", "pin_name": "VAOUT", "pin_description": "Output of the PFC transconductance voltage amplifier and it is internally connected to the Zero Power Detect comparator input and the multiplier input"}, {"pin_number": "2", "pin_name": "RT", "pin_description": "Oscillator programming pin that is set with a single resistor to GND"}, {"pin_number": "3", "pin_name": "VSENSE", "pin_description": "Inverting input to the PFC transconductance voltage amplifier, and input to the OVP, ENABLE and UVLO2 comparators"}, {"pin_number": "4", "pin_name": "D_MAX", "pin_description": "Positive input to set the maximum duty cycle clamp level of the PWM stage"}, {"pin_number": "5", "pin_name": "CT_BUFF", "pin_description": "Internally buffered PWM stage oscillator ramp output, typically used to program slope compensation with a single resistor"}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Analog ground"}, {"pin_number": "7", "pin_name": "VERR", "pin_description": "Feedback error voltage input for the PWM stage, typically connected to an optocoupler output"}, {"pin_number": "8", "pin_name": "ISENSE2", "pin_description": "Input for PWM stage current sense and peak current limit"}, {"pin_number": "9", "pin_name": "VCC", "pin_description": "Positive supply voltage pin"}, {"pin_number": "10", "pin_name": "GT2", "pin_description": "PWM stage gate drive output"}, {"pin_number": "11", "pin_name": "PWRGND", "pin_description": "Power ground for GT1, GT2 and high current return paths"}, {"pin_number": "12", "pin_name": "GT1", "pin_description": "PFC stage gate drive output"}, {"pin_number": "13", "pin_name": "SS2", "pin_description": "Soft start for the PWM stage"}, {"pin_number": "14", "pin_name": "PKLMT", "pin_description": "Voltage input to the PFC peak current limit comparator"}, {"pin_number": "15", "pin_name": "CAOUT", "pin_description": "Output of the current control amplifier of the PFC stage. CAOUT is internally connected to the PWM comparator input in the PFC stage"}, {"pin_number": "16", "pin_name": "ISENSE1", "pin_description": "Non-inverting input to the PFC stage current amplifier"}, {"pin_number": "17", "pin_name": "MOUT", "pin_description": "PFC multiplier high-impedance current output, internally connected to the current amplifier inverting input"}, {"pin_number": "18", "pin_name": "IAC", "pin_description": "Multiplier current input that is proportional to the instantaneous rectified line voltage"}, {"pin_number": "19", "pin_name": "VFF", "pin_description": "Voltage feedforward pin for the PFC stage, sources an IAC/2 current that should be externally filtered"}, {"pin_number": "20", "pin_name": "VREF", "pin_description": "Precision 7.5-V reference output"}]}], "datasheet_cn": "未找到", "datasheet_en": "slus517c.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "20V", "min_input_voltage": "10.2V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "不适用", "max_switch_frequency": "600kHz", "quiescent_current": "4mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "PWM: 1.3V, PFC: 0V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "Zero Power Detect", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "UVLO", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "PFC: Average Current Mode, PWM: Peak Current Mode", "output_voltage_accuracy": "±2%", "output_reference_voltage": "7.5V"}, "package": [{"type": "OPTION", "pin_count": "2", "pitch": "1.27", "height": "2.65", "width": "7.6", "length": "26.92"}]}]