[{"part_number": "UCC1809-1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "PWM控制器", "part_number_title": "Economy Primary Side Controller", "features": ["User Programmable Soft Start With Active Low Shutdown", "User Programmable Maximum Duty Cycle", "Accessible 5V Reference", "Undervoltage Lockout", "Operation to 1MHz", "0.4A Source/0.8A Sink FET Driver", "Low 100μA Startup Current"], "description": "The UCC3809 family of BCDMOS economy low power integrated circuits contains all the control and drive circuitry required for off-line and isolated DC-to-DC fixed frequency current mode switching power supplies with minimal external parts count. Internally implemented circuits include undervoltage lockout featuring startup current less than 100μA, a user accessible voltage reference, logic to ensure latched operation, a PWM comparator, and a totem pole output stage to sink or source peak current. The output stage, suitable for driving N-Channel MOSFETs, is low in the off state. Oscillator frequency and maximum duty cycle are programmed with two resistors and a capacitor. The UCC3809 family also features full cycle soft start.", "applications": ["off-line switching power supplies", "isolated DC-to-DC fixed frequency current mode switching power supplies", "Isolated 50W flyback converter"], "ordering_information": [{"part_number": "UCC1809-1", "package_type": "J", "min_operation_temp": "-55", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC1809-1", "package_type": "SOIC-8, PDIP-8, TSSOP-8, MSOP-8, J", "pins": [{"pin_number": "1", "pin_name": "FB", "pin_description": "This pin is the summing node for current sense feedback, voltage sense feedback (by optocoupler) and slope compensation. Slope compensation is derived from the rising voltage at the timing capacitor and can be buffered with an external small signal NPN transistor. External high frequency filter capacitance applied from this node to GND is discharged by an internal 250Ω on resistance NMOS FET during PWM off time and offers effective leading edge blanking set by the RC time constant of the feedback resistance from current sense resistor to FB input and the high frequency filter capacitor capacitance at this node to GND."}, {"pin_number": "2", "pin_name": "SS", "pin_description": "This pin serves two functions. The soft start timing capacitor connects to SS and is charged by an internal 6μA current source. Under normal soft start SS is discharged to at least 0.4V and then ramps positive to 1V during which time the output driver is held low. As SS charges from 1V to 2V soft start is implemented by an increasing output duty cycle. If SS is taken below 0.5V, the output driver is inhibited and held low. The user accessible 5V voltage reference also goes low and IVDD < 100μA."}, {"pin_number": "3", "pin_name": "RT1", "pin_description": "This pin connects to timing resistor RT1 and controls the positive ramp time of the internal oscillator (Tr = 0.74 • (CT + 27pF) • RT1). The positive threshold of the internal oscillator is sensed through inactive timing resistor RT2 which connects to pin RT2 and timing capacitor CT."}, {"pin_number": "4", "pin_name": "RT2", "pin_description": "This pin connects to timing resistor RT2 and controls the negative ramp time of the internal oscillator (Tf = 0.74 • (CT + 27pF) • RT2). The negative threshold of the internal oscillator is sensed through inactive timing resistor RT1 which connects to pin RT1 and timing capacitor CT."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Reference ground and power ground for all functions."}, {"pin_number": "6", "pin_name": "OUT", "pin_description": "This pin is the high current power driver output. A minimum series gate resistor of 3.9Ω is recommended to limit the gate drive current when operating with high bias voltages."}, {"pin_number": "7", "pin_name": "VDD", "pin_description": "The power input connection for this device. This pin is shunt regulated at 17.5V which is sufficiently below the voltage rating of the DMOS output driver stage. VDD should be bypassed with a 1μF ceramic capacitor."}, {"pin_number": "8", "pin_name": "REF", "pin_description": "The internal 5V reference output. This reference is buffered and is available on the REF pin. REF should be bypassed with a 0.47μF ceramic capacitor."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS166B", "family_comparison": "The primary difference between UCCx809-1 and UCCx809-2 is the UVLO turn-on threshold. For UCCx809-1, the turn-on threshold is 10V. For UCCx809-2, the turn-on threshold is 15V. Both have a turn-off threshold of 8V.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "19V", "min_input_voltage": "10V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "0.8A", "max_switch_frequency": "1MHz", "quiescent_current": "600µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "±5%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pin_count": "8", "pitch": "1.27", "height": "1.75", "length": "5.0", "width": "3.98"}]}, {"part_number": "UCC1809-2", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "PWM控制器", "part_number_title": "Economy Primary Side Controller", "features": ["User Programmable Soft Start With Active Low Shutdown", "User Programmable Maximum Duty Cycle", "Accessible 5V Reference", "Undervoltage Lockout", "Operation to 1MHz", "0.4A Source/0.8A Sink FET Driver", "Low 100μA Startup Current"], "description": "The UCC3809 family of BCDMOS economy low power integrated circuits contains all the control and drive circuitry required for off-line and isolated DC-to-DC fixed frequency current mode switching power supplies with minimal external parts count. Internally implemented circuits include undervoltage lockout featuring startup current less than 100μA, a user accessible voltage reference, logic to ensure latched operation, a PWM comparator, and a totem pole output stage to sink or source peak current. The output stage, suitable for driving N-Channel MOSFETs, is low in the off state. Oscillator frequency and maximum duty cycle are programmed with two resistors and a capacitor. The UCC3809 family also features full cycle soft start.", "applications": ["off-line switching power supplies", "isolated DC-to-DC fixed frequency current mode switching power supplies", "Isolated 50W flyback converter"], "ordering_information": [{"part_number": "UCC1809-2", "package_type": "J", "min_operation_temp": "-55", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCC1809-2", "package_type": "SOIC-8, PDIP-8, TSSOP-8, MSOP-8, J", "pins": [{"pin_number": "1", "pin_name": "FB", "pin_description": "This pin is the summing node for current sense feedback, voltage sense feedback (by optocoupler) and slope compensation. Slope compensation is derived from the rising voltage at the timing capacitor and can be buffered with an external small signal NPN transistor. External high frequency filter capacitance applied from this node to GND is discharged by an internal 250Ω on resistance NMOS FET during PWM off time and offers effective leading edge blanking set by the RC time constant of the feedback resistance from current sense resistor to FB input and the high frequency filter capacitor capacitance at this node to GND."}, {"pin_number": "2", "pin_name": "SS", "pin_description": "This pin serves two functions. The soft start timing capacitor connects to SS and is charged by an internal 6μA current source. Under normal soft start SS is discharged to at least 0.4V and then ramps positive to 1V during which time the output driver is held low. As SS charges from 1V to 2V soft start is implemented by an increasing output duty cycle. If SS is taken below 0.5V, the output driver is inhibited and held low. The user accessible 5V voltage reference also goes low and IVDD < 100μA."}, {"pin_number": "3", "pin_name": "RT1", "pin_description": "This pin connects to timing resistor RT1 and controls the positive ramp time of the internal oscillator (Tr = 0.74 • (CT + 27pF) • RT1). The positive threshold of the internal oscillator is sensed through inactive timing resistor RT2 which connects to pin RT2 and timing capacitor CT."}, {"pin_number": "4", "pin_name": "RT2", "pin_description": "This pin connects to timing resistor RT2 and controls the negative ramp time of the internal oscillator (Tf = 0.74 • (CT + 27pF) • RT2). The negative threshold of the internal oscillator is sensed through inactive timing resistor RT1 which connects to pin RT1 and timing capacitor CT."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Reference ground and power ground for all functions."}, {"pin_number": "6", "pin_name": "OUT", "pin_description": "This pin is the high current power driver output. A minimum series gate resistor of 3.9Ω is recommended to limit the gate drive current when operating with high bias voltages."}, {"pin_number": "7", "pin_name": "VDD", "pin_description": "The power input connection for this device. This pin is shunt regulated at 17.5V which is sufficiently below the voltage rating of the DMOS output driver stage. VDD should be bypassed with a 1μF ceramic capacitor."}, {"pin_number": "8", "pin_name": "REF", "pin_description": "The internal 5V reference output. This reference is buffered and is available on the REF pin. REF should be bypassed with a 0.47μF ceramic capacitor."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS166B", "family_comparison": "The primary difference between UCCx809-1 and UCCx809-2 is the UVLO turn-on threshold. For UCCx809-1, the turn-on threshold is 10V. For UCCx809-2, the turn-on threshold is 15V. Both have a turn-off threshold of 8V.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "19V", "min_input_voltage": "15V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "0.8A", "max_switch_frequency": "1MHz", "quiescent_current": "600µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "±5%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pin_count": "8", "pitch": "1.27", "height": "1.75", "length": "5.0", "width": "3.98"}]}, {"part_number": "UCC2809-1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "PWM控制器", "part_number_title": "Economy Primary Side Controller", "features": ["User Programmable Soft Start With Active Low Shutdown", "User Programmable Maximum Duty Cycle", "Accessible 5V Reference", "Undervoltage Lockout", "Operation to 1MHz", "0.4A Source/0.8A Sink FET Driver", "Low 100μA Startup Current"], "description": "The UCC3809 family of BCDMOS economy low power integrated circuits contains all the control and drive circuitry required for off-line and isolated DC-to-DC fixed frequency current mode switching power supplies with minimal external parts count. Internally implemented circuits include undervoltage lockout featuring startup current less than 100μA, a user accessible voltage reference, logic to ensure latched operation, a PWM comparator, and a totem pole output stage to sink or source peak current. The output stage, suitable for driving N-Channel MOSFETs, is low in the off state. Oscillator frequency and maximum duty cycle are programmed with two resistors and a capacitor. The UCC3809 family also features full cycle soft start.", "applications": ["off-line switching power supplies", "isolated DC-to-DC fixed frequency current mode switching power supplies", "Isolated 50W flyback converter"], "ordering_information": [{"part_number": "UCC2809-1", "order_device": "UCC2809D-1", "package_type": "SOIC", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC2809-1", "order_device": "UCC2809DTR-1", "package_type": "SOIC", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC2809-1", "order_device": "UCC2809P-1", "package_type": "VSSOP", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC2809-1", "order_device": "UCC2809PTR-1", "package_type": "VSSOP", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC2809-1", "order_device": "UCC2809PW-1", "package_type": "TSSOP", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC2809-1", "order_device": "UCC2809PWTR-1", "package_type": "TSSOP", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "UCC2809-1", "package_type": "SOIC-8, PDIP-8, TSSOP-8, MSOP-8, J", "pins": [{"pin_number": "1", "pin_name": "FB", "pin_description": "This pin is the summing node for current sense feedback, voltage sense feedback (by optocoupler) and slope compensation. Slope compensation is derived from the rising voltage at the timing capacitor and can be buffered with an external small signal NPN transistor. External high frequency filter capacitance applied from this node to GND is discharged by an internal 250Ω on resistance NMOS FET during PWM off time and offers effective leading edge blanking set by the RC time constant of the feedback resistance from current sense resistor to FB input and the high frequency filter capacitor capacitance at this node to GND."}, {"pin_number": "2", "pin_name": "SS", "pin_description": "This pin serves two functions. The soft start timing capacitor connects to SS and is charged by an internal 6μA current source. Under normal soft start SS is discharged to at least 0.4V and then ramps positive to 1V during which time the output driver is held low. As SS charges from 1V to 2V soft start is implemented by an increasing output duty cycle. If SS is taken below 0.5V, the output driver is inhibited and held low. The user accessible 5V voltage reference also goes low and IVDD < 100μA."}, {"pin_number": "3", "pin_name": "RT1", "pin_description": "This pin connects to timing resistor RT1 and controls the positive ramp time of the internal oscillator (Tr = 0.74 • (CT + 27pF) • RT1). The positive threshold of the internal oscillator is sensed through inactive timing resistor RT2 which connects to pin RT2 and timing capacitor CT."}, {"pin_number": "4", "pin_name": "RT2", "pin_description": "This pin connects to timing resistor RT2 and controls the negative ramp time of the internal oscillator (Tf = 0.74 • (CT + 27pF) • RT2). The negative threshold of the internal oscillator is sensed through inactive timing resistor RT1 which connects to pin RT1 and timing capacitor CT."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Reference ground and power ground for all functions."}, {"pin_number": "6", "pin_name": "OUT", "pin_description": "This pin is the high current power driver output. A minimum series gate resistor of 3.9Ω is recommended to limit the gate drive current when operating with high bias voltages."}, {"pin_number": "7", "pin_name": "VDD", "pin_description": "The power input connection for this device. This pin is shunt regulated at 17.5V which is sufficiently below the voltage rating of the DMOS output driver stage. VDD should be bypassed with a 1μF ceramic capacitor."}, {"pin_number": "8", "pin_name": "REF", "pin_description": "The internal 5V reference output. This reference is buffered and is available on the REF pin. REF should be bypassed with a 0.47μF ceramic capacitor."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS166B", "family_comparison": "The primary difference between UCCx809-1 and UCCx809-2 is the UVLO turn-on threshold. For UCCx809-1, the turn-on threshold is 10V. For UCCx809-2, the turn-on threshold is 15V. Both have a turn-off threshold of 8V.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "19V", "min_input_voltage": "10V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "0.8A", "max_switch_frequency": "1MHz", "quiescent_current": "600µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "±5%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pin_count": "8", "pitch": "1.27", "height": "1.75", "length": "5.0", "width": "3.98"}]}, {"part_number": "UCC2809-2", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "PWM控制器", "part_number_title": "Economy Primary Side Controller", "features": ["User Programmable Soft Start With Active Low Shutdown", "User Programmable Maximum Duty Cycle", "Accessible 5V Reference", "Undervoltage Lockout", "Operation to 1MHz", "0.4A Source/0.8A Sink FET Driver", "Low 100μA Startup Current"], "description": "The UCC3809 family of BCDMOS economy low power integrated circuits contains all the control and drive circuitry required for off-line and isolated DC-to-DC fixed frequency current mode switching power supplies with minimal external parts count. Internally implemented circuits include undervoltage lockout featuring startup current less than 100μA, a user accessible voltage reference, logic to ensure latched operation, a PWM comparator, and a totem pole output stage to sink or source peak current. The output stage, suitable for driving N-Channel MOSFETs, is low in the off state. Oscillator frequency and maximum duty cycle are programmed with two resistors and a capacitor. The UCC3809 family also features full cycle soft start.", "applications": ["off-line switching power supplies", "isolated DC-to-DC fixed frequency current mode switching power supplies", "Isolated 50W flyback converter"], "ordering_information": [{"part_number": "UCC2809-2", "order_device": "UCC2809D-2", "package_type": "SOIC", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC2809-2", "order_device": "UCC2809DTR-2", "package_type": "SOIC", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC2809-2", "order_device": "UCC2809P-2", "package_type": "VSSOP", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC2809-2", "order_device": "UCC2809PTR-2", "package_type": "VSSOP", "min_operation_temp": "-40", "max_operation_temp": "85"}, {"part_number": "UCC2809-2", "order_device": "UCC2809PW-2", "package_type": "TSSOP", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "UCC2809-2", "package_type": "SOIC-8, PDIP-8, TSSOP-8, MSOP-8, J", "pins": [{"pin_number": "1", "pin_name": "FB", "pin_description": "This pin is the summing node for current sense feedback, voltage sense feedback (by optocoupler) and slope compensation. Slope compensation is derived from the rising voltage at the timing capacitor and can be buffered with an external small signal NPN transistor. External high frequency filter capacitance applied from this node to GND is discharged by an internal 250Ω on resistance NMOS FET during PWM off time and offers effective leading edge blanking set by the RC time constant of the feedback resistance from current sense resistor to FB input and the high frequency filter capacitor capacitance at this node to GND."}, {"pin_number": "2", "pin_name": "SS", "pin_description": "This pin serves two functions. The soft start timing capacitor connects to SS and is charged by an internal 6μA current source. Under normal soft start SS is discharged to at least 0.4V and then ramps positive to 1V during which time the output driver is held low. As SS charges from 1V to 2V soft start is implemented by an increasing output duty cycle. If SS is taken below 0.5V, the output driver is inhibited and held low. The user accessible 5V voltage reference also goes low and IVDD < 100μA."}, {"pin_number": "3", "pin_name": "RT1", "pin_description": "This pin connects to timing resistor RT1 and controls the positive ramp time of the internal oscillator (Tr = 0.74 • (CT + 27pF) • RT1). The positive threshold of the internal oscillator is sensed through inactive timing resistor RT2 which connects to pin RT2 and timing capacitor CT."}, {"pin_number": "4", "pin_name": "RT2", "pin_description": "This pin connects to timing resistor RT2 and controls the negative ramp time of the internal oscillator (Tf = 0.74 • (CT + 27pF) • RT2). The negative threshold of the internal oscillator is sensed through inactive timing resistor RT1 which connects to pin RT1 and timing capacitor CT."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Reference ground and power ground for all functions."}, {"pin_number": "6", "pin_name": "OUT", "pin_description": "This pin is the high current power driver output. A minimum series gate resistor of 3.9Ω is recommended to limit the gate drive current when operating with high bias voltages."}, {"pin_number": "7", "pin_name": "VDD", "pin_description": "The power input connection for this device. This pin is shunt regulated at 17.5V which is sufficiently below the voltage rating of the DMOS output driver stage. VDD should be bypassed with a 1μF ceramic capacitor."}, {"pin_number": "8", "pin_name": "REF", "pin_description": "The internal 5V reference output. This reference is buffered and is available on the REF pin. REF should be bypassed with a 0.47μF ceramic capacitor."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS166B", "family_comparison": "The primary difference between UCCx809-1 and UCCx809-2 is the UVLO turn-on threshold. For UCCx809-1, the turn-on threshold is 10V. For UCCx809-2, the turn-on threshold is 15V. Both have a turn-off threshold of 8V.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "19V", "min_input_voltage": "15V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "0.8A", "max_switch_frequency": "1MHz", "quiescent_current": "600µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "±5%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pin_count": "8", "pitch": "1.27", "height": "1.75", "length": "5.0", "width": "3.98"}]}, {"part_number": "UCC3809-1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "PWM控制器", "part_number_title": "Economy Primary Side Controller", "features": ["User Programmable Soft Start With Active Low Shutdown", "User Programmable Maximum Duty Cycle", "Accessible 5V Reference", "Undervoltage Lockout", "Operation to 1MHz", "0.4A Source/0.8A Sink FET Driver", "Low 100μA Startup Current"], "description": "The UCC3809 family of BCDMOS economy low power integrated circuits contains all the control and drive circuitry required for off-line and isolated DC-to-DC fixed frequency current mode switching power supplies with minimal external parts count. Internally implemented circuits include undervoltage lockout featuring startup current less than 100μA, a user accessible voltage reference, logic to ensure latched operation, a PWM comparator, and a totem pole output stage to sink or source peak current. The output stage, suitable for driving N-Channel MOSFETs, is low in the off state. Oscillator frequency and maximum duty cycle are programmed with two resistors and a capacitor. The UCC3809 family also features full cycle soft start.", "applications": ["off-line switching power supplies", "isolated DC-to-DC fixed frequency current mode switching power supplies", "Isolated 50W flyback converter"], "ordering_information": [{"part_number": "UCC3809-1", "order_device": "UCC3809D-1", "package_type": "SOIC", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC3809-1", "order_device": "UCC3809DTR-1", "package_type": "SOIC", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC3809-1", "order_device": "UCC3809P-1", "package_type": "VSSOP", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC3809-1", "order_device": "UCC3809PTR-1", "package_type": "VSSOP", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "UCC3809-1", "package_type": "SOIC-8, PDIP-8, TSSOP-8, MSOP-8, J", "pins": [{"pin_number": "1", "pin_name": "FB", "pin_description": "This pin is the summing node for current sense feedback, voltage sense feedback (by optocoupler) and slope compensation. Slope compensation is derived from the rising voltage at the timing capacitor and can be buffered with an external small signal NPN transistor. External high frequency filter capacitance applied from this node to GND is discharged by an internal 250Ω on resistance NMOS FET during PWM off time and offers effective leading edge blanking set by the RC time constant of the feedback resistance from current sense resistor to FB input and the high frequency filter capacitor capacitance at this node to GND."}, {"pin_number": "2", "pin_name": "SS", "pin_description": "This pin serves two functions. The soft start timing capacitor connects to SS and is charged by an internal 6μA current source. Under normal soft start SS is discharged to at least 0.4V and then ramps positive to 1V during which time the output driver is held low. As SS charges from 1V to 2V soft start is implemented by an increasing output duty cycle. If SS is taken below 0.5V, the output driver is inhibited and held low. The user accessible 5V voltage reference also goes low and IVDD < 100μA."}, {"pin_number": "3", "pin_name": "RT1", "pin_description": "This pin connects to timing resistor RT1 and controls the positive ramp time of the internal oscillator (Tr = 0.74 • (CT + 27pF) • RT1). The positive threshold of the internal oscillator is sensed through inactive timing resistor RT2 which connects to pin RT2 and timing capacitor CT."}, {"pin_number": "4", "pin_name": "RT2", "pin_description": "This pin connects to timing resistor RT2 and controls the negative ramp time of the internal oscillator (Tf = 0.74 • (CT + 27pF) • RT2). The negative threshold of the internal oscillator is sensed through inactive timing resistor RT1 which connects to pin RT1 and timing capacitor CT."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Reference ground and power ground for all functions."}, {"pin_number": "6", "pin_name": "OUT", "pin_description": "This pin is the high current power driver output. A minimum series gate resistor of 3.9Ω is recommended to limit the gate drive current when operating with high bias voltages."}, {"pin_number": "7", "pin_name": "VDD", "pin_description": "The power input connection for this device. This pin is shunt regulated at 17.5V which is sufficiently below the voltage rating of the DMOS output driver stage. VDD should be bypassed with a 1μF ceramic capacitor."}, {"pin_number": "8", "pin_name": "REF", "pin_description": "The internal 5V reference output. This reference is buffered and is available on the REF pin. REF should be bypassed with a 0.47μF ceramic capacitor."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS166B", "family_comparison": "The primary difference between UCCx809-1 and UCCx809-2 is the UVLO turn-on threshold. For UCCx809-1, the turn-on threshold is 10V. For UCCx809-2, the turn-on threshold is 15V. Both have a turn-off threshold of 8V.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "19V", "min_input_voltage": "10V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "0.8A", "max_switch_frequency": "1MHz", "quiescent_current": "600µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "±5%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pin_count": "8", "pitch": "1.27", "height": "1.75", "length": "5.0", "width": "3.98"}]}, {"part_number": "UCC3809-2", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "PWM控制器", "part_number_title": "Economy Primary Side Controller", "features": ["User Programmable Soft Start With Active Low Shutdown", "User Programmable Maximum Duty Cycle", "Accessible 5V Reference", "Undervoltage Lockout", "Operation to 1MHz", "0.4A Source/0.8A Sink FET Driver", "Low 100μA Startup Current"], "description": "The UCC3809 family of BCDMOS economy low power integrated circuits contains all the control and drive circuitry required for off-line and isolated DC-to-DC fixed frequency current mode switching power supplies with minimal external parts count. Internally implemented circuits include undervoltage lockout featuring startup current less than 100μA, a user accessible voltage reference, logic to ensure latched operation, a PWM comparator, and a totem pole output stage to sink or source peak current. The output stage, suitable for driving N-Channel MOSFETs, is low in the off state. Oscillator frequency and maximum duty cycle are programmed with two resistors and a capacitor. The UCC3809 family also features full cycle soft start.", "applications": ["off-line switching power supplies", "isolated DC-to-DC fixed frequency current mode switching power supplies", "Isolated 50W flyback converter"], "ordering_information": [{"part_number": "UCC3809-2", "order_device": "UCC3809D-2", "package_type": "SOIC", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC3809-2", "order_device": "UCC3809DTR-2", "package_type": "SOIC", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC3809-2", "order_device": "UCC3809P-2", "package_type": "VSSOP", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC3809-2", "order_device": "UCC3809PTR-2", "package_type": "VSSOP", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UCC3809-2", "order_device": "UCC3809PW-2", "package_type": "TSSOP", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "UCC3809-2", "package_type": "SOIC-8, PDIP-8, TSSOP-8, MSOP-8, J", "pins": [{"pin_number": "1", "pin_name": "FB", "pin_description": "This pin is the summing node for current sense feedback, voltage sense feedback (by optocoupler) and slope compensation. Slope compensation is derived from the rising voltage at the timing capacitor and can be buffered with an external small signal NPN transistor. External high frequency filter capacitance applied from this node to GND is discharged by an internal 250Ω on resistance NMOS FET during PWM off time and offers effective leading edge blanking set by the RC time constant of the feedback resistance from current sense resistor to FB input and the high frequency filter capacitor capacitance at this node to GND."}, {"pin_number": "2", "pin_name": "SS", "pin_description": "This pin serves two functions. The soft start timing capacitor connects to SS and is charged by an internal 6μA current source. Under normal soft start SS is discharged to at least 0.4V and then ramps positive to 1V during which time the output driver is held low. As SS charges from 1V to 2V soft start is implemented by an increasing output duty cycle. If SS is taken below 0.5V, the output driver is inhibited and held low. The user accessible 5V voltage reference also goes low and IVDD < 100μA."}, {"pin_number": "3", "pin_name": "RT1", "pin_description": "This pin connects to timing resistor RT1 and controls the positive ramp time of the internal oscillator (Tr = 0.74 • (CT + 27pF) • RT1). The positive threshold of the internal oscillator is sensed through inactive timing resistor RT2 which connects to pin RT2 and timing capacitor CT."}, {"pin_number": "4", "pin_name": "RT2", "pin_description": "This pin connects to timing resistor RT2 and controls the negative ramp time of the internal oscillator (Tf = 0.74 • (CT + 27pF) • RT2). The negative threshold of the internal oscillator is sensed through inactive timing resistor RT1 which connects to pin RT1 and timing capacitor CT."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Reference ground and power ground for all functions."}, {"pin_number": "6", "pin_name": "OUT", "pin_description": "This pin is the high current power driver output. A minimum series gate resistor of 3.9Ω is recommended to limit the gate drive current when operating with high bias voltages."}, {"pin_number": "7", "pin_name": "VDD", "pin_description": "The power input connection for this device. This pin is shunt regulated at 17.5V which is sufficiently below the voltage rating of the DMOS output driver stage. VDD should be bypassed with a 1μF ceramic capacitor."}, {"pin_number": "8", "pin_name": "REF", "pin_description": "The internal 5V reference output. This reference is buffered and is available on the REF pin. REF should be bypassed with a 0.47μF ceramic capacitor."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS166B", "family_comparison": "The primary difference between UCCx809-1 and UCCx809-2 is the UVLO turn-on threshold. For UCCx809-1, the turn-on threshold is 10V. For UCCx809-2, the turn-on threshold is 15V. Both have a turn-off threshold of 8V.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "19V", "min_input_voltage": "15V", "max_output_voltage": "不适用", "min_output_voltage": "不适用", "max_output_current": "0.8A", "max_switch_frequency": "1MHz", "quiescent_current": "600µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "±5%", "output_reference_voltage": "5V"}, "package": [{"type": "OPTION", "pin_count": "8", "pitch": "1.27", "height": "1.75", "length": "5.0", "width": "3.98"}]}]