{"part_number": "UCC28064A", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "PFC控制器", "category_lv3": "交错式PFC控制器", "part_number_title": "具有较高的轻负载效率的 UCC28064A Natural Interleaving™ 转换模式 PFC 控制器", "features": ["降低了输入滤波器和输出电容器纹波电流", "为了实现更高系统可靠性和更小大容量电容器而减少的电流纹波", "缩小的电磁干扰 (EMI) 滤波器尺寸", "高轻负载效率", "具有输入电压补偿功能的用户可调节相位管理", "具有可调突发阈值的突发模式", "有助于符合 EUP Lot6 Tier II、CoC Tier II 和 DOE Level VI 标准", "无传感器电流整形简化了电路板布局并提升了效率", "输入线路前馈可实现快速线路瞬态响应", "浪涌安全电流限制: 在浪涌期间防止 MOSFET 导通，消除输出整流器中的 CCM 操作和反向恢复事件", "采用 16 引脚 SOIC 封装, 工作温度范围为 –40°C 至 +125°C", "使用 UCC28064A 及其 WEBENCH® 电源设计器创建定制设计"], "description": "与之前的同类产品相比，UCC28064A 交错式 PFC 控制器具有更高的功率额定值。该器件采用了 NaturalInterleaving™ 技术。两个通道均作为主通道运行（即没有从通道），而且这两个通道同步至同一频率。这种方法可以实现更快的响应、出色的相间导通时间匹配以及每个通道的转换模式。该器件具有突发模式功能，可获得较高的轻负载效率。由于具有突发模式，因此在轻负载运行期间无需关闭 PFC 即可实现待机功率目标。而且，由于具有该模式，在与 UCC25630x LLC 控制器和 UCC24624 同步整流器控制器配对使用时，该器件无需使用辅助反激式转换器。扩展的系统级保护 特性 包括输入欠压和压降恢复、输出过压、开环、过载、软启动、相位故障检测以及热关断保护。附加的失效防护过压保护 (OVP) 特性可防止到一个中间电压的短路，如果没有检测到此短路的话，有可能导致非常严重的器件故障。该器件具有高级非线性增益，可针对线路和负载瞬态事件提供快速而平滑的响应。特殊的线路压降处理可避免严重的电流中断。在突发模式期间，不发生切换时偏置电流会大幅降低，从而提高了待机性能。", "applications": ["高清、超高清和 LED 电视", "一体式计算机", "游戏", "适配器", "家用音频系统"], "ordering_information": [{"part_number": "UCC28064A", "order_device": "UCC28064ADT", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "SMALL T&R", "quantity": "250"}, {"part_number": "UCC28064A", "order_device": "UCC28064ADR", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-40", "max_operation_temp": "125", "carrier_description": "LARGE T&R", "quantity": "2500"}], "pin_function": [{"product_part_number": "UCC28064A", "package_type": "SOIC", "pin_number": "1", "pin_name": "ZCD_B", "pin_description": "Phase B zero current detection input", "pin_type": "I"}, {"product_part_number": "UCC28064A", "package_type": "SOIC", "pin_number": "2", "pin_name": "VSENSE", "pin_description": "Error amplifier input", "pin_type": "I"}, {"product_part_number": "UCC28064A", "package_type": "SOIC", "pin_number": "3", "pin_name": "TSET", "pin_description": "Timing set", "pin_type": "I"}, {"product_part_number": "UCC28064A", "package_type": "SOIC", "pin_number": "4", "pin_name": "PHB", "pin_description": "Phase B enable disable threshold input", "pin_type": "I"}, {"product_part_number": "UCC28064A", "package_type": "SOIC", "pin_number": "5", "pin_name": "COMP", "pin_description": "Error amplifier output", "pin_type": "O"}, {"product_part_number": "UCC28064A", "package_type": "SOIC", "pin_number": "6", "pin_name": "AGND", "pin_description": "Analog ground", "pin_type": "GND"}, {"product_part_number": "UCC28064A", "package_type": "SOIC", "pin_number": "7", "pin_name": "VINAC", "pin_description": "Input AC voltage sense", "pin_type": "I"}, {"product_part_number": "UCC28064A", "package_type": "SOIC", "pin_number": "8", "pin_name": "HVSEN", "pin_description": "High voltage output sense", "pin_type": "I"}, {"product_part_number": "UCC28064A", "package_type": "SOIC", "pin_number": "9", "pin_name": "BRST", "pin_description": "Burst mode threshold input", "pin_type": "I"}, {"product_part_number": "UCC28064A", "package_type": "SOIC", "pin_number": "10", "pin_name": "CS", "pin_description": "Current sense input", "pin_type": "I"}, {"product_part_number": "UCC28064A", "package_type": "SOIC", "pin_number": "11", "pin_name": "GDB", "pin_description": "Phase B gate driver output", "pin_type": "O"}, {"product_part_number": "UCC28064A", "package_type": "SOIC", "pin_number": "12", "pin_name": "VCC", "pin_description": "Bias supply input", "pin_type": "Power"}, {"product_part_number": "UCC28064A", "package_type": "SOIC", "pin_number": "13", "pin_name": "PGND", "pin_description": "Power ground", "pin_type": "GND"}, {"product_part_number": "UCC28064A", "package_type": "SOIC", "pin_number": "14", "pin_name": "GDA", "pin_description": "Phase A gate driver output", "pin_type": "O"}, {"product_part_number": "UCC28064A", "package_type": "SOIC", "pin_number": "15", "pin_name": "VREF", "pin_description": "Voltage reference output", "pin_type": "O"}, {"product_part_number": "UCC28064A", "package_type": "SOIC", "pin_number": "16", "pin_name": "ZCD_A", "pin_description": "Phase A zero current detection input", "pin_type": "I"}], "datasheet_cn": "ZHCSIB2B-DECEMBER 2017-REVISED OCTOBER 2019", "datasheet_en": "SLUSC60", "family_comparison": "在 UCC28064A 中，相对于其前身 (UCC28063) 引入了突发模式，以在轻负载条件下实现更高的效率。", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 2, "channel_count": 1, "max_input_voltage": "265V", "min_input_voltage": "85V", "max_output_voltage": "390V", "min_output_voltage": "未找到", "max_output_current": "不适用", "max_switch_frequency": "526kHz", "quiescent_current": "150µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "-200mV", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Burst Mode", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "output_discharge": "False", "integrated_ldo": "True", "dynamic_voltage_setting": "False", "pass_through_mode": "False", "load_disconnect": "False", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "±3%", "output_reference_voltage": "6V"}, "package": [{"pin_count": "16", "length": "10.0", "width": "4.0", "type": "SOIC", "pitch": "1.27", "height": "1.75"}]}