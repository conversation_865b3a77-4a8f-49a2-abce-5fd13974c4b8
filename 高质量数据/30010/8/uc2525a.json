[{"part_number": "UC1525A", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电压模式PWM控制器", "part_number_title": "UCx52xA Regulating Pulse Width Modulators", "features": ["8-V to 35-V Operation", "5.1-V Reference Trimmed to 1%", "100-Hz to 500-kHz Oscillator Range", "Separate Oscillator Sync Terminal", "Adjustable Dead-Time Control", "Internal Soft Start", "Pulse-by-Pulse Shutdown", "Input Undervoltage Lockout With Hysteresis", "Latching PWM to Prevent Multiple Pulses", "Dual Source and Sink Output Drivers"], "description": "The UC1525A/1527A series of pulse width modulator integrated circuits are designed to offer improved performance and lowered external parts count when used in designing all types of switching power supplies. The on-chip 5.1-V reference is trimmed to 1% and the input common-mode range of the error amplifier includes the reference voltage, eliminating external resistors. A sync input to the oscillator allows multiple units to be slaved or a single unit to be synchronized to an external system clock. A single resistor between CT and the discharge terminals provides a wide range of dead-time adjustment. These devices also feature built-in soft-start circuitry with only an external timing capacitor required. A shutdown terminal controls both the soft-start circuitry and the output stages, providing instantaneous turn off through the PWM latch with pulsed shutdown, as well as soft-start recycle with longer shutdown commands. The UC1525A output stage features NOR logic, giving a LOW output for an OFF state.", "applications": ["Off-Line and DC/DC Power Supplies", "Converters Using Voltage Mode", "Single-Ended or Two-Switch Topology Designs", "Solar Inverters", "Welding Inverters", "Motor Control", "Battery Chargers"], "ordering_information": [{"part_number": "UC1525A", "order_device": "5962-89511032A", "package_type": "LCCC", "package_drawing_code": "FK", "output_voltage": "未找到", "min_operation_temp": "-55", "max_operation_temp": "125"}, {"part_number": "UC1525A", "order_device": "5962-8951103EA", "package_type": "CDIP", "package_drawing_code": "J", "output_voltage": "未找到", "min_operation_temp": "-55", "max_operation_temp": "125"}, {"part_number": "UC1525A", "order_device": "UC1525AJ", "package_type": "CDIP", "package_drawing_code": "J", "output_voltage": "未找到", "min_operation_temp": "-55", "max_operation_temp": "125"}, {"part_number": "UC1525A", "order_device": "UC1525AL", "package_type": "LCCC", "package_drawing_code": "FK", "output_voltage": "未找到", "min_operation_temp": "-55", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCx52xA", "package_type": "CDIP/PDIP (16-Pin)", "pins": [{"pin_number": "1", "pin_name": "INV Input", "pin_description": "Inverting input to the error amplifier"}, {"pin_number": "2", "pin_name": "NI Input", "pin_description": "Noninverting input to the error amplifier"}, {"pin_number": "3", "pin_name": "SYNC", "pin_description": "Oscillator sync terminal"}, {"pin_number": "4", "pin_name": "OSC Output", "pin_description": "Oscillator frequency output"}, {"pin_number": "5", "pin_name": "CT", "pin_description": "Timing capacitor connection pin for oscillator frequency programming. The timing capacitor should be connected to the device ground using minimal trace length."}, {"pin_number": "6", "pin_name": "RT", "pin_description": "Timing resistor connection pin for oscillator frequency programming"}, {"pin_number": "7", "pin_name": "Discharge", "pin_description": "A single resistor between CT and the discharge terminals provides dead-time adjustment"}, {"pin_number": "8", "pin_name": "Soft Start", "pin_description": "Soft-start input pin."}, {"pin_number": "9", "pin_name": "Compensation", "pin_description": "Output of the error amplifier for compensation"}, {"pin_number": "10", "pin_name": "Shutdown", "pin_description": "Pull this pin high to shut down PWM output"}, {"pin_number": "11", "pin_name": "Output A", "pin_description": "output A of the on-chip drive stage"}, {"pin_number": "12", "pin_name": "Ground", "pin_description": "Ground return pin"}, {"pin_number": "13", "pin_name": "Vc", "pin_description": "Power supply pin for the output stage. This pin should be bypassed with a 0.1-µF monolithic ceramic low ESL capacitor with minimal trace lengths."}, {"pin_number": "14", "pin_name": "Output B", "pin_description": "Output B of the on-chip drive stage."}, {"pin_number": "15", "pin_name": "+VIN", "pin_description": "Input voltage"}, {"pin_number": "16", "pin_name": "VREF", "pin_description": "5.1-V reference. For stability, the reference should be bypassed with a 0.1-µF monolithic ceramic low ESL capacitor and minimal trace length to the ground plane."}]}, {"product_part_number": "UCx52xA", "package_type": "PLCC/LCCC (20-Pin)", "pins": [{"pin_number": "1, 6, 11, 16", "pin_name": "NC", "pin_description": "No internal connection"}, {"pin_number": "2", "pin_name": "INV Input", "pin_description": "Inverting input to the error amplifier"}, {"pin_number": "3", "pin_name": "NI Input", "pin_description": "Noninverting input to the error amplifier"}, {"pin_number": "4", "pin_name": "SYNC", "pin_description": "Oscillator sync terminal"}, {"pin_number": "5", "pin_name": "OSC Output", "pin_description": "Oscillator frequency output"}, {"pin_number": "7", "pin_name": "CT", "pin_description": "Timing capacitor connection pin for oscillator frequency programming. The timing capacitor should be connected to the device ground using minimal trace length."}, {"pin_number": "8", "pin_name": "RT", "pin_description": "Timing resistor connection pin for oscillator frequency programming"}, {"pin_number": "9", "pin_name": "Discharge", "pin_description": "A single resistor between CT and the discharge terminals provides dead-time adjustment"}, {"pin_number": "10", "pin_name": "Soft Start", "pin_description": "Soft-start input pin."}, {"pin_number": "12", "pin_name": "Compensation", "pin_description": "Output of the error amplifier for compensation"}, {"pin_number": "13", "pin_name": "Shutdown", "pin_description": "Pull this pin high to shut down PWM output"}, {"pin_number": "14", "pin_name": "Output A", "pin_description": "output A of the on-chip drive stage"}, {"pin_number": "15", "pin_name": "Ground", "pin_description": "Ground return pin"}, {"pin_number": "17", "pin_name": "Vc", "pin_description": "Power supply pin for the output stage. This pin should be bypassed with a 0.1-µF monolithic ceramic low ESL capacitor with minimal trace lengths."}, {"pin_number": "18", "pin_name": "Output B", "pin_description": "Output B of the on-chip drive stage."}, {"pin_number": "19", "pin_name": "+VIN", "pin_description": "Input voltage"}, {"pin_number": "20", "pin_name": "VREF", "pin_description": "5.1-V reference. For stability, the reference should be bypassed with a 0.1-µF monolithic ceramic low ESL capacitor and minimal trace length to the ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS191D", "family_comparison": "The UCx52xA family consists of two main functional types: the UCx525A with NOR logic outputs (LOW for OFF state) and the UCx527A with OR logic outputs (HIGH for OFF state). The family is further divided by temperature grades: UC15xxA for military (-55°C to 125°C), UC25xxA for industrial (-25°C to 85°C), and UC35xxA for commercial (0°C to 70°C). The higher grade versions (UC15xxA/UC25xxA) offer tighter specifications for reference voltage and oscillator stability compared to the commercial grade (UC35xxA).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "35V", "min_input_voltage": "8V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "400mA", "max_switch_frequency": "500kHz", "quiescent_current": "14mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "未找到", "operation_mode": "Push-pull", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "Voltage Mode", "output_voltage_accuracy": "±1%", "output_reference_voltage": "5.1V"}, "package": [{"type": "OPTION", "pin_count": "10", "length": "8.89", "width": "8.89", "pitch": "1.27", "height": "2.03"}]}, {"part_number": "UC1527A", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电压模式PWM控制器", "part_number_title": "UCx52xA Regulating Pulse Width Modulators", "features": ["8-V to 35-V Operation", "5.1-V Reference Trimmed to 1%", "100-Hz to 500-kHz Oscillator Range", "Separate Oscillator Sync Terminal", "Adjustable Dead-Time Control", "Internal Soft Start", "Pulse-by-Pulse Shutdown", "Input Undervoltage Lockout With Hysteresis", "Latching PWM to Prevent Multiple Pulses", "Dual Source and Sink Output Drivers"], "description": "The UC1525A/1527A series of pulse width modulator integrated circuits are designed to offer improved performance and lowered external parts count when used in designing all types of switching power supplies. The on-chip 5.1-V reference is trimmed to 1% and the input common-mode range of the error amplifier includes the reference voltage, eliminating external resistors. A sync input to the oscillator allows multiple units to be slaved or a single unit to be synchronized to an external system clock. A single resistor between CT and the discharge terminals provides a wide range of dead-time adjustment. These devices also feature built-in soft-start circuitry with only an external timing capacitor required. A shutdown terminal controls both the soft-start circuitry and the output stages, providing instantaneous turn off through the PWM latch with pulsed shutdown, as well as soft-start recycle with longer shutdown commands. The UC1527A uses OR logic, which results in a HIGH output level when OFF.", "applications": ["Off-Line and DC/DC Power Supplies", "Converters Using Voltage Mode", "Single-Ended or Two-Switch Topology Designs", "Solar Inverters", "Welding Inverters", "Motor Control", "Battery Chargers"], "ordering_information": [{"part_number": "UC1527A", "order_device": "5962-8951104EA", "package_type": "CDIP", "package_drawing_code": "J", "output_voltage": "未找到", "min_operation_temp": "-55", "max_operation_temp": "125"}, {"part_number": "UC1527A", "order_device": "UC1527AJ", "package_type": "CDIP", "package_drawing_code": "J", "output_voltage": "未找到", "min_operation_temp": "-55", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UCx52xA", "package_type": "CDIP/PDIP (16-Pin)", "pins": [{"pin_number": "1", "pin_name": "INV Input", "pin_description": "Inverting input to the error amplifier"}, {"pin_number": "2", "pin_name": "NI Input", "pin_description": "Noninverting input to the error amplifier"}, {"pin_number": "3", "pin_name": "SYNC", "pin_description": "Oscillator sync terminal"}, {"pin_number": "4", "pin_name": "OSC Output", "pin_description": "Oscillator frequency output"}, {"pin_number": "5", "pin_name": "CT", "pin_description": "Timing capacitor connection pin for oscillator frequency programming. The timing capacitor should be connected to the device ground using minimal trace length."}, {"pin_number": "6", "pin_name": "RT", "pin_description": "Timing resistor connection pin for oscillator frequency programming"}, {"pin_number": "7", "pin_name": "Discharge", "pin_description": "A single resistor between CT and the discharge terminals provides dead-time adjustment"}, {"pin_number": "8", "pin_name": "Soft Start", "pin_description": "Soft-start input pin."}, {"pin_number": "9", "pin_name": "Compensation", "pin_description": "Output of the error amplifier for compensation"}, {"pin_number": "10", "pin_name": "Shutdown", "pin_description": "Pull this pin high to shut down PWM output"}, {"pin_number": "11", "pin_name": "Output A", "pin_description": "output A of the on-chip drive stage"}, {"pin_number": "12", "pin_name": "Ground", "pin_description": "Ground return pin"}, {"pin_number": "13", "pin_name": "Vc", "pin_description": "Power supply pin for the output stage. This pin should be bypassed with a 0.1-µF monolithic ceramic low ESL capacitor with minimal trace lengths."}, {"pin_number": "14", "pin_name": "Output B", "pin_description": "Output B of the on-chip drive stage."}, {"pin_number": "15", "pin_name": "+VIN", "pin_description": "Input voltage"}, {"pin_number": "16", "pin_name": "VREF", "pin_description": "5.1-V reference. For stability, the reference should be bypassed with a 0.1-µF monolithic ceramic low ESL capacitor and minimal trace length to the ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS191D", "family_comparison": "The UCx52xA family consists of two main functional types: the UCx525A with NOR logic outputs (LOW for OFF state) and the UCx527A with OR logic outputs (HIGH for OFF state). The family is further divided by temperature grades: UC15xxA for military (-55°C to 125°C), UC25xxA for industrial (-25°C to 85°C), and UC35xxA for commercial (0°C to 70°C). The higher grade versions (UC15xxA/UC25xxA) offer tighter specifications for reference voltage and oscillator stability compared to the commercial grade (UC35xxA).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "35V", "min_input_voltage": "8V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "400mA", "max_switch_frequency": "500kHz", "quiescent_current": "14mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "未找到", "operation_mode": "Push-pull", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "Voltage Mode", "output_voltage_accuracy": "±1%", "output_reference_voltage": "5.1V"}, "package": [{"type": "OPTION", "pin_count": "10", "length": "8.89", "width": "8.89", "pitch": "1.27", "height": "2.03"}]}, {"part_number": "UC2525A", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电压模式PWM控制器", "part_number_title": "UCx52xA Regulating Pulse Width Modulators", "features": ["8-V to 35-V Operation", "5.1-V Reference Trimmed to 1%", "100-Hz to 500-kHz Oscillator Range", "Separate Oscillator Sync Terminal", "Adjustable Dead-Time Control", "Internal Soft Start", "Pulse-by-Pulse Shutdown", "Input Undervoltage Lockout With Hysteresis", "Latching PWM to Prevent Multiple Pulses", "Dual Source and Sink Output Drivers"], "description": "The UC2525A series of pulse width modulator integrated circuits are designed to offer improved performance and lowered external parts count when used in designing all types of switching power supplies. The on-chip 5.1-V reference is trimmed to 1% and the input common-mode range of the error amplifier includes the reference voltage, eliminating external resistors. A sync input to the oscillator allows multiple units to be slaved or a single unit to be synchronized to an external system clock. A single resistor between CT and the discharge terminals provides a wide range of dead-time adjustment. These devices also feature built-in soft-start circuitry with only an external timing capacitor required. A shutdown terminal controls both the soft-start circuitry and the output stages, providing instantaneous turn off through the PWM latch with pulsed shutdown, as well as soft-start recycle with longer shutdown commands. The UC2525A output stage features NOR logic, giving a LOW output for an OFF state.", "applications": ["Off-Line and DC/DC Power Supplies", "Converters Using Voltage Mode", "Single-Ended or Two-Switch Topology Designs", "Solar Inverters", "Welding Inverters", "Motor Control", "Battery Chargers"], "ordering_information": [{"part_number": "UC2525A", "order_device": "UC2525ADW", "package_type": "SOIC", "package_drawing_code": "DW", "output_voltage": "未找到", "min_operation_temp": "-25", "max_operation_temp": "85", "status": "Obsolete"}, {"part_number": "UC2525A", "order_device": "UC2525ADWTR", "package_type": "SOIC", "package_drawing_code": "DW", "output_voltage": "未找到", "min_operation_temp": "-25", "max_operation_temp": "85"}, {"part_number": "UC2525A", "order_device": "UC2525AJ", "package_type": "CDIP", "package_drawing_code": "J", "output_voltage": "未找到", "min_operation_temp": "-25", "max_operation_temp": "85"}, {"part_number": "UC2525A", "order_device": "UC2525AN", "package_type": "PDIP", "package_drawing_code": "N", "output_voltage": "未找到", "min_operation_temp": "-25", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "UCx52xA", "package_type": "CDIP/PDIP (16-Pin)", "pins": [{"pin_number": "1", "pin_name": "INV Input", "pin_description": "Inverting input to the error amplifier"}, {"pin_number": "2", "pin_name": "NI Input", "pin_description": "Noninverting input to the error amplifier"}, {"pin_number": "3", "pin_name": "SYNC", "pin_description": "Oscillator sync terminal"}, {"pin_number": "4", "pin_name": "OSC Output", "pin_description": "Oscillator frequency output"}, {"pin_number": "5", "pin_name": "CT", "pin_description": "Timing capacitor connection pin for oscillator frequency programming. The timing capacitor should be connected to the device ground using minimal trace length."}, {"pin_number": "6", "pin_name": "RT", "pin_description": "Timing resistor connection pin for oscillator frequency programming"}, {"pin_number": "7", "pin_name": "Discharge", "pin_description": "A single resistor between CT and the discharge terminals provides dead-time adjustment"}, {"pin_number": "8", "pin_name": "Soft Start", "pin_description": "Soft-start input pin."}, {"pin_number": "9", "pin_name": "Compensation", "pin_description": "Output of the error amplifier for compensation"}, {"pin_number": "10", "pin_name": "Shutdown", "pin_description": "Pull this pin high to shut down PWM output"}, {"pin_number": "11", "pin_name": "Output A", "pin_description": "output A of the on-chip drive stage"}, {"pin_number": "12", "pin_name": "Ground", "pin_description": "Ground return pin"}, {"pin_number": "13", "pin_name": "Vc", "pin_description": "Power supply pin for the output stage. This pin should be bypassed with a 0.1-µF monolithic ceramic low ESL capacitor with minimal trace lengths."}, {"pin_number": "14", "pin_name": "Output B", "pin_description": "Output B of the on-chip drive stage."}, {"pin_number": "15", "pin_name": "+VIN", "pin_description": "Input voltage"}, {"pin_number": "16", "pin_name": "VREF", "pin_description": "5.1-V reference. For stability, the reference should be bypassed with a 0.1-µF monolithic ceramic low ESL capacitor and minimal trace length to the ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS191D", "family_comparison": "The UCx52xA family consists of two main functional types: the UCx525A with NOR logic outputs (LOW for OFF state) and the UCx527A with OR logic outputs (HIGH for OFF state). The family is further divided by temperature grades: UC15xxA for military (-55°C to 125°C), UC25xxA for industrial (-25°C to 85°C), and UC35xxA for commercial (0°C to 70°C). The higher grade versions (UC15xxA/UC25xxA) offer tighter specifications for reference voltage and oscillator stability compared to the commercial grade (UC35xxA).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "35V", "min_input_voltage": "8V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "400mA", "max_switch_frequency": "500kHz", "quiescent_current": "14mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "未找到", "operation_mode": "Push-pull", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "Voltage Mode", "output_voltage_accuracy": "±1%", "output_reference_voltage": "5.1V"}, "package": [{"type": "OPTION", "pin_count": "10", "length": "8.89", "width": "8.89", "pitch": "1.27", "height": "2.03"}]}, {"part_number": "UC2527A", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电压模式PWM控制器", "part_number_title": "UCx52xA Regulating Pulse Width Modulators", "features": ["8-V to 35-V Operation", "5.1-V Reference Trimmed to 1%", "100-Hz to 500-kHz Oscillator Range", "Separate Oscillator Sync Terminal", "Adjustable Dead-Time Control", "Internal Soft Start", "Pulse-by-Pulse Shutdown", "Input Undervoltage Lockout With Hysteresis", "Latching PWM to Prevent Multiple Pulses", "Dual Source and Sink Output Drivers"], "description": "The UC2527A series of pulse width modulator integrated circuits are designed to offer improved performance and lowered external parts count when used in designing all types of switching power supplies. The on-chip 5.1-V reference is trimmed to 1% and the input common-mode range of the error amplifier includes the reference voltage, eliminating external resistors. A sync input to the oscillator allows multiple units to be slaved or a single unit to be synchronized to an external system clock. A single resistor between CT and the discharge terminals provides a wide range of dead-time adjustment. These devices also feature built-in soft-start circuitry with only an external timing capacitor required. A shutdown terminal controls both the soft-start circuitry and the output stages, providing instantaneous turn off through the PWM latch with pulsed shutdown, as well as soft-start recycle with longer shutdown commands. The UC2527A uses OR logic, which results in a HIGH output level when OFF.", "applications": ["Off-Line and DC/DC Power Supplies", "Converters Using Voltage Mode", "Single-Ended or Two-Switch Topology Designs", "Solar Inverters", "Welding Inverters", "Motor Control", "Battery Chargers"], "ordering_information": [{"part_number": "UC2527A", "order_device": "UC2527AN", "package_type": "PDIP", "package_drawing_code": "N", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "UCx52xA", "package_type": "CDIP/PDIP (16-Pin)", "pins": [{"pin_number": "1", "pin_name": "INV Input", "pin_description": "Inverting input to the error amplifier"}, {"pin_number": "2", "pin_name": "NI Input", "pin_description": "Noninverting input to the error amplifier"}, {"pin_number": "3", "pin_name": "SYNC", "pin_description": "Oscillator sync terminal"}, {"pin_number": "4", "pin_name": "OSC Output", "pin_description": "Oscillator frequency output"}, {"pin_number": "5", "pin_name": "CT", "pin_description": "Timing capacitor connection pin for oscillator frequency programming. The timing capacitor should be connected to the device ground using minimal trace length."}, {"pin_number": "6", "pin_name": "RT", "pin_description": "Timing resistor connection pin for oscillator frequency programming"}, {"pin_number": "7", "pin_name": "Discharge", "pin_description": "A single resistor between CT and the discharge terminals provides dead-time adjustment"}, {"pin_number": "8", "pin_name": "Soft Start", "pin_description": "Soft-start input pin."}, {"pin_number": "9", "pin_name": "Compensation", "pin_description": "Output of the error amplifier for compensation"}, {"pin_number": "10", "pin_name": "Shutdown", "pin_description": "Pull this pin high to shut down PWM output"}, {"pin_number": "11", "pin_name": "Output A", "pin_description": "output A of the on-chip drive stage"}, {"pin_number": "12", "pin_name": "Ground", "pin_description": "Ground return pin"}, {"pin_number": "13", "pin_name": "Vc", "pin_description": "Power supply pin for the output stage. This pin should be bypassed with a 0.1-µF monolithic ceramic low ESL capacitor with minimal trace lengths."}, {"pin_number": "14", "pin_name": "Output B", "pin_description": "Output B of the on-chip drive stage."}, {"pin_number": "15", "pin_name": "+VIN", "pin_description": "Input voltage"}, {"pin_number": "16", "pin_name": "VREF", "pin_description": "5.1-V reference. For stability, the reference should be bypassed with a 0.1-µF monolithic ceramic low ESL capacitor and minimal trace length to the ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS191D", "family_comparison": "The UCx52xA family consists of two main functional types: the UCx525A with NOR logic outputs (LOW for OFF state) and the UCx527A with OR logic outputs (HIGH for OFF state). The family is further divided by temperature grades: UC15xxA for military (-55°C to 125°C), UC25xxA for industrial (-25°C to 85°C), and UC35xxA for commercial (0°C to 70°C). The higher grade versions (UC15xxA/UC25xxA) offer tighter specifications for reference voltage and oscillator stability compared to the commercial grade (UC35xxA).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "35V", "min_input_voltage": "8V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "400mA", "max_switch_frequency": "500kHz", "quiescent_current": "14mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "未找到", "operation_mode": "Push-pull", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "Voltage Mode", "output_voltage_accuracy": "±1%", "output_reference_voltage": "5.1V"}, "package": [{"type": "OPTION", "pin_count": "10", "length": "8.89", "width": "8.89", "pitch": "1.27", "height": "2.03"}]}, {"part_number": "UC3525A", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电压模式PWM控制器", "part_number_title": "UCx52xA Regulating Pulse Width Modulators", "features": ["8-V to 35-V Operation", "5.1-V Reference Trimmed to 1%", "100-Hz to 500-kHz Oscillator Range", "Separate Oscillator Sync Terminal", "Adjustable Dead-Time Control", "Internal Soft Start", "Pulse-by-Pulse Shutdown", "Input Undervoltage Lockout With Hysteresis", "Latching PWM to Prevent Multiple Pulses", "Dual Source and Sink Output Drivers"], "description": "The UC3525A series of pulse width modulator integrated circuits are designed to offer improved performance and lowered external parts count when used in designing all types of switching power supplies. The on-chip 5.1-V reference is trimmed to 1% and the input common-mode range of the error amplifier includes the reference voltage, eliminating external resistors. A sync input to the oscillator allows multiple units to be slaved or a single unit to be synchronized to an external system clock. A single resistor between CT and the discharge terminals provides a wide range of dead-time adjustment. These devices also feature built-in soft-start circuitry with only an external timing capacitor required. A shutdown terminal controls both the soft-start circuitry and the output stages, providing instantaneous turn off through the PWM latch with pulsed shutdown, as well as soft-start recycle with longer shutdown commands. The UC3525A output stage features NOR logic, giving a LOW output for an OFF state.", "applications": ["Off-Line and DC/DC Power Supplies", "Converters Using Voltage Mode", "Single-Ended or Two-Switch Topology Designs", "Solar Inverters", "Welding Inverters", "Motor Control", "Battery Chargers"], "ordering_information": [{"part_number": "UC3525A", "order_device": "UC3525ADW", "package_type": "SOIC", "package_drawing_code": "DW", "output_voltage": "未找到", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3525A", "order_device": "UC3525ADWTR", "package_type": "SOIC", "package_drawing_code": "DW", "output_voltage": "未找到", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3525A", "order_device": "UC3525AJ", "package_type": "CDIP", "package_drawing_code": "J", "output_voltage": "未找到", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3525A", "order_device": "UC3525AN", "package_type": "PDIP", "package_drawing_code": "N", "output_voltage": "未找到", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "UCx52xA", "package_type": "CDIP/PDIP (16-Pin)", "pins": [{"pin_number": "1", "pin_name": "INV Input", "pin_description": "Inverting input to the error amplifier"}, {"pin_number": "2", "pin_name": "NI Input", "pin_description": "Noninverting input to the error amplifier"}, {"pin_number": "3", "pin_name": "SYNC", "pin_description": "Oscillator sync terminal"}, {"pin_number": "4", "pin_name": "OSC Output", "pin_description": "Oscillator frequency output"}, {"pin_number": "5", "pin_name": "CT", "pin_description": "Timing capacitor connection pin for oscillator frequency programming. The timing capacitor should be connected to the device ground using minimal trace length."}, {"pin_number": "6", "pin_name": "RT", "pin_description": "Timing resistor connection pin for oscillator frequency programming"}, {"pin_number": "7", "pin_name": "Discharge", "pin_description": "A single resistor between CT and the discharge terminals provides dead-time adjustment"}, {"pin_number": "8", "pin_name": "Soft Start", "pin_description": "Soft-start input pin."}, {"pin_number": "9", "pin_name": "Compensation", "pin_description": "Output of the error amplifier for compensation"}, {"pin_number": "10", "pin_name": "Shutdown", "pin_description": "Pull this pin high to shut down PWM output"}, {"pin_number": "11", "pin_name": "Output A", "pin_description": "output A of the on-chip drive stage"}, {"pin_number": "12", "pin_name": "Ground", "pin_description": "Ground return pin"}, {"pin_number": "13", "pin_name": "Vc", "pin_description": "Power supply pin for the output stage. This pin should be bypassed with a 0.1-µF monolithic ceramic low ESL capacitor with minimal trace lengths."}, {"pin_number": "14", "pin_name": "Output B", "pin_description": "Output B of the on-chip drive stage."}, {"pin_number": "15", "pin_name": "+VIN", "pin_description": "Input voltage"}, {"pin_number": "16", "pin_name": "VREF", "pin_description": "5.1-V reference. For stability, the reference should be bypassed with a 0.1-µF monolithic ceramic low ESL capacitor and minimal trace length to the ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS191D", "family_comparison": "The UCx52xA family consists of two main functional types: the UCx525A with NOR logic outputs (LOW for OFF state) and the UCx527A with OR logic outputs (HIGH for OFF state). The family is further divided by temperature grades: UC15xxA for military (-55°C to 125°C), UC25xxA for industrial (-25°C to 85°C), and UC35xxA for commercial (0°C to 70°C). The higher grade versions (UC15xxA/UC25xxA) offer tighter specifications for reference voltage and oscillator stability compared to the commercial grade (UC35xxA).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "35V", "min_input_voltage": "8V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "400mA", "max_switch_frequency": "500kHz", "quiescent_current": "14mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "未找到", "operation_mode": "Push-pull", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "Voltage Mode", "output_voltage_accuracy": "±2%", "output_reference_voltage": "5.1V"}, "package": [{"type": "OPTION", "pin_count": "10", "length": "8.89", "width": "8.89", "pitch": "1.27", "height": "2.03"}]}, {"part_number": "UC3527A", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电压模式PWM控制器", "part_number_title": "UCx52xA Regulating Pulse Width Modulators", "features": ["8-V to 35-V Operation", "5.1-V Reference Trimmed to 1%", "100-Hz to 500-kHz Oscillator Range", "Separate Oscillator Sync Terminal", "Adjustable Dead-Time Control", "Internal Soft Start", "Pulse-by-Pulse Shutdown", "Input Undervoltage Lockout With Hysteresis", "Latching PWM to Prevent Multiple Pulses", "Dual Source and Sink Output Drivers"], "description": "The UC3527A series of pulse width modulator integrated circuits are designed to offer improved performance and lowered external parts count when used in designing all types of switching power supplies. The on-chip 5.1-V reference is trimmed to 1% and the input common-mode range of the error amplifier includes the reference voltage, eliminating external resistors. A sync input to the oscillator allows multiple units to be slaved or a single unit to be synchronized to an external system clock. A single resistor between CT and the discharge terminals provides a wide range of dead-time adjustment. These devices also feature built-in soft-start circuitry with only an external timing capacitor required. A shutdown terminal controls both the soft-start circuitry and the output stages, providing instantaneous turn off through the PWM latch with pulsed shutdown, as well as soft-start recycle with longer shutdown commands. The UC3527A uses OR logic, which results in a HIGH output level when OFF.", "applications": ["Off-Line and DC/DC Power Supplies", "Converters Using Voltage Mode", "Single-Ended or Two-Switch Topology Designs", "Solar Inverters", "Welding Inverters", "Motor Control", "Battery Chargers"], "ordering_information": [{"part_number": "UC3527A", "order_device": "UC3527AN", "package_type": "PDIP", "package_drawing_code": "N", "output_voltage": "未找到", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "UCx52xA", "package_type": "CDIP/PDIP (16-Pin)", "pins": [{"pin_number": "1", "pin_name": "INV Input", "pin_description": "Inverting input to the error amplifier"}, {"pin_number": "2", "pin_name": "NI Input", "pin_description": "Noninverting input to the error amplifier"}, {"pin_number": "3", "pin_name": "SYNC", "pin_description": "Oscillator sync terminal"}, {"pin_number": "4", "pin_name": "OSC Output", "pin_description": "Oscillator frequency output"}, {"pin_number": "5", "pin_name": "CT", "pin_description": "Timing capacitor connection pin for oscillator frequency programming. The timing capacitor should be connected to the device ground using minimal trace length."}, {"pin_number": "6", "pin_name": "RT", "pin_description": "Timing resistor connection pin for oscillator frequency programming"}, {"pin_number": "7", "pin_name": "Discharge", "pin_description": "A single resistor between CT and the discharge terminals provides dead-time adjustment"}, {"pin_number": "8", "pin_name": "Soft Start", "pin_description": "Soft-start input pin."}, {"pin_number": "9", "pin_name": "Compensation", "pin_description": "Output of the error amplifier for compensation"}, {"pin_number": "10", "pin_name": "Shutdown", "pin_description": "Pull this pin high to shut down PWM output"}, {"pin_number": "11", "pin_name": "Output A", "pin_description": "output A of the on-chip drive stage"}, {"pin_number": "12", "pin_name": "Ground", "pin_description": "Ground return pin"}, {"pin_number": "13", "pin_name": "Vc", "pin_description": "Power supply pin for the output stage. This pin should be bypassed with a 0.1-µF monolithic ceramic low ESL capacitor with minimal trace lengths."}, {"pin_number": "14", "pin_name": "Output B", "pin_description": "Output B of the on-chip drive stage."}, {"pin_number": "15", "pin_name": "+VIN", "pin_description": "Input voltage"}, {"pin_number": "16", "pin_name": "VREF", "pin_description": "5.1-V reference. For stability, the reference should be bypassed with a 0.1-µF monolithic ceramic low ESL capacitor and minimal trace length to the ground plane."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS191D", "family_comparison": "The UCx52xA family consists of two main functional types: the UCx525A with NOR logic outputs (LOW for OFF state) and the UCx527A with OR logic outputs (HIGH for OFF state). The family is further divided by temperature grades: UC15xxA for military (-55°C to 125°C), UC25xxA for industrial (-25°C to 85°C), and UC35xxA for commercial (0°C to 70°C). The higher grade versions (UC15xxA/UC25xxA) offer tighter specifications for reference voltage and oscillator stability compared to the commercial grade (UC35xxA).", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "35V", "min_input_voltage": "8V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "400mA", "max_switch_frequency": "500kHz", "quiescent_current": "14mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "未找到", "operation_mode": "Push-pull", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "Voltage Mode", "output_voltage_accuracy": "±2%", "output_reference_voltage": "5.1V"}, "package": [{"type": "OPTION", "pin_count": "10", "length": "8.89", "width": "8.89", "pitch": "1.27", "height": "2.03"}]}]