[{"part_number": "UC1572", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "DC-DC控制器", "part_number_title": "Negative Output Flyback Pulse Width Modulator", "features": ["Simple Single Inductor Flyback PWM for Negative Voltage Generation", "Drives External PMOS Switch", "Contains UVLO Circuit", "Includes Pulse-by-Pulse Current Limit", "Low 50µA Sleep Mode Current"], "description": "The UC3572 is a negative output flyback pulse width modulator which converts a positive input voltage to a regulated negative output voltage. The chip is optimized for use in a single inductor negative flyback switching converter employing an external PMOS switch. The block diagram consists of a precision reference, an error amplifier configured for voltage mode operation, an oscillator, a PWM comparator with latching logic, and a 0.5A peak gate driver. The UC3572 includes an undervoltage lockout circuit to insure sufficient input supply voltage is present before any switching activity can occur, and a pulse-by-pulse current limit. Output current can be sensed and limited to a user determined maximum value. The UVLO circuit turns the chip off when the input voltage is below the UVLO threshold. In addition, a sleep comparator interfaces to the UVLO circuit to turn the chip off. This reduces the supply current to only 50µA, making the UC3572 ideal for battery powered applications.", "applications": ["Negative Voltage Generation", "Battery Powered Applications", "+5V to -12V flyback converter"], "ordering_information": [{"part_number": "UC1572", "order_device": "UC1572J", "package_type": "CDIP", "package_drawing_code": "J", "min_operation_temp": "-55", "max_operation_temp": "125", "output_voltage": "可调"}], "pin_function": [{"product_part_number": "UC1572", "package_type": "DIL-8/SOIC-8", "pins": [{"pin_number": "1", "pin_name": "EAINV", "pin_description": "Inverting input to error amplifier. Summing junction for 3VREF and VOUT sense. The non-inverting input of the error amplifier is internally connected to GND. This pin will source a maximum of 1mA."}, {"pin_number": "2", "pin_name": "EAOUT", "pin_description": "Output of error amplifier. Use EAOUT and EAINV for loop compensation components."}, {"pin_number": "3", "pin_name": "CS", "pin_description": "Current limit sense pin. Connect to a ground referenced current sense resistor in series with the flyback inductor. OUT will be held high (PMOS switch off) if CS exceeds 0.2V."}, {"pin_number": "4", "pin_name": "VCC", "pin_description": "Input voltage supply to chip. Range is 4.75 to 30V. Bypass with a 1µF capacitor."}, {"pin_number": "5", "pin_name": "OUT", "pin_description": "Gate drive for external PMOS switch connected between Vcc and the flyback inductor. OUT drives the gate of the PMOS switch between Vcc and GND."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Circuit Ground."}, {"pin_number": "7", "pin_name": "RAMP", "pin_description": "Oscillator and ramp for pulse width modulator. Frequency is set by a capacitor to GND by the equation F = 1 / (15k * CRAMP). Recommended operating frequency range is 10kHz to 200kHz."}, {"pin_number": "8", "pin_name": "3VREF", "pin_description": "Precision 3V reference. Bypass with 100nF capacitor to GND."}]}], "datasheet_cn": "未找到", "datasheet_en": "slus275a.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "PMOS", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "30V", "min_input_voltage": "4.75V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "外部可调", "max_switch_frequency": "200kHz", "quiescent_current": "9000µA", "high_side_mosfet_resistance": "不适用(外部器件)", "low_side_mosfet_resistance": "不适用(外部器件)", "over_current_protection_threshold": "0.205V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Sleep Mode", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "±2%", "output_reference_voltage": "3V"}, "package": [{"type": "OPTION", "pin_count": "1572", "pitch": "3.94", "height": "3.94", "width": "0.5", "length": "507"}]}, {"part_number": "UC2572", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "DC-DC控制器", "part_number_title": "Negative Output Flyback Pulse Width Modulator", "features": ["Simple Single Inductor Flyback PWM for Negative Voltage Generation", "Drives External PMOS Switch", "Contains UVLO Circuit", "Includes Pulse-by-Pulse Current Limit", "Low 50µA Sleep Mode Current"], "description": "The UC3572 is a negative output flyback pulse width modulator which converts a positive input voltage to a regulated negative output voltage. The chip is optimized for use in a single inductor negative flyback switching converter employing an external PMOS switch. The block diagram consists of a precision reference, an error amplifier configured for voltage mode operation, an oscillator, a PWM comparator with latching logic, and a 0.5A peak gate driver. The UC3572 includes an undervoltage lockout circuit to insure sufficient input supply voltage is present before any switching activity can occur, and a pulse-by-pulse current limit. Output current can be sensed and limited to a user determined maximum value. The UVLO circuit turns the chip off when the input voltage is below the UVLO threshold. In addition, a sleep comparator interfaces to the UVLO circuit to turn the chip off. This reduces the supply current to only 50µA, making the UC3572 ideal for battery powered applications.", "applications": ["Negative Voltage Generation", "Battery Powered Applications", "+5V to -12V flyback converter"], "ordering_information": [{"part_number": "UC2572", "order_device": "UC2572D", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "可调"}, {"part_number": "UC2572", "order_device": "UC2572DTR", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "可调"}, {"part_number": "UC2572", "order_device": "UC2572N", "package_type": "PDIP", "package_drawing_code": "N", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "可调"}, {"part_number": "UC2572", "order_device": "UC2572J", "package_type": "CDIP", "package_drawing_code": "J", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "可调"}], "pin_function": [{"product_part_number": "UC2572", "package_type": "DIL-8/SOIC-8", "pins": [{"pin_number": "1", "pin_name": "EAINV", "pin_description": "Inverting input to error amplifier. Summing junction for 3VREF and VOUT sense. The non-inverting input of the error amplifier is internally connected to GND. This pin will source a maximum of 1mA."}, {"pin_number": "2", "pin_name": "EAOUT", "pin_description": "Output of error amplifier. Use EAOUT and EAINV for loop compensation components."}, {"pin_number": "3", "pin_name": "CS", "pin_description": "Current limit sense pin. Connect to a ground referenced current sense resistor in series with the flyback inductor. OUT will be held high (PMOS switch off) if CS exceeds 0.2V."}, {"pin_number": "4", "pin_name": "VCC", "pin_description": "Input voltage supply to chip. Range is 4.75 to 30V. Bypass with a 1µF capacitor."}, {"pin_number": "5", "pin_name": "OUT", "pin_description": "Gate drive for external PMOS switch connected between Vcc and the flyback inductor. OUT drives the gate of the PMOS switch between Vcc and GND."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Circuit Ground."}, {"pin_number": "7", "pin_name": "RAMP", "pin_description": "Oscillator and ramp for pulse width modulator. Frequency is set by a capacitor to GND by the equation F = 1 / (15k * CRAMP). Recommended operating frequency range is 10kHz to 200kHz."}, {"pin_number": "8", "pin_name": "3VREF", "pin_description": "Precision 3V reference. Bypass with 100nF capacitor to GND."}]}], "datasheet_cn": "未找到", "datasheet_en": "slus275a.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "PMOS", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "30V", "min_input_voltage": "4.75V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "外部可调", "max_switch_frequency": "200kHz", "quiescent_current": "9000µA", "high_side_mosfet_resistance": "不适用(外部器件)", "low_side_mosfet_resistance": "不适用(外部器件)", "over_current_protection_threshold": "0.205V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Sleep Mode", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "±2%", "output_reference_voltage": "3V"}, "package": [{"type": "OPTION", "pin_count": "1572", "pitch": "3.94", "height": "3.94", "width": "0.5", "length": "507"}]}, {"part_number": "UC3572", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "DC-DC控制器", "part_number_title": "Negative Output Flyback Pulse Width Modulator", "features": ["Simple Single Inductor Flyback PWM for Negative Voltage Generation", "Drives External PMOS Switch", "Contains UVLO Circuit", "Includes Pulse-by-Pulse Current Limit", "Low 50µA Sleep Mode Current"], "description": "The UC3572 is a negative output flyback pulse width modulator which converts a positive input voltage to a regulated negative output voltage. The chip is optimized for use in a single inductor negative flyback switching converter employing an external PMOS switch. The block diagram consists of a precision reference, an error amplifier configured for voltage mode operation, an oscillator, a PWM comparator with latching logic, and a 0.5A peak gate driver. The UC3572 includes an undervoltage lockout circuit to insure sufficient input supply voltage is present before any switching activity can occur, and a pulse-by-pulse current limit. Output current can be sensed and limited to a user determined maximum value. The UVLO circuit turns the chip off when the input voltage is below the UVLO threshold. In addition, a sleep comparator interfaces to the UVLO circuit to turn the chip off. This reduces the supply current to only 50µA, making the UC3572 ideal for battery powered applications.", "applications": ["Negative Voltage Generation", "Battery Powered Applications", "+5V to -12V flyback converter"], "ordering_information": [{"part_number": "UC3572", "order_device": "UC3572D", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "0", "max_operation_temp": "70", "output_voltage": "可调"}, {"part_number": "UC3572", "order_device": "UC3572DTR", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "0", "max_operation_temp": "70", "output_voltage": "可调"}, {"part_number": "UC3572", "order_device": "UC3572N", "package_type": "PDIP", "package_drawing_code": "N", "min_operation_temp": "0", "max_operation_temp": "70", "output_voltage": "可调"}], "pin_function": [{"product_part_number": "UC3572", "package_type": "DIL-8/SOIC-8", "pins": [{"pin_number": "1", "pin_name": "EAINV", "pin_description": "Inverting input to error amplifier. Summing junction for 3VREF and VOUT sense. The non-inverting input of the error amplifier is internally connected to GND. This pin will source a maximum of 1mA."}, {"pin_number": "2", "pin_name": "EAOUT", "pin_description": "Output of error amplifier. Use EAOUT and EAINV for loop compensation components."}, {"pin_number": "3", "pin_name": "CS", "pin_description": "Current limit sense pin. Connect to a ground referenced current sense resistor in series with the flyback inductor. OUT will be held high (PMOS switch off) if CS exceeds 0.2V."}, {"pin_number": "4", "pin_name": "VCC", "pin_description": "Input voltage supply to chip. Range is 4.75 to 30V. Bypass with a 1µF capacitor."}, {"pin_number": "5", "pin_name": "OUT", "pin_description": "Gate drive for external PMOS switch connected between Vcc and the flyback inductor. OUT drives the gate of the PMOS switch between Vcc and GND."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Circuit Ground."}, {"pin_number": "7", "pin_name": "RAMP", "pin_description": "Oscillator and ramp for pulse width modulator. Frequency is set by a capacitor to GND by the equation F = 1 / (15k * CRAMP). Recommended operating frequency range is 10kHz to 200kHz."}, {"pin_number": "8", "pin_name": "3VREF", "pin_description": "Precision 3V reference. Bypass with 100nF capacitor to GND."}]}], "datasheet_cn": "未找到", "datasheet_en": "slus275a.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "PMOS", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "30V", "min_input_voltage": "4.75V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "外部可调", "max_switch_frequency": "200kHz", "quiescent_current": "9000µA", "high_side_mosfet_resistance": "不适用(外部器件)", "low_side_mosfet_resistance": "不适用(外部器件)", "over_current_protection_threshold": "0.205V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Sleep Mode", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "电压模式", "output_voltage_accuracy": "±2%", "output_reference_voltage": "3V"}, "package": [{"type": "OPTION", "pin_count": "1572", "pitch": "3.94", "height": "3.94", "width": "0.5", "length": "507"}]}]