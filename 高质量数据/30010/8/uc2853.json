[{"part_number": "UC2853A", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "功率因数校正(PFC)", "part_number_title": "High Power Factor Preregulator", "features": ["Complete 8-pin Power Factor Solution", "Reduced External Components", "RMS Line Voltage Compensation", "Precision Multiplier/Squarer/Divider", "Internal 63kHz Synchronizable Oscillator", "Average Current Mode PWM Control", "Overvoltage Protection Comparator", "High Current, Clamped Gate Driver"], "description": "The UC2853A provides simple, yet high performance active power factor correction. Using the same control technique as the UC1854, this 8-pin device exploits a simplified architecture and an internal oscillator to minimize external component count. The UC2853A incorporates a precision multiplier/squarer/divider circuit, voltage and current loop error amplifiers, and a precision voltage reference to implement average current mode control with RMS line voltage compensation. This control technique maintains constant loop gain with changes in input voltage, which minimizes input line current distortion over the worldwide input voltage range. The UC2853A is identical to the UC2853 except the internal oscillator frequency has been reduced from 75kHz to 63kHz.", "applications": ["Active power factor correction"], "ordering_information": [{"part_number": "UC2853A", "order_device": "UC2853ADTR", "package_type": "SOIC", "package_drawing_code": "D0008A", "min_operation_temp": "-40", "max_operation_temp": "105"}, {"part_number": "UC2853A", "order_device": "UC2853AD", "package_type": "SOIC", "package_drawing_code": "D0008A", "min_operation_temp": "-40", "max_operation_temp": "105"}], "pin_function": [{"product_part_number": "UC2853A", "package_type": "PDIP-8, SOIC-8", "pins": [{"pin_number": "1", "pin_name": "IAC", "pin_description": "AC Waveform Input. This input provides voltage waveform information to the multiplier. The current loop will try to produce a current waveform with the same shape as the IAC signal. Connect a resistor from the rectified input line to IAC which will conduct 500µA at maximum line voltage."}, {"pin_number": "2", "pin_name": "VCC", "pin_description": "Input Supply Voltage. This pin supplies power to the chip and provides an input voltage level signal to the squarer circuit. The UC2853A input voltage range extends from 12V to 40V."}, {"pin_number": "3", "pin_name": "OUT", "pin_description": "Gate Driver Output. OUT provides high current gate drive for the external power MOSFET. A 15V clamp prevents excessive MOSFET gate-to-source voltage."}, {"pin_number": "4", "pin_name": "GND", "pin_description": "Ground. All voltages are measured with respect to GND."}, {"pin_number": "5", "pin_name": "FB", "pin_description": "Voltage Amplifier Inverting Input, Overvoltage Comparator Input, Sync Input. This pin accepts a fraction of the power factor corrected output voltage through a voltage divider, and is nominally regulated to 3V. FB voltages 5% greater than nominal will trip the overvoltage comparator and shut down the output stage."}, {"pin_number": "6", "pin_name": "VCOMP", "pin_description": "Voltage Loop Error Amplifier Output. A feedback impedance between VCOMP and FB for loop compensation must be avoided. Compensate the voltage loop with an impedance between VCOMP and GND."}, {"pin_number": "7", "pin_name": "IMO", "pin_description": "Multiplier Output and Current Sense Inverting Input. The output of the multiplier and the inverting input of the current amplifier are connected together at IMO."}, {"pin_number": "8", "pin_name": "ICOMP", "pin_description": "Current Loop Error Amplifier Output. Compensate the current loop by placing an impedance between ICOMP and IMO."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS650A - FEBRUARY 2005 - REVISED JANUARY 2006", "family_comparison": "UC1853 is specified for operation from -55°C to +125°C, UC2853 is specified for operation from -25°C to +85°C, UC3853 is specified for operation from 0°C to +70°C, and UC2853A is specified for operation from -40°C to 105°C.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "40V", "min_input_voltage": "11.5V", "max_output_voltage": "Adjustable", "min_output_voltage": "Adjustable", "max_output_current": "未找到", "max_switch_frequency": "63kHz", "quiescent_current": "10000µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "未找到", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "2.5%", "output_reference_voltage": "3V"}, "package": [{"type": "SOIC", "pin_count": "8", "pitch": "40", "height": "40", "length": "63", "width": "1.5"}]}, {"part_number": "UC1853", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "功率因数校正(PFC)", "part_number_title": "High Power Factor Preregulator", "features": ["Complete 8-pin Power Factor Solution", "Reduced External Components", "RMS Line Voltage Compensation", "Precision Multiplier/Squarer/Divider", "Internal 75kHz Synchronizable Oscillator", "Average Current Mode PWM Control", "Overvoltage Protection Comparator", "High Current, Clamped Gate Driver"], "description": "The UC3853 provides simple, yet high performance active power factor correction. Using the same control technique as the UC1854, this 8-pin device exploits a simplified architecture and an internal oscillator to minimize external component count. The UC3853 incorporates a precision multiplier/squarer/divider circuit, voltage and current loop error amplifiers, and a precision voltage reference to implement average current mode control with RMS line voltage compensation. This control technique maintains constant loop gain with changes in input voltage, which minimizes input line current distortion over the worldwide input voltage range.", "applications": ["Active power factor correction"], "ordering_information": [], "pin_function": [{"product_part_number": "UC1853", "package_type": "PDIP-8, SOIC-8", "pins": [{"pin_number": "1", "pin_name": "IAC", "pin_description": "AC Waveform Input. This input provides voltage waveform information to the multiplier. The current loop will try to produce a current waveform with the same shape as the IAC signal. Connect a resistor from the rectified input line to IAC which will conduct 500µA at maximum line voltage."}, {"pin_number": "2", "pin_name": "VCC", "pin_description": "Input Supply Voltage. This pin supplies power to the chip and provides an input voltage level signal to the squarer circuit. The UC2853A input voltage range extends from 12V to 40V."}, {"pin_number": "3", "pin_name": "OUT", "pin_description": "Gate Driver Output. OUT provides high current gate drive for the external power MOSFET. A 15V clamp prevents excessive MOSFET gate-to-source voltage."}, {"pin_number": "4", "pin_name": "GND", "pin_description": "Ground. All voltages are measured with respect to GND."}, {"pin_number": "5", "pin_name": "FB", "pin_description": "Voltage Amplifier Inverting Input, Overvoltage Comparator Input, Sync Input. This pin accepts a fraction of the power factor corrected output voltage through a voltage divider, and is nominally regulated to 3V. FB voltages 5% greater than nominal will trip the overvoltage comparator and shut down the output stage."}, {"pin_number": "6", "pin_name": "VCOMP", "pin_description": "Voltage Loop Error Amplifier Output. A feedback impedance between VCOMP and FB for loop compensation must be avoided. Compensate the voltage loop with an impedance between VCOMP and GND."}, {"pin_number": "7", "pin_name": "IMO", "pin_description": "Multiplier Output and Current Sense Inverting Input. The output of the multiplier and the inverting input of the current amplifier are connected together at IMO."}, {"pin_number": "8", "pin_name": "ICOMP", "pin_description": "Current Loop Error Amplifier Output. Compensate the current loop by placing an impedance between ICOMP and IMO."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS650A - FEBRUARY 2005 - REVISED JANUARY 2006", "family_comparison": "UC1853 is specified for operation from -55°C to +125°C, UC2853 is specified for operation from -25°C to +85°C, UC3853 is specified for operation from 0°C to +70°C, and UC2853A is specified for operation from -40°C to 105°C.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "40V", "min_input_voltage": "11.5V", "max_output_voltage": "Adjustable", "min_output_voltage": "Adjustable", "max_output_current": "未找到", "max_switch_frequency": "75kHz", "quiescent_current": "10000µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "未找到", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "2.5%", "output_reference_voltage": "3V"}, "package": [{"type": "SOIC", "pin_count": "8", "pitch": "40", "height": "40", "length": "63", "width": "1.5"}]}, {"part_number": "UC2853", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industry", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "功率因数校正(PFC)", "part_number_title": "High Power Factor Preregulator", "features": ["Complete 8-pin Power Factor Solution", "Reduced External Components", "RMS Line Voltage Compensation", "Precision Multiplier/Squarer/Divider", "Internal 75kHz Synchronizable Oscillator", "Average Current Mode PWM Control", "Overvoltage Protection Comparator", "High Current, Clamped Gate Driver"], "description": "The UC3853 provides simple, yet high performance active power factor correction. Using the same control technique as the UC1854, this 8-pin device exploits a simplified architecture and an internal oscillator to minimize external component count. The UC3853 incorporates a precision multiplier/squarer/divider circuit, voltage and current loop error amplifiers, and a precision voltage reference to implement average current mode control with RMS line voltage compensation. This control technique maintains constant loop gain with changes in input voltage, which minimizes input line current distortion over the worldwide input voltage range.", "applications": ["Active power factor correction"], "ordering_information": [{"part_number": "UC2853", "order_device": "UC2853D", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-25", "max_operation_temp": "85"}, {"part_number": "UC2853", "order_device": "UC2853DTR", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "-25", "max_operation_temp": "85"}, {"part_number": "UC2853", "order_device": "UC2853N", "package_type": "PDIP", "package_drawing_code": "P", "min_operation_temp": "-25", "max_operation_temp": "85"}], "pin_function": [{"product_part_number": "UC2853", "package_type": "PDIP-8, SOIC-8", "pins": [{"pin_number": "1", "pin_name": "IAC", "pin_description": "AC Waveform Input. This input provides voltage waveform information to the multiplier. The current loop will try to produce a current waveform with the same shape as the IAC signal. Connect a resistor from the rectified input line to IAC which will conduct 500µA at maximum line voltage."}, {"pin_number": "2", "pin_name": "VCC", "pin_description": "Input Supply Voltage. This pin supplies power to the chip and provides an input voltage level signal to the squarer circuit. The UC2853A input voltage range extends from 12V to 40V."}, {"pin_number": "3", "pin_name": "OUT", "pin_description": "Gate Driver Output. OUT provides high current gate drive for the external power MOSFET. A 15V clamp prevents excessive MOSFET gate-to-source voltage."}, {"pin_number": "4", "pin_name": "GND", "pin_description": "Ground. All voltages are measured with respect to GND."}, {"pin_number": "5", "pin_name": "FB", "pin_description": "Voltage Amplifier Inverting Input, Overvoltage Comparator Input, Sync Input. This pin accepts a fraction of the power factor corrected output voltage through a voltage divider, and is nominally regulated to 3V. FB voltages 5% greater than nominal will trip the overvoltage comparator and shut down the output stage."}, {"pin_number": "6", "pin_name": "VCOMP", "pin_description": "Voltage Loop Error Amplifier Output. A feedback impedance between VCOMP and FB for loop compensation must be avoided. Compensate the voltage loop with an impedance between VCOMP and GND."}, {"pin_number": "7", "pin_name": "IMO", "pin_description": "Multiplier Output and Current Sense Inverting Input. The output of the multiplier and the inverting input of the current amplifier are connected together at IMO."}, {"pin_number": "8", "pin_name": "ICOMP", "pin_description": "Current Loop Error Amplifier Output. Compensate the current loop by placing an impedance between ICOMP and IMO."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS650A - FEBRUARY 2005 - REVISED JANUARY 2006", "family_comparison": "UC1853 is specified for operation from -55°C to +125°C, UC2853 is specified for operation from -25°C to +85°C, UC3853 is specified for operation from 0°C to +70°C, and UC2853A is specified for operation from -40°C to 105°C.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "40V", "min_input_voltage": "11.5V", "max_output_voltage": "Adjustable", "min_output_voltage": "Adjustable", "max_output_current": "未找到", "max_switch_frequency": "75kHz", "quiescent_current": "10000µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "未找到", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "2.5%", "output_reference_voltage": "3V"}, "package": [{"type": "SOIC", "pin_count": "8", "pitch": "40", "height": "40", "length": "63", "width": "1.5"}]}, {"part_number": "UC3853", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Consumer", "category_lv1": "电源管理芯片", "category_lv2": "AC/DC转换器", "category_lv3": "功率因数校正(PFC)", "part_number_title": "High Power Factor Preregulator", "features": ["Complete 8-pin Power Factor Solution", "Reduced External Components", "RMS Line Voltage Compensation", "Precision Multiplier/Squarer/Divider", "Internal 75kHz Synchronizable Oscillator", "Average Current Mode PWM Control", "Overvoltage Protection Comparator", "High Current, Clamped Gate Driver"], "description": "The UC3853 provides simple, yet high performance active power factor correction. Using the same control technique as the UC1854, this 8-pin device exploits a simplified architecture and an internal oscillator to minimize external component count. The UC3853 incorporates a precision multiplier/squarer/divider circuit, voltage and current loop error amplifiers, and a precision voltage reference to implement average current mode control with RMS line voltage compensation. This control technique maintains constant loop gain with changes in input voltage, which minimizes input line current distortion over the worldwide input voltage range.", "applications": ["Active power factor correction"], "ordering_information": [{"part_number": "UC3853", "order_device": "UC3853D", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3853", "order_device": "UC3853DTR", "package_type": "SOIC", "package_drawing_code": "D", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3853", "order_device": "UC3853N", "package_type": "PDIP", "package_drawing_code": "P", "min_operation_temp": "0", "max_operation_temp": "70"}, {"part_number": "UC3853", "order_device": "UC3853NG4", "package_type": "PDIP", "package_drawing_code": "P", "min_operation_temp": "0", "max_operation_temp": "70"}], "pin_function": [{"product_part_number": "UC3853", "package_type": "PDIP-8, SOIC-8", "pins": [{"pin_number": "1", "pin_name": "IAC", "pin_description": "AC Waveform Input. This input provides voltage waveform information to the multiplier. The current loop will try to produce a current waveform with the same shape as the IAC signal. Connect a resistor from the rectified input line to IAC which will conduct 500µA at maximum line voltage."}, {"pin_number": "2", "pin_name": "VCC", "pin_description": "Input Supply Voltage. This pin supplies power to the chip and provides an input voltage level signal to the squarer circuit. The UC2853A input voltage range extends from 12V to 40V."}, {"pin_number": "3", "pin_name": "OUT", "pin_description": "Gate Driver Output. OUT provides high current gate drive for the external power MOSFET. A 15V clamp prevents excessive MOSFET gate-to-source voltage."}, {"pin_number": "4", "pin_name": "GND", "pin_description": "Ground. All voltages are measured with respect to GND."}, {"pin_number": "5", "pin_name": "FB", "pin_description": "Voltage Amplifier Inverting Input, Overvoltage Comparator Input, Sync Input. This pin accepts a fraction of the power factor corrected output voltage through a voltage divider, and is nominally regulated to 3V. FB voltages 5% greater than nominal will trip the overvoltage comparator and shut down the output stage."}, {"pin_number": "6", "pin_name": "VCOMP", "pin_description": "Voltage Loop Error Amplifier Output. A feedback impedance between VCOMP and FB for loop compensation must be avoided. Compensate the voltage loop with an impedance between VCOMP and GND."}, {"pin_number": "7", "pin_name": "IMO", "pin_description": "Multiplier Output and Current Sense Inverting Input. The output of the multiplier and the inverting input of the current amplifier are connected together at IMO."}, {"pin_number": "8", "pin_name": "ICOMP", "pin_description": "Current Loop Error Amplifier Output. Compensate the current loop by placing an impedance between ICOMP and IMO."}]}], "datasheet_cn": "未找到", "datasheet_en": "SLUS650A - FEBRUARY 2005 - REVISED JANUARY 2006", "family_comparison": "UC1853 is specified for operation from -55°C to +125°C, UC2853 is specified for operation from -25°C to +85°C, UC3853 is specified for operation from 0°C to +70°C, and UC2853A is specified for operation from -40°C to 105°C.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "40V", "min_input_voltage": "11.5V", "max_output_voltage": "Adjustable", "min_output_voltage": "Adjustable", "max_output_current": "未找到", "max_switch_frequency": "75kHz", "quiescent_current": "10000µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "未找到", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "No", "output_discharge": "No", "integrated_ldo": "No", "dynamic_voltage_setting": "No", "pass_through_mode": "No", "load_disconnect": "No", "loop_control_mode": "平均电流模式", "output_voltage_accuracy": "2.5%", "output_reference_voltage": "3V"}, "package": [{"type": "SOIC", "pin_count": "8", "pitch": "40", "height": "40", "length": "63", "width": "1.5"}]}]