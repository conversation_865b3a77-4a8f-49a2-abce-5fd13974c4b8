{"part_number": "UC2856Q", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Auto", "category_lv1": "电源管理芯片", "category_lv2": "直流直流变换器", "category_lv3": "PWM控制器", "part_number_title": "UC2856Q IMPROVED CURRENT MODE PWM CONTROLLER", "features": ["Pin-for-Pin Compatible With the UC2846", "65-ns Typical Delay From Shutdown to Outputs and 50-ns Typical Delay From Sync to Outputs", "Improved Current Sense Amplifier With Reduced Noise Sensitivity", "Differential Current Sense With 3-V Common Mode Range", "Trimmed Oscillator Discharge Current for Accurate Deadband Control", "Accurate 1-V Shutdown Threshold", "High Current Dual Totem Pole Outputs (1.5-A peak)", "TTL Compatible Oscillator SYNC Pin Thresholds", "4-kV ESD Protection"], "description": "The UC2856 is a high performance version of the popular UC2846 series of current mode controllers, and is intended for both new applications and new upgrades of designs using the UC2846. All input to output logic is identical to the UC2846, and the current sense output is slew rate limited to reduce noise sensitivity. Fast 1.5-A peak output stages have been added to allow rapid switching of power FETs. A low impedance TTL compatible sync output has been implemented with a 3-state function when used as a sync input. Internal chip grounding has been improved to minimize internal noise caused when driving large capacitive loads. This, in conjunction with the improved differential current sense amplifier, results in enhanced noise immunity. Other features include a trimmed oscillator current (8%) for accurate frequency and dead time control; a 1 V, 5% shutdown threshold; and 4 kV minimum ESD protection on all pins.", "applications": [], "ordering_information": [{"part_number": "UC2856Q", "order_device": "UC2856QDWR", "package_type": "SOIC", "package_drawing_code": "DW0016A", "output_voltage": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125"}], "pin_function": [{"product_part_number": "UC2856Q", "package_type": "DW (SOIC-16)", "pins": [{"pin_number": "1", "pin_name": "CL SS", "pin_description": "Current Limit / Soft Start. Sets the current limit threshold and soft-start ramp time with an external capacitor."}, {"pin_number": "2", "pin_name": "VREF", "pin_description": "5.1V Reference Voltage Output."}, {"pin_number": "3", "pin_name": "CS-", "pin_description": "Inverting input for the differential current sense amplifier."}, {"pin_number": "4", "pin_name": "CS+", "pin_description": "Non-inverting input for the differential current sense amplifier."}, {"pin_number": "5", "pin_name": "EA+", "pin_description": "Non-inverting input to the error amplifier."}, {"pin_number": "6", "pin_name": "EA-", "pin_description": "Inverting input to the error amplifier."}, {"pin_number": "7", "pin_name": "COMP", "pin_description": "Error Amplifier output and PWM comparator input for frequency compensation."}, {"pin_number": "8", "pin_name": "CT", "pin_description": "Oscillator timing capacitor connection."}, {"pin_number": "9", "pin_name": "RT", "pin_description": "Oscillator timing resistor connection."}, {"pin_number": "10", "pin_name": "SYNC", "pin_description": "Oscillator synchronization input/output."}, {"pin_number": "11", "pin_name": "AOUT", "pin_description": "Totem-pole driver output A."}, {"pin_number": "12", "pin_name": "GND", "pin_description": "Ground."}, {"pin_number": "13", "pin_name": "VC", "pin_description": "Collector supply voltage for the output drivers."}, {"pin_number": "14", "pin_name": "BOUT", "pin_description": "Totem-pole driver output B."}, {"pin_number": "15", "pin_name": "VIN", "pin_description": "Input supply voltage."}, {"pin_number": "16", "pin_name": "SHUTDOWN", "pin_description": "Shutdown input. A voltage above 1V shuts down the controller."}]}], "datasheet_cn": "未找到", "datasheet_en": "SGLS265A.pdf", "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "40V", "min_input_voltage": "8V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "未找到", "max_switch_frequency": "未找到", "quiescent_current": "18mA", "high_side_mosfet_resistance": "不适用(不集成)", "low_side_mosfet_resistance": "不适用(不集成)", "over_current_protection_threshold": "外部可调", "operation_mode": "电流模式", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "No", "output_discharge": "False", "integrated_ldo": "True", "dynamic_voltage_setting": "False", "pass_through_mode": "False", "load_disconnect": "False", "loop_control_mode": "峰值电流模式", "output_voltage_accuracy": "±2%", "output_reference_voltage": "5.1V"}, "package": [{"type": "SOIC", "height": "2.65", "length": "10.5", "width": "7.6", "pin_count": "16", "pitch": "1.27"}]}